<?php

namespace App\Services;

use App\Models\Organization;
use App\Models\Plan;
use App\Models\User;
use App\Models\Branch;
use Carbon\Carbon;

class PlanLimitService
{
    /**
     * Check if organization can add a new user.
     */
    public static function canAddUser(Organization $organization): bool
    {
        $plan = $organization->plan;
        if (!$plan || $plan->user_limit === null) {
            return true; // No limit or unlimited plan
        }

        $currentUserCount = $organization->users()->count();
        return $currentUserCount < $plan->user_limit;
    }

    /**
     * Check if organization can add a new branch.
     */
    public static function canAddBranch(Organization $organization): bool
    {
        $plan = $organization->plan;
        if (!$plan || $plan->branch_limit === null) {
            return true; // No limit or unlimited plan
        }

        $currentBranchCount = $organization->branches()->count();
        return $currentBranchCount < $plan->branch_limit;
    }

    /**
     * Check if organization can create a new order.
     */
    public static function canCreateOrder(Organization $organization): bool
    {
        $plan = $organization->plan;
        if (!$plan || $plan->order_limit === null) {
            return true; // No limit or unlimited plan
        }

        $currentMonthOrders = $organization->getCurrentMonthOrderCount();
        return $currentMonthOrders < $plan->order_limit;
    }

    /**
     * Get remaining user slots for organization.
     */
    public static function getRemainingUserSlots(Organization $organization): int
    {
        $plan = $organization->plan;
        if (!$plan || $plan->user_limit === null) {
            return PHP_INT_MAX; // Unlimited
        }

        $currentUserCount = $organization->users()->count();
        return max(0, $plan->user_limit - $currentUserCount);
    }

    /**
     * Get remaining branch slots for organization.
     */
    public static function getRemainingBranchSlots(Organization $organization): int
    {
        $plan = $organization->plan;
        if (!$plan || $plan->branch_limit === null) {
            return PHP_INT_MAX; // Unlimited
        }

        $currentBranchCount = $organization->branches()->count();
        return max(0, $plan->branch_limit - $currentBranchCount);
    }

    /**
     * Get remaining order slots for current month.
     */
    public static function getRemainingOrderSlots(Organization $organization): int
    {
        $plan = $organization->plan;
        if (!$plan || $plan->order_limit === null) {
            return PHP_INT_MAX; // Unlimited
        }

        $currentMonthOrders = $organization->getCurrentMonthOrderCount();
        return max(0, $plan->order_limit - $currentMonthOrders);
    }

    /**
     * Get data retention cutoff date for organization.
     */
    public static function getDataRetentionCutoff(Organization $organization): ?Carbon
    {
        $plan = $organization->plan;
        if (!$plan || $plan->data_retention_days === null) {
            return null; // No retention limit
        }

        return Carbon::now()->subDays($plan->data_retention_days);
    }

    /**
     * Check if organization has exceeded any limits.
     */
    public static function checkLimits(Organization $organization): array
    {
        $violations = [];
        $plan = $organization->plan;

        if (!$plan) {
            return $violations;
        }

        // Check user limit
        if ($plan->user_limit !== null) {
            $currentUsers = $organization->users()->count();
            if ($currentUsers > $plan->user_limit) {
                $violations[] = [
                    'type' => 'user_limit',
                    'message' => "User limit exceeded: {$currentUsers}/{$plan->user_limit}",
                    'current' => $currentUsers,
                    'limit' => $plan->user_limit,
                ];
            }
        }

        // Check branch limit
        if ($plan->branch_limit !== null) {
            $currentBranches = $organization->branches()->count();
            if ($currentBranches > $plan->branch_limit) {
                $violations[] = [
                    'type' => 'branch_limit',
                    'message' => "Branch limit exceeded: {$currentBranches}/{$plan->branch_limit}",
                    'current' => $currentBranches,
                    'limit' => $plan->branch_limit,
                ];
            }
        }

        return $violations;
    }

    /**
     * Get plan usage summary for organization.
     */
    public static function getUsageSummary(Organization $organization): array
    {
        $plan = $organization->plan;

        if (!$plan) {
            return [
                'plan_name' => 'No Plan',
                'users' => ['current' => 0, 'limit' => 'Unlimited', 'percentage' => 0],
                'branches' => ['current' => 0, 'limit' => 'Unlimited', 'percentage' => 0],
                'orders' => ['current' => 0, 'limit' => 'Unlimited', 'percentage' => 0],
                'data_retention' => ['days' => 'Unlimited', 'cutoff_date' => null],
            ];
        }

        $currentUsers = $organization->users()->count();
        $currentBranches = $organization->branches()->count();
        $currentMonthOrders = $organization->getCurrentMonthOrderCount();

        return [
            'plan_name' => $plan->name,
            'users' => [
                'current' => $currentUsers,
                'limit' => $plan->user_limit ?? 'Unlimited',
                'percentage' => $plan->user_limit ? round(($currentUsers / $plan->user_limit) * 100, 1) : 0,
                'remaining' => $plan->user_limit ? max(0, $plan->user_limit - $currentUsers) : 'Unlimited',
            ],
            'branches' => [
                'current' => $currentBranches,
                'limit' => $plan->branch_limit ?? 'Unlimited',
                'percentage' => $plan->branch_limit ? round(($currentBranches / $plan->branch_limit) * 100, 1) : 0,
                'remaining' => $plan->branch_limit ? max(0, $plan->branch_limit - $currentBranches) : 'Unlimited',
            ],
            'orders' => [
                'current' => $currentMonthOrders,
                'limit' => $plan->order_limit ?? 'Unlimited',
                'percentage' => $plan->order_limit ? round(($currentMonthOrders / $plan->order_limit) * 100, 1) : 0,
                'remaining' => $plan->order_limit ? max(0, $plan->order_limit - $currentMonthOrders) : 'Unlimited',
                'period' => 'This month',
            ],
            'data_retention' => [
                'days' => $plan->data_retention_days ?? 'Unlimited',
                'cutoff_date' => self::getDataRetentionCutoff($organization),
            ],
        ];
    }

    /**
     * Clean up old data based on plan retention policy.
     */
    public static function cleanupOldData(Organization $organization): int
    {
        $cutoffDate = self::getDataRetentionCutoff($organization);

        if (!$cutoffDate) {
            return 0; // No cleanup needed
        }

        $deletedCount = 0;

        // Clean up old sales records (example - adjust based on your models)
        if (class_exists('\App\Models\Sale')) {
            $deletedSales = \App\Models\Sale::where('organization_id', $organization->id)
                ->where('created_at', '<', $cutoffDate)
                ->delete();
            $deletedCount += $deletedSales;
        }

        // Clean up old inventory movements (example)
        if (class_exists('\App\Models\InventoryMovement')) {
            $deletedMovements = \App\Models\InventoryMovement::whereHas('product', function($query) use ($organization) {
                $query->where('organization_id', $organization->id);
            })->where('created_at', '<', $cutoffDate)->delete();
            $deletedCount += $deletedMovements;
        }

        // Add more cleanup logic for other models as needed

        return $deletedCount;
    }

    /**
     * Get plan limit error message for specific limit type.
     */
    public static function getLimitErrorMessage(string $limitType, Organization $organization): string
    {
        $plan = $organization->plan;

        switch ($limitType) {
            case 'user':
                return "Your {$plan->name} plan allows up to {$plan->user_limit} users. Please upgrade your plan to add more users.";
            case 'branch':
                return "Your {$plan->name} plan allows up to {$plan->branch_limit} branches. Please upgrade your plan to add more branches.";
            case 'data_retention':
                return "Your {$plan->name} plan retains data for {$plan->data_retention_days} days. Older data will be automatically removed.";
            default:
                return "This action exceeds your current plan limits. Please upgrade your plan.";
        }
    }
}
