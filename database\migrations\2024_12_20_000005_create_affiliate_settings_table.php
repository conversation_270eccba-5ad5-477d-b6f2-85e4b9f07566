<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('affiliate_settings', function (Blueprint $table) {
            $table->id();
            $table->decimal('default_commission_rate', 5, 2)->default(10.00); // Default 10%
            $table->decimal('minimum_withdrawal', 10, 2)->default(50.00); // Minimum $50
            $table->json('payment_methods')->default('["bank_transfer", "paypal"]'); // Available payment methods
            $table->text('terms_and_conditions')->nullable();
            $table->boolean('auto_approve_earnings')->default(false);
            $table->boolean('auto_approve_affiliates')->default(false);
            $table->integer('cookie_duration_days')->default(30); // Attribution window
            $table->decimal('withdrawal_fee_percentage', 5, 2)->default(0.00);
            $table->decimal('withdrawal_fee_fixed', 10, 2)->default(0.00);
            $table->boolean('program_active')->default(true);
            $table->text('welcome_message')->nullable();
            $table->json('commission_tiers')->nullable(); // Tiered commission structure
            $table->boolean('recurring_commissions')->default(false);
            $table->integer('max_referrals_per_affiliate')->nullable(); // Optional limit
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('affiliate_settings');
    }
};
