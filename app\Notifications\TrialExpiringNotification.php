<?php

namespace App\Notifications;

use App\Models\Organization;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TrialExpiringNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $organization;
    protected $daysRemaining;

    /**
     * Create a new notification instance.
     */
    public function __construct(Organization $organization, int $daysRemaining)
    {
        $this->organization = $organization;
        $this->daysRemaining = $daysRemaining;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $subject = $this->getSubject();
        $greeting = $this->getGreeting();
        $message = $this->getMessage();
        $actionText = $this->getActionText();
        $actionUrl = $this->getActionUrl();

        return (new MailMessage)
            ->subject($subject)
            ->greeting($greeting)
            ->line($message)
            ->line($this->getTrialDetails())
            ->action($actionText, $actionUrl)
            ->line('Thank you for trying our Sales Management System!')
            ->line('If you have any questions, please contact our support team.');
    }

    /**
     * Get the subject line
     */
    private function getSubject(): string
    {
        if ($this->daysRemaining === 0) {
            return 'Your trial has expired - Upgrade now to continue';
        } elseif ($this->daysRemaining === 1) {
            return 'Your trial expires tomorrow - Don\'t lose access!';
        } else {
            return "Your trial expires in {$this->daysRemaining} days";
        }
    }

    /**
     * Get the greeting
     */
    private function getGreeting(): string
    {
        return "Hello {$this->organization->name} team,";
    }

    /**
     * Get the main message
     */
    private function getMessage(): string
    {
        if ($this->daysRemaining === 0) {
            return 'Your free trial has expired. To continue using our Sales Management System and avoid any interruption to your business operations, please upgrade to a paid plan immediately.';
        } elseif ($this->daysRemaining === 1) {
            return 'Your free trial will expire tomorrow. Don\'t lose access to your sales data and business operations - upgrade now to ensure uninterrupted service.';
        } else {
            return "Your free trial will expire in {$this->daysRemaining} days. To ensure uninterrupted access to your sales management system, we recommend upgrading to a paid plan soon.";
        }
    }

    /**
     * Get trial details
     */
    private function getTrialDetails(): string
    {
        $trialEndDate = $this->organization->trial_ends_at->format('F j, Y');
        
        if ($this->daysRemaining === 0) {
            return "Trial ended: {$trialEndDate}";
        } else {
            return "Trial ends: {$trialEndDate}";
        }
    }

    /**
     * Get action text
     */
    private function getActionText(): string
    {
        if ($this->daysRemaining === 0) {
            return 'Upgrade Now to Restore Access';
        } else {
            return 'Choose Your Plan';
        }
    }

    /**
     * Get action URL
     */
    private function getActionUrl(): string
    {
        // This should point to your plan selection page
        return url('/plan-change');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'organization_id' => $this->organization->id,
            'organization_name' => $this->organization->name,
            'days_remaining' => $this->daysRemaining,
            'trial_ends_at' => $this->organization->trial_ends_at,
            'type' => 'trial_expiring'
        ];
    }
}
