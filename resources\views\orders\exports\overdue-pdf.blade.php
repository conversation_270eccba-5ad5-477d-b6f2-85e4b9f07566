<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Overdue Orders Report</title>
    <style>
        body {
            font-family: <PERSON>ja<PERSON><PERSON>s, Arial, sans-serif;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .company-info {
            margin-bottom: 20px;
            text-align: left;
            font-size: 14px;
        }
        .report-meta {
            margin-bottom: 30px;
            text-align: right;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }
        th, td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 12px;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .overdue-critical {
            color: #dc2626;
            font-weight: bold;
        }
        .overdue-high {
            color: #ea580c;
            font-weight: bold;
        }
        .overdue-medium {
            color: #ca8a04;
        }
        .priority-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #eee;
            font-size: 12px;
            color: #666;
        }
        .page-number {
            position: fixed;
            bottom: 20px;
            right: 20px;
            font-size: 12px;
        }
        .summary {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .summary-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 style="color: #2563eb;">Overdue Orders Report</h1>
        <div class="company-info">
            @php
                $settings = App\Models\Setting::first();
                $organizationName = $settings && $settings->organization_name ? $settings->organization_name : config('app.name');
            @endphp
            {{ $organizationName }}<br>
            Generated by: {{ auth()->user()->name }}<br>
            Contact: {{ auth()->user()->email }}
        </div>
        <div class="report-meta">
            Generated on: {{ now()->format('F d, Y H:i:s') }}<br>
            Report Period: All overdue orders as of {{ now()->format('F d, Y') }}
        </div>
    </div>

    <div class="summary">
        <div class="summary-title">Summary</div>
        <p>Total Overdue Orders: {{ $orders->count() }}</p>
        <p>Critical (>7 days): {{ $orders->where('overdue_days', '>', 7)->count() }}</p>
        <p>High Priority (4-7 days): {{ $orders->whereBetween('overdue_days', [4, 7])->count() }}</p>
        <p>Medium Priority (1-3 days): {{ $orders->whereBetween('overdue_days', [1, 3])->count() }}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>Order #</th>
                <th>Customer</th>
                <th>Expected Delivery</th>
                <th>Days Overdue</th>
                <th>Status</th>
                <th>Priority</th>
            </tr>
        </thead>
        <tbody>
            @foreach($orders as $order)
                <tr>
                    <td>{{ $order->order_number }}</td>
                    <td>
                        <strong>{{ $order->customer_name }}</strong><br>
                        <small>{{ $order->phone_number }}</small>
                    </td>
                    <td>
                        {{ Carbon\Carbon::parse($order->expected_delivery_date)->format('M d, Y') }}<br>
                        <small>{{ $order->expected_delivery_time }}</small>
                    </td>
                    <td class="overdue-{{ $order->overdue_days > 7 ? 'critical' : ($order->overdue_days > 3 ? 'high' : 'medium') }}">
                        {{ $order->overdue_days }} days
                    </td>
                    <td>{{ $order->status }}</td>
                    <td>
                        <span class="priority-tag">{{ $order->priority_level['level'] }}</span>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <p>This report is system-generated and confidential.</p>
        <p>Please take immediate action on critical and high-priority overdue orders.</p>
    </div>

    <div class="page-number"></div>

    <script type="text/php">
        if (isset($pdf)) {
            $text = "Page {PAGE_NUM} of {PAGE_COUNT}";
            $size = 10;
            $font = $fontMetrics->getFont("DejaVu Sans");
            $width = $fontMetrics->get_text_width($text, $font, $size) / 2;
            $x = ($pdf->get_width() - $width) - 50;
            $y = $pdf->get_height() - 35;
            $pdf->page_text($x, $y, $text, $font, $size);
        }
    </script>
</body>
</html>
