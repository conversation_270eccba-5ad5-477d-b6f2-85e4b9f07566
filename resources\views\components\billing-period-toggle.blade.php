@props(['selectedPeriod' => 'monthly'])

<div class="billing-period-toggle mb-4">
    <div class="d-flex justify-content-center">
        <div class="btn-group" role="group" aria-label="Billing Period">
            <input type="radio" 
                   class="btn-check" 
                   name="billing_period" 
                   id="monthly" 
                   value="monthly" 
                   {{ $selectedPeriod === 'monthly' ? 'checked' : '' }}>
            <label class="btn btn-outline-primary" for="monthly">
                <i class="fas fa-calendar-alt me-2"></i>
                Monthly
            </label>

            <input type="radio" 
                   class="btn-check" 
                   name="billing_period" 
                   id="annual" 
                   value="annual" 
                   {{ $selectedPeriod === 'annual' ? 'checked' : '' }}>
            <label class="btn btn-outline-primary" for="annual">
                <i class="fas fa-calendar me-2"></i>
                Annual
                <span class="badge bg-success ms-2">Save up to 20%</span>
            </label>
        </div>
    </div>
    <div class="text-center mt-2">
        <small class="text-muted">
            <span id="period-description">
                @if($selectedPeriod === 'annual')
                    Pay annually and save money with our discounted rates
                @else
                    Pay monthly with flexible billing
                @endif
            </span>
        </small>
    </div>
</div>

<style>
.billing-period-toggle .btn-check:checked + .btn {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

.billing-period-toggle .btn {
    min-width: 120px;
    padding: 12px 20px;
    font-weight: 500;
}

.billing-period-toggle .badge {
    font-size: 0.7em;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const monthlyRadio = document.getElementById('monthly');
    const annualRadio = document.getElementById('annual');
    const periodDescription = document.getElementById('period-description');
    
    function updateDescription() {
        if (annualRadio.checked) {
            periodDescription.textContent = 'Pay annually and save money with our discounted rates';
        } else {
            periodDescription.textContent = 'Pay monthly with flexible billing';
        }
        
        // Trigger plan price updates
        updatePlanPrices();
    }
    
    function updatePlanPrices() {
        const selectedPeriod = annualRadio.checked ? 'annual' : 'monthly';
        const planCards = document.querySelectorAll('.plan-card');
        const currencySymbol = '{{ user_currency_symbol() }}';
        
        planCards.forEach(card => {
            const planId = card.dataset.planId;
            const monthlyPrice = parseFloat(card.dataset.monthlyPrice || 0);
            const annualPrice = parseFloat(card.dataset.annualPrice || 0);
            const annualDiscount = parseInt(card.dataset.annualDiscount || 0);
            
            const priceElement = card.querySelector('.plan-price');
            const periodElement = card.querySelector('.plan-period');
            const savingsElement = card.querySelector('.plan-savings');
            
            if (selectedPeriod === 'annual') {
                let effectiveAnnualPrice = annualPrice;
                if (!effectiveAnnualPrice && annualDiscount > 0) {
                    const monthlyTotal = monthlyPrice * 12;
                    effectiveAnnualPrice = monthlyTotal - (monthlyTotal * (annualDiscount / 100));
                }
                
                if (effectiveAnnualPrice > 0) {
                    priceElement.textContent = currencySymbol + effectiveAnnualPrice.toFixed(2);
                    periodElement.textContent = 'per year';

                    const monthlyTotal = monthlyPrice * 12;
                    const savings = monthlyTotal - effectiveAnnualPrice;
                    const savingsPercentage = ((savings / monthlyTotal) * 100).toFixed(0);

                    if (savingsElement && savings > 0) {
                        savingsElement.innerHTML = `<small class="text-success"><i class="fas fa-tag me-1"></i>Save ${currencySymbol}${savings.toFixed(2)} (${savingsPercentage}%) annually</small>`;
                        savingsElement.style.display = 'block';
                    }
                } else {
                    // Fallback to monthly if no annual pricing
                    priceElement.textContent = currencySymbol + monthlyPrice.toFixed(2);
                    periodElement.textContent = 'per month';
                    if (savingsElement) savingsElement.style.display = 'none';
                }
            } else {
                priceElement.textContent = currencySymbol + monthlyPrice.toFixed(2);
                periodElement.textContent = 'per month';
                if (savingsElement) savingsElement.style.display = 'none';
            }
        });
    }
    
    monthlyRadio.addEventListener('change', updateDescription);
    annualRadio.addEventListener('change', updateDescription);
    
    // Initialize
    updateDescription();
});
</script>
