<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use App\Models\SystemLog;
use App\Models\User;
use App\Models\Organization;

class SetupSystemLogs extends Command
{
    protected $signature = 'setup:system-logs';
    protected $description = 'Create system logs table and add sample data';

    public function handle()
    {
        $this->info('Setting up system logs...');

        // Create table if it doesn't exist
        if (!Schema::hasTable('system_logs')) {
            $this->info('Creating system_logs table...');
            
            Schema::create('system_logs', function (Blueprint $table) {
                $table->id();
                $table->string('level', 20)->index();
                $table->text('message');
                $table->json('context')->nullable();
                $table->string('channel', 50)->index();
                $table->timestamp('datetime')->index();
                $table->unsignedBigInteger('user_id')->nullable()->index();
                $table->unsignedBigInteger('organization_id')->nullable()->index();
                $table->string('ip_address', 45)->nullable();
                $table->text('user_agent')->nullable();
                $table->text('url')->nullable();
                $table->string('method', 10)->nullable();
                $table->integer('status_code')->nullable();
                $table->decimal('response_time', 8, 3)->nullable();
                $table->bigInteger('memory_usage')->nullable();
                $table->json('tags')->nullable();
                $table->string('environment', 20)->nullable();
                $table->string('session_id')->nullable()->index();
                $table->string('request_id')->nullable()->index();
                $table->timestamps();
            });

            $this->info('✅ Table created successfully!');
        } else {
            $this->info('Table already exists.');
        }

        // Add sample data
        $this->info('Adding sample log entries...');
        
        $users = User::limit(3)->get();
        $organizations = Organization::limit(2)->get();

        $sampleLogs = [
            [
                'level' => 'info',
                'message' => 'User logged in successfully',
                'channel' => 'authentication',
                'context' => ['login_method' => 'email'],
                'url' => '/login',
                'method' => 'POST',
                'status_code' => 200,
            ],
            [
                'level' => 'warning',
                'message' => 'Failed login attempt',
                'channel' => 'security',
                'context' => ['reason' => 'invalid_password'],
                'url' => '/login',
                'method' => 'POST',
                'status_code' => 401,
            ],
            [
                'level' => 'error',
                'message' => 'Database connection timeout',
                'channel' => 'database',
                'context' => ['timeout' => 30],
                'url' => '/dashboard',
                'method' => 'GET',
                'status_code' => 500,
            ],
            [
                'level' => 'info',
                'message' => 'Order created successfully',
                'channel' => 'application',
                'context' => ['order_id' => 123],
                'url' => '/orders',
                'method' => 'POST',
                'status_code' => 201,
            ],
            [
                'level' => 'critical',
                'message' => 'Payment gateway connection failed',
                'channel' => 'application',
                'context' => ['gateway' => 'stripe'],
                'url' => '/billing/payment',
                'method' => 'POST',
                'status_code' => 502,
            ],
        ];

        foreach ($sampleLogs as $index => $logData) {
            $user = $users->isNotEmpty() ? $users->random() : null;
            $organization = $organizations->isNotEmpty() ? $organizations->random() : null;
            
            SystemLog::create([
                'level' => $logData['level'],
                'message' => $logData['message'],
                'context' => $logData['context'],
                'channel' => $logData['channel'],
                'datetime' => now()->subHours(rand(1, 24)),
                'user_id' => $user?->id,
                'organization_id' => $organization?->id,
                'ip_address' => '192.168.1.' . rand(1, 255),
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'url' => $logData['url'],
                'method' => $logData['method'],
                'status_code' => $logData['status_code'],
                'response_time' => rand(100, 2000) / 1000,
                'memory_usage' => rand(50, 200) * 1024 * 1024,
                'environment' => 'local',
                'session_id' => 'sess_' . uniqid(),
                'request_id' => 'req_' . uniqid(),
            ]);
        }

        $this->info('✅ Added ' . count($sampleLogs) . ' sample log entries!');
        $this->info('🎉 System logs setup complete!');
        $this->info('You can now access the logs at: /super-admin/system-logs');

        return 0;
    }
}
