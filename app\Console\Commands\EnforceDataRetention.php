<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Organization;
use App\Models\Order;
use App\Models\Customer;
use App\Models\Expenditure;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class EnforceDataRetention extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:enforce-retention {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Enforce data retention policies based on organization plans';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('🔍 DRY RUN MODE - No data will be deleted');
        } else {
            $this->info('🗑️  ENFORCING DATA RETENTION POLICIES');
        }

        $organizations = Organization::with('plan')->where('is_active', true)->get();
        $totalDeleted = 0;

        foreach ($organizations as $organization) {
            if (!$organization->plan) {
                $this->warn("⚠️  Organization {$organization->name} has no plan - skipping");
                continue;
            }

            $retentionDays = $organization->plan->data_retention_days;
            
            // Skip unlimited retention
            if ($retentionDays >= 999) {
                $this->info("♾️  Organization {$organization->name} has unlimited retention");
                continue;
            }

            $cutoffDate = Carbon::now()->subDays($retentionDays);
            $this->info("📅 Processing {$organization->name} - Retention: {$retentionDays} days (cutoff: {$cutoffDate->format('Y-m-d')})");

            $deletedCount = $this->enforceRetentionForOrganization($organization, $cutoffDate, $dryRun);
            $totalDeleted += $deletedCount;
        }

        if ($dryRun) {
            $this->info("✅ DRY RUN COMPLETE - Would delete {$totalDeleted} records");
        } else {
            $this->info("✅ DATA RETENTION ENFORCEMENT COMPLETE - Deleted {$totalDeleted} records");
        }

        return 0;
    }

    /**
     * Enforce retention policy for a specific organization
     */
    private function enforceRetentionForOrganization(Organization $organization, Carbon $cutoffDate, bool $dryRun): int
    {
        $deletedCount = 0;

        // 1. Delete old orders
        $oldOrders = Order::where('organization_id', $organization->id)
            ->where('created_at', '<', $cutoffDate)
            ->get();

        foreach ($oldOrders as $order) {
            if ($dryRun) {
                $this->line("  📦 Would delete order: {$order->id} - {$order->created_at->format('Y-m-d')}");
            } else {
                // Delete associated files first
                if ($order->attachment_path && Storage::exists($order->attachment_path)) {
                    Storage::delete($order->attachment_path);
                }
                
                $order->delete();
                $this->line("  🗑️  Deleted order: {$order->id}");
            }
            $deletedCount++;
        }

        // 2. Delete old expenditures
        if (class_exists(Expenditure::class)) {
            $oldExpenditures = Expenditure::where('organization_id', $organization->id)
                ->where('created_at', '<', $cutoffDate)
                ->get();

            foreach ($oldExpenditures as $expenditure) {
                if ($dryRun) {
                    $this->line("  💰 Would delete expenditure: {$expenditure->id} - {$expenditure->created_at->format('Y-m-d')}");
                } else {
                    $expenditure->delete();
                    $this->line("  🗑️  Deleted expenditure: {$expenditure->id}");
                }
                $deletedCount++;
            }
        }

        // 3. Clean up orphaned customer data (customers with no recent orders)
        $inactiveCustomers = Customer::where('organization_id', $organization->id)
            ->whereDoesntHave('orders', function ($query) use ($cutoffDate) {
                $query->where('created_at', '>=', $cutoffDate);
            })
            ->where('created_at', '<', $cutoffDate)
            ->get();

        foreach ($inactiveCustomers as $customer) {
            if ($dryRun) {
                $this->line("  👤 Would delete inactive customer: {$customer->name} - {$customer->created_at->format('Y-m-d')}");
            } else {
                $customer->delete();
                $this->line("  🗑️  Deleted inactive customer: {$customer->name}");
            }
            $deletedCount++;
        }

        // 4. Clean up old log files and temporary data
        $this->cleanupOldFiles($organization, $cutoffDate, $dryRun);

        if ($deletedCount > 0) {
            Log::info("Data retention enforced for organization {$organization->name}: {$deletedCount} records processed", [
                'organization_id' => $organization->id,
                'retention_days' => $organization->plan->data_retention_days,
                'cutoff_date' => $cutoffDate->toDateString(),
                'deleted_count' => $deletedCount,
                'dry_run' => $dryRun
            ]);
        }

        return $deletedCount;
    }

    /**
     * Clean up old files and temporary data
     */
    private function cleanupOldFiles(Organization $organization, Carbon $cutoffDate, bool $dryRun): void
    {
        // Clean up old invoice PDFs
        $invoiceDir = "invoices/org_{$organization->id}";
        if (Storage::exists($invoiceDir)) {
            $files = Storage::files($invoiceDir);
            
            foreach ($files as $file) {
                $fileTime = Storage::lastModified($file);
                if (Carbon::createFromTimestamp($fileTime)->lt($cutoffDate)) {
                    if ($dryRun) {
                        $this->line("  📄 Would delete file: {$file}");
                    } else {
                        Storage::delete($file);
                        $this->line("  🗑️  Deleted file: {$file}");
                    }
                }
            }
        }

        // Clean up old thermal printing receipts
        $receiptDir = "receipts/org_{$organization->id}";
        if (Storage::exists($receiptDir)) {
            $files = Storage::files($receiptDir);
            
            foreach ($files as $file) {
                $fileTime = Storage::lastModified($file);
                if (Carbon::createFromTimestamp($fileTime)->lt($cutoffDate)) {
                    if ($dryRun) {
                        $this->line("  🧾 Would delete receipt: {$file}");
                    } else {
                        Storage::delete($file);
                        $this->line("  🗑️  Deleted receipt: {$file}");
                    }
                }
            }
        }
    }
}
