<?php

namespace App\Services;

use App\Models\Affiliate;
use App\Models\AffiliateReferral;
use App\Models\AffiliateClick;
use App\Models\AffiliateSetting;
use App\Models\Organization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Log;

class ReferralTrackingService
{
    const COOKIE_NAME = 'affiliate_referral';

    /**
     * Track referral from URL parameters
     */
    public function trackReferral(Request $request): ?array
    {
        $referralCode = $request->get('ref');

        if (!$referralCode) {
            // Check if there's an existing cookie
            return $this->getStoredReferral($request);
        }

        // Validate referral code
        $affiliate = Affiliate::where('affiliate_code', $referralCode)
            ->where('status', Affiliate::STATUS_ACTIVE)
            ->first();

        if (!$affiliate) {
            Log::warning('Invalid referral code attempted', ['code' => $referralCode]);
            return null;
        }

        // Check if affiliate program is active
        $settings = AffiliateSetting::getInstance();
        if (!$settings->isProgramActive()) {
            Log::info('Referral attempted but program is inactive', ['code' => $referralCode]);
            return null;
        }

        // Check if affiliate can make more referrals
        if (!$settings->canMakeMoreReferrals($affiliate)) {
            Log::info('Affiliate has reached referral limit', [
                'affiliate_id' => $affiliate->id,
                'code' => $referralCode
            ]);
            return null;
        }

        // Prepare tracking data
        $trackingData = [
            'affiliate_id' => $affiliate->id,
            'affiliate_code' => $referralCode,
            'utm_source' => $request->get('utm_source'),
            'utm_medium' => $request->get('utm_medium'),
            'utm_campaign' => $request->get('utm_campaign'),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referrer' => $request->header('referer'),
            'tracked_at' => now()->toISOString(),
        ];

        // Store in cookie
        $this->storeReferralCookie($trackingData, $settings->getCookieDuration());

        Log::info('Referral tracked successfully', [
            'affiliate_id' => $affiliate->id,
            'code' => $referralCode,
            'ip' => $request->ip()
        ]);

        return $trackingData;
    }

    /**
     * Store referral data in cookie
     */
    protected function storeReferralCookie(array $data, int $durationDays): void
    {
        $minutes = $durationDays * 24 * 60; // Convert days to minutes
        Cookie::queue(Cookie::make(
            self::COOKIE_NAME,
            json_encode($data),
            $minutes,
            '/',
            null,
            false,
            true // HTTP only
        ));
    }

    /**
     * Get stored referral data from cookie
     */
    public function getStoredReferral(Request $request): ?array
    {
        $cookieData = $request->cookie(self::COOKIE_NAME);

        if (!$cookieData) {
            return null;
        }

        try {
            $data = json_decode($cookieData, true);

            // Validate that the affiliate is still active
            $affiliate = Affiliate::find($data['affiliate_id'] ?? null);
            if (!$affiliate || !$affiliate->isActive()) {
                $this->clearReferralCookie();
                return null;
            }

            return $data;
        } catch (\Exception $e) {
            Log::warning('Invalid referral cookie data', ['error' => $e->getMessage()]);
            $this->clearReferralCookie();
            return null;
        }
    }

    /**
     * Clear referral cookie
     */
    public function clearReferralCookie(): void
    {
        Cookie::queue(Cookie::forget(self::COOKIE_NAME));
    }

    /**
     * Create referral record when organization registers
     */
    public function createReferral(Organization $organization, array $referralData): ?AffiliateReferral
    {
        try {
            $affiliate = Affiliate::find($referralData['affiliate_id']);

            if (!$affiliate) {
                Log::error('Affiliate not found when creating referral', [
                    'affiliate_id' => $referralData['affiliate_id']
                ]);
                return null;
            }

            // Check for duplicate referral
            $existingReferral = AffiliateReferral::where('organization_id', $organization->id)
                ->where('affiliate_id', $affiliate->id)
                ->first();

            if ($existingReferral) {
                Log::info('Duplicate referral attempt prevented', [
                    'organization_id' => $organization->id,
                    'affiliate_id' => $affiliate->id
                ]);
                return $existingReferral;
            }

            // Create referral record
            $referral = AffiliateReferral::create([
                'affiliate_id' => $affiliate->id,
                'organization_id' => $organization->id,
                'referral_code' => $referralData['affiliate_code'],
                'registration_date' => now(),
                'status' => AffiliateReferral::STATUS_PENDING,
                'utm_source' => $referralData['utm_source'] ?? null,
                'utm_medium' => $referralData['utm_medium'] ?? null,
                'utm_campaign' => $referralData['utm_campaign'] ?? null,
                'tracking_data' => $referralData,
            ]);

            Log::info('Referral created successfully', [
                'referral_id' => $referral->id,
                'organization_id' => $organization->id,
                'affiliate_id' => $affiliate->id
            ]);

            // Clear the cookie after successful registration
            $this->clearReferralCookie();

            // Mark recent clicks from this IP as converted
            $this->markClicksAsConverted($affiliate->id, $referralData['ip_address'] ?? null);

            return $referral;
        } catch (\Exception $e) {
            Log::error('Failed to create referral', [
                'error' => $e->getMessage(),
                'organization_id' => $organization->id,
                'referral_data' => $referralData
            ]);
            return null;
        }
    }

    /**
     * Mark referral as converted when first payment is made
     */
    public function convertReferral(Organization $organization): bool
    {
        try {
            $referral = AffiliateReferral::where('organization_id', $organization->id)
                ->where('status', AffiliateReferral::STATUS_PENDING)
                ->first();

            if (!$referral) {
                return false;
            }

            $referral->markAsConverted();

            Log::info('Referral converted successfully', [
                'referral_id' => $referral->id,
                'organization_id' => $organization->id,
                'affiliate_id' => $referral->affiliate_id
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to convert referral', [
                'error' => $e->getMessage(),
                'organization_id' => $organization->id
            ]);
            return false;
        }
    }

    /**
     * Generate referral link for affiliate
     */
    public function generateReferralLink(Affiliate $affiliate, array $utmParams = []): string
    {
        $baseUrl = url('/register');
        $params = ['ref' => $affiliate->affiliate_code];

        // Add UTM parameters if provided
        if (!empty($utmParams['utm_source'])) {
            $params['utm_source'] = $utmParams['utm_source'];
        }
        if (!empty($utmParams['utm_medium'])) {
            $params['utm_medium'] = $utmParams['utm_medium'];
        }
        if (!empty($utmParams['utm_campaign'])) {
            $params['utm_campaign'] = $utmParams['utm_campaign'];
        }

        return $baseUrl . '?' . http_build_query($params);
    }

    /**
     * Get referral statistics for affiliate
     */
    public function getReferralStats(Affiliate $affiliate): array
    {
        $referrals = $affiliate->referrals();

        return [
            'total_referrals' => $referrals->count(),
            'pending_referrals' => $referrals->where('status', AffiliateReferral::STATUS_PENDING)->count(),
            'converted_referrals' => $referrals->where('status', AffiliateReferral::STATUS_CONVERTED)->count(),
            'cancelled_referrals' => $referrals->where('status', AffiliateReferral::STATUS_CANCELLED)->count(),
            'conversion_rate' => $affiliate->conversion_rate,
            'total_commission_earned' => $affiliate->total_earnings,
            'pending_commission' => $affiliate->pending_balance,
            'available_balance' => $affiliate->available_balance,
        ];
    }

    /**
     * Check if request has valid referral tracking
     */
    public function hasValidReferral(Request $request): bool
    {
        $referralData = $this->getStoredReferral($request);
        return $referralData !== null;
    }

    /**
     * Get affiliate from referral data
     */
    public function getAffiliateFromReferral(Request $request): ?Affiliate
    {
        $referralData = $this->getStoredReferral($request);

        if (!$referralData) {
            return null;
        }

        return Affiliate::find($referralData['affiliate_id']);
    }

    /**
     * Mark recent clicks as converted when a referral registers
     */
    public function markClicksAsConverted($affiliateId, $ipAddress = null): void
    {
        if (!$ipAddress) {
            return;
        }

        try {
            // Mark clicks from the last 24 hours from this IP as converted
            $yesterday = now()->subDay();

            AffiliateClick::where('affiliate_id', $affiliateId)
                ->where('ip_address', $ipAddress)
                ->where('clicked_at', '>', $yesterday)
                ->where('converted', false)
                ->update(['converted' => true]);

            Log::info('Clicks marked as converted', [
                'affiliate_id' => $affiliateId,
                'ip_address' => $ipAddress
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to mark clicks as converted', [
                'affiliate_id' => $affiliateId,
                'ip_address' => $ipAddress,
                'error' => $e->getMessage()
            ]);
        }
    }
}
