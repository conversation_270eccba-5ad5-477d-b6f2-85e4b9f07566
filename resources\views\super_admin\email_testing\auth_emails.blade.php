@extends('super_admin.layouts.app')

@section('title', 'Authentication Email Testing')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">🔐 Authentication Email Testing</h1>
                <div class="btn-group" role="group">
                    <a href="{{ route('super_admin.email-testing.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Back to Email Testing
                    </a>
                    <a href="{{ route('super_admin.email-testing.account-creation') }}" class="btn btn-outline-success">
                        <i class="fas fa-user-plus"></i> Account Creation Testing
                    </a>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('warning'))
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> {{ session('warning') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle"></i> 
                    @foreach($errors->all() as $error)
                        {{ $error }}<br>
                    @endforeach
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Email Testing Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-paper-plane"></i> Authentication Email Testing Controls
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="authEmailTestForm">
                        @csrf
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="test_email" class="form-label">Test Email Address</label>
                                    <input type="email" class="form-control" id="test_email" name="test_email" 
                                           value="{{ old('test_email', '<EMAIL>') }}" required>
                                    <div class="form-text">Enter the email address where test emails should be sent</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-2">
                                        <button type="submit" formaction="{{ route('super_admin.email-testing.send-verification-test') }}" class="btn btn-success">
                                            <i class="fas fa-check-circle"></i> Test Email Verification
                                        </button>
                                        <button type="submit" formaction="{{ route('super_admin.email-testing.test-affiliate-verification') }}" class="btn btn-info">
                                            <i class="fas fa-user-check"></i> Test Affiliate Verification
                                        </button>
                                        <button type="submit" formaction="{{ route('super_admin.email-testing.send-password-reset-test') }}" class="btn btn-warning">
                                            <i class="fas fa-key"></i> Test Password Reset
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Available Email Templates -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt"></i> Available Authentication Email Templates
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-check-circle"></i> Email Verification
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text"><strong>Template:</strong> <code>emails.verify-email</code></p>
                                    <p class="card-text"><strong>Mailable:</strong> <code>App\Mail\CustomVerifyEmail</code></p>
                                    <p class="card-text"><strong>Trigger:</strong> User registration or manual verification request</p>
                                    <p class="card-text"><strong>Features:</strong></p>
                                    <ul class="small">
                                        <li>Secure verification links</li>
                                        <li>Professional styling</li>
                                        <li>Time-limited tokens</li>
                                        <li>Clear instructions</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-key"></i> Password Reset
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text"><strong>Template:</strong> Laravel default with custom styling</p>
                                    <p class="card-text"><strong>Notification:</strong> Laravel built-in password reset</p>
                                    <p class="card-text"><strong>Trigger:</strong> Password reset request</p>
                                    <p class="card-text"><strong>Features:</strong></p>
                                    <ul class="small">
                                        <li>Secure reset tokens</li>
                                        <li>Time-limited links</li>
                                        <li>Professional branding</li>
                                        <li>Clear reset instructions</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Testing Flow -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-route"></i> Authentication Email Testing Flow
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-success">
                                <i class="fas fa-check-circle"></i> Email Verification Testing
                            </h6>
                            <ol class="small">
                                <li>Enter test email address</li>
                                <li>Click "Test Email Verification" button</li>
                                <li>Check Mailtrap inbox for verification email</li>
                                <li>Verify email template renders correctly</li>
                                <li>Test verification link functionality</li>
                                <li>Confirm professional styling and branding</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">
                                <i class="fas fa-key"></i> Password Reset Testing
                            </h6>
                            <ol class="small">
                                <li>Enter test email address</li>
                                <li>Click "Test Password Reset" button</li>
                                <li>Check Mailtrap inbox for reset email</li>
                                <li>Verify email template renders correctly</li>
                                <li>Test reset link functionality</li>
                                <li>Confirm security token is included</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt"></i> Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ route('super_admin.email-testing.index') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-envelope"></i><br>
                                    <small>Main Email Dashboard</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ route('super_admin.email-testing.account-creation') }}" class="btn btn-outline-success">
                                    <i class="fas fa-user-plus"></i><br>
                                    <small>Account Creation Testing</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ url('/register') }}" class="btn btn-outline-info" target="_blank">
                                    <i class="fas fa-external-link-alt"></i><br>
                                    <small>Test Registration Flow</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ url('/password/reset') }}" class="btn btn-outline-warning" target="_blank">
                                    <i class="fas fa-external-link-alt"></i><br>
                                    <small>Test Password Reset Flow</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Testing Instructions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-book"></i> Testing Instructions & Expected Results
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📋 Testing Process</h6>
                            <ol class="small">
                                <li><strong>Email Verification:</strong> Tests the custom verification email template</li>
                                <li><strong>Password Reset:</strong> Tests Laravel's built-in password reset functionality</li>
                                <li><strong>Check Mailtrap:</strong> All emails should appear in your Mailtrap inbox</li>
                                <li><strong>Verify Links:</strong> Click email links to ensure they work correctly</li>
                                <li><strong>Template Rendering:</strong> Verify emails display properly with correct branding</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6>✅ Expected Results</h6>
                            <div class="alert alert-info">
                                <ul class="mb-0 small">
                                    <li>✅ All emails delivered to Mailtrap</li>
                                    <li>✅ Email templates render with proper styling</li>
                                    <li>✅ All links and buttons functional</li>
                                    <li>✅ No errors during email sending process</li>
                                    <li>✅ Professional branding and layout</li>
                                    <li>✅ Secure tokens and time-limited links</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
