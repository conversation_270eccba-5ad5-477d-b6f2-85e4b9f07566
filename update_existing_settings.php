<?php
require_once 'vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;

try {
    // Get the first organization
    $organization = DB::table('organizations')->first();

    if (!$organization) {
        echo "No organizations found. Please create an organization first.\n";
        exit;
    }

    // Update the existing settings to belong to the first organization
    $updated = DB::table('settings')
        ->where('id', 1)
        ->update(['organization_id' => $organization->id]);

    if ($updated) {
        echo "Existing settings updated to be associated with organization ID: " . $organization->id . "\n";
    } else {
        echo "No settings record found or no update was necessary.\n";
    }

    // Copy default settings for any organizations that don't have settings
    $organizationsWithoutSettings = DB::table('organizations')
        ->leftJoin('settings', 'organizations.id', '=', 'settings.organization_id')
        ->whereNull('settings.id')
        ->select('organizations.id', 'organizations.name')
        ->get();

    $defaultSettings = DB::table('settings')->where('id', 1)->first();

    if ($defaultSettings && count($organizationsWithoutSettings) > 0) {
        foreach ($organizationsWithoutSettings as $org) {
            // Skip the first organization as it already has settings
            if ($org->id == $organization->id) continue;

            // Create settings for this organization based on the default settings
            $newSettings = (array) $defaultSettings;
            unset($newSettings['id']); // Remove the ID so a new record is created
            $newSettings['organization_id'] = $org->id;
            $newSettings['organization_name'] = $org->name;

            DB::table('settings')->insert($newSettings);
            echo "Created settings for organization: " . $org->name . " (ID: " . $org->id . ")\n";
        }
    }

    // Ensure Setting model is updated to use organization_id
    $settingModelPath = __DIR__ . '/app/Models/Setting.php';
    if (file_exists($settingModelPath)) {
        $content = file_get_contents($settingModelPath);

        if (strpos($content, 'protected $fillable = [') !== false && strpos($content, 'organization_id') === false) {
            $oldCode = 'protected $fillable = [';
            $newCode = 'protected $fillable = [
        // Organization relationship
        \'organization_id\',
        ';

            $content = str_replace($oldCode, $newCode, $content);

            // Add relationship method if it doesn't exist
            if (strpos($content, 'public function organization()') === false) {
                $position = strrpos($content, '}');
                $relationMethod = '
    /**
     * Get the organization that owns the settings.
     */
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }
';
                $content = substr_replace($content, $relationMethod . "\n}", $position, 1);
            }

            file_put_contents($settingModelPath, $content);
            echo "Updated Setting model to support organization relationships.\n";
        } else {
            echo "Setting model already supports organization relationships.\n";
        }
    }

    echo "All settings have been updated successfully.\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
