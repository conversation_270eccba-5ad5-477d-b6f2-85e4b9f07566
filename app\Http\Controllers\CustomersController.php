<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;
use Codedge\Fpdf\Fpdf\Fpdf;
use Illuminate\Support\Facades\Auth;

class CustomersController extends Controller
{
    protected $fpdf;

    public function __construct()
    {
        $this->fpdf = new Fpdf;
    }

    public function index()
    {
        // Get unique customers from orders filtered by the user's organization
        $query = Order::select('customer_name', 'phone_number')
            ->where('organization_id', Auth::user()->organization_id)
            ->distinct()
            ->orderBy('customer_name');

        // Filter by branch if user is assigned to a branch
        if (Auth::user()->branch_id) {
            $query->where('branch_id', Auth::user()->branch_id);
        }

        $customers = $query->get();

        return view('customers.index', compact('customers'));
    }

    public function generatePDF()
    {
        try {
            // Get organization settings for the current user's organization
            $settings = \App\Models\Setting::where('organization_id', Auth::user()->organization_id)->first();
            $organizationName = $settings && $settings->organization_name
                ? $settings->organization_name
                : 'Order Flow Pro';
            $address = $settings && $settings->company_address
                ? $settings->company_address
                : '';

            $this->fpdf->AddPage();
            $this->fpdf->SetAutoPageBreak(true, 10);

            // Add company header
            $this->fpdf->SetFont('Arial', 'B', 16);
            $this->fpdf->Cell(0, 10, $organizationName, 0, 1, 'C');
            $this->fpdf->SetFont('Arial', '', 10);

            // Split address into lines
            if (!empty($address)) {
                $addressLines = explode("\n", $address);
                foreach ($addressLines as $line) {
                    $this->fpdf->Cell(0, 5, trim($line), 0, 1, 'C');
                }
            }

            $this->fpdf->Ln(10);

            // Add report title
            $this->fpdf->SetFont('Arial', 'B', 14);
            $this->fpdf->Cell(0, 10, 'Customer Directory', 0, 1, 'C');
            $this->fpdf->Cell(0, 5, 'Generated: ' . now()->format('d/m/Y H:i'), 0, 1, 'C');
            $this->fpdf->Ln(5);

            // Add table headers
            $this->fpdf->SetFont('Arial', 'B', 11);
            $this->fpdf->SetFillColor(200, 220, 255);
            $this->fpdf->Cell(10, 8, '#', 1, 0, 'C', true);
            $this->fpdf->Cell(90, 8, 'Customer Name', 1, 0, 'L', true);
            $this->fpdf->Cell(90, 8, 'Phone Number', 1, 1, 'L', true);

            // Add customer data
            $this->fpdf->SetFont('Arial', '', 10);
            $counter = 1;

            // Get unique customers with order count, filtered by organization
            $customers = Order::select('customer_name', 'phone_number')
                ->where('organization_id', Auth::user()->organization_id)
                ->selectRaw('COUNT(DISTINCT id) as order_count')
                ->groupBy('customer_name', 'phone_number')
                ->orderBy('customer_name');

            // Filter by branch if user is assigned to a branch
            if (Auth::user()->branch_id) {
                $customers->where('branch_id', Auth::user()->branch_id);
            }

            $customers = $customers->get();

            foreach ($customers as $customer) {
                // Alternate row colors
                $fill = $counter % 2 == 0;
                if ($fill) {
                    $this->fpdf->SetFillColor(245, 245, 245);
                }

                $this->fpdf->Cell(10, 8, $counter, 1, 0, 'C', $fill);
                $this->fpdf->Cell(90, 8, $customer->customer_name, 1, 0, 'L', $fill);
                $this->fpdf->Cell(90, 8, $customer->phone_number, 1, 1, 'L', $fill);
                $counter++;
            }

            // Add footer
            $this->fpdf->Ln(10);
            $this->fpdf->SetFont('Arial', 'I', 8);
            $this->fpdf->Cell(0, 5, 'Total Customers: ' . $customers->count(), 0, 1, 'L');
            $this->fpdf->Cell(0, 5, 'Report Generated by OrderFlow Pro v4.0.1', 0, 1, 'L');
            $this->fpdf->Cell(0, 5, 'Page ' . $this->fpdf->PageNo(), 0, 1, 'L');

            return $this->fpdf->Output('D', 'customer-directory-' . now()->format('Y-m-d-His') . '.pdf');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate PDF: ' . $e->getMessage());
        }
    }
}
