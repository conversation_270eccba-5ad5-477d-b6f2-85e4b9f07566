<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffiliateEarning;
use App\Models\Affiliate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AffiliateEarningController extends Controller
{
    public function __construct()
    {
        $this->middleware('super_admin');
    }

    /**
     * Display affiliate earnings
     */
    public function index(Request $request)
    {
        $query = AffiliateEarning::with(['affiliate.user', 'organization', 'approvedBy']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Search by affiliate name
        if ($request->filled('search')) {
            $query->whereHas('affiliate.user', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('earned_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('earned_at', '<=', $request->date_to);
        }

        $earnings = $query->latest('earned_at')->paginate(20);

        // Get summary statistics
        $stats = [
            'total_earnings' => AffiliateEarning::sum('amount'),
            'pending_earnings' => AffiliateEarning::where('status', AffiliateEarning::STATUS_PENDING)->sum('amount'),
            'approved_earnings' => AffiliateEarning::where('status', AffiliateEarning::STATUS_APPROVED)->sum('amount'),
            'pending_count' => AffiliateEarning::where('status', AffiliateEarning::STATUS_PENDING)->count(),
        ];

        return view('super_admin.affiliate_earnings.index', compact('earnings', 'stats'));
    }

    /**
     * Show earning details
     */
    public function show(AffiliateEarning $affiliateEarning)
    {
        $affiliateEarning->load(['affiliate.user', 'organization', 'referral', 'approvedBy']);
        return view('super_admin.affiliate_earnings.show', ['earning' => $affiliateEarning]);
    }

    /**
     * Approve earning
     */
    public function approve(Request $request, AffiliateEarning $affiliateEarning)
    {
        if ($affiliateEarning->status !== AffiliateEarning::STATUS_PENDING) {
            return back()->withErrors(['error' => 'Only pending earnings can be approved.']);
        }

        $request->validate([
            'notes' => ['nullable', 'string', 'max:500'],
        ]);

        try {
            $affiliateEarning->approve(
                Auth::guard('super_admin')->id(),
                $request->notes
            );

            return back()->with('success', 'Earning approved successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to approve earning: ' . $e->getMessage()]);
        }
    }

    /**
     * Reject earning
     */
    public function reject(Request $request, AffiliateEarning $affiliateEarning)
    {
        if ($affiliateEarning->status !== AffiliateEarning::STATUS_PENDING) {
            return back()->withErrors(['error' => 'Only pending earnings can be rejected.']);
        }

        $request->validate([
            'notes' => ['required', 'string', 'max:500'],
        ]);

        try {
            $affiliateEarning->reject(
                Auth::guard('super_admin')->id(),
                $request->notes
            );

            return back()->with('success', 'Earning rejected successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to reject earning: ' . $e->getMessage()]);
        }
    }

    /**
     * Bulk approve earnings
     */
    public function bulkApprove(Request $request)
    {
        $request->validate([
            'earning_ids' => ['required', 'array'],
            'earning_ids.*' => ['exists:affiliate_earnings,id'],
            'notes' => ['nullable', 'string', 'max:500'],
        ]);

        $earningIds = $request->earning_ids;
        $results = ['success' => [], 'failed' => []];

        foreach ($earningIds as $earningId) {
            try {
                $earning = AffiliateEarning::find($earningId);

                if ($earning->status === AffiliateEarning::STATUS_PENDING) {
                    $earning->approve(
                        Auth::guard('super_admin')->id(),
                        $request->notes
                    );
                    $results['success'][] = "#{$earning->id}";
                } else {
                    $results['failed'][] = "#{$earning->id} (not pending)";
                }
            } catch (\Exception $e) {
                $results['failed'][] = "#{$earning->id} ({$e->getMessage()})";
            }
        }

        $message = '';
        if (!empty($results['success'])) {
            $message .= 'Successfully approved: ' . implode(', ', $results['success']) . '. ';
        }
        if (!empty($results['failed'])) {
            $message .= 'Failed: ' . implode(', ', $results['failed']) . '. ';
        }

        return back()->with('success', trim($message));
    }

    /**
     * Bulk reject earnings
     */
    public function bulkReject(Request $request)
    {
        $request->validate([
            'earning_ids' => ['required', 'array'],
            'earning_ids.*' => ['exists:affiliate_earnings,id'],
            'notes' => ['required', 'string', 'max:500'],
        ]);

        $earningIds = $request->earning_ids;
        $results = ['success' => [], 'failed' => []];

        foreach ($earningIds as $earningId) {
            try {
                $earning = AffiliateEarning::find($earningId);

                if ($earning->status === AffiliateEarning::STATUS_PENDING) {
                    $earning->reject(
                        Auth::guard('super_admin')->id(),
                        $request->notes
                    );
                    $results['success'][] = "#{$earning->id}";
                } else {
                    $results['failed'][] = "#{$earning->id} (not pending)";
                }
            } catch (\Exception $e) {
                $results['failed'][] = "#{$earning->id} ({$e->getMessage()})";
            }
        }

        $message = '';
        if (!empty($results['success'])) {
            $message .= 'Successfully rejected: ' . implode(', ', $results['success']) . '. ';
        }
        if (!empty($results['failed'])) {
            $message .= 'Failed: ' . implode(', ', $results['failed']) . '. ';
        }

        return back()->with('success', trim($message));
    }
}
