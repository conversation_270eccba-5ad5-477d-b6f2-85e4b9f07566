@extends('layouts.app')

@section('title', 'Support Center')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Support Center</h1>
                <div>
                    <a href="{{ route('user.support.knowledge-base') }}" class="btn btn-outline-info me-2">
                        <i class="fas fa-book"></i> Knowledge Base
                    </a>
                    <a href="{{ route('user.support.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Ticket
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h5 class="card-title text-primary">{{ $stats['total'] }}</h5>
                            <p class="card-text">Total Tickets</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h5 class="card-title text-success">{{ $stats['open'] }}</h5>
                            <p class="card-text">Open Tickets</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <h5 class="card-title text-info">{{ $stats['resolved'] }}</h5>
                            <p class="card-text">Resolved</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <h5 class="card-title text-warning">{{ $stats['avg_response_time'] }}h</h5>
                            <p class="card-text">Avg Response</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- My Tickets -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-ticket-alt me-2"></i>
                                My Support Tickets
                            </h5>
                            <a href="{{ route('user.support.create') }}" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> New Ticket
                            </a>
                        </div>
                        <div class="card-body p-0">
                            @if($tickets->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Ticket #</th>
                                                <th>Subject</th>
                                                <th>Status</th>
                                                <th>Priority</th>
                                                <th>Last Update</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($tickets as $ticket)
                                            <tr>
                                                <td>
                                                    <strong>{{ $ticket->ticket_number }}</strong>
                                                </td>
                                                <td>
                                                    <div class="text-truncate" style="max-width: 250px;">
                                                        {{ $ticket->title }}
                                                    </div>
                                                    <small class="text-muted">
                                                        {{ ucfirst(str_replace('_', ' ', $ticket->category)) }}
                                                    </small>
                                                </td>
                                                <td>{!! $ticket->status_badge !!}</td>
                                                <td>{!! $ticket->priority_badge !!}</td>
                                                <td>
                                                    <small>
                                                        {{ $ticket->updated_at->format('M d, Y') }}<br>
                                                        <span class="text-muted">{{ $ticket->updated_at->diffForHumans() }}</span>
                                                    </small>
                                                </td>
                                                <td>
                                                    <a href="{{ route('user.support.show', $ticket) }}"
                                                       class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                @if($tickets->hasPages())
                                <div class="card-footer">
                                    {{ $tickets->links() }}
                                </div>
                                @endif
                            @else
                                <div class="text-center py-5">
                                    <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No support tickets yet</h5>
                                    <p class="text-muted">Create your first support ticket to get help from our team.</p>
                                    <a href="{{ route('user.support.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Create First Ticket
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Quick Help -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-question-circle me-2"></i>
                                Quick Help
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('user.support.knowledge-base') }}" class="btn btn-outline-info">
                                    <i class="fas fa-book me-2"></i>Browse Knowledge Base
                                </a>
                                <a href="{{ route('user.support.search') }}?q=getting+started" class="btn btn-outline-success">
                                    <i class="fas fa-play me-2"></i>Getting Started Guide
                                </a>
                                <a href="{{ route('user.support.create') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-envelope me-2"></i>Contact Support
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Articles -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-newspaper me-2"></i>
                                Recent Articles
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            @if($recentArticles->count() > 0)
                                <div class="list-group list-group-flush">
                                    @foreach($recentArticles as $article)
                                    <a href="{{ route('user.support.article', $article->id) }}"
                                       class="list-group-item list-group-item-action">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">{{ $article->title }}</h6>
                                                <p class="mb-1 text-muted small">{{ $article->category->name ?? 'General' }}</p>
                                            </div>
                                            <small class="text-muted">{{ $article->reading_time }}m read</small>
                                        </div>
                                    </a>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-3">
                                    <i class="fas fa-book fa-2x text-muted mb-2"></i>
                                    <p class="text-muted small">No articles available</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Popular Articles -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-star me-2"></i>
                                Popular Articles
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            @if($popularArticles->count() > 0)
                                <div class="list-group list-group-flush">
                                    @foreach($popularArticles as $article)
                                    <a href="{{ route('user.support.article', $article->id) }}"
                                       class="list-group-item list-group-item-action">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">{{ $article->title }}</h6>
                                                <p class="mb-1 text-muted small">{{ $article->category->name ?? 'General' }}</p>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-primary">{{ $article->view_count }} views</span>
                                            </div>
                                        </div>
                                    </a>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-3">
                                    <i class="fas fa-star fa-2x text-muted mb-2"></i>
                                    <p class="text-muted small">No popular articles yet</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.list-group-item-action:hover {
    background-color: #f8f9fa;
}
</style>
@endsection
