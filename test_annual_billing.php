<?php

require_once 'vendor/autoload.php';

use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Organization;

echo "=== Testing Annual Billing Implementation ===\n\n";

try {
    // 1. Test Plan model annual billing methods
    echo "1. Testing Plan model annual billing methods...\n";
    
    // Create a test plan
    $testPlan = new Plan([
        'name' => 'Test Plan',
        'price' => 50.00,
        'annual_price' => 510.00, // 15% discount
        'billing_period' => 'both',
        'annual_discount_percentage' => 15
    ]);
    
    echo "   Monthly price: \${$testPlan->price}\n";
    echo "   Annual price: \${$testPlan->annual_price}\n";
    echo "   Effective annual price: \${$testPlan->getEffectiveAnnualPrice()}\n";
    echo "   Monthly equivalent of annual: \${$testPlan->getMonthlyEquivalentOfAnnual()}\n";
    echo "   Annual savings: \${$testPlan->getAnnualSavings()}\n";
    echo "   Annual savings percentage: {$testPlan->getAnnualSavingsPercentage()}%\n";
    echo "   Supports annual billing: " . ($testPlan->supportsAnnualBilling() ? 'Yes' : 'No') . "\n";
    echo "   Supports monthly billing: " . ($testPlan->supportsMonthlyBilling() ? 'Yes' : 'No') . "\n\n";
    
    // 2. Test available billing periods
    echo "2. Testing available billing periods...\n";
    $periods = $testPlan->getAvailableBillingPeriods();
    foreach ($periods as $period => $details) {
        echo "   {$details['label']}: \${$details['price']} {$details['period_label']}\n";
        if (isset($details['savings'])) {
            echo "      Savings: \${$details['savings']} ({$details['savings_percentage']}%)\n";
        }
    }
    echo "\n";
    
    // 3. Test price calculation for different periods
    echo "3. Testing price calculation for different periods...\n";
    echo "   Monthly price: \${$testPlan->getPriceForPeriod('monthly')}\n";
    echo "   Annual price: \${$testPlan->getPriceForPeriod('annual')}\n\n";
    
    // 4. Test Subscription model billing period methods
    echo "4. Testing Subscription model billing period methods...\n";
    
    $monthlySubscription = new Subscription([
        'billing_period' => 'monthly',
        'start_date' => now(),
        'end_date' => now()->addMonth()
    ]);
    
    $annualSubscription = new Subscription([
        'billing_period' => 'annual',
        'start_date' => now(),
        'end_date' => now()->addYear()
    ]);
    
    echo "   Monthly subscription:\n";
    echo "      Is annual: " . ($monthlySubscription->isAnnual() ? 'Yes' : 'No') . "\n";
    echo "      Is monthly: " . ($monthlySubscription->isMonthly() ? 'Yes' : 'No') . "\n";
    echo "      Period in months: {$monthlySubscription->getPeriodInMonths()}\n";
    
    echo "   Annual subscription:\n";
    echo "      Is annual: " . ($annualSubscription->isAnnual() ? 'Yes' : 'No') . "\n";
    echo "      Is monthly: " . ($annualSubscription->isMonthly() ? 'Yes' : 'No') . "\n";
    echo "      Period in months: {$annualSubscription->getPeriodInMonths()}\n\n";
    
    // 5. Test billing date calculations
    echo "5. Testing billing date calculations...\n";
    $nextMonthlyBilling = $monthlySubscription->calculateNextBillingDate();
    $nextAnnualBilling = $annualSubscription->calculateNextBillingDate();
    
    echo "   Next monthly billing: {$nextMonthlyBilling->format('Y-m-d')}\n";
    echo "   Next annual billing: {$nextAnnualBilling->format('Y-m-d')}\n\n";
    
    // 6. Test plan with percentage discount (no explicit annual price)
    echo "6. Testing plan with percentage discount only...\n";
    $discountPlan = new Plan([
        'name' => 'Discount Plan',
        'price' => 100.00,
        'annual_price' => null,
        'billing_period' => 'both',
        'annual_discount_percentage' => 20
    ]);
    
    echo "   Monthly price: \${$discountPlan->price}\n";
    echo "   Calculated annual price: \${$discountPlan->getEffectiveAnnualPrice()}\n";
    echo "   Annual savings: \${$discountPlan->getAnnualSavings()}\n";
    echo "   Annual savings percentage: {$discountPlan->getAnnualSavingsPercentage()}%\n\n";
    
    // 7. Test monthly-only plan
    echo "7. Testing monthly-only plan...\n";
    $monthlyOnlyPlan = new Plan([
        'name' => 'Monthly Only Plan',
        'price' => 25.00,
        'annual_price' => null,
        'billing_period' => 'monthly',
        'annual_discount_percentage' => 0
    ]);
    
    echo "   Supports annual billing: " . ($monthlyOnlyPlan->supportsAnnualBilling() ? 'Yes' : 'No') . "\n";
    echo "   Supports monthly billing: " . ($monthlyOnlyPlan->supportsMonthlyBilling() ? 'Yes' : 'No') . "\n";
    echo "   Available periods: " . implode(', ', array_keys($monthlyOnlyPlan->getAvailableBillingPeriods())) . "\n\n";
    
    echo "=== All Tests Completed Successfully! ===\n";
    echo "\nAnnual billing features implemented:\n";
    echo "✓ Plan model supports annual pricing\n";
    echo "✓ Automatic discount calculations\n";
    echo "✓ Flexible billing period support\n";
    echo "✓ Subscription model handles billing periods\n";
    echo "✓ Billing date calculations for annual/monthly\n";
    echo "✓ Price calculations for different periods\n";
    echo "✓ Savings calculations and display\n";
    echo "\nTo use annual billing:\n";
    echo "1. Plans can have explicit annual_price or use annual_discount_percentage\n";
    echo "2. Set billing_period to 'both', 'monthly', or 'annual'\n";
    echo "3. Subscriptions track their billing_period\n";
    echo "4. UI components show both monthly and annual options\n";
    echo "5. Proration service handles billing period changes\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
