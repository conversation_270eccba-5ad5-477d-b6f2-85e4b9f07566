<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Plan;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default plans
        $plans = [
            [
                'name' => 'Starter',
                'slug' => 'starter',
                'description' => 'Perfect for small businesses just getting started. Basic features for a single branch.',
                'price' => 19.00,
                'branch_limit' => 1,
                'user_limit' => 5,
                'data_retention_days' => 30,
                'thermal_printing' => false,
                'advanced_reporting' => false,
                'api_access' => false,
                'white_label' => false,
                'custom_branding' => false,
                'is_active' => true,
                'is_featured' => false,
            ],
            [
                'name' => 'Business',
                'slug' => 'business',
                'description' => 'For growing businesses with multiple branches. Includes advanced features and better customer support.',
                'price' => 49.00,
                'branch_limit' => 3,
                'user_limit' => 15,
                'data_retention_days' => 90,
                'thermal_printing' => true,
                'advanced_reporting' => true,
                'api_access' => false,
                'white_label' => false,
                'custom_branding' => false,
                'is_active' => true,
                'is_featured' => true,
            ],
            [
                'name' => 'Professional',
                'slug' => 'professional',
                'description' => 'For established businesses with multiple branches. Includes all features and premium support.',
                'price' => 99.00,
                'branch_limit' => 10,
                'user_limit' => 999, // Unlimited
                'data_retention_days' => 365,
                'thermal_printing' => true,
                'advanced_reporting' => true,
                'api_access' => true,
                'white_label' => true,
                'custom_branding' => true,
                'is_active' => true,
                'is_featured' => false,
            ],
            [
                'name' => 'Enterprise',
                'slug' => 'enterprise',
                'description' => 'Custom solution for large organizations. Includes unlimited branches, users, and premium support.',
                'price' => 299.00,
                'branch_limit' => 999, // Unlimited
                'user_limit' => 999, // Unlimited
                'data_retention_days' => 999, // Unlimited
                'thermal_printing' => true,
                'advanced_reporting' => true,
                'api_access' => true,
                'white_label' => true,
                'custom_branding' => true,
                'additional_features' => ['custom_integrations', 'priority_support', 'dedicated_account_manager'],
                'is_active' => true,
                'is_featured' => false,
            ],
        ];

        foreach ($plans as $planData) {
            Plan::updateOrCreate(
                ['slug' => $planData['slug']],
                $planData
            );
        }
    }
}
