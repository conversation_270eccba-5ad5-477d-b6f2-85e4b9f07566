@extends('layouts.settings')

@section('title', 'Settings')

@section('app_settings')
<h2 class="text-2xl font-bold mb-6">Application Settings</h2>

<form action="{{ route('settings.update') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
    @csrf
    @method('PATCH')

    <!-- Theme Mode -->
    <div>
        <label for="theme_mode" class="block text-sm font-medium text-gray-700">Theme Mode</label>
        <select name="theme_mode" id="theme_mode"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="light" {{ old('theme_mode', $setting->theme_mode) === 'light' ? 'selected' : '' }}>Light</option>
            <option value="dark" {{ old('theme_mode', $setting->theme_mode) === 'dark' ? 'selected' : '' }}>Dark</option>
        </select>
        @error('theme_mode')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>

    <!-- Primary Color -->
    <div>
        <label for="primary_color" class="block text-sm font-medium text-gray-700">Primary Color</label>
        <div class="mt-1 flex items-center space-x-2">
            <input type="color" name="primary_color" id="primary_color"
                class="h-10 w-20 p-1 rounded border border-gray-300"
                value="{{ old('primary_color', $setting->primary_color) }}">
            <input type="text" id="primary_color_hex"
                class="block w-32 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                value="{{ old('primary_color', $setting->primary_color) }}"
                readonly>
        </div>
        @error('primary_color')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>

    <!-- Sidebar Color -->
    <div>
        <label for="sidebar_color" class="block text-sm font-medium text-gray-700">Sidebar Color</label>
        <div class="mt-1 flex items-center space-x-2">
            <input type="color" name="sidebar_color" id="sidebar_color"
                class="h-10 w-20 p-1 rounded border border-gray-300"
                value="{{ old('sidebar_color', $setting->sidebar_color) }}">
            <input type="text" id="sidebar_color_hex"
                class="block w-32 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                value="{{ old('sidebar_color', $setting->sidebar_color) }}"
                readonly>
        </div>
        @error('sidebar_color')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>

    <!-- Preview Section -->
    <div class="mt-8 border rounded-lg bg-gray-50">
        <h3 class="text-lg font-medium text-gray-900 p-4 border-b">Preview</h3>

        <!-- Sidebar Preview -->
        <div class="p-4 space-y-6">
            <div class="w-64 border rounded-lg overflow-hidden">
                <!-- Header Preview -->
                <div class="p-4" style="background-color: {{ $setting->sidebar_color }}">
                    <!-- Color Preview -->
                    <div class="flex justify-center">
                        <div class="w-16 h-16 rounded-full" style="background-color: {{ $setting->primary_color }}"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="flex justify-end pt-6">
        <button type="submit"
            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            Save Settings
        </button>
    </div>
</form>
@endsection

@section('business_settings')
<h2 class="text-2xl font-bold mb-6">Business Settings</h2>

<form action="{{ route('settings.business.update') }}" method="POST">
    @csrf
    @method('PUT')

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Company Information</h3>

        <div class="mb-4">
            <label for="organization_name" class="block text-gray-700 text-sm font-bold mb-2">
                Organization Name
            </label>
            <input
                type="text"
                name="organization_name"
                id="organization_name"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-100"
                value="{{ old('organization_name', $setting->organization_name) }}"
                placeholder="Your Company Name"
                readonly
            >
            <p class="text-gray-500 text-xs mt-1">This name was set during registration and cannot be changed.</p>
            @error('organization_name')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="mb-4">
                <label for="business_registration_number" class="block text-gray-700 text-sm font-bold mb-2">
                    Business Registration Number
                </label>
                <input
                    type="text"
                    name="business_registration_number"
                    id="business_registration_number"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    value="{{ old('business_registration_number', $setting->business_registration_number) }}"
                    placeholder="RC12345678"
                >
                @error('business_registration_number')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-4">
                <label for="tax_identification_number" class="block text-gray-700 text-sm font-bold mb-2">
                    Tax Identification Number (TIN)
                </label>
                <input
                    type="text"
                    name="tax_identification_number"
                    id="tax_identification_number"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    value="{{ old('tax_identification_number', $setting->tax_identification_number) }}"
                    placeholder="1234567890"
                >
                @error('tax_identification_number')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Order Settings</h3>

        <div class="mb-4">
            <label for="default_departments" class="block text-gray-700 text-sm font-bold mb-2">
                Default Departments
            </label>
            <input
                type="text"
                name="default_departments"
                id="default_departments"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value="{{ old('default_departments', $setting->default_departments) }}"
                placeholder="Design, Production, Digital, Large Format"
            >
            <p class="text-gray-500 text-xs mt-1">Separate departments with commas. These will be available as options when creating orders.</p>
            @error('default_departments')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <div class="flex items-center">
                <input
                    type="checkbox"
                    name="require_payment_upfront"
                    id="require_payment_upfront"
                    class="mr-2"
                    value="1"
                    {{ old('require_payment_upfront', $setting->require_payment_upfront) ? 'checked' : '' }}
                >
                <label for="require_payment_upfront" class="text-gray-700 text-sm font-bold">
                    Require Payment Upfront
                </label>
            </div>
            <p class="text-gray-500 text-xs mt-1">If checked, new orders will require payment before processing.</p>
            @error('require_payment_upfront')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="default_payment_due_days" class="block text-gray-700 text-sm font-bold mb-2">
                Default Payment Due Days
            </label>
            <input
                type="number"
                name="default_payment_due_days"
                id="default_payment_due_days"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value="{{ old('default_payment_due_days', $setting->default_payment_due_days) }}"
                min="0"
                max="90"
            >
            <p class="text-gray-500 text-xs mt-1">Number of days before payment is due (0 means payment is due immediately).</p>
            @error('default_payment_due_days')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
    </div>

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Order Numbering</h3>

        <div class="mb-4">
            <label for="order_number_prefix" class="block text-gray-700 text-sm font-bold mb-2">
                Order Number Prefix
            </label>
            <input
                type="text"
                name="order_number_prefix"
                id="order_number_prefix"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value="{{ old('order_number_prefix', $setting->order_number_prefix) }}"
                placeholder="ORD-"
                maxlength="8"
            >
            <p class="text-gray-500 text-xs mt-1">Prefix for order numbers, e.g., "ORD-" will generate numbers like ORD-ABCDEF.</p>
            @error('order_number_prefix')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="order_number_format" class="block text-gray-700 text-sm font-bold mb-2">
                Order Number Format
            </label>
            <select
                name="order_number_format"
                id="order_number_format"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            >
                <option value="alphanumeric" {{ old('order_number_format', $setting->order_number_format) === 'alphanumeric' ? 'selected' : '' }}>Alphanumeric (e.g., ORD-ABC123)</option>
                <option value="numeric" {{ old('order_number_format', $setting->order_number_format) === 'numeric' ? 'selected' : '' }}>Numeric Only (e.g., ORD-123456)</option>
                <option value="sequential" {{ old('order_number_format', $setting->order_number_format) === 'sequential' ? 'selected' : '' }}>Sequential (e.g., ORD-000001)</option>
            </select>
            <p class="text-gray-500 text-xs mt-1">Choose the format for your order numbers.</p>
            @error('order_number_format')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="order_number_digits" class="block text-gray-700 text-sm font-bold mb-2">
                Order Number Digits
            </label>
            <input
                type="number"
                name="order_number_digits"
                id="order_number_digits"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value="{{ old('order_number_digits', $setting->order_number_digits ?? 6) }}"
                min="4"
                max="10"
                step="1"
            >
            <p class="text-gray-500 text-xs mt-1">Number of digits to use in the numeric part of sequential order numbers (e.g., 6 digits for ORD-000001).</p>
            @error('order_number_digits')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <div class="flex items-center">
                <input
                    type="checkbox"
                    name="include_year_in_order_number"
                    id="include_year_in_order_number"
                    class="mr-2"
                    value="1"
                    {{ old('include_year_in_order_number', $setting->include_year_in_order_number) ? 'checked' : '' }}
                >
                <label for="include_year_in_order_number" class="text-gray-700 text-sm font-bold">
                    Include Year in Order Number
                </label>
            </div>
            <p class="text-gray-500 text-xs mt-1">If checked, order numbers will include the current year (e.g., ORD-2024-ABCDEF).</p>
            @error('include_year_in_order_number')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
    </div>

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Currency Settings</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="mb-4">
                <label for="currency_symbol" class="block text-gray-700 text-sm font-bold mb-2">
                    Currency Symbol
                </label>
                <input
                    type="text"
                    name="currency_symbol"
                    id="currency_symbol"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    value="{{ old('currency_symbol', $setting->currency_symbol) }}"
                    placeholder="₦"
                    maxlength="3"
                >
                @error('currency_symbol')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-4">
                <label for="currency_code" class="block text-gray-700 text-sm font-bold mb-2">
                    Currency Code
                </label>
                <input
                    type="text"
                    name="currency_code"
                    id="currency_code"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    value="{{ old('currency_code', $setting->currency_code) }}"
                    placeholder="NGN"
                    maxlength="3"
                >
                @error('currency_code')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Time Settings</h3>

        <div class="mb-4">
            <label for="timezone" class="block text-gray-700 text-sm font-bold mb-2">
                Timezone
            </label>
            <select
                name="timezone"
                id="timezone"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            >
                @php
                    $timezones = [
                        'UTC' => 'UTC',
                        'Africa/Lagos' => 'West Africa Time (WAT)',
                        'Africa/Cairo' => 'Eastern European Time (EET)',
                        'Africa/Johannesburg' => 'South Africa Standard Time (SAST)',
                        'America/New_York' => 'Eastern Time (ET)',
                        'America/Chicago' => 'Central Time (CT)',
                        'America/Denver' => 'Mountain Time (MT)',
                        'America/Los_Angeles' => 'Pacific Time (PT)',
                        'Asia/Dubai' => 'Gulf Standard Time (GST)',
                        'Asia/Kolkata' => 'India Standard Time (IST)',
                        'Asia/Singapore' => 'Singapore Time (SGT)',
                        'Asia/Tokyo' => 'Japan Standard Time (JST)',
                        'Europe/London' => 'Greenwich Mean Time (GMT)',
                        'Europe/Paris' => 'Central European Time (CET)',
                        'Australia/Sydney' => 'Australian Eastern Time (AET)',
                    ];
                @endphp

                @foreach($timezones as $key => $value)
                    <option value="{{ $key }}" {{ old('timezone', $setting->timezone ?? 'UTC') == $key ? 'selected' : '' }}>
                        {{ $value }} ({{ $key }})
                    </option>
                @endforeach
            </select>
            <p class="text-gray-500 text-xs mt-1">All dates and times will be displayed in this timezone throughout the application.</p>
            @error('timezone')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
    </div>

    <div class="flex items-center justify-end pt-6">
        <button
            type="submit"
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded focus:outline-none focus:shadow-outline"
        >
            Save Business Settings
        </button>
    </div>
</form>
@endsection

@section('receipt_settings')
<h2 class="text-2xl font-bold mb-6">Receipt Settings</h2>
<p class="text-gray-600 mb-4">This information will appear on your receipts.</p>

<form action="{{ route('settings.receipt.update') }}" method="POST">
    @csrf
    @method('PUT')

    <div class="mb-6">
        <div class="mb-4">
            <label for="company_address" class="block text-gray-700 text-sm font-bold mb-2">
                Branch Address
            </label>
            <textarea
                name="company_address"
                id="company_address"
                rows="4"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                placeholder="Enter your branch's address (use new lines for formatting)"
            >{{ old('company_address', $setting->company_address) }}</textarea>
            <p class="text-gray-500 text-xs mt-1">Use line breaks for proper formatting on the receipt.</p>
            @error('company_address')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="company_phone" class="block text-gray-700 text-sm font-bold mb-2">
                Contact Phone
            </label>
            <input
                type="text"
                name="company_phone"
                id="company_phone"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value="{{ old('company_phone', $setting->company_phone) }}"
                placeholder="e.g., 08065521205, 08147271386"
            >
            @error('company_phone')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="company_email" class="block text-gray-700 text-sm font-bold mb-2">
                Company Email
            </label>
            <input
                type="email"
                name="company_email"
                id="company_email"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value="{{ old('company_email', $setting->company_email) }}"
                placeholder="e.g., <EMAIL>"
            >
            @error('company_email')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
    </div>

    <div class="flex items-center justify-end pt-6">
        <button
            type="submit"
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded focus:outline-none focus:shadow-outline"
        >
            Save Receipt Settings
        </button>
    </div>
</form>

<div class="bg-white shadow-md rounded-lg p-6 mt-6">
    <h3 class="text-xl font-semibold mb-4">Receipt Preview</h3>
    <p class="text-gray-600 mb-4">Below is how your company information will appear on receipts:</p>

    <div class="border rounded-lg p-4 font-mono text-sm">
        <div class="text-center border-b pb-2 mb-2">
            <div class="font-bold">{{ $setting->organization_name ?: config('app.name', 'Kadmon Printing Company Ltd.') }}</div>
            <div style="white-space: pre-line">{{ $setting->company_address }}</div>
            <div>Sales Receipt</div>
        </div>
        <div class="mb-2">
            <strong>Receipt #:</strong> RCP-EXAMPLE<br>
            <strong>Date:</strong> {{ now()->format('M d, Y h:i A') }}<br>
            <strong>Customer:</strong> Sample Customer<br>
            <strong>Phone:</strong> 08012345678
        </div>
        <div class="border-t pt-2 text-center">
            <div>Thank you for your patronage!</div>
            <div class="text-xs mt-1">
                For Enquiries: {{ $setting->company_phone ?? '080, 081' }}
            </div>
        </div>
    </div>
</div>
@endsection

@section('notification_settings')
<h2 class="text-2xl font-bold mb-6">Notification Settings</h2>
<p class="text-gray-600 mb-4">Configure which automated notifications are sent to customers.</p>

<form action="{{ route('settings.notifications.update') }}" method="POST">
    @csrf
    @method('PUT')

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Email Notifications</h3>

        <div class="space-y-4">
            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input
                        id="order_confirmation_emails"
                        name="order_confirmation_emails"
                        type="checkbox"
                        value="1"
                        {{ old('order_confirmation_emails', $setting->order_confirmation_emails) ? 'checked' : '' }}
                        class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                    >
                </div>
                <div class="ml-3 text-sm">
                    <label for="order_confirmation_emails" class="font-medium text-gray-700">Order Confirmation Emails</label>
                    <p class="text-gray-500">Send an email to customers when their order is created.</p>
                </div>
            </div>

            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input
                        id="order_status_update_emails"
                        name="order_status_update_emails"
                        type="checkbox"
                        value="1"
                        {{ old('order_status_update_emails', $setting->order_status_update_emails) ? 'checked' : '' }}
                        class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                    >
                </div>
                <div class="ml-3 text-sm">
                    <label for="order_status_update_emails" class="font-medium text-gray-700">Order Status Update Emails</label>
                    <p class="text-gray-500">Send an email to customers when their order status changes.</p>
                </div>
            </div>

            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input
                        id="payment_reminder_emails"
                        name="payment_reminder_emails"
                        type="checkbox"
                        value="1"
                        {{ old('payment_reminder_emails', $setting->payment_reminder_emails) ? 'checked' : '' }}
                        class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                    >
                </div>
                <div class="ml-3 text-sm">
                    <label for="payment_reminder_emails" class="font-medium text-gray-700">Payment Reminder Emails</label>
                    <p class="text-gray-500">Send payment reminder emails for orders with pending payments.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="flex justify-end pt-6">
        <button type="submit"
            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            Save Notification Settings
        </button>
    </div>
</form>
@endsection

@section('printer_settings')
<h2 class="text-2xl font-bold mb-6">Printer Settings</h2>
<p class="text-gray-600 mb-4">Configure your thermal printer for receipt printing.</p>

<form action="{{ route('settings.printer.update') }}" method="POST">
    @csrf
    @method('PUT')

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Thermal Printer Configuration</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="mb-4">
                <label for="thermal_printer_name" class="block text-gray-700 text-sm font-bold mb-2">
                    Printer Name
                </label>
                <input
                    type="text"
                    name="thermal_printer_name"
                    id="thermal_printer_name"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    value="{{ old('thermal_printer_name', $setting->thermal_printer_name) }}"
                    placeholder="XP-58"
                >
                <p class="text-gray-500 text-xs mt-1">Enter the exact name of your thermal printer as it appears in Windows Devices & Printers.</p>
                @error('thermal_printer_name')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-4">
                <label for="thermal_paper_width" class="block text-gray-700 text-sm font-bold mb-2">
                    Paper Width (mm)
                </label>
                <input
                    type="number"
                    name="thermal_paper_width"
                    id="thermal_paper_width"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    value="{{ old('thermal_paper_width', $setting->thermal_paper_width) }}"
                    min="58"
                    max="80"
                    step="1"
                >
                <p class="text-gray-500 text-xs mt-1">Common widths: 58mm, 80mm</p>
                @error('thermal_paper_width')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Test Print</h3>
        <p class="text-gray-600 mb-4">Test your printer configuration with a sample receipt.</p>

        <div class="flex items-center justify-start">
            <button
                type="button"
                id="test-print-button"
                class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mr-4"
                onclick="testPrint()"
            >
                Print Test Receipt
            </button>
            <span id="test-print-result" class="text-sm"></span>
        </div>
    </div>

    <div class="flex items-center justify-end">
        <button
            type="submit"
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded focus:outline-none focus:shadow-outline"
        >
            Save Printer Settings
        </button>
    </div>
</form>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Color picker functionality for app settings
    function setupColorPicker(colorId, hexId) {
        const colorPicker = document.getElementById(colorId);
        const hexInput = document.getElementById(hexId);

        if (colorPicker && hexInput) {
            colorPicker.addEventListener('input', function(e) {
                hexInput.value = e.target.value.toUpperCase();
                updatePreview();
            });
        }
    }

    setupColorPicker('primary_color', 'primary_color_hex');
    setupColorPicker('sidebar_color', 'sidebar_color_hex');

    // Update preview
    function updatePreview() {
        const sidebarPreview = document.querySelector('.rounded-lg .p-4');
        const primaryColorPreview = document.querySelector('.rounded-full');
        const primaryColor = document.getElementById('primary_color').value;
        const sidebarColor = document.getElementById('sidebar_color').value;

        if (sidebarPreview) sidebarPreview.style.backgroundColor = sidebarColor;
        if (primaryColorPreview) primaryColorPreview.style.backgroundColor = primaryColor;
    }
});

// Test print function for printer settings
function testPrint() {
    const button = document.getElementById('test-print-button');
    const result = document.getElementById('test-print-result');

    if (!button || !result) return;

    button.disabled = true;
    button.innerText = 'Printing...';
    result.innerText = '';

    fetch('{{ route("settings.printer.test") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            printer_name: document.getElementById('thermal_printer_name').value,
            paper_width: document.getElementById('thermal_paper_width').value
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            result.innerText = '✓ Test print sent successfully!';
            result.className = 'text-sm text-green-600';
        } else {
            result.innerText = '✗ Error: ' + data.message;
            result.className = 'text-sm text-red-600';
        }
    })
    .catch(error => {
        result.innerText = '✗ Error connecting to server: ' + error;
        result.className = 'text-sm text-red-600';
    })
    .finally(() => {
        button.disabled = false;
        button.innerText = 'Print Test Receipt';
    });
}
</script>
@endpush
