<!DOCTYPE html>
<html>
<head>
    <title>Announcement System Debug</title>
    <meta name="csrf-token" content="test-token">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <h1>🔍 Announcement System Debug Tool</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>📡 API Test</h5>
                    </div>
                    <div class="card-body">
                        <button id="testApi" class="btn btn-primary">Test /api/announcements</button>
                        <div id="apiResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>🎨 Display Test</h5>
                    </div>
                    <div class="card-body">
                        <button id="testDisplay" class="btn btn-success">Test Display Function</button>
                        <div id="displayResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>📢 Announcements Container</h5>
                    </div>
                    <div class="card-body">
                        <div id="announcements-container" style="border: 2px dashed #ccc; min-height: 100px; padding: 20px;">
                            <p class="text-muted">Announcements will appear here...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>📝 Debug Log</h5>
                    </div>
                    <div class="card-body">
                        <div id="debugLog" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Debug logging function
        function debugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debugLog');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        // Test API endpoint
        document.getElementById('testApi').addEventListener('click', function() {
            debugLog('🔄 Testing /api/announcements endpoint...');
            
            fetch('/api/announcements')
                .then(response => {
                    debugLog(`📡 Response status: ${response.status}`);
                    debugLog(`📡 Response headers: ${JSON.stringify([...response.headers])}`);
                    return response.json();
                })
                .then(data => {
                    debugLog(`✅ API Response received`);
                    debugLog(`📊 Data: ${JSON.stringify(data, null, 2)}`);
                    
                    document.getElementById('apiResult').innerHTML = `
                        <div class="alert alert-success">
                            <strong>✅ API Working!</strong><br>
                            Announcements found: ${data.announcements ? data.announcements.length : 0}
                        </div>
                        <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
                    `;
                })
                .catch(error => {
                    debugLog(`❌ API Error: ${error.message}`);
                    document.getElementById('apiResult').innerHTML = `
                        <div class="alert alert-danger">
                            <strong>❌ API Error!</strong><br>
                            ${error.message}
                        </div>
                    `;
                });
        });

        // Test display function
        document.getElementById('testDisplay').addEventListener('click', function() {
            debugLog('🎨 Testing display function with sample data...');
            
            const sampleAnnouncements = [
                {
                    id: 999,
                    title: "Test Announcement",
                    content: "This is a test announcement to verify the display function works.",
                    type: "info",
                    priority: "normal",
                    is_dismissible: true,
                    alert_class: "alert-info",
                    icon: "fas fa-info-circle",
                    affected_features: null,
                    starts_at: null,
                    ends_at: null
                }
            ];
            
            displayAnnouncements(sampleAnnouncements);
            
            document.getElementById('displayResult').innerHTML = `
                <div class="alert alert-success">
                    <strong>✅ Display function called!</strong><br>
                    Check the announcements container below.
                </div>
            `;
        });

        // Copy the exact display functions from the main app
        function displayAnnouncements(announcements) {
            debugLog('🎨 displayAnnouncements called');
            debugLog(`📊 Announcements data: ${JSON.stringify(announcements)}`);
            
            const container = document.getElementById('announcements-container');
            debugLog(`📦 Container found: ${!!container}`);

            if (!container) {
                debugLog('❌ Announcements container not found!');
                return;
            }

            if (!announcements || announcements.length === 0) {
                debugLog('ℹ️ No announcements to display');
                container.innerHTML = '<p class="text-muted">No announcements to display</p>';
                return;
            }

            container.innerHTML = '';
            debugLog(`🔄 Displaying ${announcements.length} announcements`);

            announcements.forEach((announcement, index) => {
                debugLog(`🔄 Processing announcement ${index + 1}: ${announcement.title}`);
                const announcementHtml = createAnnouncementHtml(announcement);
                container.insertAdjacentHTML('beforeend', announcementHtml);
            });

            debugLog('✅ All announcements displayed');
        }

        function createAnnouncementHtml(announcement) {
            const dismissButton = announcement.is_dismissible
                ? `<button type="button" class="btn-close" onclick="dismissAnnouncement(${announcement.id})" aria-label="Close"></button>`
                : '';

            const priorityClass = announcement.priority === 'urgent' ? 'border-danger border-3' : '';

            return `
                <div id="announcement-${announcement.id}" class="alert ${announcement.alert_class} ${priorityClass} d-flex align-items-start mb-3" role="alert">
                    <i class="${announcement.icon} fa-lg me-3 mt-1"></i>
                    <div class="flex-grow-1">
                        <h5 class="alert-heading mb-2">${announcement.title}</h5>
                        <div>${announcement.content}</div>
                        ${announcement.affected_features && announcement.affected_features.length > 0 ?
                            `<div class="mt-2">
                                <strong>Affected Features:</strong>
                                ${announcement.affected_features.map(feature => `<span class="badge bg-dark me-1">${feature}</span>`).join('')}
                            </div>` : ''
                        }
                        ${announcement.starts_at || announcement.ends_at ?
                            `<div class="mt-2 small">
                                ${announcement.starts_at ? `<div><strong>Start:</strong> ${new Date(announcement.starts_at).toLocaleString()}</div>` : ''}
                                ${announcement.ends_at ? `<div><strong>End:</strong> ${new Date(announcement.ends_at).toLocaleString()}</div>` : ''}
                            </div>` : ''
                        }
                    </div>
                    ${dismissButton}
                </div>
            `;
        }

        function dismissAnnouncement(announcementId) {
            debugLog(`🗑️ Dismissing announcement ${announcementId}`);
            // For testing, just remove the element
            const element = document.getElementById(`announcement-${announcementId}`);
            if (element) {
                element.style.transition = 'opacity 0.3s ease';
                element.style.opacity = '0';
                setTimeout(() => element.remove(), 300);
            }
        }

        // Auto-load announcements on page load
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('🚀 Page loaded, testing announcement system...');
            
            // Test API automatically
            setTimeout(() => {
                document.getElementById('testApi').click();
            }, 1000);
        });
    </script>
</body>
</html>
