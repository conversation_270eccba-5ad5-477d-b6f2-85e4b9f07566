<?php

namespace App\Providers;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\ServiceProvider;

class CurrencyServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Clear currency cache when settings are updated
        Setting::updated(function (Setting $setting) {
            if ($setting->isDirty('currency_symbol') || $setting->isDirty('currency_code')) {
                Cache::forget('currency_symbol');
                Cache::forget('currency_code');
            }
        });
    }
}
