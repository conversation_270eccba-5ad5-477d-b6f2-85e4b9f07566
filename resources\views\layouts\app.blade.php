@php
    if (auth()->check()) {
        $settings = \App\Models\Setting::where('organization_id', auth()->user()->organization_id)->first()
                    ?? new \App\Models\Setting(['organization_id' => auth()->user()->organization_id]);
    } else {
        $settings = new \App\Models\Setting;
    }
@endphp
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="{{ $settings->theme_mode }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $settings->site_title }} - {{ $settings->app_name }}</title>

    <!-- Favicon -->
    <link rel="icon" type="https://res.cloudinary.com/dnyl8mkez/image/upload/v1753291187/favicon_smlh7q.ico" href="{{ $settings->favicon_url }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Bootstrap and jQuery -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    @production
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @else
    <script src="https://cdn.tailwindcss.com"></script>
    @endproduction

    <!-- Add this in the head section -->
    <script src="{{ asset('vendor/js/sweetalert2.min.js') }}"></script>

    <!-- Fallback CSS for offline scenarios -->
    <style>
        /* Critical CSS for basic layout */
        .sidebar { width: 16rem; }
        .sidebar-icon { width: 1.25rem; height: 1.25rem; }
        /* Add other critical styles here */
    </style>

    <!-- Offline detection -->
    <script>
        function handleOffline() {
            document.documentElement.classList.add('offline-mode');
        }
        function handleOnline() {
            document.documentElement.classList.remove('offline-mode');
        }
        window.addEventListener('offline', handleOffline);
        window.addEventListener('online', handleOnline);
        if (!navigator.onLine) handleOffline();
    </script>

    <style>
        :root {
            --primary-color: {{ $settings->primary_color }};
            --sidebar-color: {{ $settings->sidebar_color }};
            --primary-color-80: {{ $settings->primary_color }}cc;
            --primary-color-60: {{ $settings->primary_color }}99;
        }

        /* Apply primary color to various elements */
        .bg-primary {
            background-color: var(--primary-color) !important;
        }

        .bg-primary-80 {
            background-color: var(--primary-color-80) !important;
        }

        .bg-primary-60 {
            background-color: var(--primary-color-60) !important;
        }

        .text-primary {
            color: var(--primary-color) !important;
        }

        .border-primary {
            border-color: var(--primary-color) !important;
        }

        /* Apply to focus states */
        .focus\:ring-primary:focus {
            --tw-ring-color: var(--primary-color) !important;
        }

        .hover\:bg-primary:hover {
            background-color: var(--primary-color) !important;
        }
    </style>

    <!-- Add manifest for PWA -->
    <link rel="manifest" href="/manifest.json">

    <!-- Add fallback fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* Fallback styles for offline use */
        .sidebar-icon {
            width: 1.5rem;
            height: 1.5rem;
            min-width: 1.5rem;
        }

        /* Ensure consistent font loading */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
    </style>

    <!-- Critical CSS -->
    <style>
        /* Base styles that prevent FOUC (Flash of Unstyled Content) */
        body {
            display: none;
        }

        .sidebar-icon {
            width: 1.5rem;
            height: 1.5rem;
            min-width: 1.5rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        /* Basic layout structure */
        .min-h-screen { min-height: 100vh; }
        .flex { display: flex; }
        .flex-1 { flex: 1 1 0%; }
        .flex-shrink-0 { flex-shrink: 0; }

        /* Sidebar basic styles */
        .w-64 { width: 16rem; }
        .bg-gray-800 { background-color: #1f2937; }
        .text-white { color: #ffffff; }

        /* Navigation styles */
        .space-y-2 > * + * { margin-top: 0.5rem; }
        .p-4 { padding: 1rem; }
        .rounded { border-radius: 0.25rem; }
    </style>

    <!-- Show body once CSS is loaded -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.display = 'block';
        });
    </script>

    <!-- Download fonts asynchronously -->
    <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
    <style>
        @font-face {
            font-family: 'Inter';
            font-weight: 100 900;
            font-display: swap;
            font-style: normal;
            font-named-instance: 'Regular';
            src: url("/fonts/inter-var.woff2") format("woff2");
        }

        @font-face {
            font-family: 'Font Awesome 5 Free';
            font-style: normal;
            font-weight: 900;
            font-display: swap;
            src: url("/webfonts/fa-solid-900.woff2") format("woff2");
        }
    </style>

    <!-- Bootstrap CSS already loaded above -->

    <!-- Styles -->
    <link href="{{ asset('vendor/css/tailwind.min.css') }}" rel="stylesheet">
    <link href="{{ asset('vendor/css/fontawesome.min.css') }}" rel="stylesheet">
    <link href="{{ asset('vendor/css/sweetalert2.min.css') }}" rel="stylesheet">



    <!-- Font Awesome -->
    <link href="{{ asset('vendor/css/fontawesome.min.css') }}" rel="stylesheet">
    <style>
        /* Fix for Font Awesome icons */
        @font-face {
            font-family: "Font Awesome 5 Free";
            font-style: normal;
            font-weight: 900;
            font-display: block;
            src: url("{{ asset('vendor/webfonts/fa-solid-900.woff2') }}") format("woff2");
        }

        @font-face {
            font-family: "Font Awesome 5 Free";
            font-style: normal;
            font-weight: 400;
            font-display: block;
            src: url("{{ asset('vendor/webfonts/fa-regular-400.woff2') }}") format("woff2");
        }

        @font-face {
            font-family: "Font Awesome 5 Brands";
            font-style: normal;
            font-weight: 400;
            font-display: block;
            src: url("{{ asset('vendor/webfonts/fa-brands-400.woff2') }}") format("woff2");
        }

        /* Make buttons more compact */
        .btn-sm {
            padding: 0.25rem 0.5rem;
        }

        /* Add gap between flex items */
        .gap-1 {
            gap: 0.25rem;
        }

        /* Main layout container */
        .main-layout {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar styling */
        .sidebar {
            background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
            width: 256px;
            flex-shrink: 0;
            overflow-y: auto;
        }

        /* Main content area */
        .main-content {
            flex: 1;
            min-width: 0;
            overflow-x: hidden;
        }

        /* Ensure sidebar is always visible on desktop */
        @media (min-width: 768px) {
            .sidebar {
                display: block !important;
            }
        }

        /* Mobile sidebar behavior */
        @media (max-width: 767.98px) {
            .main-layout {
                flex-direction: column;
            }

            .sidebar {
                display: none !important;
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 1050;
                width: 256px;
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
                overflow-y: auto;
                overflow-x: hidden;
            }

            .sidebar.show {
                display: block !important;
                transform: translateX(0);
            }

            .main-content {
                width: 100%;
            }
        }

        .sidebar .nav-link {
            color: #ecf0f1;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.125rem 0;
            transition: all 0.2s ease-in-out;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .sidebar .nav-link.active {
            background-color: #e74c3c;
            color: #fff;
        }

        .border-left-primary {
            border-left: 0.25rem solid #1f2937 !important;
        }

        .border-left-success {
            border-left: 0.25rem solid #27ae60 !important;
        }

        .border-left-info {
            border-left: 0.25rem solid #3498db !important;
        }

        /* Mobile backdrop */
        .sidebar-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1040;
        }

        /* Custom scrollbar for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(31, 41, 55, 0.8);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(156, 163, 175, 0.3);
            border-radius: 2px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(156, 163, 175, 0.5);
        }

        /* Firefox scrollbar */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(156, 163, 175, 0.3) rgba(31, 41, 55, 0.8);
        }



        /* Header styling improvements */
        .top-header {
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
        }

        /* Currency badge styling */
        .currency-badge {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        /* Ensure dropdown menu is properly positioned and visible */
        .dropdown-menu {
            z-index: 1060 !important;
            min-width: 220px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
            border: 1px solid rgba(0, 0, 0, 0.15) !important;
            margin-top: 0.5rem !important;
            border-radius: 0.5rem !important;
            padding: 0.5rem 0 !important;
        }

        /* Ensure dropdown doesn't get cut off */
        .dropdown {
            position: relative !important;
        }

        /* Force dropdown to show when active */
        .dropdown-menu.show {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        .dropdown-toggle::after {
            margin-left: 0.5rem;
        }

        /* User dropdown specific styling */
        .dropdown .btn {
            border-color: #6c757d;
            color: #495057;
            font-weight: 500;
        }

        .dropdown .btn:hover {
            background-color: #e9ecef;
            border-color: #6c757d;
        }

        .dropdown .btn:focus {
            box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
        }

        /* Dropdown menu styling */
        .dropdown-menu {
            border-radius: 0.5rem;
            padding: 0.5rem 0;
        }

        .dropdown-header {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6c757d;
            padding: 0.75rem 1rem;
            margin-bottom: 0;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            transition: all 0.15s ease-in-out;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item.text-danger:hover {
            background-color: #f8d7da;
            color: #721c24 !important;
        }

        /* Responsive adjustments for user dropdown */
        @media (max-width: 576px) {
            .dropdown .btn {
                padding: 0.375rem 0.75rem;
                font-size: 0.875rem;
            }

            .dropdown-menu {
                min-width: 180px;
                right: 0 !important;
                left: auto !important;
            }
        }

        /* Ensure dropdown button is always visible */
        .dropdown .btn {
            white-space: nowrap;
            min-width: 100px;
        }

        /* Main content area adjustments */
        .main-content {
            padding: 0;
            margin: 0;
            background-color: #f8f9fa;
        }

        /* Fix any potential overflow issues */
        body {
            overflow-x: hidden;
        }

        /* Email Verification Success Popup Styles */
        .email-verification-popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.5s ease-out;
        }

        .email-verification-popup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            position: relative;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            animation: slideUp 0.6s ease-out 0.2s both;
        }

        .popup-content {
            background: white;
            margin: 4px;
            border-radius: 16px;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .success-icon-container {
            position: relative;
            margin-bottom: 30px;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            animation: bounceIn 0.8s ease-out 0.5s both;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
        }

        .success-icon i {
            font-size: 40px;
            color: white;
            animation: checkmark 0.6s ease-out 1s both;
        }

        .success-stars {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
        }

        .success-stars i {
            position: absolute;
            color: #ffd700;
            font-size: 16px;
            animation: sparkle 2s ease-in-out infinite;
        }

        .star-1 { top: -40px; left: -40px; animation-delay: 0.2s; }
        .star-2 { top: -40px; right: -40px; animation-delay: 0.4s; }
        .star-3 { bottom: -40px; left: -40px; animation-delay: 0.6s; }
        .star-4 { bottom: -40px; right: -40px; animation-delay: 0.8s; }

        .popup-title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            animation: fadeInUp 0.6s ease-out 0.8s both;
        }

        .popup-message {
            font-size: 16px;
            color: #6c757d;
            margin-bottom: 25px;
            line-height: 1.5;
            animation: fadeInUp 0.6s ease-out 1s both;
        }

        .organization-branding {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            border: 2px solid #e1bee7;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 30px;
            font-weight: 600;
            color: #4a148c;
            animation: fadeInUp 0.6s ease-out 1.2s both;
        }

        .popup-actions {
            animation: fadeInUp 0.6s ease-out 1.4s both;
        }

        .popup-close-btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
        }

        .popup-close-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0, 123, 255, 0.4);
        }

        .popup-close-x {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .popup-close-x:hover {
            background: rgba(255, 255, 255, 1);
            color: #dc3545;
            transform: scale(1.1);
        }

        /* Confetti Styles */
        .confetti-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
            border-radius: 20px;
        }

        .confetti-piece {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #ff6b6b;
            animation: confettiFall 3s linear infinite;
        }

        .confetti-piece:nth-child(2n) { background: #4ecdc4; }
        .confetti-piece:nth-child(3n) { background: #45b7d1; }
        .confetti-piece:nth-child(4n) { background: #f9ca24; }
        .confetti-piece:nth-child(5n) { background: #6c5ce7; }
        .confetti-piece:nth-child(6n) { background: #a29bfe; }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.1);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes checkmark {
            0% {
                opacity: 0;
                transform: scale(0);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes sparkle {
            0%, 100% {
                opacity: 0;
                transform: scale(0) rotate(0deg);
            }
            50% {
                opacity: 1;
                transform: scale(1) rotate(180deg);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes confettiFall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(720deg);
                opacity: 0;
            }
        }

        /* Responsive Design */
        @media (max-width: 576px) {
            .email-verification-popup {
                width: 95%;
                margin: 20px;
            }

            .popup-content {
                padding: 30px 20px;
            }

            .popup-title {
                font-size: 24px;
            }

            .success-icon {
                width: 60px;
                height: 60px;
            }

            .success-icon i {
                font-size: 30px;
            }
        }
    </style>

    <!-- International Telephone Input -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/css/intlTelInput.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/intlTelInput.min.js"></script>
</head>
<body class="font-sans antialiased bg-gray-100">
    <!-- Impersonation Banner -->
    @include('components.impersonation-banner')

    <!-- Mobile backdrop -->
    <div class="sidebar-backdrop d-md-none" id="sidebar-backdrop" style="display: none;"></div>

    <div class="main-layout">
        <!-- Sidebar -->
        <nav class="sidebar d-md-block" id="sidebar">
            <x-sidebar :settings="$settings" />
        </nav>

        <!-- Main content -->
        <main class="main-content">
                <!-- Top Navigation -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom" style="padding-left: 1.5rem; padding-right: 1.5rem;">
                    <div class="d-flex align-items-center">
                        <button class="btn btn-outline-secondary d-md-none me-2" type="button" id="mobile-menu-btn">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="h2 mb-0">@yield('page-title', 'Dashboard')</h1>
                    </div>

                    <div class="d-flex align-items-center flex-wrap gap-2">
                        <!-- Currency Override Indicator -->
                        @php
                            $overrideEnabled = \App\Models\CurrencySetting::get('system_currency_override_enabled', false);
                            $overrideCurrency = \App\Models\CurrencySetting::get('system_currency_override', 'USD');
                        @endphp

                        @if($overrideEnabled)
                            <div class="d-none d-md-block">
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-globe-americas me-1"></i>
                                    System Override: {{ $overrideCurrency }}
                                </span>
                            </div>
                        @endif

                        <!-- Current Currency Display -->
                        <div class="text-center d-none d-sm-block">
                            <div class="small text-muted">Currency</div>
                            <span class="badge bg-{{ user_currency() === 'NGN' ? 'success' : 'primary' }}">
                                {{ user_currency_symbol() }} {{ user_currency() }}
                            </span>
                        </div>

                        <!-- User Info -->
                        @if(auth()->check())
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle d-flex align-items-center"
                                    type="button" id="userDropdown" data-bs-toggle="dropdown"
                                    aria-expanded="false" title="User Menu - {{ auth()->user()->name }}"
                                    style="min-width: 120px;">
                                <i class="fas fa-user me-1"></i>
                                <span class="d-none d-md-inline">{{ auth()->user()->name }}</span>
                                <span class="d-md-none">{{ Str::limit(auth()->user()->name, 10) }}</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="userDropdown"
                                style="min-width: 220px; z-index: 1060;">
                                <li>
                                    <h6 class="dropdown-header d-flex align-items-center">
                                        <i class="fas fa-user-circle me-2 text-primary"></i>
                                        <div>
                                            <div class="fw-bold">{{ auth()->user()->name }}</div>
                                            <small class="text-muted">{{ auth()->user()->email }}</small>
                                        </div>
                                    </h6>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item d-flex align-items-center" href="{{ route('profile.show') }}">
                                        <i class="fas fa-user-cog me-3 text-info"></i>
                                        <div>
                                            <div>Profile Settings</div>
                                            <small class="text-muted">Manage your account</small>
                                        </div>
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}" class="d-inline w-100">
                                        @csrf
                                        <button type="submit" class="dropdown-item text-danger d-flex align-items-center"
                                                onclick="return confirm('Are you sure you want to logout?')">
                                            <i class="fas fa-sign-out-alt me-3"></i>
                                            <div>
                                                <div>Logout</div>
                                                <small class="text-muted">Sign out of your account</small>
                                            </div>
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                        @else
                        <!-- Debug: Show when user is not authenticated -->
                        <div class="text-muted small">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            Not authenticated
                        </div>
                        @endif
                    </div>
                </div>
                <!-- Page Heading -->
                @if (isset($header))
                    <header class="bg-white shadow mb-4">
                        <div class="container-fluid py-3">
                            {{ $header }}
                        </div>
                    </header>
                @endif

                <!-- Main Content -->
                <div class="px-4 py-3">
                    <!-- Session Messages -->
                    @if(session('success'))
                        @if(str_contains(session('success'), 'Email verified successfully') || str_contains(session('success'), 'email is already verified'))
                            <!-- Email Verification Success Popup -->
                            <div id="email-verification-popup" class="email-verification-popup-overlay">
                                <div class="email-verification-popup">
                                    <div class="confetti-container" id="confetti-container"></div>

                                    <div class="popup-content">
                                        <div class="success-icon-container">
                                            <div class="success-icon">
                                                <i class="fas fa-check"></i>
                                            </div>
                                            <div class="success-stars">
                                                <i class="fas fa-star star-1"></i>
                                                <i class="fas fa-star star-2"></i>
                                                <i class="fas fa-star star-3"></i>
                                                <i class="fas fa-star star-4"></i>
                                            </div>
                                        </div>

                                        <h2 class="popup-title">🎉 Email Verified Successfully!</h2>
                                        <p class="popup-message">{{ session('success') }}</p>

                                        <div class="organization-branding">
                                            <i class="fas fa-building text-primary me-2"></i>
                                            <span>Organization Account Activated</span>
                                        </div>

                                        <div class="popup-actions">
                                            <button type="button" class="btn btn-primary btn-lg popup-close-btn" onclick="closeEmailVerificationPopup()">
                                                <i class="fas fa-rocket me-2"></i>Continue to Dashboard
                                            </button>
                                        </div>

                                        <button type="button" class="popup-close-x" onclick="closeEmailVerificationPopup()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                {{ session('success') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        @endif
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(session('info'))
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ session('info') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(session('warning'))
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {{ session('warning') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(session('status'))
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ session('status') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <!-- Announcements -->
                    <div id="announcements-container"></div>

                    @yield('content')
                </div>
            </main>
    </div>

    <!-- Bootstrap JS already loaded above -->

    <!-- Ensure dropdown functionality works -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing dropdowns...');

            // Check if Bootstrap is loaded
            if (typeof bootstrap === 'undefined') {
                console.error('Bootstrap is not loaded!');
                return;
            }

            // Initialize all dropdowns
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            console.log('Found dropdown elements:', dropdownElementList.length);

            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });

            console.log('Initialized dropdowns:', dropdownList.length);

            // Enhanced user dropdown handling
            const userDropdown = document.getElementById('userDropdown');
            if (userDropdown) {
                console.log('User dropdown found');

                // Add click event listener for debugging
                userDropdown.addEventListener('click', function(e) {
                    console.log('User dropdown clicked');
                    // Let Bootstrap handle the dropdown naturally
                });

                // Add event listeners for dropdown state changes
                userDropdown.addEventListener('shown.bs.dropdown', function () {
                    console.log('Dropdown shown');
                });

                userDropdown.addEventListener('hidden.bs.dropdown', function () {
                    console.log('Dropdown hidden');
                });

                userDropdown.addEventListener('show.bs.dropdown', function () {
                    console.log('Dropdown showing...');
                });
            } else {
                console.error('User dropdown not found!');
            }
        });
    </script>

    <!-- Mobile sidebar script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const backdrop = document.getElementById('sidebar-backdrop');
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');

            if (sidebar && backdrop && mobileMenuBtn) {
                // Toggle sidebar on mobile menu button click
                mobileMenuBtn.addEventListener('click', function() {
                    if (window.innerWidth < 768) {
                        const isVisible = sidebar.classList.contains('show');

                        if (isVisible) {
                            // Hide sidebar
                            sidebar.classList.remove('show');
                            backdrop.style.display = 'none';
                            document.body.style.overflow = ''; // Restore body scroll
                        } else {
                            // Show sidebar
                            sidebar.classList.add('show');
                            backdrop.style.display = 'block';
                            document.body.style.overflow = 'hidden'; // Prevent body scroll
                        }
                    }
                });

                // Close sidebar when backdrop is clicked
                backdrop.addEventListener('click', function() {
                    sidebar.classList.remove('show');
                    backdrop.style.display = 'none';
                    document.body.style.overflow = ''; // Restore body scroll
                });

                // Handle window resize
                window.addEventListener('resize', function() {
                    if (window.innerWidth >= 768) {
                        // Desktop: ensure sidebar is visible and backdrop is hidden
                        sidebar.classList.remove('show');
                        backdrop.style.display = 'none';
                        document.body.style.overflow = ''; // Restore body scroll
                    }
                });
            }
        });
    </script>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- User Management JavaScript -->
    @if(request()->routeIs('users.*'))
        <script>
            console.log('Loading user-management.js from:', '{{ asset('js/user-management.js') }}');
            console.log('Current URL:', window.location.href);
            console.log('Base URL:', '{{ url('/') }}');
        </script>
        <script src="{{ asset('js/user-management.js') }}"
                onerror="console.error('Failed to load user-management.js from {{ asset('js/user-management.js') }}')"
                onload="console.log('user-management.js loaded successfully')"></script>
        <script>
            // Fallback check after script should have loaded
            setTimeout(() => {
                if (typeof window.UserManagement === 'undefined') {
                    console.error('UserManagement still not defined after script load attempt');
                    console.log('Available window properties:', Object.keys(window).filter(k => k.includes('User')));
                } else {
                    console.log('UserManagement successfully available');
                }
            }, 100);
        </script>
    @endif

    @stack('scripts')

    <script>
    // Register Service Worker
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('ServiceWorker registered: ', registration);
                })
                .catch(error => {
                    console.log('ServiceWorker registration failed: ', error);
                });
        });
    }

    // Handle offline/online status
    window.addEventListener('online', function() {
        document.body.classList.remove('offline');
        Swal.fire({
            icon: 'success',
            title: 'Back Online',
            text: 'Your connection has been restored.',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000
        });
    });

    window.addEventListener('offline', function() {
        document.body.classList.add('offline');
        Swal.fire({
            icon: 'warning',
            title: 'You\'re Offline',
            text: 'Some features may be limited.',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000
        });
    });

    function closeAlert(id) {
        document.getElementById(id).style.display = 'none';
    }

    // Global error handler
    window.addEventListener('error', function(e) {
        console.error('🚨 JavaScript Error:', e.error);
        console.error('🚨 Error message:', e.message);
        console.error('🚨 Error source:', e.filename + ':' + e.lineno);
    });

    // Auto-hide alerts after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
        try {
            console.log('📱 DOM Content Loaded');

            const alerts = document.querySelectorAll('[id$="-alert"]');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 5000);
            });

            // Announcements are now loaded server-side in dashboard
            @auth
            console.log('✅ User is authenticated - announcements loaded server-side');
            @endauth
        } catch (error) {
            console.error('🚨 Error in DOMContentLoaded:', error);
        }
    });

    // Announcements functionality
    @auth
    function loadAnnouncements() {
        try {
            console.log('Loading announcements...');
            console.log('Current URL:', window.location.href);
            console.log('Fetching from:', '/announcements-api');

            fetch('/announcements-api')
            .then(response => {
                console.log('✅ Announcements API response received');
                console.log('📊 Response status:', response.status);
                console.log('📊 Response ok:', response.ok);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                console.log('🔄 Converting response to JSON...');
                return response.json();
            })
            .then(data => {
                console.log('✅ Announcements JSON data received:', data);
                console.log('📊 Number of announcements:', data.announcements ? data.announcements.length : 0);
                console.log('📊 Announcements array:', data.announcements);
                displayAnnouncements(data.announcements);
            })
            .catch(error => {
                console.error('Error loading announcements:', error);
                console.error('Error details:', error.message);
            });
        } catch (error) {
            console.error('🚨 Error in loadAnnouncements function:', error);
        }
    }

    function displayAnnouncements(announcements) {
        console.log('🎨 displayAnnouncements called');
        console.log('📊 Announcements parameter:', announcements);
        console.log('📊 Announcements type:', typeof announcements);
        console.log('📊 Announcements is array:', Array.isArray(announcements));

        const container = document.getElementById('announcements-container');
        console.log('📦 Container element:', container);
        console.log('📦 Container found:', !!container);

        if (!container) {
            console.error('❌ Announcements container not found!');
            return;
        }

        if (!announcements || announcements.length === 0) {
            console.log('ℹ️ No announcements to display');
            container.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>No announcements at this time.</div>';
            return;
        }

        console.log('🧹 Clearing container...');
        container.innerHTML = '';
        console.log('🔄 Displaying', announcements.length, 'announcements');

        announcements.forEach((announcement, index) => {
            console.log(`🔄 Processing announcement ${index + 1}:`, announcement);
            console.log(`📝 Title: ${announcement.title}`);
            const announcementHtml = createAnnouncementHtml(announcement);
            console.log(`📝 Generated HTML length: ${announcementHtml.length}`);
            container.insertAdjacentHTML('beforeend', announcementHtml);
            console.log(`✅ Announcement ${index + 1} added to container`);
        });

        console.log('🎉 All announcements displayed successfully!');
        console.log('📦 Final container HTML length:', container.innerHTML.length);
    }

    function createAnnouncementHtml(announcement) {
        const dismissButton = announcement.is_dismissible
            ? `<button type="button" class="btn-close" onclick="dismissAnnouncement(${announcement.id})" aria-label="Close"></button>`
            : '';

        const priorityClass = announcement.priority === 'urgent' ? 'border-danger border-3' : '';

        return `
            <div id="announcement-${announcement.id}" class="alert ${announcement.alert_class} ${priorityClass} d-flex align-items-start mb-3" role="alert">
                <i class="${announcement.icon} fa-lg me-3 mt-1"></i>
                <div class="flex-grow-1">
                    <h5 class="alert-heading mb-2">${announcement.title}</h5>
                    <div>${announcement.content}</div>
                    ${announcement.affected_features && announcement.affected_features.length > 0 ?
                        `<div class="mt-2">
                            <strong>Affected Features:</strong>
                            ${announcement.affected_features.map(feature => `<span class="badge bg-dark me-1">${feature}</span>`).join('')}
                        </div>` : ''
                    }
                    ${announcement.starts_at || announcement.ends_at ?
                        `<div class="mt-2 small">
                            ${announcement.starts_at ? `<div><strong>Start:</strong> ${new Date(announcement.starts_at).toLocaleString()}</div>` : ''}
                            ${announcement.ends_at ? `<div><strong>End:</strong> ${new Date(announcement.ends_at).toLocaleString()}</div>` : ''}
                        </div>` : ''
                    }
                </div>
                ${dismissButton}
            </div>
        `;
    }

    // dismissAnnouncement function moved to dashboard.blade.php

    // markAnnouncementAsRead function removed - not needed for server-side implementation
    @endauth
    </script>

    <style>
        [x-cloak] { display: none !important; }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            .sidebar {
                height: 100vh;
                padding-top: 4rem; /* Add padding for mobile header */
            }
        }

        /* Scrollbar styling for sidebar */
        .sidebar::-webkit-scrollbar {
            width: 2px;
        }

        .sidebar::-webkit-scrollbar-track {
            background-color: rgba(31, 41, 55, 0.8);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background-color: rgba(156, 163, 175, 0.3);
            border-radius: 1px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background-color: rgba(156, 163, 175, 0.5);
        }

        /* Firefox scrollbar */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(156, 163, 175, 0.3) rgba(31, 41, 55, 0.8);
        }

        /* Responsive grid adjustments */
        @media (max-width: 640px) {
            .grid {
                grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
            }
        }
    </style>

    <!-- Email Verification Popup JavaScript -->
    <script>
        // Email Verification Popup Functions
        function closeEmailVerificationPopup() {
            const popup = document.getElementById('email-verification-popup');
            if (popup) {
                popup.style.animation = 'fadeOut 0.3s ease-out forwards';
                setTimeout(() => {
                    popup.remove();
                }, 300);
            }
        }

        function createConfetti() {
            const container = document.getElementById('confetti-container');
            if (!container) return;

            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#f9ca24', '#6c5ce7', '#a29bfe'];

            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.className = 'confetti-piece';
                confetti.style.left = Math.random() * 100 + '%';
                confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.animationDelay = Math.random() * 3 + 's';
                confetti.style.animationDuration = (Math.random() * 2 + 2) + 's';
                container.appendChild(confetti);
            }
        }

        // Initialize popup when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            const popup = document.getElementById('email-verification-popup');
            if (popup) {
                // Create confetti effect
                createConfetti();

                // Auto-close after 15 seconds
                setTimeout(() => {
                    closeEmailVerificationPopup();
                }, 15000);

                // Close on overlay click
                popup.addEventListener('click', function(e) {
                    if (e.target === popup) {
                        closeEmailVerificationPopup();
                    }
                });

                // Close on escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape') {
                        closeEmailVerificationPopup();
                    }
                });
            }
        });

        // Add fadeOut animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>



