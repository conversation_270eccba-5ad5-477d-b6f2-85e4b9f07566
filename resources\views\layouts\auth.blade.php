<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Sederly') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #4e73df;
            --primary-dark: #3a5bc7;
            --success-color: #1cc88a;
            --info-color: #36b9cc;
            --warning-color: #f6c23e;
            --danger-color: #e74a3b;
            --secondary-color: #858796;
            --light-color: #f8f9fc;
            --dark-color: #5a5c69;
        }

        body {
            font-family: 'Poppins', sans-serif;
            height: 100vh;
            background: linear-gradient(135deg, #8e2de2, #4a00e0);
            background-size: cover;
            background-attachment: fixed;
            display: flex;
            align-items: center;
        }

        .auth-brand {
            margin-bottom: 2rem;
            text-align: center;
        }

        .auth-brand img {
            height: 50px;
        }

        .auth-brand h1 {
            font-size: 1.8rem;
            font-weight: 700;
            color: white;
            margin-top: 0.5rem;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.2);
        }

        .auth-wrapper {
            width: 100%;
            padding: 15px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .card-header {
            background: var(--primary-color);
            border-bottom: none;
            position: relative;
            overflow: hidden;
            padding: 1.5rem;
        }

        .card-header h3 {
            font-weight: 600;
            position: relative;
            z-index: 2;
        }

        .card-body {
            padding: 2rem;
        }

        .card-footer {
            background-color: rgba(248, 249, 252, 0.5);
            border-top: none;
            padding: 1.25rem;
        }

        .form-floating label {
            color: #6c757d;
        }

        .form-floating>.form-control:focus~label,
        .form-floating>.form-control:not(:placeholder-shown)~label {
            color: var(--primary-color);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            padding: 0.6rem 1.5rem;
            font-weight: 500;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .alert {
            border-radius: 8px;
        }

        .alert-danger {
            background-color: rgba(231, 74, 59, 0.1);
            border-color: var(--danger-color);
            color: var(--danger-color);
        }

        /* Shape decoration elements */
        .shape-1 {
            position: absolute;
            top: -20px;
            right: -50px;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            z-index: 1;
        }

        .shape-2 {
            position: absolute;
            bottom: -30px;
            left: -30px;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            z-index: 1;
        }

        /* Footer styling */
        .auth-footer {
            text-align: center;
            margin-top: 2rem;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        /* Links styling */
        a {
            color: var(--info-color);
            text-decoration: none;
            transition: all 0.3s;
        }

        a:hover {
            color: #2d9faf;
        }

        .text-primary {
            color: var(--primary-color) !important;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
    </style>
</head>
<body>
    <div class="auth-wrapper">
        <div class="container">
            <div class="auth-brand fade-in">
                <h1>Order Flow Pro</h1>
            </div>

            <!-- Login Announcements -->
            <div id="login-announcements-container" class="fade-in" style="animation-delay: 0.2s;"></div>

            <div class="fade-in" style="animation-delay: 0.3s;">
                @yield('content')
            </div>

            <div class="auth-footer fade-in" style="animation-delay: 0.6s;">
                <p>&copy; {{ date('Y') }} Order Flow Pro. All rights reserved.</p>
            </div>
        </div>
    </div>

    <!-- Bootstrap & jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function togglePasswordVisibility(inputId) {
            const passwordInput = document.getElementById(inputId);
            const icon = document.querySelector(`i[data-target="${inputId}"]`);

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            }
        }

        // Load login announcements
        document.addEventListener('DOMContentLoaded', function() {
            loadLoginAnnouncements();
        });

        function loadLoginAnnouncements() {
            console.log('Loading login announcements...');
            fetch('/api/announcements/login')
                .then(response => {
                    console.log('Login announcements API response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Login announcements data received:', data);
                    displayLoginAnnouncements(data.announcements);
                })
                .catch(error => {
                    console.error('Error loading login announcements:', error);
                });
        }

        function displayLoginAnnouncements(announcements) {
            const container = document.getElementById('login-announcements-container');
            if (!container || announcements.length === 0) return;

            container.innerHTML = '';

            announcements.forEach(announcement => {
                const announcementHtml = createLoginAnnouncementHtml(announcement);
                container.insertAdjacentHTML('beforeend', announcementHtml);
            });
        }

        function createLoginAnnouncementHtml(announcement) {
            const priorityClass = announcement.priority === 'urgent' ? 'border-danger border-3' : '';

            return `
                <div class="alert ${announcement.alert_class} ${priorityClass} d-flex align-items-start mb-3" role="alert" style="background: rgba(255,255,255,0.95); backdrop-filter: blur(10px);">
                    <i class="${announcement.icon} fa-lg me-3 mt-1"></i>
                    <div class="flex-grow-1">
                        <h6 class="alert-heading mb-2">${announcement.title}</h6>
                        <div class="small">${announcement.content}</div>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
