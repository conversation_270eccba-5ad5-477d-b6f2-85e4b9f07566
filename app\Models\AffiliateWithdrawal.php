<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AffiliateWithdrawal extends Model
{
    use HasFactory;

    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_PAID = 'paid';
    const STATUS_CANCELLED = 'cancelled';

    const PAYMENT_METHOD_BANK = 'bank_transfer';
    const PAYMENT_METHOD_PAYPAL = 'paypal';
    const PAYMENT_METHOD_STRIPE = 'stripe';
    const PAYMENT_METHOD_MANUAL = 'manual';

    protected $fillable = [
        'affiliate_id',
        'amount',
        'payment_method',
        'payment_details',
        'status',
        'requested_at',
        'processed_at',
        'processed_by',
        'notes',
        'transaction_reference',
        'rejection_reason',
        'fee_amount',
        'net_amount',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'fee_amount' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'requested_at' => 'datetime',
        'processed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'payment_details' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($withdrawal) {
            if (empty($withdrawal->requested_at)) {
                $withdrawal->requested_at = now();
            }
            if (empty($withdrawal->net_amount)) {
                $withdrawal->net_amount = $withdrawal->amount - $withdrawal->fee_amount;
            }
        });
    }

    /**
     * Get the affiliate that requested this withdrawal
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(Affiliate::class);
    }

    /**
     * Get the super admin who processed this withdrawal
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(SuperAdmin::class, 'processed_by');
    }

    /**
     * Scope for pending withdrawals
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for approved withdrawals
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * Scope for paid withdrawals
     */
    public function scopePaid($query)
    {
        return $query->where('status', self::STATUS_PAID);
    }

    /**
     * Scope for rejected withdrawals
     */
    public function scopeRejected($query)
    {
        return $query->where('status', self::STATUS_REJECTED);
    }

    /**
     * Check if withdrawal is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if withdrawal is approved
     */
    public function isApproved(): bool
    {
        return $this->status === self::STATUS_APPROVED;
    }

    /**
     * Check if withdrawal is paid
     */
    public function isPaid(): bool
    {
        return $this->status === self::STATUS_PAID;
    }

    /**
     * Check if withdrawal is rejected
     */
    public function isRejected(): bool
    {
        return $this->status === self::STATUS_REJECTED;
    }

    /**
     * Approve the withdrawal
     */
    public function approve($processedBy = null, $notes = null): bool
    {
        $this->status = self::STATUS_APPROVED;
        $this->processed_at = now();
        $this->processed_by = $processedBy;
        if ($notes) {
            $this->notes = $notes;
        }

        return $this->save();
    }

    /**
     * Reject the withdrawal
     */
    public function reject($processedBy = null, $reason = null, $notes = null): bool
    {
        $this->status = self::STATUS_REJECTED;
        $this->processed_at = now();
        $this->processed_by = $processedBy;
        if ($reason) {
            $this->rejection_reason = $reason;
        }
        if ($notes) {
            $this->notes = $notes;
        }

        // Return amount to affiliate's available balance
        $affiliate = $this->affiliate;
        $affiliate->available_balance += $this->amount;
        $affiliate->save();

        return $this->save();
    }

    /**
     * Mark as paid
     */
    public function markAsPaid($transactionReference = null, $processedBy = null): bool
    {
        $this->status = self::STATUS_PAID;
        $this->processed_at = now();
        $this->processed_by = $processedBy;
        if ($transactionReference) {
            $this->transaction_reference = $transactionReference;
        }

        // Update affiliate's withdrawn amount
        $affiliate = $this->affiliate;
        $affiliate->withdrawn_amount += $this->amount;
        $affiliate->save();

        return $this->save();
    }

    /**
     * Cancel the withdrawal
     */
    public function cancel($reason = null): bool
    {
        $this->status = self::STATUS_CANCELLED;
        $this->processed_at = now();
        if ($reason) {
            $this->rejection_reason = $reason;
        }

        // Return amount to affiliate's available balance
        $affiliate = $this->affiliate;
        $affiliate->available_balance += $this->amount;
        $affiliate->save();

        return $this->save();
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute(): string
    {
        return '$' . number_format($this->amount, 2);
    }

    /**
     * Get formatted net amount
     */
    public function getFormattedNetAmountAttribute(): string
    {
        return '$' . number_format($this->net_amount, 2);
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'bg-yellow-100 text-yellow-800',
            self::STATUS_APPROVED => 'bg-green-100 text-green-800',
            self::STATUS_PAID => 'bg-blue-100 text-blue-800',
            self::STATUS_REJECTED => 'bg-red-100 text-red-800',
            self::STATUS_CANCELLED => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get payment method display name
     */
    public function getPaymentMethodDisplayAttribute(): string
    {
        return match($this->payment_method) {
            self::PAYMENT_METHOD_BANK => 'Bank Transfer',
            self::PAYMENT_METHOD_PAYPAL => 'PayPal',
            self::PAYMENT_METHOD_STRIPE => 'Stripe',
            self::PAYMENT_METHOD_MANUAL => 'Manual Payment',
            default => ucfirst(str_replace('_', ' ', $this->payment_method)),
        };
    }

    /**
     * Get days since request
     */
    public function getDaysSinceRequestAttribute(): int
    {
        return $this->requested_at->diffInDays(now());
    }
}
