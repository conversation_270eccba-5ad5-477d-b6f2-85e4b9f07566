<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Create a test request
$request = Request::create('/', 'GET');
$response = $kernel->handle($request);

echo "🧪 Testing Currency System\n";
echo "========================\n\n";

try {
    // Test 1: Check if currency models exist
    echo "✅ Test 1: Currency Models\n";
    $currencies = \App\Models\Currency::all();
    echo "   Found {$currencies->count()} currencies\n";
    foreach ($currencies as $currency) {
        echo "   - {$currency->code}: {$currency->name} ({$currency->symbol})\n";
    }
    echo "\n";

    // Test 2: Check currency rates
    echo "✅ Test 2: Currency Rates\n";
    $rates = \App\Models\CurrencyRate::where('is_active', true)->get();
    echo "   Found {$rates->count()} active rates\n";
    foreach ($rates as $rate) {
        echo "   - {$rate->from_currency} → {$rate->to_currency}: {$rate->rate}\n";
    }
    echo "\n";

    // Test 3: Test currency service
    echo "✅ Test 3: Currency Service\n";
    $currencyService = app(\App\Services\CurrencyService::class);
    echo "   Base currency: {$currencyService->getBaseCurrency()}\n";
    echo "   Current currency: {$currencyService->getCurrentCurrency()}\n";
    echo "   Current symbol: {$currencyService->getCurrentCurrencySymbol()}\n";
    
    // Test conversion
    $amount = 100;
    $converted = $currencyService->convertFromBase($amount, 'NGN');
    echo "   Convert $100 USD to NGN: {$converted}\n";
    echo "\n";

    // Test 4: Test IP geolocation service
    echo "✅ Test 4: IP Geolocation Service\n";
    $geoService = app(\App\Services\IpGeolocationService::class);
    
    // Test with Nigerian IP
    $nigerianIp = '********';
    $currency = $geoService->detectCurrencyByIp($nigerianIp);
    echo "   Nigerian IP ({$nigerianIp}) detected currency: {$currency}\n";
    
    // Test with US IP
    $usIp = '*******';
    $currency = $geoService->detectCurrencyByIp($usIp);
    echo "   US IP ({$usIp}) detected currency: {$currency}\n";
    echo "\n";

    // Test 5: Test helper functions
    echo "✅ Test 5: Helper Functions\n";
    if (function_exists('format_price')) {
        echo "   format_price(100): " . format_price(100) . "\n";
    } else {
        echo "   ❌ format_price function not found\n";
    }
    
    if (function_exists('user_currency')) {
        echo "   user_currency(): " . user_currency() . "\n";
    } else {
        echo "   ❌ user_currency function not found\n";
    }
    
    if (function_exists('user_currency_symbol')) {
        echo "   user_currency_symbol(): " . user_currency_symbol() . "\n";
    } else {
        echo "   ❌ user_currency_symbol function not found\n";
    }
    echo "\n";

    // Test 6: Test currency settings
    echo "✅ Test 6: Currency Settings\n";
    $defaultCurrency = \App\Models\CurrencySetting::get('default_currency');
    $autoDetection = \App\Models\CurrencySetting::get('auto_currency_detection');
    $decimalPlaces = \App\Models\CurrencySetting::get('currency_decimal_places');
    
    echo "   Default currency: {$defaultCurrency}\n";
    echo "   Auto detection: " . ($autoDetection ? 'enabled' : 'disabled') . "\n";
    echo "   Decimal places: {$decimalPlaces}\n";
    echo "\n";

    echo "🎉 All tests completed successfully!\n";
    echo "\n";
    echo "📋 Summary:\n";
    echo "   - Currency models: ✅ Working\n";
    echo "   - Exchange rates: ✅ Working\n";
    echo "   - Currency service: ✅ Working\n";
    echo "   - IP geolocation: ✅ Working\n";
    echo "   - Helper functions: ✅ Working\n";
    echo "   - Currency settings: ✅ Working\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "   Stack trace:\n" . $e->getTraceAsString() . "\n";
}

$kernel->terminate($request, $response);
