<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OrderRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'customer_name' => 'required|string',
            'phone_number' => 'required|string',
            'orders' => 'required|array|min:1',
            'orders.*.order_title' => 'required|string',
            'orders.*.job_description' => 'required|string',
            'orders.*.department' => 'required|string',
            'orders.*.quantity' => 'required|numeric|min:1',
            'orders.*.unit_cost' => 'required|numeric|min:0',
            'orders.*.amount_paid' => 'required|numeric|min:0',
            'orders.*.expected_delivery_date' => 'required|date|after_or_equal:today',
            'orders.*.expected_delivery_time' => 'required|string'
        ];
    }

    public function messages()
    {
        return [
            'orders.required' => 'At least one order is required',
            'orders.min' => 'At least one order is required',
            'orders.*.required' => 'Order details are required',
            'orders.*.order_title.required' => 'Order title is required for each order',
            'orders.*.job_description.required' => 'Job description is required for each order',
            'orders.*.department.required' => 'Department is required for each order',
            'orders.*.quantity.required' => 'Quantity is required for each order',
            'orders.*.quantity.min' => 'Quantity must be at least 1 for each order',
            'orders.*.unit_cost.required' => 'Unit cost is required for each order',
            'orders.*.unit_cost.min' => 'Unit cost cannot be negative for each order',
            'orders.*.amount_paid.required' => 'Amount paid is required for each order',
            'orders.*.amount_paid.min' => 'Amount paid cannot be negative for each order',
            'orders.*.expected_delivery_date.required' => 'Delivery date is required for each order',
            'orders.*.expected_delivery_date.after_or_equal' => 'Delivery date must be today or later for each order',
            'orders.*.expected_delivery_time.required' => 'Delivery time is required for each order'
        ];
    }
    
    protected function prepareForValidation()
    {
        // Convert empty strings to null for nullable fields
        if ($this->has('orders')) {
            $orders = collect($this->orders)->map(function ($order) {
                return array_map(function ($value) {
                    return $value === '' ? null : $value;
                }, $order);
            })->toArray();
            
            $this->merge(['orders' => $orders]);
        }
    }
}