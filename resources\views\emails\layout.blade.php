<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Sederly')</title>
    <style>
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
        }

        /* Base styles */
        body {
            margin: 0;
            padding: 0;
            width: 100% !important;
            min-width: 100%;
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }

        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px 20px;
            text-align: center;
        }

        .email-header h1 {
            color: #ffffff;
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }

        .email-header .tagline {
            color: #e8eaff;
            margin: 5px 0 0 0;
            font-size: 14px;
        }

        .email-body {
            padding: 40px 30px;
        }

        .email-body h2 {
            color: #333333;
            font-size: 24px;
            margin: 0 0 20px 0;
            font-weight: 400;
        }

        .email-body p {
            margin: 0 0 16px 0;
            font-size: 16px;
            line-height: 1.6;
        }

        .email-body .greeting {
            font-size: 18px;
            color: #555555;
            margin-bottom: 25px;
        }

        .btn {
            display: inline-block;
            padding: 14px 28px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            font-size: 16px;
            margin: 20px 0;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .btn-center {
            text-align: center;
            margin: 30px 0;
        }

        .info-box {
            background-color: #f8f9ff;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 6px 6px 0;
        }

        .info-box h3 {
            margin: 0 0 10px 0;
            color: #333333;
            font-size: 18px;
        }

        .info-box p {
            margin: 0;
            color: #666666;
        }

        .email-footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }

        .email-footer p {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #6c757d;
        }

        .email-footer .social-links {
            margin: 20px 0;
        }

        .email-footer .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #6c757d;
            text-decoration: none;
        }

        .divider {
            height: 1px;
            background-color: #e9ecef;
            margin: 30px 0;
        }

        .text-center {
            text-align: center;
        }

        .text-muted {
            color: #6c757d !important;
        }

        .small {
            font-size: 14px;
        }

        /* Responsive styles */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
            }
            
            .email-header,
            .email-body,
            .email-footer {
                padding-left: 20px !important;
                padding-right: 20px !important;
            }
            
            .email-header h1 {
                font-size: 24px !important;
            }
            
            .email-body h2 {
                font-size: 20px !important;
            }
            
            .btn {
                display: block !important;
                width: 100% !important;
                text-align: center !important;
                box-sizing: border-box !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <h1>@yield('header-title', 'Sederly Solutions')</h1>
            <p class="tagline">@yield('header-tagline', 'Professional Business Management Solution')</p>
        </div>

        <!-- Body -->
        <div class="email-body">
            @yield('content')
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p><strong>Sederly Solutions</strong></p>
            <p>Professional Business Management Solution</p>
            
            <div class="divider"></div>
            
            <p class="small text-muted">
                This email was sent to you because you have an account with Sederly.com.
                If you have any questions, please contact our support team.
            </p>
            
            <p class="small text-muted">
                © {{ date('Y') }} Sederly Solutions. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
