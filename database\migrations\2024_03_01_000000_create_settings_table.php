<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('app_name');
            $table->string('app_slogan')->nullable();
            $table->string('theme_mode')->default('light');
            $table->string('primary_color')->default('#4F46E5');
            $table->string('sidebar_color')->default('#090377');
            $table->string('site_title');
            $table->string('site_logo_path')->nullable();
            $table->string('favicon_path')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('settings');
    }
};