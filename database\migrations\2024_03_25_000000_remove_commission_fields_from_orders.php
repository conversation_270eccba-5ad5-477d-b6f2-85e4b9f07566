<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            // Check each column individually before dropping
            if (Schema::hasColumn('orders', 'sales_team')) {
                $table->dropColumn('sales_team');
            }

            if (Schema::hasColumn('orders', 'sales_commission')) {
                $table->dropColumn('sales_commission');
            }

            if (Schema::hasColumn('orders', 'operators_commission')) {
                $table->dropColumn('operators_commission');
            }
        });
    }

    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            if (!Schema::hasColumn('orders', 'sales_team')) {
                $table->string('sales_team')->default('None');
            }

            if (!Schema::hasColumn('orders', 'sales_commission')) {
                $table->decimal('sales_commission', 10, 2)->default(0);
            }

            if (!Schema::hasColumn('orders', 'operators_commission')) {
                $table->decimal('operators_commission', 10, 2)->default(0);
            }
        });
    }
};
