<?php

namespace App\Services;

use App\Models\Currency;
use App\Models\CurrencyRate;
use App\Models\CurrencyRateHistory;
use App\Models\CurrencySetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CurrencyService
{
    protected $currentCurrency = 'USD';
    protected $baseCurrency = 'USD';

    public function __construct()
    {
        // Base currency should ALWAYS be USD regardless of overrides
        // This is the currency used for storage in the database
        $this->baseCurrency = 'USD';
        $this->currentCurrency = $this->getUserCurrency();
    }

    /**
     * Get user currency based on authentication guard
     */
    protected function getUserCurrency()
    {
        // Check for system override first
        $overrideEnabled = CurrencySetting::get('system_currency_override_enabled', false);
        if ($overrideEnabled) {
            return CurrencySetting::get('system_currency_override', 'USD');
        }

        // Determine user type
        $userType = 'web';
        if (auth()->guard('super_admin')->check()) {
            $userType = 'super_admin';
        } elseif (auth()->guard('affiliate')->check()) {
            $userType = 'affiliate';
        }

        // Get currency from session for this user type
        $sessionKey = "user_currency_{$userType}";
        return session($sessionKey, $this->baseCurrency);
    }

    /**
     * Set current currency for the user
     */
    public function setCurrentCurrency($currency)
    {
        $this->currentCurrency = $currency;

        // Determine user type and set appropriate session key
        $userType = 'web';
        if (auth()->guard('super_admin')->check()) {
            $userType = 'super_admin';
        } elseif (auth()->guard('affiliate')->check()) {
            $userType = 'affiliate';
        }

        $sessionKey = "user_currency_{$userType}";
        session([$sessionKey => $currency]);
    }

    /**
     * Get current currency
     */
    public function getCurrentCurrency()
    {
        // Always check for system override
        $overrideEnabled = CurrencySetting::get('system_currency_override_enabled', false);
        if ($overrideEnabled) {
            $this->currentCurrency = CurrencySetting::get('system_currency_override', 'USD');
        }

        return $this->currentCurrency;
    }

    /**
     * Get current currency symbol
     */
    public function getCurrentCurrencySymbol()
    {
        $currency = Currency::getByCode($this->currentCurrency);
        return $currency ? $currency->symbol : '$';
    }

    /**
     * Get base currency
     */
    public function getBaseCurrency()
    {
        return $this->baseCurrency;
    }

    /**
     * Convert amount from base currency to current currency
     */
    public function convertFromBase($amount, $toCurrency = null)
    {
        $toCurrency = $toCurrency ?: $this->currentCurrency;
        
        if ($this->baseCurrency === $toCurrency) {
            return $amount;
        }

        try {
            return CurrencyRate::convert($amount, $this->baseCurrency, $toCurrency);
        } catch (\Exception $e) {
            Log::warning('Currency conversion failed', [
                'from' => $this->baseCurrency,
                'to' => $toCurrency,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);
            return $amount; // Return original amount if conversion fails
        }
    }

    /**
     * Convert amount to base currency from current currency
     */
    public function convertToBase($amount, $fromCurrency = null)
    {
        $fromCurrency = $fromCurrency ?: $this->currentCurrency;
        
        if ($fromCurrency === $this->baseCurrency) {
            return $amount;
        }

        try {
            return CurrencyRate::convert($amount, $fromCurrency, $this->baseCurrency);
        } catch (\Exception $e) {
            Log::warning('Currency conversion to base failed', [
                'from' => $fromCurrency,
                'to' => $this->baseCurrency,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);
            return $amount; // Return original amount if conversion fails
        }
    }

    /**
     * Convert between any two currencies
     */
    public function convert($amount, $fromCurrency, $toCurrency)
    {
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        try {
            return CurrencyRate::convert($amount, $fromCurrency, $toCurrency);
        } catch (\Exception $e) {
            Log::warning('Currency conversion failed', [
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);
            return $amount;
        }
    }

    /**
     * Format amount with currency symbol
     */
    public function format($amount, $currency = null, $decimals = null)
    {
        $currency = $currency ?: $this->currentCurrency;
        $decimals = $decimals ?: CurrencySetting::getCurrencyDecimalPlaces();
        
        $currencyObj = Currency::getByCode($currency);
        $symbol = $currencyObj ? $currencyObj->symbol : '$';
        
        return $symbol . number_format($amount, $decimals);
    }

    /**
     * Format amount in current currency (with conversion if needed)
     */
    public function formatInCurrentCurrency($amount, $fromCurrency = null)
    {
        $fromCurrency = $fromCurrency ?: $this->baseCurrency;
        
        if ($fromCurrency !== $this->currentCurrency) {
            $amount = $this->convert($amount, $fromCurrency, $this->currentCurrency);
        }
        
        return $this->format($amount, $this->currentCurrency);
    }

    /**
     * Get current exchange rate between two currencies
     */
    public function getExchangeRate($fromCurrency, $toCurrency)
    {
        return CurrencyRate::getCurrentRate($fromCurrency, $toCurrency);
    }

    /**
     * Update exchange rate (Super Admin only)
     */
    public function updateExchangeRate($fromCurrency, $toCurrency, $newRate, $userId, $reason = null)
    {
        try {
            Log::info('Starting exchange rate update', [
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'rate' => $newRate,
                'user' => $userId,
                'reason' => $reason
            ]);

            // Validate inputs
            if (!$fromCurrency || !$toCurrency || !$newRate || !$userId) {
                throw new \InvalidArgumentException('Missing required parameters for exchange rate update');
            }

            // Get current rate for history
            $currentRate = CurrencyRate::where('from_currency', $fromCurrency)
                ->where('to_currency', $toCurrency)
                ->where('is_active', true)
                ->first();

            $oldRate = $currentRate ? $currentRate->rate : null;

            Log::info('Current rate found', ['current_rate' => $oldRate]);

            // Deactivate current rate
            if ($currentRate) {
                $currentRate->update([
                    'is_active' => false,
                    'effective_to' => now()
                ]);
                Log::info('Deactivated current rate');
            }

            // Create new rate
            $newRateRecord = CurrencyRate::create([
                'from_currency' => $fromCurrency,
                'to_currency' => $toCurrency,
                'rate' => $newRate,
                'is_active' => true,
                'effective_from' => now(),
                'created_by' => $userId,
            ]);

            Log::info('Created new rate record', ['new_rate_id' => $newRateRecord->id]);

            // Log the change
            CurrencyRateHistory::logChange(
                $fromCurrency,
                $toCurrency,
                $oldRate,
                $newRate,
                $currentRate ? 'updated' : 'created',
                $userId,
                $reason
            );

            Log::info('Logged rate change to history');

            // Clear cache
            $this->clearExchangeRateCache($fromCurrency, $toCurrency);

            Log::info('Exchange rate update completed successfully');

            return $newRateRecord;

        } catch (\Exception $e) {
            Log::error('Failed to update exchange rate', [
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'rate' => $newRate,
                'user' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get all active currencies
     */
    public function getActiveCurrencies()
    {
        return Currency::getActiveCurrencies();
    }

    /**
     * Get currency by code
     */
    public function getCurrency($code)
    {
        return Currency::getByCode($code);
    }

    /**
     * Clear exchange rate cache
     */
    protected function clearExchangeRateCache($fromCurrency, $toCurrency)
    {
        Cache::forget("exchange_rate_{$fromCurrency}_{$toCurrency}");
        Cache::forget("exchange_rate_{$toCurrency}_{$fromCurrency}");
    }

    /**
     * Get rate history for a currency pair
     */
    public function getRateHistory($fromCurrency, $toCurrency, $limit = 50)
    {
        return CurrencyRateHistory::getHistoryForPair($fromCurrency, $toCurrency, $limit);
    }

    /**
     * Check if user can manage currencies (Super Admin only)
     */
    public function canManageCurrencies($user = null)
    {
        // Check if we're in Super Admin context
        if (auth()->guard('super_admin')->check()) {
            return true;
        }

        $user = $user ?: auth()->user();

        if (!$user) {
            return false;
        }

        return $user->roles()->where('name', 'Super Admin')->exists();
    }

    /**
     * Get currency statistics
     */
    public function getCurrencyStats()
    {
        return [
            'total_currencies' => Currency::where('is_active', true)->count(),
            'total_rates' => CurrencyRate::where('is_active', true)->count(),
            'last_rate_update' => CurrencyRate::where('is_active', true)
                ->orderBy('created_at', 'desc')
                ->first()?->created_at,
            'rate_changes_today' => CurrencyRateHistory::whereDate('created_at', today())->count(),
        ];
    }
}
