<?php

// Legacy helper functions (for backward compatibility)
if (!function_exists('currency_symbol')) {
    /**
     * Get the system's currency symbol
     *
     * @return string
     */
    function currency_symbol()
    {
        try {
            return user_currency_symbol();
        } catch (\Exception $e) {
            return '$'; // Default fallback
        }
    }
}

if (!function_exists('currency_code')) {
    /**
     * Get the system's currency code
     *
     * @return string
     */
    function currency_code()
    {
        try {
            return user_currency();
        } catch (\Exception $e) {
            return 'USD'; // Default fallback
        }
    }
}

if (!function_exists('format_money')) {
    /**
     * Format a number as currency
     *
     * @param float $amount
     * @param int $decimals
     * @return string
     */
    function format_money($amount, $decimals = 2)
    {
        try {
            return format_price($amount);
        } catch (\Exception $e) {
            // Ultimate fallback
            return '$' . number_format($amount, $decimals);
        }
    }
}

// New dual-currency system helper functions
if (!function_exists('currency_format')) {
    /**
     * Format amount with currency symbol
     */
    function currency_format($amount, $currency = null, $decimals = null)
    {
        $currencyService = app(\App\Services\CurrencyService::class);
        return $currencyService->format($amount, $currency, $decimals);
    }
}

if (!function_exists('currency_convert')) {
    /**
     * Convert amount between currencies
     */
    function currency_convert($amount, $fromCurrency, $toCurrency)
    {
        $currencyService = app(\App\Services\CurrencyService::class);
        return $currencyService->convert($amount, $fromCurrency, $toCurrency);
    }
}

if (!function_exists('currency_format_user')) {
    /**
     * Format amount in user's current currency (with conversion if needed)
     */
    function currency_format_user($amount, $fromCurrency = null)
    {
        $currencyService = app(\App\Services\CurrencyService::class);
        return $currencyService->formatInCurrentCurrency($amount, $fromCurrency);
    }
}

if (!function_exists('user_currency')) {
    /**
     * Get current user's currency
     */
    function user_currency()
    {
        try {
            // Check for system override first
            $overrideEnabled = \App\Models\CurrencySetting::get('system_currency_override_enabled', false);
            if ($overrideEnabled) {
                return \App\Models\CurrencySetting::get('system_currency_override', 'USD');
            }

            $currencyService = app(\App\Services\CurrencyService::class);
            return $currencyService->getCurrentCurrency();
        } catch (\Exception $e) {
            // Fallback: check session directly based on user type
            $userType = 'web';
            if (auth()->guard('super_admin')->check()) {
                $userType = 'super_admin';
            } elseif (auth()->guard('affiliate')->check()) {
                $userType = 'affiliate';
            }

            return session("user_currency_{$userType}", 'USD');
        }
    }
}

if (!function_exists('user_currency_symbol')) {
    /**
     * Get current user's currency symbol
     */
    function user_currency_symbol()
    {
        $currencyService = app(\App\Services\CurrencyService::class);
        return $currencyService->getCurrentCurrencySymbol();
    }
}

if (!function_exists('base_currency')) {
    /**
     * Get base currency
     */
    function base_currency()
    {
        // Base currency is always USD regardless of overrides
        // This is the currency used for storage in the database
        return 'USD';
    }
}

if (!function_exists('exchange_rate')) {
    /**
     * Get exchange rate between two currencies
     */
    function exchange_rate($fromCurrency, $toCurrency)
    {
        $currencyService = app(\App\Services\CurrencyService::class);
        return $currencyService->getExchangeRate($fromCurrency, $toCurrency);
    }
}

if (!function_exists('price_in_user_currency')) {
    /**
     * Convert price from base currency to user currency
     */
    function price_in_user_currency($amount)
    {
        $currencyService = app(\App\Services\CurrencyService::class);
        return $currencyService->convertFromBase($amount);
    }
}

if (!function_exists('price_to_base_currency')) {
    /**
     * Convert price from user currency to base currency
     */
    function price_to_base_currency($amount)
    {
        $currencyService = app(\App\Services\CurrencyService::class);
        return $currencyService->convertToBase($amount);
    }
}

if (!function_exists('format_price')) {
    /**
     * Format price with automatic currency conversion and symbol
     * This is the main function to use throughout the application
     */
    function format_price($amount, $fromCurrency = null, $showOriginal = false)
    {
        try {
            $currencyService = app(\App\Services\CurrencyService::class);
            $fromCurrency = $fromCurrency ?: base_currency();
            $userCurrency = user_currency();

            // Convert to user currency if different
            if ($fromCurrency !== $userCurrency) {
                $convertedAmount = $currencyService->convert($amount, $fromCurrency, $userCurrency);
            } else {
                $convertedAmount = $amount;
            }

            $formatted = $currencyService->format($convertedAmount, $userCurrency);

            // Optionally show original price
            if ($showOriginal && $fromCurrency !== $userCurrency) {
                $originalFormatted = $currencyService->format($amount, $fromCurrency);
                return $formatted . ' <small class="text-muted">(' . $originalFormatted . ')</small>';
            }

            return $formatted;
        } catch (\Exception $e) {
            // Fallback to legacy format_money if currency service fails
            return format_money($amount);
        }
    }
}

if (!function_exists('currency_info')) {
    /**
     * Get currency information
     */
    function currency_info($currencyCode = null)
    {
        try {
            $currencyCode = $currencyCode ?: user_currency();
            $currency = \App\Models\Currency::getByCode($currencyCode);

            return $currency ? [
                'code' => $currency->code,
                'name' => $currency->name,
                'symbol' => $currency->symbol,
                'is_base' => $currency->is_base_currency,
            ] : null;
        } catch (\Exception $e) {
            return null;
        }
    }
}

if (!function_exists('is_nigerian_user')) {
    /**
     * Check if current user is detected as Nigerian (NGN currency)
     */
    function is_nigerian_user()
    {
        try {
            return user_currency() === 'NGN';
        } catch (\Exception $e) {
            return false;
        }
    }
}

if (!function_exists('is_usd_user')) {
    /**
     * Check if current user is using USD
     */
    function is_usd_user()
    {
        try {
            return user_currency() === 'USD';
        } catch (\Exception $e) {
            return true; // Default to USD
        }
    }
}

if (!function_exists('location_info')) {
    /**
     * Get user's location information from session
     */
    function location_info($key = null)
    {
        // Determine user type
        $userType = 'web';
        if (auth()->guard('super_admin')->check()) {
            $userType = 'super_admin';
        } elseif (auth()->guard('affiliate')->check()) {
            $userType = 'affiliate';
        }

        $locationInfo = session("location_info_{$userType}", []);

        if ($key) {
            return $locationInfo[$key] ?? null;
        }

        return $locationInfo;
    }
}

if (!function_exists('currency_class')) {
    /**
     * Get CSS class for currency styling
     */
    function currency_class($currency = null)
    {
        try {
            $currency = $currency ?: user_currency();

            switch ($currency) {
                case 'NGN':
                    return 'currency-ngn text-success';
                case 'USD':
                    return 'currency-usd text-primary';
                default:
                    return 'currency-default';
            }
        } catch (\Exception $e) {
            return 'currency-default';
        }
    }
}
