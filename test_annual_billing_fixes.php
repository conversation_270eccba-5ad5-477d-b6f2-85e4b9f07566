<?php

require_once 'vendor/autoload.php';

use App\Models\Plan;
use App\Models\Subscription;

echo "=== Testing Annual Billing Fixes ===\n\n";

try {
    // 1. Test Plan model with null values
    echo "1. Testing Plan model null safety...\n";
    
    $testPlan = new Plan([
        'name' => 'Test Plan',
        'price' => 50.00,
        'annual_price' => null,
        'billing_period' => null,
        'annual_discount_percentage' => null
    ]);
    
    echo "   getPriceForPeriod(null): \${$testPlan->getPriceForPeriod(null)}\n";
    echo "   getPriceForPeriod(''): \${$testPlan->getPriceForPeriod('')}\n";
    echo "   getPriceForPeriod('monthly'): \${$testPlan->getPriceForPeriod('monthly')}\n";
    echo "   getPriceForPeriod('annual'): \${$testPlan->getPriceForPeriod('annual')}\n";
    echo "   supportsAnnualBilling(): " . ($testPlan->supportsAnnualBilling() ? 'Yes' : 'No') . "\n";
    echo "   supportsMonthlyBilling(): " . ($testPlan->supportsMonthlyBilling() ? 'Yes' : 'No') . "\n\n";
    
    // 2. Test Subscription model with null values
    echo "2. Testing Subscription model null safety...\n";
    
    $testSubscription = new Subscription([
        'billing_period' => null,
        'start_date' => now(),
        'end_date' => now()->addMonth()
    ]);
    
    echo "   isMonthly() with null billing_period: " . ($testSubscription->isMonthly() ? 'Yes' : 'No') . "\n";
    echo "   isAnnual() with null billing_period: " . ($testSubscription->isAnnual() ? 'Yes' : 'No') . "\n";
    echo "   getPeriodInMonths() with null billing_period: {$testSubscription->getPeriodInMonths()}\n\n";
    
    // 3. Test with actual database data if available
    echo "3. Testing with actual database data...\n";
    
    $plan = Plan::first();
    if ($plan) {
        echo "   First plan: {$plan->name}\n";
        echo "   Billing period: " . ($plan->billing_period ?: 'null') . "\n";
        echo "   Monthly price: \${$plan->getPriceForPeriod('monthly')}\n";
        echo "   Annual price: \${$plan->getPriceForPeriod('annual')}\n";
        echo "   Supports annual: " . ($plan->supportsAnnualBilling() ? 'Yes' : 'No') . "\n";
        
        if ($plan->supportsAnnualBilling()) {
            echo "   Annual savings: \${$plan->getAnnualSavings()}\n";
            echo "   Savings percentage: {$plan->getAnnualSavingsPercentage()}%\n";
        }
    } else {
        echo "   No plans found in database\n";
    }
    
    $subscription = Subscription::first();
    if ($subscription) {
        echo "   First subscription billing period: " . ($subscription->billing_period ?: 'null') . "\n";
        echo "   Is monthly: " . ($subscription->isMonthly() ? 'Yes' : 'No') . "\n";
        echo "   Is annual: " . ($subscription->isAnnual() ? 'Yes' : 'No') . "\n";
        
        if ($subscription->plan) {
            echo "   Current period amount: \${$subscription->getCurrentPeriodAmount()}\n";
        }
    } else {
        echo "   No subscriptions found in database\n";
    }
    
    echo "\n=== All Tests Passed! ===\n";
    echo "\nFixes implemented:\n";
    echo "✓ Plan::getPriceForPeriod() handles null values\n";
    echo "✓ Subscription::isMonthly() defaults to true for null billing_period\n";
    echo "✓ Subscription::getCurrentPeriodAmount() uses fallback billing period\n";
    echo "✓ ProrationService handles null values safely\n";
    echo "✓ All methods have proper null safety\n";
    echo "\nThe annual billing system should now work without errors!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
