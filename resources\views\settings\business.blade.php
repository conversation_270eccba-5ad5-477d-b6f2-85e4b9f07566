@extends('layouts.settings')

@section('title', 'Business Settings')

@section('business_settings')
<h2 class="text-2xl font-bold mb-6">Business Settings</h2>

<form action="{{ route('settings.business.update') }}" method="POST">
    @csrf
    @method('PUT')

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Company Information</h3>

        <div class="mb-4">
            <label for="organization_name" class="block text-gray-700 text-sm font-bold mb-2">
                Organization Name
            </label>
            <input
                type="text"
                name="organization_name"
                id="organization_name"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-100"
                value="{{ old('organization_name', $setting->organization_name) }}"
                placeholder="Your Company Name"
                readonly
            >
            <p class="text-gray-500 text-xs mt-1">This name was set during registration and cannot be changed.</p>
            @error('organization_name')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="mb-4">
                <label for="business_registration_number" class="block text-gray-700 text-sm font-bold mb-2">
                    Business Registration Number
                </label>
                <input
                    type="text"
                    name="business_registration_number"
                    id="business_registration_number"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    value="{{ old('business_registration_number', $setting->business_registration_number) }}"
                    placeholder="RC12345678"
                >
                @error('business_registration_number')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-4">
                <label for="tax_identification_number" class="block text-gray-700 text-sm font-bold mb-2">
                    Tax Identification Number (TIN)
                </label>
                <input
                    type="text"
                    name="tax_identification_number"
                    id="tax_identification_number"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    value="{{ old('tax_identification_number', $setting->tax_identification_number) }}"
                    placeholder="1234567890"
                >
                @error('tax_identification_number')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Order Settings</h3>

        <div class="mb-4">
            <label for="default_departments" class="block text-gray-700 text-sm font-bold mb-2">
                Default Departments
            </label>
            <input
                type="text"
                name="default_departments"
                id="default_departments"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value="{{ old('default_departments', $setting->default_departments) }}"
                placeholder="Design, Production, Digital, Large Format"
            >
            <p class="text-gray-500 text-xs mt-1">Separate departments with commas. These will be available as options when creating orders.</p>
            @error('default_departments')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <div class="flex items-center">
                <input
                    type="checkbox"
                    name="require_payment_upfront"
                    id="require_payment_upfront"
                    class="mr-2"
                    value="1"
                    {{ old('require_payment_upfront', $setting->require_payment_upfront) ? 'checked' : '' }}
                >
                <label for="require_payment_upfront" class="text-gray-700 text-sm font-bold">
                    Require Payment Upfront
                </label>
            </div>
            <p class="text-gray-500 text-xs mt-1">If checked, new orders will require payment before processing.</p>
            @error('require_payment_upfront')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="default_payment_due_days" class="block text-gray-700 text-sm font-bold mb-2">
                Default Payment Due Days
            </label>
            <input
                type="number"
                name="default_payment_due_days"
                id="default_payment_due_days"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value="{{ old('default_payment_due_days', $setting->default_payment_due_days) }}"
                min="0"
                max="90"
            >
            <p class="text-gray-500 text-xs mt-1">Number of days before payment is due (0 means payment is due immediately).</p>
            @error('default_payment_due_days')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
    </div>

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Order Numbering</h3>

        <div class="mb-4">
            <label for="order_number_prefix" class="block text-gray-700 text-sm font-bold mb-2">
                Order Number Prefix
            </label>
            <input
                type="text"
                name="order_number_prefix"
                id="order_number_prefix"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value="{{ old('order_number_prefix', $setting->order_number_prefix) }}"
                placeholder="ORD-"
                maxlength="8"
            >
            <p class="text-gray-500 text-xs mt-1">Prefix for order numbers, e.g., "ORD-" will generate numbers like ORD-ABCDEF.</p>
            @error('order_number_prefix')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="order_number_format" class="block text-gray-700 text-sm font-bold mb-2">
                Order Number Format
            </label>
            <select
                name="order_number_format"
                id="order_number_format"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            >
                <option value="alphanumeric" {{ old('order_number_format', $setting->order_number_format) === 'alphanumeric' ? 'selected' : '' }}>Alphanumeric (e.g., ORD-ABC123)</option>
                <option value="numeric" {{ old('order_number_format', $setting->order_number_format) === 'numeric' ? 'selected' : '' }}>Numeric Only (e.g., ORD-123456)</option>
                <option value="sequential" {{ old('order_number_format', $setting->order_number_format) === 'sequential' ? 'selected' : '' }}>Sequential (e.g., ORD-000001)</option>
            </select>
            <p class="text-gray-500 text-xs mt-1">Choose the format for your order numbers.</p>
            @error('order_number_format')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="order_number_digits" class="block text-gray-700 text-sm font-bold mb-2">
                Order Number Digits
            </label>
            <input
                type="number"
                name="order_number_digits"
                id="order_number_digits"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value="{{ old('order_number_digits', $setting->order_number_digits ?? 6) }}"
                min="4"
                max="10"
                step="1"
            >
            <p class="text-gray-500 text-xs mt-1">Number of digits to use in the numeric part of sequential order numbers (e.g., 6 digits for ORD-000001).</p>
            @error('order_number_digits')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <div class="flex items-center">
                <input
                    type="checkbox"
                    name="include_year_in_order_number"
                    id="include_year_in_order_number"
                    class="mr-2"
                    value="1"
                    {{ old('include_year_in_order_number', $setting->include_year_in_order_number) ? 'checked' : '' }}
                >
                <label for="include_year_in_order_number" class="text-gray-700 text-sm font-bold">
                    Include Year in Order Number
                </label>
            </div>
            <p class="text-gray-500 text-xs mt-1">If checked, order numbers will include the current year (e.g., ORD-2024-ABCDEF).</p>
            @error('include_year_in_order_number')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
    </div>

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Currency Settings</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="mb-4">
                <label for="currency_symbol" class="block text-gray-700 text-sm font-bold mb-2">
                    Currency Symbol
                </label>
                <input
                    type="text"
                    name="currency_symbol"
                    id="currency_symbol"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    value="{{ old('currency_symbol', $setting->currency_symbol) }}"
                    placeholder="₦"
                    maxlength="3"
                >
                @error('currency_symbol')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-4">
                <label for="currency_code" class="block text-gray-700 text-sm font-bold mb-2">
                    Currency Code
                </label>
                <input
                    type="text"
                    name="currency_code"
                    id="currency_code"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    value="{{ old('currency_code', $setting->currency_code) }}"
                    placeholder="NGN"
                    maxlength="3"
                >
                @error('currency_code')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Time Settings</h3>

        <div class="mb-4">
            <label for="timezone" class="block text-gray-700 text-sm font-bold mb-2">
                Timezone
            </label>
            <select
                name="timezone"
                id="timezone"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            >
                @php
                    $timezones = [
                        'UTC' => 'UTC',
                        'Africa/Lagos' => 'West Africa Time (WAT)',
                        'Africa/Cairo' => 'Eastern European Time (EET)',
                        'Africa/Johannesburg' => 'South Africa Standard Time (SAST)',
                        'America/New_York' => 'Eastern Time (ET)',
                        'America/Chicago' => 'Central Time (CT)',
                        'America/Denver' => 'Mountain Time (MT)',
                        'America/Los_Angeles' => 'Pacific Time (PT)',
                        'Asia/Dubai' => 'Gulf Standard Time (GST)',
                        'Asia/Kolkata' => 'India Standard Time (IST)',
                        'Asia/Singapore' => 'Singapore Time (SGT)',
                        'Asia/Tokyo' => 'Japan Standard Time (JST)',
                        'Europe/London' => 'Greenwich Mean Time (GMT)',
                        'Europe/Paris' => 'Central European Time (CET)',
                        'Australia/Sydney' => 'Australian Eastern Time (AET)',
                    ];
                @endphp

                @foreach($timezones as $key => $value)
                    <option value="{{ $key }}" {{ old('timezone', $setting->timezone ?? 'UTC') == $key ? 'selected' : '' }}>
                        {{ $value }} ({{ $key }})
                    </option>
                @endforeach
            </select>
            <p class="text-gray-500 text-xs mt-1">All dates and times will be displayed in this timezone throughout the application.</p>
            @error('timezone')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
    </div>

    <div class="flex items-center justify-end pt-6">
        <button
            type="submit"
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded focus:outline-none focus:shadow-outline"
        >
            Save Business Settings
        </button>
    </div>
</form>
@endsection
