<?php

// Simple test to check announcement system
echo "Testing announcement system...\n";

// Check if we can connect to database
try {
    $pdo = new PDO('mysql:host=localhost;dbname=salesmanagement', 'root', '');
    echo "✓ Database connection successful\n";
    
    // Check if announcements table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'announcements'");
    if ($stmt->rowCount() > 0) {
        echo "✓ Announcements table exists\n";
        
        // Check current announcements
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM announcements");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Current announcements in database: " . $result['count'] . "\n";
        
        // Get all announcements
        $stmt = $pdo->query("SELECT id, title, target_audience, is_active, published_at, show_on_dashboard FROM announcements");
        $announcements = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($announcements) > 0) {
            echo "\nExisting announcements:\n";
            foreach ($announcements as $announcement) {
                echo "- ID: {$announcement['id']}, Title: {$announcement['title']}\n";
                echo "  Target: {$announcement['target_audience']}, Active: {$announcement['is_active']}\n";
                echo "  Published: {$announcement['published_at']}, Dashboard: {$announcement['show_on_dashboard']}\n\n";
            }
        }
        
        // Check super_admins table
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM super_admins");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Super admins in database: " . $result['count'] . "\n";
        
        if ($result['count'] == 0) {
            echo "Creating test super admin...\n";
            $stmt = $pdo->prepare("INSERT INTO super_admins (name, email, password, role, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())");
            $stmt->execute(['Test Super Admin', '<EMAIL>', password_hash('password', PASSWORD_DEFAULT), 'super_admin', 1]);
            $superAdminId = $pdo->lastInsertId();
            echo "✓ Test super admin created with ID: $superAdminId\n";
        } else {
            $stmt = $pdo->query("SELECT id FROM super_admins LIMIT 1");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $superAdminId = $result['id'];
            echo "Using existing super admin ID: $superAdminId\n";
        }
        
        // Create test announcement if none exist
        if (count($announcements) == 0) {
            echo "\nCreating test announcement...\n";
            $stmt = $pdo->prepare("
                INSERT INTO announcements 
                (title, content, type, priority, target_audience, is_active, is_dismissible, show_on_login, show_on_dashboard, send_email, published_at, created_by, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, NOW(), NOW())
            ");
            
            $stmt->execute([
                'Test Announcement for Organizations',
                'This is a test announcement to verify the system is working correctly for organization users.',
                'info',
                'normal',
                'organizations',
                1, // is_active
                1, // is_dismissible
                0, // show_on_login
                1, // show_on_dashboard
                0, // send_email
                $superAdminId
            ]);
            
            $announcementId = $pdo->lastInsertId();
            echo "✓ Test announcement created with ID: $announcementId\n";
            
            // Also create one for all users
            $stmt->execute([
                'Test Announcement for All Users',
                'This is a test announcement visible to all users.',
                'warning',
                'high',
                'all',
                1, // is_active
                1, // is_dismissible
                0, // show_on_login
                1, // show_on_dashboard
                0, // send_email
                $superAdminId
            ]);
            
            $announcementId2 = $pdo->lastInsertId();
            echo "✓ Test announcement for all users created with ID: $announcementId2\n";
        }
        
        // Test the query that would be used by the API
        echo "\nTesting API query for organizations...\n";
        $stmt = $pdo->prepare("
            SELECT * FROM announcements 
            WHERE is_active = 1 
            AND published_at IS NOT NULL 
            AND published_at <= NOW()
            AND (starts_at IS NULL OR starts_at <= NOW())
            AND (ends_at IS NULL OR ends_at >= NOW())
            AND (target_audience = 'all' OR target_audience = 'organizations')
            AND show_on_dashboard = 1
            ORDER BY priority DESC, created_at DESC
        ");
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Announcements found for organizations: " . count($results) . "\n";
        foreach ($results as $result) {
            echo "- {$result['title']} (ID: {$result['id']})\n";
        }
        
    } else {
        echo "✗ Announcements table does not exist\n";
    }
    
} catch (PDOException $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
}

echo "\nTest complete!\n";
