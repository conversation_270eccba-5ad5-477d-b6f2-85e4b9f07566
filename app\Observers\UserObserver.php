<?php

namespace App\Observers;

use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use App\Mail\WelcomeEmail;

class UserObserver
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        Log::info('New user created', [
            'user_id' => $user->id,
            'email' => $user->email,
            'roles' => $user->roles()->pluck('name')
        ]);

        // Determine user type for welcome email
        $userType = $this->determineUserType($user);

        // Send welcome email
        try {
            Mail::to($user->email)->send(new WelcomeEmail($user, $userType));
            Log::info('Welcome email sent', [
                'user_id' => $user->id,
                'email' => $user->email,
                'user_type' => $userType
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send welcome email', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Determine the user type for welcome email.
     */
    private function determineUserType(User $user): string
    {
        // Check if user has super admin role
        if ($user->hasRole('super_admin')) {
            return 'super_admin';
        }

        // Check if user is an affiliate
        if ($user->affiliate()->exists()) {
            return 'affiliate';
        }

        // Default to organization user
        return 'organization';
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        $changes = $user->getChanges();

        if (isset($changes['status'])) {
            if ($changes['status'] === 'inactive') {
                // Force logout from all devices when deactivated
                $user->tokens()->delete();
                $user->sessions()->delete();

                Log::warning('User deactivated', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'deactivated_by' => auth()->id() ?? 'system'
                ]);
            } else {
                Log::info('User reactivated', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'activated_by' => auth()->id() ?? 'system'
                ]);
            }
        }

        if (isset($changes['password'])) {
            // Force logout from other devices on password change
            $user->tokens()->where('name', '!=', 'current-device')->delete();
            $user->sessions()->where('id', '!=', session()->getId())->delete();

            Log::info('User password changed', [
                'user_id' => $user->id,
                'changed_by' => auth()->id() ?? 'system'
            ]);
        }

        // Clear role cache when user is updated
        $this->clearUserCache($user);
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        Log::warning('User deleted', [
            'user_id' => $user->id,
            'email' => $user->email,
            'deleted_by' => auth()->id() ?? 'system'
        ]);

        // Clean up associated data
        $user->tokens()->delete();
        $user->sessions()->delete();
        $this->clearUserCache($user);
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void
    {
        Log::info('User restored', [
            'user_id' => $user->id,
            'email' => $user->email,
            'restored_by' => auth()->id() ?? 'system'
        ]);
    }

    /**
     * Handle the User "force deleted" event.
     */
    public function forceDeleted(User $user): void
    {
        Log::warning('User force deleted', [
            'user_id' => $user->id,
            'email' => $user->email,
            'deleted_by' => auth()->id() ?? 'system'
        ]);

        // Clean up associated data
        $user->tokens()->delete();
        $user->sessions()->delete();
        $this->clearUserCache($user);
    }

    /**
     * Clear all cache entries related to the user
     */
    protected function clearUserCache(User $user): void
    {
        Cache::forget("user_{$user->id}_permissions");

        // Clear role-related cache
        $user->roles->each(function ($role) use ($user) {
            Cache::forget("user_{$user->id}_has_role_{$role->name}");
        });

        // Clear role combination caches
        Cache::forget("user_{$user->id}_has_any_roles_*");
        Cache::forget("user_{$user->id}_has_all_roles_*");
    }
}
