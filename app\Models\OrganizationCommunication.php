<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class OrganizationCommunication extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'message',
        'type',
        'priority',
        'organization_id',
        'sender_id',
        'scheduled_at',
        'sent_at',
        'status',
        'target_audience',
        'metadata',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'target_audience' => 'array',
        'metadata' => 'array',
    ];

    // Communication types
    const TYPE_ANNOUNCEMENT = 'announcement';
    const TYPE_MAINTENANCE = 'maintenance';
    const TYPE_FEATURE_UPDATE = 'feature_update';
    const TYPE_BILLING_NOTICE = 'billing_notice';
    const TYPE_SECURITY_ALERT = 'security_alert';
    const TYPE_GENERAL = 'general';

    // Priority levels
    const PRIORITY_LOW = 'low';
    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    // Status
    const STATUS_DRAFT = 'draft';
    const STATUS_SCHEDULED = 'scheduled';
    const STATUS_SENT = 'sent';
    const STATUS_CANCELLED = 'cancelled';

    // Target audiences
    const AUDIENCE_ALL_USERS = 'all_users';
    const AUDIENCE_ADMINS_ONLY = 'admins_only';
    const AUDIENCE_SPECIFIC_ROLES = 'specific_roles';

    /**
     * Get the organization this communication belongs to
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the super admin who sent this communication
     */
    public function sender(): BelongsTo
    {
        return $this->belongsTo(SuperAdmin::class, 'sender_id');
    }

    /**
     * Get read receipts for this communication
     */
    public function readReceipts(): HasMany
    {
        return $this->hasMany(CommunicationReadReceipt::class);
    }

    /**
     * Scope for filtering by type
     */
    public function scopeType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for filtering by priority
     */
    public function scopePriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope for filtering by status
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for sent communications
     */
    public function scopeSent($query)
    {
        return $query->where('status', self::STATUS_SENT);
    }

    /**
     * Scope for scheduled communications
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', self::STATUS_SCHEDULED)
                    ->where('scheduled_at', '>', now());
    }

    /**
     * Scope for overdue scheduled communications
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', self::STATUS_SCHEDULED)
                    ->where('scheduled_at', '<=', now());
    }

    /**
     * Get type badge HTML
     */
    public function getTypeBadgeAttribute()
    {
        $colors = [
            self::TYPE_ANNOUNCEMENT => 'primary',
            self::TYPE_MAINTENANCE => 'warning',
            self::TYPE_FEATURE_UPDATE => 'success',
            self::TYPE_BILLING_NOTICE => 'info',
            self::TYPE_SECURITY_ALERT => 'danger',
            self::TYPE_GENERAL => 'secondary',
        ];

        $color = $colors[$this->type] ?? 'secondary';
        return "<span class='badge bg-{$color}'>" . ucfirst(str_replace('_', ' ', $this->type)) . "</span>";
    }

    /**
     * Get priority badge HTML
     */
    public function getPriorityBadgeAttribute()
    {
        $colors = [
            self::PRIORITY_LOW => 'secondary',
            self::PRIORITY_NORMAL => 'primary',
            self::PRIORITY_HIGH => 'warning',
            self::PRIORITY_URGENT => 'danger',
        ];

        $color = $colors[$this->priority] ?? 'secondary';
        return "<span class='badge bg-{$color}'>" . ucfirst($this->priority) . "</span>";
    }

    /**
     * Get status badge HTML
     */
    public function getStatusBadgeAttribute()
    {
        $colors = [
            self::STATUS_DRAFT => 'secondary',
            self::STATUS_SCHEDULED => 'warning',
            self::STATUS_SENT => 'success',
            self::STATUS_CANCELLED => 'danger',
        ];

        $color = $colors[$this->status] ?? 'secondary';
        return "<span class='badge bg-{$color}'>" . ucfirst($this->status) . "</span>";
    }

    /**
     * Get read percentage
     */
    public function getReadPercentageAttribute()
    {
        $totalUsers = $this->organization->users()->count();
        if ($totalUsers === 0) {
            return 0;
        }

        $readCount = $this->readReceipts()->count();
        return round(($readCount / $totalUsers) * 100, 1);
    }

    /**
     * Check if communication is overdue
     */
    public function getIsOverdueAttribute()
    {
        return $this->status === self::STATUS_SCHEDULED && 
               $this->scheduled_at && 
               $this->scheduled_at->isPast();
    }

    /**
     * Get all type options
     */
    public static function getTypeOptions()
    {
        return [
            self::TYPE_ANNOUNCEMENT => 'Announcement',
            self::TYPE_MAINTENANCE => 'Maintenance Notice',
            self::TYPE_FEATURE_UPDATE => 'Feature Update',
            self::TYPE_BILLING_NOTICE => 'Billing Notice',
            self::TYPE_SECURITY_ALERT => 'Security Alert',
            self::TYPE_GENERAL => 'General Communication',
        ];
    }

    /**
     * Get all priority options
     */
    public static function getPriorityOptions()
    {
        return [
            self::PRIORITY_LOW => 'Low',
            self::PRIORITY_NORMAL => 'Normal',
            self::PRIORITY_HIGH => 'High',
            self::PRIORITY_URGENT => 'Urgent',
        ];
    }

    /**
     * Get all audience options
     */
    public static function getAudienceOptions()
    {
        return [
            self::AUDIENCE_ALL_USERS => 'All Users',
            self::AUDIENCE_ADMINS_ONLY => 'Admins Only',
            self::AUDIENCE_SPECIFIC_ROLES => 'Specific Roles',
        ];
    }

    /**
     * Send communication immediately
     */
    public function send()
    {
        $this->update([
            'status' => self::STATUS_SENT,
            'sent_at' => now(),
        ]);

        // Create notifications for target users
        $this->createNotifications();
    }

    /**
     * Schedule communication for later
     */
    public function schedule($dateTime)
    {
        $this->update([
            'status' => self::STATUS_SCHEDULED,
            'scheduled_at' => $dateTime,
        ]);
    }

    /**
     * Cancel scheduled communication
     */
    public function cancel()
    {
        $this->update(['status' => self::STATUS_CANCELLED]);
    }

    /**
     * Create notifications for target users
     */
    private function createNotifications()
    {
        $users = $this->getTargetUsers();

        foreach ($users as $user) {
            // Create in-app notification
            $user->notifications()->create([
                'type' => 'organization_communication',
                'title' => $this->title,
                'message' => $this->message,
                'data' => [
                    'communication_id' => $this->id,
                    'type' => $this->type,
                    'priority' => $this->priority,
                ],
            ]);
        }
    }

    /**
     * Get target users based on audience settings
     */
    private function getTargetUsers()
    {
        $query = $this->organization->users();

        switch ($this->target_audience['type'] ?? self::AUDIENCE_ALL_USERS) {
            case self::AUDIENCE_ADMINS_ONLY:
                $query->whereHas('roles', function($q) {
                    $q->where('name', 'admin');
                });
                break;
            
            case self::AUDIENCE_SPECIFIC_ROLES:
                if (!empty($this->target_audience['roles'])) {
                    $query->whereHas('roles', function($q) {
                        $q->whereIn('name', $this->target_audience['roles']);
                    });
                }
                break;
            
            case self::AUDIENCE_ALL_USERS:
            default:
                // No additional filtering needed
                break;
        }

        return $query->where('status', 'active')->get();
    }
}
