<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            // Add new billing period field if it doesn't exist
            if (!Schema::hasColumn('subscriptions', 'billing_period_months')) {
                $table->integer('billing_period_months')->default(1)->after('status')
                      ->comment('Billing period in months (1, 12, 24, 48)');
            }
        });

        // Migrate existing data only if billing_period column exists
        if (Schema::hasColumn('subscriptions', 'billing_period')) {
            DB::table('subscriptions')
                ->where('billing_period', 'annual')
                ->update(['billing_period_months' => 12]);

            DB::table('subscriptions')
                ->where('billing_period', 'monthly')
                ->orWhereNull('billing_period')
                ->update(['billing_period_months' => 1]);
        }

        // Remove old billing_period column
        Schema::table('subscriptions', function (Blueprint $table) {
            if (Schema::hasColumn('subscriptions', 'billing_period')) {
                $table->dropColumn('billing_period');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            // Restore old billing_period column
            $table->string('billing_period')->default('monthly')->after('status');
        });

        // Migrate data back
        DB::table('subscriptions')
            ->where('billing_period_months', 12)
            ->update(['billing_period' => 'annual']);

        DB::table('subscriptions')
            ->where('billing_period_months', 1)
            ->update(['billing_period' => 'monthly']);

        // Remove new column
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn('billing_period_months');
        });
    }
};
