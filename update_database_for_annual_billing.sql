-- SQL script to update database for annual billing support
-- Run this in phpMyAdmin or MySQL command line

-- 1. Add annual billing columns to plans table if they don't exist
ALTER TABLE plans 
ADD COLUMN IF NOT EXISTS annual_price DECIMAL(10,2) NULL,
ADD COLUMN IF NOT EXISTS billing_period VARCHAR(255) DEFAULT 'monthly',
ADD COLUMN IF NOT EXISTS annual_discount_percentage INT DEFAULT 0;

-- 2. Add billing_period column to subscriptions table if it doesn't exist
ALTER TABLE subscriptions 
ADD COLUMN IF NOT EXISTS billing_period VARCHAR(255) DEFAULT 'monthly';

-- 3. Update existing subscriptions to have monthly billing period
UPDATE subscriptions 
SET billing_period = 'monthly' 
WHERE billing_period IS NULL OR billing_period = '';

-- 4. Update existing plans to support annual billing
UPDATE plans 
SET 
    billing_period = 'both',
    annual_discount_percentage = 15,
    annual_price = ROUND(price * 12 * 0.85, 2)  -- 15% discount
WHERE billing_period IS NULL OR billing_period = '';

-- 5. Verify the updates
SELECT 
    name,
    price as monthly_price,
    annual_price,
    billing_period,
    annual_discount_percentage
FROM plans;

SELECT 
    id,
    billing_period,
    start_date,
    end_date
FROM subscriptions
LIMIT 5;
