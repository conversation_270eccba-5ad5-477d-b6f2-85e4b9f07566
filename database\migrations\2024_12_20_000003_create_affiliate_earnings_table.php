<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('affiliate_earnings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('affiliate_id')->constrained('affiliates')->onDelete('cascade');
            $table->foreignId('referral_id')->nullable()->constrained('affiliate_referrals')->onDelete('set null');
            $table->foreignId('organization_id')->constrained('organizations')->onDelete('cascade');
            $table->foreignId('subscription_payment_id')->nullable()->constrained('subscription_payments')->onDelete('set null');
            $table->decimal('amount', 10, 2);
            $table->enum('type', ['commission', 'bonus', 'adjustment', 'penalty'])->default('commission');
            $table->text('description')->nullable();
            $table->enum('status', ['pending', 'approved', 'paid', 'rejected'])->default('pending');
            $table->timestamp('earned_at');
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('super_admins')->onDelete('set null');
            $table->timestamp('paid_at')->nullable();
            $table->string('payment_reference')->nullable();
            $table->text('notes')->nullable(); // Admin notes
            $table->timestamps();

            $table->index(['affiliate_id']);
            $table->index(['organization_id']);
            $table->index(['status']);
            $table->index(['type']);
            $table->index(['earned_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('affiliate_earnings');
    }
};
