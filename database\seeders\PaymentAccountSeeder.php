<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PaymentAccount;

class PaymentAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $accounts = [
            [
                'account_name' => 'Sales Management System Ltd',
                'bank_name' => 'First Bank Nigeria',
                'account_number' => '**********',
                'account_type' => 'current',
                'routing_number' => null,
                'swift_code' => 'FBNNNGLA',
                'additional_instructions' => 'Please include your organization name and subscription reference in the payment description.',
                'is_active' => true,
                'is_primary' => true,
            ],
            [
                'account_name' => 'Sales Management System Ltd',
                'bank_name' => 'Guaranty Trust Bank',
                'account_number' => '**********',
                'account_type' => 'current',
                'routing_number' => null,
                'swift_code' => 'GTBINGLA',
                'additional_instructions' => 'Alternative payment account. Please include your organization name and subscription reference.',
                'is_active' => true,
                'is_primary' => false,
            ],
            [
                'account_name' => 'Sales Management System Ltd',
                'bank_name' => 'Access Bank',
                'account_number' => '**********',
                'account_type' => 'savings',
                'routing_number' => null,
                'swift_code' => 'ABNGNGLA',
                'additional_instructions' => 'For international transfers, please use SWIFT code. Include organization name in transfer details.',
                'is_active' => true,
                'is_primary' => false,
            ],
        ];

        foreach ($accounts as $accountData) {
            PaymentAccount::updateOrCreate(
                [
                    'account_number' => $accountData['account_number'],
                    'bank_name' => $accountData['bank_name']
                ],
                $accountData
            );
        }
    }
}
