@extends('affiliate.layouts.app')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<!-- Welcome Message -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-primary border-left-primary" role="alert">
            <h4 class="alert-heading">
                <i class="fas fa-hand-wave me-2"></i>
                Welcome back, {{ $affiliate->user->name }}!
            </h4>
            <p class="mb-0">Track your referrals and earnings here. Your affiliate code is <strong>{{ $affiliate->affiliate_code }}</strong></p>
        </div>
    </div>
</div>

<!-- Rejected Withdrawals Notification -->
@if(isset($stats['rejected_withdrawals']) && $stats['rejected_withdrawals']->count() > 0)
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-danger border-left-danger alert-dismissible show" role="alert" id="rejectedWithdrawalsAlert">
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close notification"></button>
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>
                {{ $stats['rejected_withdrawals']->count() }} Withdrawal Request{{ $stats['rejected_withdrawals']->count() > 1 ? 's' : '' }} Rejected
            </h5>
            <p class="mb-3">
                <strong>Action Required:</strong> Please review the rejection reason{{ $stats['rejected_withdrawals']->count() > 1 ? 's' : '' }} below and address any issues before submitting new withdrawal requests.
            </p>
            <div class="alert alert-light border-0 mb-3 py-2">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    This notification will remain visible until you click the close button (×) above. It will reappear on each page load until dismissed.
                </small>
            </div>

            @foreach($stats['rejected_withdrawals'] as $rejection)
            <div class="mb-3 p-3 bg-light rounded border">
                <div class="row align-items-start">
                    <div class="col-md-8">
                        <div class="mb-2">
                            <strong class="text-dark">{{ format_price($rejection->amount) }}</strong>
                            <small class="text-muted ms-2">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $rejection->processed_at->format('M j, Y g:i A') }}
                            </small>
                        </div>
                        <div class="rejection-reason">
                            <strong class="text-danger d-block mb-1">Rejection Reason:</strong>
                            <p class="text-danger mb-0" style="line-height: 1.4;">{{ $rejection->rejection_reason }}</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ route('affiliate.withdrawals.show', $rejection) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye me-1"></i>View Details
                        </a>
                    </div>
                </div>
            </div>
            @endforeach

            <div class="mt-3 d-flex flex-wrap gap-2">
                <a href="{{ route('affiliate.withdrawals') }}" class="btn btn-sm btn-danger">
                    <i class="fas fa-list me-2"></i>View All Withdrawals
                </a>
                <a href="{{ route('affiliate.withdrawals.create') }}" class="btn btn-sm btn-outline-success">
                    <i class="fas fa-plus me-2"></i>Request New Withdrawal
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Inline script to ensure notification visibility -->
<script>
(function() {
    // Immediate check to ensure the alert is visible
    const alert = document.getElementById('rejectedWithdrawalsAlert');
    if (alert) {
        // Force visibility
        alert.style.display = 'block';
        alert.style.opacity = '1';
        alert.style.visibility = 'visible';

        // Check localStorage to see if this should be hidden
        try {
            const dismissedAlerts = JSON.parse(localStorage.getItem('dismissedRejectionAlerts') || '[]');
            const currentRejectionIds = @json($stats['rejected_withdrawals']->pluck('id')->toArray() ?? []);

            // Only hide if ALL current rejections have been dismissed
            const allDismissed = currentRejectionIds.length > 0 && currentRejectionIds.every(id => dismissedAlerts.includes(id));

            if (allDismissed) {
                alert.style.display = 'none';
            }
        } catch (e) {
            // If localStorage fails, show the alert anyway
            console.log('LocalStorage check failed, showing alert');
        }
    }
})();
</script>
@endif

<!-- Stats Cards -->
<div class="row mb-4">
    <!-- Total Referrals -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Referrals
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_referrals'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-success">
                        <i class="fas fa-check-circle"></i>
                        {{ $stats['converted_referrals'] }} converted
                    </small>
                    <span class="text-muted"> • </span>
                    <small class="text-warning">
                        <i class="fas fa-clock"></i>
                        {{ $stats['pending_referrals'] }} pending
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Conversion Rate -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Conversion Rate
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['conversion_rate'], 1) }}%</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-percentage fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Balance -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Available Balance
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ format_price($stats['available_balance']) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-wallet fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-clock"></i>
                        {{ format_price($stats['pending_balance']) }} pending
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Earnings -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Earnings
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ format_price($stats['total_earnings']) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-money-bill-wave"></i>
                        {{ format_price($stats['withdrawn_amount']) }} withdrawn
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Click Performance Stats -->
<div class="row mb-4">
    <!-- Total Clicks -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Clicks
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total_clicks'] ?? 0) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-mouse-pointer fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-info">
                        <i class="fas fa-star"></i>
                        {{ number_format($stats['unique_clicks'] ?? 0) }} unique
                    </small>
                    <span class="text-muted"> • </span>
                    <small class="text-success">
                        <i class="fas fa-calendar-day"></i>
                        {{ number_format($stats['today_clicks'] ?? 0) }} today
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Click-to-Registration Rate -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Click Conversion
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['click_to_registration_rate'] ?? 0, 1) }}%</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Clicks that become registrations
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- This Month Clicks -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-secondary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                            This Month
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['this_month_clicks'] ?? 0) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-secondary">
                        <i class="fas fa-star"></i>
                        {{ number_format($stats['this_month_unique_clicks'] ?? 0) }} unique
                    </small>
                    @if(isset($stats['last_month_clicks']) && $stats['last_month_clicks'] > 0)
                        @php
                            $clickGrowth = $stats['this_month_clicks'] > 0 && $stats['last_month_clicks'] > 0
                                ? (($stats['this_month_clicks'] - $stats['last_month_clicks']) / $stats['last_month_clicks']) * 100
                                : 0;
                        @endphp
                        <span class="text-muted"> • </span>
                        <small class="{{ $clickGrowth >= 0 ? 'text-success' : 'text-danger' }}">
                            <i class="fas fa-{{ $clickGrowth >= 0 ? 'arrow-up' : 'arrow-down' }}"></i>
                            {{ number_format(abs($clickGrowth), 1) }}% vs last month
                        </small>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Link Performance -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-dark shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">
                            Link Performance
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            @if(($stats['unique_clicks'] ?? 0) > 0)
                                {{ number_format((($stats['total_referrals'] ?? 0) / ($stats['unique_clicks'] ?? 1)) * 100, 1) }}%
                            @else
                                0%
                            @endif
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-link fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Overall effectiveness
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
                <div class="d-flex flex-wrap gap-2">
                    <a href="{{ route('affiliate.referral-tools') }}" class="btn btn-primary">
                        <i class="fas fa-link me-2"></i>
                        Get Referral Links
                    </a>

                    @if($stats['available_balance'] >= 50)
                    <a href="{{ route('affiliate.withdrawals.create') }}" class="btn btn-success">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        Request Withdrawal
                    </a>
                    @endif

                    <a href="{{ route('affiliate.referrals') }}" class="btn btn-secondary">
                        <i class="fas fa-users me-2"></i>
                        View All Referrals
                    </a>

                    <a href="{{ route('affiliate.earnings') }}" class="btn btn-info">
                        <i class="fas fa-chart-bar me-2"></i>
                        View Earnings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <!-- Recent Referrals -->
        <div class="card shadow h-100">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-users me-2"></i>Recent Referrals
                </h6>
            </div>
            <div class="card-body">
                @if($recentReferrals->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($recentReferrals as $referral)
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <div>
                                <h6 class="mb-1">{{ $referral->organization->name }}</h6>
                                <small class="text-muted">{{ $referral->registration_date->format('M j, Y') }}</small>
                            </div>
                            <div class="text-end">
                                @php
                                    $badgeClass = match($referral->status) {
                                        'converted' => 'bg-success',
                                        'pending' => 'bg-warning',
                                        default => 'bg-secondary'
                                    };
                                @endphp
                                <span class="badge {{ $badgeClass }}">{{ ucfirst($referral->status) }}</span>
                                @if($referral->commission_earned > 0)
                                    <br><small class="text-muted">{{ format_price($referral->commission_earned) }}</small>
                                @endif
                            </div>
                        </div>
                        @endforeach
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('affiliate.referrals') }}" class="btn btn-sm btn-outline-primary">
                            View all referrals <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-users text-muted mb-3" style="font-size: 2rem;"></i>
                        <p class="text-muted">No referrals yet. Start sharing your referral link!</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <!-- Recent Earnings -->
        <div class="card shadow h-100">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-line me-2"></i>Recent Earnings
                </h6>
            </div>
            <div class="card-body">
                @if($recentEarnings->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($recentEarnings as $earning)
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <div>
                                <h6 class="mb-1">{{ ucfirst($earning->type) }}</h6>
                                <small class="text-muted">{{ $earning->earned_at->format('M j, Y') }}</small>
                                @if($earning->organization)
                                    <br><small class="text-muted">{{ $earning->organization->name }}</small>
                                @endif
                            </div>
                            <div class="text-end">
                                <h6 class="mb-1">{{ format_price($earning->amount) }}</h6>
                                @php
                                    $badgeClass = match($earning->status) {
                                        'approved' => 'bg-success',
                                        'pending' => 'bg-warning',
                                        'rejected' => 'bg-danger',
                                        default => 'bg-secondary'
                                    };
                                @endphp
                                <span class="badge {{ $badgeClass }}">{{ ucfirst($earning->status) }}</span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('affiliate.earnings') }}" class="btn btn-sm btn-outline-primary">
                            View all earnings <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line text-muted mb-3" style="font-size: 2rem;"></i>
                        <p class="text-muted">No earnings yet. Start referring customers!</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Affiliate Info -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <div class="d-flex align-items-start">
                <i class="fas fa-info-circle me-3 mt-1"></i>
                <div>
                    <h5 class="alert-heading">Your Affiliate Code: {{ $affiliate->affiliate_code }}</h5>
                    <p class="mb-1">Commission Rate: {{ number_format($affiliate->commission_rate, 1) }}%</p>
                    <p class="mb-0">Member since: {{ $affiliate->joined_at->format('F j, Y') }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.rejection-reason {
    background-color: #f8f9fa;
    border-left: 4px solid #dc3545;
    padding: 10px 15px;
    border-radius: 0 4px 4px 0;
    margin-top: 8px;
}

.rejection-reason p {
    font-size: 14px;
    margin-bottom: 0;
    word-wrap: break-word;
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    z-index: 2;
    padding: 0.375rem 0.375rem;
}

#rejectedWithdrawalsAlert {
    position: relative;
    padding-right: 3rem;
}

.border-left-danger {
    border-left: 0.25rem solid #dc3545 !important;
}

@media (max-width: 768px) {
    .rejection-reason {
        margin-top: 10px;
    }

    #rejectedWithdrawalsAlert .row {
        margin: 0;
    }

    #rejectedWithdrawalsAlert .col-md-4 {
        margin-top: 10px;
        text-align: left !important;
    }
}

/* Gentle pulse animation to draw attention */
@keyframes gentle-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0.1);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

/* Ensure the alert doesn't auto-hide and stays visible */
#rejectedWithdrawalsAlert {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    transition: opacity 0.3s ease-in-out;
}

#rejectedWithdrawalsAlert.show {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* Override Bootstrap's fade class behavior for this specific alert */
#rejectedWithdrawalsAlert.fade {
    opacity: 1 !important;
}

/* Prevent any Bootstrap auto-hide animations */
#rejectedWithdrawalsAlert.fade.show {
    opacity: 1 !important;
}

/* Manual hide state (only when user dismisses) */
#rejectedWithdrawalsAlert.manual-hide {
    opacity: 0 !important;
    transition: opacity 0.3s ease-out;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle alert dismissal with local storage
    const rejectedAlert = document.getElementById('rejectedWithdrawalsAlert');

    if (rejectedAlert) {
        // Completely disable Bootstrap's alert auto-hide functionality
        // Remove any Bootstrap event listeners that might cause auto-hiding
        rejectedAlert.removeAttribute('data-bs-dismiss');

        // Override Bootstrap's Alert class methods to prevent auto-hiding
        if (window.bootstrap && window.bootstrap.Alert) {
            const alertInstance = window.bootstrap.Alert.getOrCreateInstance(rejectedAlert);
            if (alertInstance) {
                // Override the close method to only work when manually triggered
                const originalClose = alertInstance.close;
                alertInstance.close = function() {
                    // Only close if explicitly called by our dismiss button
                    if (this._isManualClose) {
                        originalClose.call(this);
                    }
                };
            }
        }

        // Get current rejection IDs and dismissed alerts
        const currentRejectionIds = @json($stats['rejected_withdrawals']->pluck('id')->toArray() ?? []);
        const dismissedAlerts = JSON.parse(localStorage.getItem('dismissedRejectionAlerts') || '[]');

        // Only hide if ALL current rejections have been manually dismissed
        const allDismissed = currentRejectionIds.length > 0 && currentRejectionIds.every(id => dismissedAlerts.includes(id));

        if (allDismissed) {
            rejectedAlert.style.display = 'none';
        } else {
            // Ensure the alert is visible and stays visible
            rejectedAlert.style.display = 'block';
            rejectedAlert.style.opacity = '1';
            rejectedAlert.style.visibility = 'visible';

            // Add a subtle animation to draw attention when the page loads
            setTimeout(function() {
                if (rejectedAlert.style.display !== 'none') {
                    rejectedAlert.style.animation = 'gentle-pulse 2s ease-in-out';
                }
            }, 500);
        }

        // Handle manual dismiss button click
        const dismissButton = rejectedAlert.querySelector('.btn-close');
        if (dismissButton) {
            dismissButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Store dismissed rejection IDs in localStorage
                const existingDismissed = JSON.parse(localStorage.getItem('dismissedRejectionAlerts') || '[]');
                const newDismissed = [...new Set([...existingDismissed, ...currentRejectionIds])];
                localStorage.setItem('dismissedRejectionAlerts', JSON.stringify(newDismissed));

                // Clean up old entries (keep only last 50)
                if (newDismissed.length > 50) {
                    const cleaned = newDismissed.slice(-50);
                    localStorage.setItem('dismissedRejectionAlerts', JSON.stringify(cleaned));
                }

                // Manually hide the alert with smooth transition
                rejectedAlert.style.transition = 'opacity 0.3s ease-out';
                rejectedAlert.style.opacity = '0';
                setTimeout(function() {
                    rejectedAlert.style.display = 'none';
                }, 300);
            });
        }

        // Prevent any automatic hiding by overriding common auto-hide triggers
        rejectedAlert.addEventListener('show.bs.alert', function(e) {
            e.preventDefault();
        });

        rejectedAlert.addEventListener('hide.bs.alert', function(e) {
            // Only allow hiding if it's a manual dismiss
            if (!e.target.classList.contains('manual-dismiss')) {
                e.preventDefault();
                e.stopPropagation();
            }
        });
    }
});
</script>
@endpush
