<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

class CreateDefaultAdminSeeder extends Seeder
{
    public function run(): void
    {
        // Create default admin user if it doesn't exist
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Admin',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );

        // Ensure Organization Owner role exists
        $adminRole = Role::firstOrCreate(['name' => 'Organization Owner']);

        // Assign role if not already assigned
        if (!$user->hasRole('Organization Owner')) {
            $user->roles()->attach($adminRole);
        }
    }
}