@extends('layouts.app')

@section('title', $category->name . ' - Knowledge Base')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ route('user.support.index') }}">Support Center</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ route('user.support.knowledge-base') }}">Knowledge Base</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $category->name }}</li>
                </ol>
            </nav>

            <!-- Category Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            @if($category->icon)
                                <i class="{{ $category->icon }} fa-3x mb-3"
                                   style="color: {{ $category->color ?? '#007bff' }}"></i>
                            @else
                                <i class="fas fa-folder fa-3x mb-3 text-primary"></i>
                            @endif
                            <h1 class="h2 mb-2">{{ $category->name }}</h1>
                            @if($category->description)
                                <p class="lead text-muted">{{ $category->description }}</p>
                            @endif
                            <div class="d-flex justify-content-center align-items-center">
                                <span class="badge bg-primary me-3">
                                    {{ $articles->total() }} {{ Str::plural('article', $articles->total()) }}
                                </span>
                                <a href="{{ route('user.support.knowledge-base') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Knowledge Base
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Bar -->
            <div class="row mb-4">
                <div class="col-lg-8 mx-auto">
                    <form method="GET" action="{{ route('user.support.search') }}">
                        <div class="input-group">
                            <input type="text" name="q" class="form-control"
                                   placeholder="Search articles in {{ $category->name }}..."
                                   value="{{ request('q') }}">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Articles Grid -->
            @if($articles->count() > 0)
                <div class="row">
                    @foreach($articles as $article)
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title">
                                    <a href="{{ route('user.support.article', $article) }}" class="text-decoration-none">
                                        {{ $article->title }}
                                    </a>
                                </h5>
                                <p class="card-text text-muted flex-grow-1">
                                    {{ $article->truncated_excerpt }}
                                </p>
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ $article->reading_time }}m read
                                        </small>
                                        <div>
                                            <span class="badge bg-primary">{{ $article->view_count }} views</span>
                                            @if($article->helpfulness_percentage)
                                                <span class="badge bg-success">{{ $article->helpfulness_percentage }}% helpful</span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <a href="{{ route('user.support.article', $article->id) }}" class="btn btn-outline-primary btn-sm w-100">
                                            <i class="fas fa-book-open me-2"></i>Read Article
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                @if($articles->hasPages())
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-center">
                            {{ $articles->links() }}
                        </div>
                    </div>
                </div>
                @endif
            @else
                <!-- No Articles Found -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center py-5">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h4 class="text-muted">No articles found</h4>
                                <p class="text-muted">There are no articles in this category yet.</p>
                                <div class="mt-4">
                                    <a href="{{ route('user.support.knowledge-base') }}" class="btn btn-primary me-2">
                                        <i class="fas fa-book me-2"></i>Browse All Categories
                                    </a>
                                    <a href="{{ route('user.support.create') }}" class="btn btn-outline-primary">
                                        <i class="fas fa-envelope me-2"></i>Contact Support
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Help Section -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5 class="card-title">
                                <i class="fas fa-question-circle me-2"></i>
                                Can't find what you're looking for?
                            </h5>
                            <p class="card-text">Try searching in other categories or contact our support team directly.</p>
                            <div class="d-flex justify-content-center gap-3 flex-wrap">
                                <a href="{{ route('user.support.knowledge-base') }}" class="btn btn-outline-info">
                                    <i class="fas fa-book me-2"></i>
                                    Browse All Categories
                                </a>
                                <a href="{{ route('user.support.search') }}" class="btn btn-outline-success">
                                    <i class="fas fa-search me-2"></i>
                                    Search All Articles
                                </a>
                                <a href="{{ route('user.support.create') }}" class="btn btn-primary">
                                    <i class="fas fa-envelope me-2"></i>
                                    Contact Support
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.card-title a {
    color: inherit;
}

.card-title a:hover {
    color: #007bff;
}

.badge {
    font-size: 0.75rem;
}
</style>
@endsection
