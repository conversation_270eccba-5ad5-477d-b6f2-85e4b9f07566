<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Expenditure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;

class FinancialController extends Controller
{
    public function overview(Request $request)
    {
        $period = $request->get('period', 'daily'); // daily, monthly, yearly
        $date = $request->get('date', now()->format('Y-m-d'));
        $selectedDate = Carbon::parse($date);

        // Base query with organization filter
        $baseQuery = Order::where('organization_id', Auth::user()->organization_id);

        // Filter by branch if user is assigned to a branch
        if (Auth::user()->branch_id) {
            $baseQuery->where('branch_id', Auth::user()->branch_id);
        }

        // Check if expenditures have organization and branch columns
        if ($request->has('include_expenditures') && $request->include_expenditures) {
            $expenditureQuery = Expenditure::query()
                ->where('status', 'Approved');

            // Check if organization_id column exists
            if (Schema::hasColumn('expenditures', 'organization_id')) {
                $expenditureQuery->where('organization_id', Auth::user()->organization_id);
            }

            // Filter by branch if user is assigned to a branch and column exists
            if (Auth::user()->branch_id && Schema::hasColumn('expenditures', 'branch_id')) {
                $expenditureQuery->where('branch_id', Auth::user()->branch_id);
            }

            // Date filtering logic based on period
            // ... (rest of the expenditure query)
        }

        switch ($period) {
            case 'monthly':
                $revenue = (clone $baseQuery)
                    ->whereYear('created_at', $selectedDate->year)
                    ->whereMonth('created_at', $selectedDate->month)
                    ->sum('total_amount');

                $summaryData = (clone $baseQuery)
                    ->selectRaw('
                        DATE(created_at) as date,
                        SUM(total_amount) as revenue,
                        COUNT(*) as orders_count
                    ')
                    ->whereYear('created_at', $selectedDate->year)
                    ->whereMonth('created_at', $selectedDate->month)
                    ->groupBy('date')
                    ->orderBy('date')
                    ->get();
                break;

            case 'yearly':
                $revenue = (clone $baseQuery)
                    ->whereYear('created_at', $selectedDate->year)
                    ->sum('total_amount');

                $summaryData = (clone $baseQuery)
                    ->selectRaw('
                        MONTH(created_at) as month,
                        SUM(total_amount) as revenue,
                        COUNT(*) as orders_count
                    ')
                    ->whereYear('created_at', $selectedDate->year)
                    ->groupBy('month')
                    ->orderBy('month')
                    ->get();
                break;

            default: // daily
                $revenue = (clone $baseQuery)
                    ->whereDate('created_at', $selectedDate)
                    ->sum('total_amount');

                $summaryData = (clone $baseQuery)
                    ->selectRaw('
                        HOUR(created_at) as hour,
                        SUM(total_amount) as revenue,
                        COUNT(*) as orders_count
                    ')
                    ->whereDate('created_at', $selectedDate)
                    ->groupBy('hour')
                    ->orderBy('hour')
                    ->get();
                break;
        }

        // Get payment statistics
        $paymentStats = (clone $baseQuery)
            ->selectRaw('
                SUM(total_amount) as total_revenue,
                SUM(amount_paid) as total_paid,
                SUM(pending_payment) as total_pending,
                COUNT(*) as total_orders
            ')
            ->when($period === 'monthly', function($query) use ($selectedDate) {
                return $query->whereYear('created_at', $selectedDate->year)
                           ->whereMonth('created_at', $selectedDate->month);
            })
            ->when($period === 'yearly', function($query) use ($selectedDate) {
                return $query->whereYear('created_at', $selectedDate->year);
            })
            ->when($period === 'daily', function($query) use ($selectedDate) {
                return $query->whereDate('created_at', $selectedDate);
            })
            ->first();

        return view('financial.overview', compact(
            'period',
            'date',
            'summaryData',
            'paymentStats'
        ));
    }
}
