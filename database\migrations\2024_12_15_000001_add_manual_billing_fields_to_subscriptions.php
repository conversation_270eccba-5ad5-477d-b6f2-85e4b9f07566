<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only run if subscriptions table exists
        if (Schema::hasTable('subscriptions')) {
            Schema::table('subscriptions', function (Blueprint $table) {
                // Add manual billing fields (only if they don't exist)
                if (!Schema::hasColumn('subscriptions', 'payment_reference')) {
                    $table->string('payment_reference')->nullable()->after('payment_id'); // Bank transfer reference, receipt number
                }
                if (!Schema::hasColumn('subscriptions', 'payment_status')) {
                    $table->string('payment_status')->default('pending')->after('payment_reference'); // pending, paid, failed, refunded
                }
                if (!Schema::hasColumn('subscriptions', 'last_payment_date')) {
                    $table->dateTime('last_payment_date')->nullable()->after('payment_status');
                }
                if (!Schema::hasColumn('subscriptions', 'next_payment_date')) {
                    $table->dateTime('next_payment_date')->nullable()->after('last_payment_date');
                }
                if (!Schema::hasColumn('subscriptions', 'amount_due')) {
                    $table->decimal('amount_due', 10, 2)->default(0)->after('amount_paid');
                }
            });

            // Update existing payment_method values to 'manual' if they are null or empty
            DB::table('subscriptions')
                ->whereNull('payment_method')
                ->orWhere('payment_method', '')
                ->update(['payment_method' => 'manual']);

            // Now safely change the column default
            Schema::table('subscriptions', function (Blueprint $table) {
                if (Schema::hasColumn('subscriptions', 'payment_method')) {
                    $table->string('payment_method')->default('manual')->change();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn([
                'payment_reference',
                'payment_status',
                'last_payment_date',
                'next_payment_date',
                'amount_due'
            ]);
        });
    }
};
