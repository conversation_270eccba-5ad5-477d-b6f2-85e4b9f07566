<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AffiliateGuestMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only check if user is authenticated with the affiliate guard
        // Don't redirect if they're logged in with other guards (web, super_admin)
        if (Auth::guard('affiliate')->check()) {
            // User is already logged in as affiliate, redirect to dashboard
            return redirect()->route('affiliate.dashboard');
        }

        return $next($request);
    }
}
