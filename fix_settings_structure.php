<?php
require_once 'vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

try {
    // First, check if the organization_id column exists in the settings table
    if (!Schema::hasColumn('settings', 'organization_id')) {
        // Add organization_id column to settings table
        DB::statement('ALTER TABLE settings ADD COLUMN organization_id BIGINT UNSIGNED NULL AFTER id');

        // Add foreign key constraint
        DB::statement('ALTER TABLE settings ADD CONSTRAINT settings_organization_id_foreign FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE');

        echo "Added organization_id column to settings table.\n";
    } else {
        echo "The organization_id column already exists in the settings table.\n";
    }

    // Update RegisterController behavior
    $registerControllerPath = __DIR__ . '/app/Http/Controllers/Auth/RegisterController.php';
    if (file_exists($registerControllerPath)) {
        $content = file_get_contents($registerControllerPath);

        // Check if we need to update the code
        if (strpos($content, 'Setting::firstOrNew([])') !== false) {
            // Replace the problematic code with our fixed version
            $oldCode = '$setting = Setting::firstOrNew([]);
            $setting->organization_name = $request->organization_name;
            $setting->company_address = $request->branch_address; // Set branch address in receipt settings
            $setting->company_phone = $request->branch_phone;
            $setting->company_email = $request->branch_email;
            $setting->save();';

            $newCode = '// Create organization-specific settings
            $setting = new Setting([
                \'organization_id\' => $organization->id,
                \'app_name\' => \'Order Flow Pro\',
                \'app_slogan\' => \'Manage Orders & Reports\',
                \'theme_mode\' => \'light\',
                \'primary_color\' => \'#1f2937\',
                \'sidebar_color\' => \'#1f2937\',
                \'site_title\' => \'OFP\',
                \'organization_name\' => $request->organization_name,
                \'company_address\' => $request->branch_address,
                \'company_phone\' => $request->branch_phone,
                \'company_email\' => $request->branch_email
            ]);
            $setting->save();';

            $content = str_replace($oldCode, $newCode, $content);
            file_put_contents($registerControllerPath, $content);

            echo "Updated RegisterController to use organization-specific settings.\n";
        } else {
            echo "RegisterController already updated.\n";
        }
    }

    echo "Settings structure has been fixed successfully.\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
