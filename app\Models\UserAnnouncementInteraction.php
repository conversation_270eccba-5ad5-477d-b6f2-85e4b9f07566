<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserAnnouncementInteraction extends Model
{
    use HasFactory;

    protected $fillable = [
        'announcement_id',
        'user_id',
        'is_dismissed',
        'is_read',
        'dismissed_at',
        'read_at',
    ];

    protected $casts = [
        'is_dismissed' => 'boolean',
        'is_read' => 'boolean',
        'dismissed_at' => 'datetime',
        'read_at' => 'datetime',
    ];

    /**
     * Get the announcement
     */
    public function announcement(): BelongsTo
    {
        return $this->belongsTo(Announcement::class);
    }

    /**
     * Get the user
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
