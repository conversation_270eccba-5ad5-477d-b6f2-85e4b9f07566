<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\App;

class DropAllTables extends Command
{
    protected $signature = 'db:drop-all';
    protected $description = 'Drop all tables in the database';

    public function handle()
    {
        if (App::environment('production') && !$this->confirm('This will drop all tables in your database. Are you sure?')) {
            return;
        }

        DB::statement('SET FOREIGN_KEY_CHECKS=0');
        
        $tables = DB::select('SHOW TABLES');
        $dbName = DB::getDatabaseName();
        $column = "Tables_in_$dbName";
        
        foreach ($tables as $table) {
            $dropQuery = "DROP TABLE IF EXISTS `$table->$column`";
            DB::statement($dropQuery);
            $this->info("Dropped table: {$table->$column}");
        }

        DB::statement('SET FOREIGN_KEY_CHECKS=1');

        $this->info('All tables have been dropped successfully.');
    }
}