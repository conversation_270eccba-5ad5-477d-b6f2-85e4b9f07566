@extends('layouts.app')

@section('styles')
<link href="{{ asset('css/sweetalert2.min.css') }}" rel="stylesheet">
@endsection

@section('scripts')
<script src="{{ asset('js/sweetalert2.min.js') }}"></script>
<script src="{{ asset('js/user-management.js') }}"></script>
@endsection

@section('content')
<x-slot name="header">
    <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit User') }}: {{ $user->name }}
        </h2>
        <span class="text-sm text-gray-500">Last updated: {{ $user->updated_at->diffForHumans() }}</span>
    </div>
</x-slot>

<div class="py-12" x-data="{ formChanged: false }">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
                <form method="POST"
                      action="{{ route('users.update', $user) }}"
                      class="space-y-6"
                      id="editUserForm"
                      @change="formChanged = true"
                      onsubmit="submitForm(event)">
                    @csrf
                    @method('PUT')

                    @if($errors->any())
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
                            <ul class="list-disc list-inside">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <!-- Name -->
                    <div>
                        <x-input-label for="name" :value="__('Name')" />
                        <x-text-input id="name"
                                     class="block mt-1 w-full"
                                     type="text"
                                     name="name"
                                     :value="old('name', $user->name)"
                                     required
                                     autofocus
                                     minlength="2"
                                     maxlength="255"
                                     data-validation-message="Name must be between 2 and 255 characters" />
                        <p class="mt-1 text-sm text-red-600 hidden" id="name-error"></p>
                    </div>

                    <!-- Email -->
                    <div>
                        <x-input-label for="email" :value="__('Email')" />
                        <div class="relative">
                            @if($user->email === '<EMAIL>')
                                <!-- For admin users, use a display field with a hidden input -->
                                <div class="block mt-1 w-full p-2 border border-gray-300 rounded-md bg-gray-100">
                                    {{ $user->email }} <span class="text-xs text-gray-500">(cannot be changed)</span>
                                </div>
                                <input type="hidden" id="email" name="email" value="{{ $user->email }}">
                            @else
                                <!-- For regular users, use a normal input -->
                                <x-text-input id="email"
                                             class="block mt-1 w-full"
                                             type="email"
                                             name="email"
                                             value="{{ old('email', $user->email) }}"
                                             required />
                            @endif
                            <p class="mt-1 text-sm text-gray-500">Current email: {{ $user->email }}</p>
                        </div>
                        <p class="mt-1 text-sm text-red-600 hidden" id="email-error"></p>
                    </div>

                    <!-- Password -->
                    <div>
                        <x-input-label for="password" :value="__('New Password')" />
                        <div class="relative">
                            <x-text-input id="password"
                                         class="block mt-1 w-full pr-10"
                                         type="password"
                                         name="password"
                                         autocomplete="new-password"
                                         placeholder="Leave blank to keep current password"
                                         minlength="8" />
                            <button type="button"
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5 text-gray-500 hover:text-gray-700 focus:outline-none"
                                    onclick="togglePasswordVisibility('password')">
                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                            </button>
                        </div>
                        <p class="mt-1 text-sm text-red-600 hidden" id="password-error"></p>
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <x-input-label for="password_confirmation" :value="__('Confirm New Password')" />
                        <div class="relative">
                            <x-text-input id="password_confirmation"
                                         class="block mt-1 w-full pr-10"
                                         type="password"
                                         name="password_confirmation"
                                         placeholder="Leave blank to keep current password" />
                            <button type="button"
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5 text-gray-500 hover:text-gray-700 focus:outline-none"
                                    onclick="togglePasswordVisibility('password_confirmation')">
                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                            </button>
                        </div>
                        <p class="mt-1 text-sm text-red-600 hidden" id="password-confirmation-error"></p>
                    </div>

                    <!-- Status -->
                    <div>
                        <x-input-label for="status" :value="__('Status')" />
                        <select id="status"
                                name="status"
                                class="block mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                                required
                                {{ $user->email === '<EMAIL>' ? 'disabled' : '' }}>
                            <option value="active" {{ old('status', $user->status) == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ old('status', $user->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                        @if($user->email === '<EMAIL>')
                            <p class="mt-1 text-sm text-gray-500">Organization Owner status cannot be changed</p>
                        @endif
                        <p class="mt-1 text-sm text-red-600 hidden" id="status-error"></p>
                    </div>

                    <!-- Branch -->
                    <div>
                        <x-input-label for="branch_id" :value="__('Branch')" />
                        <select id="branch_id"
                                name="branch_id"
                                class="block mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                                {{ $user->email === '<EMAIL>' ? 'disabled' : '' }}>
                            <option value="">None (No Branch Assignment)</option>
                            @foreach($branches as $branch)
                                <option value="{{ $branch->id }}" {{ old('branch_id', $user->branch_id) == $branch->id ? 'selected' : '' }}>
                                    {{ $branch->name }}
                                </option>
                            @endforeach
                        </select>
                        @if($user->email === '<EMAIL>')
                            <p class="mt-1 text-sm text-gray-500">Organization Owner branch cannot be changed here</p>
                        @else
                            <p class="mt-1 text-sm text-gray-500">Assign this user to a specific branch</p>
                        @endif
                        <p class="mt-1 text-sm text-red-600 hidden" id="branch_id-error"></p>
                    </div>

                    <!-- Roles -->
                    <div>
                        <x-input-label :value="__('Roles')" />
                        <div class="mt-2 space-y-2 max-h-48 overflow-y-auto border rounded-md p-3">
                            @foreach($roles as $role)
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="role_{{ $role->id }}"
                                           name="roles[]"
                                           value="{{ $role->id }}"
                                           class="rounded border-gray-300 text-primary shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                                           {{ $user->email === '<EMAIL>' && $role->name === 'Organization Owner' ? 'checked disabled' : '' }}
                                           {{ in_array($role->id, old('roles', $user->roles->pluck('id')->toArray())) ? 'checked' : '' }}>
                                    <label for="role_{{ $role->id }}" class="ml-2 text-sm text-gray-600">
                                        {{ $role->name }}
                                    </label>
                                </div>
                            @endforeach
                        </div>
                        <!-- Add hidden field to ensure disabled Organization Owner role is submitted -->
                        @if($user->email === '<EMAIL>')
                            @foreach($roles as $role)
                                @if($role->name === 'Organization Owner')
                                    <input type="hidden" name="roles[]" value="{{ $role->id }}">
                                @endif
                            @endforeach
                        @endif
                        <p class="mt-1 text-sm text-red-600 hidden" id="roles-error"></p>
                    </div>

                    <div class="flex items-center justify-end mt-4">
                        <x-secondary-button type="button" onclick="confirmCancel()" class="mr-3">
                            {{ __('Cancel') }}
                        </x-secondary-button>
                        <x-primary-button type="submit" id="updateUserBtn">
                            {{ __('Update User') }}
                        </x-primary-button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Basic functions
function togglePasswordVisibility(fieldId) {
    const field = document.getElementById(fieldId);
    if (field) {
        field.type = field.type === 'password' ? 'text' : 'password';
    }
}

function confirmCancel() {
    if (window.formChanged) {
        Swal.fire({
            title: 'Discard Changes?',
            text: "You have unsaved changes. Are you sure you want to leave?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, discard changes',
            cancelButtonText: 'No, keep editing'
        }).then((result) => {
            if (result.isConfirmed) {
                window.formChanged = false; // Reset flag before navigating
                window.location.href = "{{ route('users.index') }}";
            }
        });
    } else {
        window.location.href = "{{ route('users.index') }}";
    }
}

// Form submission handling
function submitForm(event) {
    if (event && event.preventDefault) {
        event.preventDefault();
    }

    const form = document.getElementById('editUserForm');
    if (!form) {
        console.error('Form not found');
        return;
    }

    // Reset all previous error messages
    document.querySelectorAll('.text-red-600').forEach(el => {
        el.textContent = '';
        el.classList.add('hidden');
    });

    let hasErrors = false;

    // Get all visible fields that need validation (excluding hidden and readonly fields)
    const fieldsToValidate = Array.from(form.elements).filter(field => {
        // Skip hidden fields, submit buttons, readonly fields, and disabled fields
        return field.type !== 'hidden' &&
               field.type !== 'submit' &&
               !field.hasAttribute('readonly') &&
               !field.disabled;
    });

    // Validate required fields
    fieldsToValidate.forEach(field => {
        if (field.hasAttribute('required')) {
            const errorElement = document.getElementById(`${field.id}-error`);

            // Check if field has a value (trim to handle whitespace-only)
            if ((!field.value || field.value.trim() === '') && errorElement) {
                errorElement.textContent = 'This field is required';
                errorElement.classList.remove('hidden');
                hasErrors = true;
            }

            // Special validation for email field
            if (field.type === 'email' && field.value && !isValidEmail(field.value)) {
                const emailError = document.getElementById('email-error');
                if (emailError) {
                    emailError.textContent = 'Please enter a valid email address';
                    emailError.classList.remove('hidden');
                    hasErrors = true;
                }
            }
        }
    });

    // Check for at least one role selected
    const roleCheckboxesValidation = form.querySelectorAll('input[name="roles[]"]:checked:not([disabled])');
    const rolesError = document.getElementById('roles-error');

    if (roleCheckboxesValidation.length === 0 && rolesError) {
        rolesError.textContent = 'Please select at least one role';
        rolesError.classList.remove('hidden');
        hasErrors = true;
    }

    if (hasErrors) {
        Swal.fire('Validation Error', 'Please check the form for errors', 'error');
        return;
    }

    // Create form data from the form
    const formData = new FormData(form);

    // Ensure the email field is included correctly
    const emailField = form.querySelector('#email');
    if (emailField) {
        // If the email field exists, make sure its value is included
        // This ensures it's included even if readonly
        formData.set('email', emailField.value);
    }

    // Explicitly add all role checkboxes
    const roleCheckboxesForm = form.querySelectorAll('input[name="roles[]"]:checked');
    formData.delete('roles[]'); // Remove any previous entries
    roleCheckboxesForm.forEach(checkbox => {
        formData.append('roles[]', checkbox.value);
    });

    // Add any disabled fields (like status for admin)
    const disabledFields = form.querySelectorAll('select[disabled], input[disabled]:not([type="checkbox"])');
    disabledFields.forEach(field => {
        if (field.name) {
            formData.append(field.name, field.value);
        }
    });

    // For debugging, log form data
    console.log('Form data:');
    for (let [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
    }

    const submitButton = document.getElementById('updateUserBtn');

    if (submitButton) {
        submitButton.disabled = true;
    }

    // Show loading state
    Swal.fire({
        title: 'Updating user...',
        text: 'Please wait',
        allowOutsideClick: false,
        showConfirmButton: false,
        willOpen: () => {
            Swal.showLoading();
        }
    });

    // Send the request
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (response.ok && response.headers.get('content-type')?.includes('application/json')) {
            return response.json();
        } else if (response.headers.get('content-type')?.includes('application/json')) {
            return response.json().then(data => Promise.reject(data));
        } else {
            return Promise.reject({
                message: 'Server returned an unexpected response'
            });
        }
    })
    .then(data => {
        // Success case
        window.formChanged = false; // Reset form changed flag

        Swal.fire({
            title: 'Success!',
            text: data.message || 'User updated successfully',
            icon: 'success',
            timer: 1500,
            showConfirmButton: false
        }).then(() => {
            window.location.href = "{{ route('users.index') }}";
        });
    })
    .catch(error => {
        // Error case
        console.error('Update error:', error);

        let errorMessage = 'An error occurred while updating the user.';

        if (error.errors) {
            // Laravel validation errors
            errorMessage = Object.values(error.errors).flat().join('<br>');
        } else if (error.message) {
            errorMessage = error.message;
        }

        Swal.fire({
            title: 'Update Failed',
            html: errorMessage,
            icon: 'error',
            confirmButtonText: 'OK',
            showCancelButton: true,
            cancelButtonText: 'Discard changes and go back',
        }).then((result) => {
            if (result.dismiss === Swal.DismissReason.cancel) {
                window.formChanged = false;
                window.location.href = "{{ route('users.index') }}";
            }
        });
    })
    .finally(() => {
        if (submitButton) {
            submitButton.disabled = false;
        }
    });
}

// Helper function to validate email
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Initialize form on page load
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editUserForm');
    const updateBtn = document.getElementById('updateUserBtn');

    if (form) {
        // Track form changes
        window.formChanged = false;
        form.addEventListener('change', () => {
            window.formChanged = true;
        });

        // Submit handler
        form.addEventListener('submit', submitForm);
    }

    // Warn about unsaved changes
    window.addEventListener('beforeunload', (e) => {
        if (window.formChanged) {
            e.preventDefault();
            e.returnValue = '';
        }
    });
});
</script>
@endpush

@endsection

