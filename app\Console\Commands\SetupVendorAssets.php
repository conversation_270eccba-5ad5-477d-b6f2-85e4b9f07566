<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;

class SetupVendorAssets extends Command
{
    protected $signature = 'setup:vendor-assets';
    protected $description = 'Download and setup vendor assets for offline use';

    private $assets = [
        'css' => [
            'tailwind' => [
                'url' => 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
                'path' => 'vendor/css/tailwind.min.css'
            ],
            'fontawesome' => [
                'url' => 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css',
                'path' => 'vendor/css/fontawesome.min.css'
            ],
            'sweetalert2' => [
                'url' => 'https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css',
                'path' => 'vendor/css/sweetalert2.min.css'
            ]
        ],
        'js' => [
            'sweetalert2' => [
                'url' => 'https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.all.min.js',
                'path' => 'vendor/js/sweetalert2.min.js'
            ]
        ],
        'webfonts' => [
            'fa-solid' => [
                'url' => 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/webfonts/fa-solid-900.woff2',
                'path' => 'vendor/webfonts/fa-solid-900.woff2'
            ],
            'fa-regular' => [
                'url' => 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/webfonts/fa-regular-400.woff2',
                'path' => 'vendor/webfonts/fa-regular-400.woff2'
            ],
            'fa-brands' => [
                'url' => 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/webfonts/fa-brands-400.woff2',
                'path' => 'vendor/webfonts/fa-brands-400.woff2'
            ]
        ]
    ];

    public function handle()
    {
        $this->info('Setting up vendor assets...');

        // Create directories if they don't exist
        $this->createDirectories();

        // Download assets
        $this->downloadAssets();

        // Update Font Awesome CSS to use local paths
        $this->updateFontAwesomePaths();

        $this->info('Vendor assets setup completed successfully!');
    }

    private function createDirectories()
    {
        $directories = ['vendor/css', 'vendor/js', 'vendor/webfonts'];
        
        foreach ($directories as $dir) {
            $path = public_path($dir);
            if (!File::exists($path)) {
                File::makeDirectory($path, 0755, true);
                $this->info("Created directory: {$dir}");
            }
        }
    }

    private function downloadAssets()
    {
        foreach ($this->assets as $type => $assets) {
            foreach ($assets as $name => $asset) {
                $this->downloadAsset($name, $asset['url'], $asset['path']);
            }
        }
    }

    private function downloadAsset($name, $url, $path)
    {
        $this->info("Downloading {$name}...");
        
        try {
            $response = Http::get($url);
            
            if ($response->successful()) {
                File::put(public_path($path), $response->body());
                $this->info("Successfully downloaded {$name}");
            } else {
                $this->error("Failed to download {$name}");
            }
        } catch (\Exception $e) {
            $this->error("Error downloading {$name}: " . $e->getMessage());
        }
    }

    private function updateFontAwesomePaths()
    {
        $faPath = public_path('vendor/css/fontawesome.min.css');
        if (File::exists($faPath)) {
            $content = File::get($faPath);
            
            // Update font paths to use local vendor directory
            $content = preg_replace(
                '/(url\(")([^"]+)(woff2"\))/',
                '$1/vendor/webfonts/$3',
                $content
            );
            
            File::put($faPath, $content);
            $this->info('Updated Font Awesome paths');
        }
    }
} 