<?php

namespace App\Http\Controllers\Affiliate;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Affiliate;
use App\Models\AffiliateSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules;
use Illuminate\Auth\Events\Registered;

class AffiliateController extends Controller
{
    /**
     * Show the affiliate registration form
     */
    public function showRegistrationForm(Request $request)
    {
        $settings = AffiliateSetting::getInstance();

        if (!$settings->program_active) {
            return redirect()->route('login')
                ->with('error', 'The affiliate program is currently not accepting new registrations.');
        }

        // Check for referral data
        $referralData = null;
        if ($request->has('ref')) {
            $referralData = [
                'referrer_code' => $request->ref,
                'utm_source' => $request->utm_source,
                'utm_medium' => $request->utm_medium,
                'utm_campaign' => $request->utm_campaign,
            ];
        }

        return view('affiliate.register', compact('settings', 'referralData'));
    }

    /**
     * Handle affiliate registration
     */
    public function register(Request $request)
    {
        $settings = AffiliateSetting::getInstance();

        if (!$settings->program_active) {
            return redirect()->route('login')
                ->with('error', 'The affiliate program is currently not accepting new registrations.');
        }

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'phone' => ['required', 'string', 'max:20'],
            'phone_full' => ['nullable', 'string', 'max:20'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'bio' => ['nullable', 'string', 'max:500'],
            'website' => ['nullable', 'url', 'max:255'],
            'social_media' => ['nullable', 'string', 'max:255'],
            'payment_method' => ['required', 'in:bank_transfer'],
            'bank_name' => ['required', 'string', 'max:255'],
            'account_number' => ['required', 'string', 'max:255'],
            'account_name' => ['required', 'string', 'max:255'],
            'routing_number' => ['nullable', 'string', 'max:255'],
            'terms_accepted' => ['required', 'accepted'],
        ]);

        try {
            DB::beginTransaction();

            // Create user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone_full ?: $request->phone, // Use full international number if available
                'password' => Hash::make($request->password),
                'status' => User::STATUS_ACTIVE,
            ]);

            // Prepare payment details (bank transfer only)
            $paymentDetails = [
                'method' => 'bank_transfer',
                'bank_name' => $request->bank_name,
                'account_number' => $request->account_number,
                'account_name' => $request->account_name,
                'routing_number' => $request->routing_number,
            ];

            // Create affiliate
            $affiliate = Affiliate::create([
                'user_id' => $user->id,
                'status' => $settings->auto_approve_affiliates ? Affiliate::STATUS_ACTIVE : Affiliate::STATUS_PENDING,
                'commission_rate' => $settings->default_commission_rate,
                'payment_details' => $paymentDetails,
                'bio' => $request->bio,
                'website' => $request->website,
                'social_media' => $request->social_media,
            ]);

            DB::commit();

            // Fire the registered event for email verification and welcome email
            event(new Registered($user));

            // Log the user in using affiliate guard
            Auth::guard('affiliate')->login($user);

            // Redirect based on status
            if ($affiliate->status === Affiliate::STATUS_PENDING) {
                return redirect()->route('affiliate.pending')
                    ->with('success', 'Your affiliate application has been submitted and is pending approval.');
            } else {
                return redirect()->route('affiliate.dashboard')
                    ->with('success', 'Welcome to our affiliate program! Your account is now active.');
            }

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Registration failed: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Show the affiliate login form
     */
    public function showLoginForm()
    {
        return view('affiliate.login');
    }

    /**
     * Handle affiliate login
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        // Check if user exists first
        $user = \App\Models\User::where('email', $request->email)->first();
        if (!$user) {
            return back()->withErrors([
                'email' => 'The provided credentials do not match our records.',
            ])->onlyInput('email');
        }

        // Check if this user has an organization account - prevent affiliate login
        if ($user->organization_id) {
            return back()->withErrors([
                'email' => 'This email is registered as an organization user. Please use the organization login page.',
            ])->onlyInput('email');
        }

        if (Auth::guard('affiliate')->attempt($request->only('email', 'password'), $request->boolean('remember'))) {
            $request->session()->regenerate();

            $user = Auth::guard('affiliate')->user();

            // Check if email is verified
            if (!$user->hasVerifiedEmail()) {
                return redirect()->route('affiliate.verification.notice')
                    ->with('info', 'Please verify your email address to continue.');
            }

            // Check if this is a recent email verification (within last 10 minutes)
            $recentlyVerified = $user->email_verified_at &&
                               $user->email_verified_at->diffInMinutes(now()) <= 10;

            // Check if user has affiliate account
            $affiliate = Affiliate::where('user_id', $user->id)->first();

            if (!$affiliate) {
                Auth::guard('affiliate')->logout();
                return redirect()->route('affiliate.register')
                    ->with('error', 'You need to register as an affiliate first.');
            }

            // Check affiliate status
            if ($affiliate->status === Affiliate::STATUS_PENDING) {
                $message = $recentlyVerified ?
                    'Email verified successfully! Your affiliate account is pending approval.' :
                    'Your affiliate account is pending approval.';
                return redirect()->route('affiliate.pending')
                    ->with('success', $message);
            }

            if ($affiliate->status !== Affiliate::STATUS_ACTIVE) {
                Auth::guard('affiliate')->logout();
                return redirect()->route('affiliate.login')
                    ->with('error', 'Your affiliate account is not active. Please contact support.');
            }

            // Add success message for recently verified users
            if ($recentlyVerified) {
                return redirect()->route('affiliate.dashboard')
                    ->with('success', 'Email verified successfully! Welcome to your affiliate dashboard.');
            }

            // Always redirect to affiliate dashboard, don't use intended
            return redirect()->route('affiliate.dashboard');
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }

    /**
     * Handle affiliate logout
     */
    public function logout(Request $request)
    {
        Auth::guard('affiliate')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('affiliate.login')
            ->with('success', 'You have been logged out successfully.');
    }

    /**
     * Show affiliate program information
     */
    public function showProgram()
    {
        $settings = AffiliateSetting::getInstance();
        return view('affiliate.program', compact('settings'));
    }

    /**
     * Show pending approval page
     */
    public function showPending()
    {
        $affiliate = $this->getAffiliate();

        if (!$affiliate || $affiliate->status !== Affiliate::STATUS_PENDING) {
            return redirect()->route('affiliate.dashboard');
        }

        return view('affiliate.pending', compact('affiliate'));
    }



    /**
     * Show affiliate profile
     */
    public function profile(Request $request)
    {
        $affiliate = $request->affiliate ?? $this->getAffiliate();

        if (!$affiliate) {
            return redirect()->route('affiliate.dashboard');
        }

        return view('affiliate.profile', compact('affiliate'));
    }

    /**
     * Update affiliate profile
     */
    public function updateProfile(Request $request)
    {
        $affiliate = $request->affiliate ?? $this->getAffiliate();

        if (!$affiliate) {
            return redirect()->route('affiliate.dashboard');
        }

        // Check if this is a password change request
        if ($request->has('password_change')) {
            return $this->updatePassword($request, $affiliate);
        }

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $affiliate->user_id],
            'phone' => ['required', 'string', 'max:20'],
            'phone_full' => ['nullable', 'string', 'max:20'],
            'bio' => ['nullable', 'string', 'max:500'],
            'website' => ['nullable', 'url', 'max:255'],
            'social_media' => ['nullable', 'string', 'max:255'],
        ]);

        try {
            DB::beginTransaction();

            // Update user
            $affiliate->user->update([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone_full ?: $request->phone, // Use full international number if available
            ]);

            // Update affiliate
            $affiliate->update([
                'bio' => $request->bio,
                'website' => $request->website,
                'social_media' => $request->social_media,
            ]);

            DB::commit();

            return redirect()->route('affiliate.profile')
                ->with('success', 'Profile updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Failed to update profile: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Update affiliate password
     */
    private function updatePassword(Request $request, Affiliate $affiliate)
    {
        $request->validate([
            'current_password' => ['required'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        // Verify current password
        if (!Hash::check($request->current_password, $affiliate->user->password)) {
            return back()->withErrors(['current_password' => 'The current password is incorrect.']);
        }

        try {
            // Update password
            $affiliate->user->update([
                'password' => Hash::make($request->password),
            ]);

            return redirect()->route('affiliate.profile')
                ->with('success', 'Password updated successfully.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update password: ' . $e->getMessage()]);
        }
    }

    /**
     * Get the current user's affiliate record
     */
    protected function getAffiliate(): ?Affiliate
    {
        return Affiliate::where('user_id', Auth::guard('affiliate')->id())->first();
    }
}
