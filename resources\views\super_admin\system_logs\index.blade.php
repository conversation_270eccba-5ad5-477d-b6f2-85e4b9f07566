@extends('super_admin.layouts.app')

@section('title', 'System Logs')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">System Logs</h1>
                <div>
                    <a href="{{ route('super_admin.system-logs.export', request()->query()) }}" 
                       class="btn btn-outline-primary me-2">
                        <i class="fas fa-download"></i> Export CSV
                    </a>
                    <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cleanupModal">
                        <i class="fas fa-trash"></i> Cleanup Old Logs
                    </button>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-left-primary">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Logs
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold">{{ number_format($stats['total_logs']) }}</div>
                                </div>
                                <div class="text-primary">
                                    <i class="fas fa-list fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card border-left-danger">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                        Critical Logs
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold">{{ number_format($stats['critical_logs']) }}</div>
                                </div>
                                <div class="text-danger">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card border-left-info">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Most Active Channel
                                    </div>
                                    <div class="h6 mb-0 font-weight-bold">
                                        @if(!empty($stats['by_channel']))
                                            {{ ucfirst(array_keys($stats['by_channel'], max($stats['by_channel']))[0]) }}
                                        @else
                                            N/A
                                        @endif
                                    </div>
                                </div>
                                <div class="text-info">
                                    <i class="fas fa-chart-bar fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="card border-left-success">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Time Range
                                    </div>
                                    <div class="h6 mb-0 font-weight-bold">
                                        {{ request('hours', 24) }} Hours
                                    </div>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>
                        Filters
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('super_admin.system-logs.index') }}">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">From Date</label>
                                <input type="datetime-local" class="form-control" id="date_from" name="date_from" 
                                       value="{{ request('date_from') }}">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">To Date</label>
                                <input type="datetime-local" class="form-control" id="date_to" name="date_to" 
                                       value="{{ request('date_to') }}">
                            </div>
                            <div class="col-md-2">
                                <label for="level" class="form-label">Level</label>
                                <select class="form-select" id="level" name="level">
                                    <option value="">All Levels</option>
                                    @foreach($filterOptions['levels'] as $value => $label)
                                        <option value="{{ $value }}" {{ request('level') === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="channel" class="form-label">Channel</label>
                                <select class="form-select" id="channel" name="channel">
                                    <option value="">All Channels</option>
                                    @foreach($filterOptions['channels'] as $value => $label)
                                        <option value="{{ $value }}" {{ request('channel') === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="organization_id" class="form-label">Organization</label>
                                <select class="form-select" id="organization_id" name="organization_id">
                                    <option value="">All Organizations</option>
                                    @foreach($filterOptions['organizations'] as $id => $name)
                                        <option value="{{ $id }}" {{ request('organization_id') == $id ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        
                        <div class="row g-3 mt-2">
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ request('search') }}" placeholder="Search in message, URL, context...">
                            </div>
                            <div class="col-md-2">
                                <label for="hours" class="form-label">Recent Hours</label>
                                <select class="form-select" id="hours" name="hours">
                                    <option value="1" {{ request('hours') == '1' ? 'selected' : '' }}>Last Hour</option>
                                    <option value="6" {{ request('hours') == '6' ? 'selected' : '' }}>Last 6 Hours</option>
                                    <option value="24" {{ request('hours', '24') == '24' ? 'selected' : '' }}>Last 24 Hours</option>
                                    <option value="72" {{ request('hours') == '72' ? 'selected' : '' }}>Last 3 Days</option>
                                    <option value="168" {{ request('hours') == '168' ? 'selected' : '' }}>Last Week</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="critical_only" name="critical_only" 
                                           value="1" {{ request('critical_only') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="critical_only">
                                        Critical Only
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="{{ route('super_admin.system-logs.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Logs Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        System Logs ({{ $logs->total() }} entries)
                    </h5>
                </div>
                <div class="card-body p-0">
                    @if($logs->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="140">DateTime</th>
                                        <th width="80">Level</th>
                                        <th width="100">Channel</th>
                                        <th>Message</th>
                                        <th width="150">User</th>
                                        <th width="120">Organization</th>
                                        <th width="100">IP Address</th>
                                        <th width="80">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($logs as $log)
                                        <tr class="{{ $log->isCritical() ? 'table-danger' : '' }}">
                                            <td>
                                                <small>{{ $log->datetime->format('M d, Y') }}</small><br>
                                                <small class="text-muted">{{ $log->datetime->format('H:i:s') }}</small>
                                            </td>
                                            <td>{!! $log->level_badge !!}</td>
                                            <td>
                                                <span class="badge bg-secondary">{{ ucfirst($log->channel) }}</span>
                                            </td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 300px;" title="{{ $log->message }}">
                                                    {{ $log->message }}
                                                </div>
                                                @if($log->url)
                                                    <small class="text-muted d-block">{{ $log->method }} {{ $log->url }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                @if($log->user)
                                                    <div class="text-truncate">{{ $log->user->name }}</div>
                                                    <small class="text-muted">{{ $log->user->email }}</small>
                                                @else
                                                    <span class="text-muted">System</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($log->organization)
                                                    <div class="text-truncate" title="{{ $log->organization->name }}">
                                                        {{ $log->organization->name }}
                                                    </div>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <small>{{ $log->ip_address }}</small>
                                            </td>
                                            <td>
                                                <a href="{{ route('super_admin.system-logs.show', $log) }}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            {{ $logs->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-list fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No logs found</h5>
                            <p class="text-muted">Try adjusting your filters or time range.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cleanup Modal -->
<div class="modal fade" id="cleanupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-trash me-2"></i>
                    Cleanup Old Logs
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('super_admin.system-logs.cleanup') }}">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This action will permanently delete old log entries and cannot be undone.
                    </div>
                    <div class="mb-3">
                        <label for="days" class="form-label">Delete logs older than:</label>
                        <select class="form-select" id="days" name="days" required>
                            <option value="7">7 days</option>
                            <option value="30" selected>30 days</option>
                            <option value="60">60 days</option>
                            <option value="90">90 days</option>
                            <option value="180">180 days</option>
                            <option value="365">1 year</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>
                        Delete Old Logs
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #007bff !important;
}
.border-left-danger {
    border-left: 0.25rem solid #dc3545 !important;
}
.border-left-info {
    border-left: 0.25rem solid #17a2b8 !important;
}
.border-left-success {
    border-left: 0.25rem solid #28a745 !important;
}
</style>

<script>
// Auto-refresh every 30 seconds
setInterval(function() {
    if (!document.hidden) {
        // Only refresh if no modals are open
        if (!document.querySelector('.modal.show')) {
            window.location.reload();
        }
    }
}, 30000);
</script>
@endsection
