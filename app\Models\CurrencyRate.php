<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class CurrencyRate extends Model
{
    use HasFactory;

    protected $fillable = [
        'from_currency',
        'to_currency',
        'rate',
        'is_active',
        'effective_from',
        'effective_to',
        'created_by',
    ];

    protected $casts = [
        'rate' => 'decimal:6',
        'is_active' => 'boolean',
        'effective_from' => 'datetime',
        'effective_to' => 'datetime',
    ];

    /**
     * Get the user who created this rate
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the super admin who created this rate
     */
    public function creatorSuperAdmin()
    {
        return $this->belongsTo(\App\Models\SuperAdmin::class, 'created_by');
    }

    /**
     * Get the name of who created this rate
     */
    public function getCreatorNameAttribute()
    {
        // Try to get regular user first
        $user = $this->creator;
        if ($user) {
            return $user->name;
        }

        // Try to get super admin
        try {
            $superAdmin = \App\Models\SuperAdmin::find($this->created_by);
            if ($superAdmin) {
                return $superAdmin->name . ' (Super Admin)';
            }
        } catch (\Exception $e) {
            // Super admin model might not exist or have issues
        }

        return 'System';
    }

    /**
     * Get the from currency
     */
    public function fromCurrency()
    {
        return $this->belongsTo(Currency::class, 'from_currency', 'code');
    }

    /**
     * Get the to currency
     */
    public function toCurrency()
    {
        return $this->belongsTo(Currency::class, 'to_currency', 'code');
    }

    /**
     * Get current active rate between two currencies
     */
    public static function getCurrentRate($fromCurrency, $toCurrency)
    {
        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }

        $rate = static::where('from_currency', $fromCurrency)
            ->where('to_currency', $toCurrency)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('effective_from')
                    ->orWhere('effective_from', '<=', now());
            })
            ->where(function ($query) {
                $query->whereNull('effective_to')
                    ->orWhere('effective_to', '>', now());
            })
            ->orderBy('effective_from', 'desc')
            ->first();

        if ($rate) {
            return $rate->rate;
        }

        // Try reverse rate
        $reverseRate = static::where('from_currency', $toCurrency)
            ->where('to_currency', $fromCurrency)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('effective_from')
                    ->orWhere('effective_from', '<=', now());
            })
            ->where(function ($query) {
                $query->whereNull('effective_to')
                    ->orWhere('effective_to', '>', now());
            })
            ->orderBy('effective_from', 'desc')
            ->first();

        if ($reverseRate) {
            return 1 / $reverseRate->rate;
        }

        return null;
    }

    /**
     * Convert amount from one currency to another
     */
    public static function convert($amount, $fromCurrency, $toCurrency)
    {
        $rate = static::getCurrentRate($fromCurrency, $toCurrency);
        
        if ($rate === null) {
            throw new \Exception("No exchange rate found for {$fromCurrency} to {$toCurrency}");
        }

        return $amount * $rate;
    }

    /**
     * Check if rate is currently effective
     */
    public function isEffective()
    {
        $now = now();
        
        $effectiveFromCheck = $this->effective_from === null || $this->effective_from <= $now;
        $effectiveToCheck = $this->effective_to === null || $this->effective_to > $now;
        
        return $this->is_active && $effectiveFromCheck && $effectiveToCheck;
    }
}
