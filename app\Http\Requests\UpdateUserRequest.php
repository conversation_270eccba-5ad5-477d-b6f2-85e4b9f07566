<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;
use App\Rules\LastActiveAdmin;

class UpdateUserRequest extends FormRequest
{
    public function authorize()
    {
        return $this->user()->hasRole('Organization Owner') &&
               $this->route('user')->email !== '<EMAIL>';
    }

    public function rules()
    {
        $userId = $this->route('user')->id;

        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $userId],
            'roles' => ['required', 'array', 'exists:roles,id'],
            'status' => ['required', 'in:active,inactive', new LastActiveAdmin()],
            'branch_id' => ['nullable', 'exists:branches,id'],
        ];

        if ($this->filled('password')) {
            $rules['password'] = [
                'confirmed',
                Password::min(8)
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised()
            ];
        }

        // If trying to update organization owner, force status to active
        if ($this->route('user')->email === '<EMAIL>') {
            $rules['status'] = ['required', 'in:active'];
        }

        return $rules;
    }

    public function messages()
    {
        return [
            'name.required' => 'The name field is required.',
            'email.required' => 'The email field is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email is already taken.',
            'roles.required' => 'Please select at least one role.',
            'status.required' => 'Please select a status.',
            'status.in' => 'Invalid status selected.'
        ];
    }
}
