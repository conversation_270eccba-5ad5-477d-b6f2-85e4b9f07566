<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('expenditures', function (Blueprint $table) {
            $table->id();
            $table->string('heading');
            $table->decimal('amount', 10, 2);
            $table->string('applicant')->nullable(); // Person who applied for the expenditure
            $table->string('issued_to')->nullable();
            $table->string('approved_by')->nullable();
            $table->enum('status', ['Pending', 'Approved', 'Rejected'])->default('Pending');
            $table->timestamp('approved_at')->nullable();
            $table->unsignedBigInteger('organization_id')->nullable(); // Changed to avoid foreign key
            $table->unsignedBigInteger('branch_id')->nullable(); // Changed to avoid foreign key
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('expenditures');
    }
};
