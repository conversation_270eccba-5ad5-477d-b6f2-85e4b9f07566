<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            if (Schema::hasColumn('orders', 'running_cost')) {
                $table->dropColumn('running_cost');
            }

            if (Schema::hasColumn('orders', 'production_amount')) {
                $table->dropColumn('production_amount');
            }
        });
    }

    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            if (!Schema::hasColumn('orders', 'running_cost')) {
                $table->decimal('running_cost', 10, 2)->default(0);
            }

            if (!Schema::hasColumn('orders', 'production_amount')) {
                $table->decimal('production_amount', 10, 2)->default(0);
            }
        });
    }
};
