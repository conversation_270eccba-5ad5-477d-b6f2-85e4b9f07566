<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('affiliates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('affiliate_code', 10)->unique();
            $table->enum('status', ['pending', 'active', 'inactive', 'suspended'])->default('pending');
            $table->decimal('commission_rate', 5, 2)->default(10.00); // Default 10%
            $table->decimal('total_earnings', 10, 2)->default(0.00);
            $table->decimal('available_balance', 10, 2)->default(0.00);
            $table->decimal('pending_balance', 10, 2)->default(0.00);
            $table->decimal('withdrawn_amount', 10, 2)->default(0.00);
            $table->string('referral_link')->nullable();
            $table->timestamp('joined_at')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('super_admins')->onDelete('set null');
            $table->json('payment_details')->nullable(); // Bank info, PayPal, etc.
            $table->text('bio')->nullable();
            $table->string('website')->nullable();
            $table->string('social_media')->nullable();
            $table->timestamps();

            $table->index(['status']);
            $table->index(['affiliate_code']);
            $table->index(['user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('affiliates');
    }
};
