<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use App\Models\Subscription;
use App\Models\SubscriptionPayment;
use App\Services\ProrationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PlanChangeController extends Controller
{
    protected $prorationService;

    public function __construct(ProrationService $prorationService)
    {
        $this->prorationService = $prorationService;
        $this->middleware('auth');
        $this->middleware('role:Organization Owner|Manager');
    }

    /**
     * Show plan comparison and upgrade options
     */
    public function index()
    {
        $organization = Auth::user()->organization;
        $currentPlan = $organization->plan;

        // Get all active plans
        $allPlans = Plan::where('is_active', true)->orderBy('price')->get();

        // Filter out current plan from available plans
        $availablePlans = $allPlans->filter(function($plan) use ($currentPlan) {
            return !$currentPlan || $plan->id !== $currentPlan->id;
        });

        $activeSubscription = $organization->activeSubscription;

        // Calculate proration for each plan
        $planComparisons = [];
        foreach ($availablePlans as $plan) {
            try {
                if ($activeSubscription) {
                    $proration = $this->prorationService->calculatePlanChange($activeSubscription, $plan);
                } else {
                    $proration = [
                        'type' => 'new_subscription',
                        'net_amount' => $plan->price,
                        'proration_details' => 'New subscription - full monthly charge'
                    ];
                }
            } catch (\Exception $e) {
                // Fallback to simple calculation if proration service fails
                $proration = [
                    'type' => 'simple_change',
                    'net_amount' => $plan->price,
                    'proration_details' => 'Plan change - contact support for exact pricing'
                ];
                \Log::warning('Proration calculation failed', [
                    'plan_id' => $plan->id,
                    'organization_id' => $organization->id,
                    'error' => $e->getMessage()
                ]);
            }

            $planComparisons[] = [
                'plan' => $plan,
                'proration' => $proration,
                'is_upgrade' => !$currentPlan || $plan->price > $currentPlan->price,
                'is_downgrade' => $currentPlan && $plan->price < $currentPlan->price,
            ];
        }

        // Get all plans for reference (including current)
        $plans = $allPlans;

        // Check for pending payments
        $pendingPayments = $organization->subscriptionPayments()->pending()->get();

        return view('plan-change.index', compact(
            'organization',
            'currentPlan',
            'availablePlans',
            'planComparisons',
            'activeSubscription',
            'plans',
            'pendingPayments'
        ));
    }

    /**
     * Show plan change preview
     */
    public function preview(Request $request, Plan $plan)
    {
        $organization = Auth::user()->organization;
        $currentPlan = $organization->plan;
        $activeSubscription = $organization->activeSubscription;
        $billingPeriodMonths = (int) $request->get('billing_period_months', 1);

        // For free plans, billing period is not relevant
        if (!$plan->isFree()) {
            // Validate billing period for paid plans only
            if (!in_array($billingPeriodMonths, [1, 12, 24, 48])) {
                return redirect()->route('plan-change.index')
                    ->with('error', 'Invalid billing period selected.');
            }
        } else {
            // For free plans, always use 1 month (doesn't matter since price is 0)
            $billingPeriodMonths = 1;
        }

        try {
            if (!$activeSubscription) {
                $planPrice = $plan->getPriceForPeriod($billingPeriodMonths);
                $periodLabel = $billingPeriodMonths === 1 ? 'monthly' : "{$billingPeriodMonths} months";

                $proration = [
                    'type' => 'new_subscription',
                    'net_amount' => $planPrice,
                    'proration_details' => "New subscription - full {$periodLabel} charge",
                    'current_plan_credit' => 0,
                    'new_plan_charge' => $planPrice,
                    'remaining_days' => $billingPeriodMonths * 30,
                    'billing_period_months' => $billingPeriodMonths
                ];
            } else {
                $proration = $this->prorationService->calculatePlanChange($activeSubscription, $plan, $billingPeriodMonths);
            }
        } catch (\Exception $e) {
            // Fallback calculation
            $proration = [
                'type' => 'simple_change',
                'net_amount' => $plan->getPriceForPeriod($billingPeriodMonths),
                'proration_details' => 'Plan change - exact pricing will be calculated at checkout',
                'current_plan_credit' => 0,
                'new_plan_charge' => $plan->getPriceForPeriod($billingPeriodMonths),
                'remaining_days' => $billingPeriodMonths * 30
            ];
            \Log::warning('Proration calculation failed in preview', [
                'plan_id' => $plan->id,
                'organization_id' => $organization->id,
                'billing_period_months' => $billingPeriodMonths,
                'error' => $e->getMessage()
            ]);
        }

        $changeType = $request->get('change_type', 'immediate');

        return view('plan-change.preview', compact(
            'organization',
            'currentPlan',
            'plan',
            'proration',
            'activeSubscription',
            'changeType'
        ));
    }

    /**
     * Process plan change (redirect to payment page)
     */
    public function change(Request $request, Plan $plan)
    {
        // Adjust validation for free plans
        $validationRules = [
            'change_type' => 'required|in:immediate,end_of_cycle',
            'confirm' => 'required|accepted',
        ];

        // Only validate billing period for paid plans
        if (!$plan->isFree()) {
            $validationRules['billing_period_months'] = 'required|integer|in:1,12,24,48';
        }

        $request->validate($validationRules);

        $organization = Auth::user()->organization;
        $changeType = $request->get('change_type');
        $billingPeriodMonths = (int) $request->get('billing_period_months', 1);

        // For free plans, billing period doesn't matter
        if ($plan->isFree()) {
            $billingPeriodMonths = 1;
        }

        // Calculate proration for payment
        $activeSubscription = $organization->activeSubscription;
        try {
            if ($activeSubscription) {
                $proration = $this->prorationService->calculatePlanChange($activeSubscription, $plan, $billingPeriodMonths);
            } else {
                $planPrice = $plan->getPriceForPeriod($billingPeriodMonths);
                $periodLabel = $billingPeriodMonths === 1 ? 'monthly' : "{$billingPeriodMonths} months";

                $proration = [
                    'type' => 'new_subscription',
                    'net_amount' => $planPrice,
                    'proration_details' => "New subscription - full {$periodLabel} charge",
                    'current_plan_credit' => 0,
                    'new_plan_charge' => $planPrice,
                    'remaining_days' => $billingPeriodMonths * 30,
                    'billing_period_months' => $billingPeriodMonths
                ];
            }
        } catch (\Exception $e) {
            $proration = [
                'type' => 'simple_change',
                'net_amount' => $plan->getPriceForPeriod($billingPeriodMonths),
                'proration_details' => 'Plan change - exact pricing calculated at payment',
                'current_plan_credit' => 0,
                'new_plan_charge' => $plan->getPriceForPeriod($billingPeriodMonths),
                'remaining_days' => $billingPeriodMonths * 30
            ];
        }

        // For free plans, process immediately without payment
        if ($plan->isFree() && ($proration['net_amount'] ?? 0) == 0) {
            // Process free plan change immediately
            try {
                DB::beginTransaction();

                // Update organization plan
                $organization->update(['plan_id' => $plan->id]);

                // Create or update subscription for free plan
                $subscription = $organization->activeSubscription;
                if ($subscription) {
                    $subscription->update([
                        'plan_id' => $plan->id,
                        'status' => 'active',
                        'billing_period_months' => 1,
                        'amount_paid' => 0,
                        'amount_due' => 0,
                    ]);
                } else {
                    Subscription::create([
                        'organization_id' => $organization->id,
                        'plan_id' => $plan->id,
                        'status' => 'active',
                        'billing_period_months' => 1,
                        'start_date' => now(),
                        'end_date' => now()->addDays(14), // Free plan trial for 14 days
                        'amount_paid' => 0,
                        'amount_due' => 0,
                        'auto_renew' => false,
                    ]);
                }

                DB::commit();

                return redirect()->route('dashboard')
                    ->with('success', "Successfully switched to {$plan->name} plan!");

            } catch (\Exception $e) {
                DB::rollback();
                return redirect()->route('plan-change.index')
                    ->with('error', 'Failed to process plan change: ' . $e->getMessage());
            }
        }

        // Store plan change details in session for payment page (paid plans only)
        session([
            'plan_change' => [
                'organization_id' => $organization->id,
                'current_plan_id' => $organization->plan_id,
                'requested_plan_id' => $plan->id,
                'change_type' => $changeType,
                'billing_period_months' => $billingPeriodMonths,
                'proration' => $proration,
                'amount_due' => $proration['net_amount'] ?? 0,
            ]
        ]);

        // Redirect to payment page for paid plans
        return redirect()->route('plan-change.payment', $plan)
            ->with('success', 'Please complete payment to activate your new plan.');
    }

    /**
     * Show payment page for plan change
     */
    public function payment(Plan $plan)
    {
        $organization = Auth::user()->organization;
        $planChangeData = session('plan_change');

        // Validate session data
        if (!$planChangeData || $planChangeData['requested_plan_id'] != $plan->id) {
            return redirect()->route('plan-change.index')
                ->with('error', 'Invalid plan change session. Please try again.');
        }

        $currentPlan = $organization->plan;
        $proration = $planChangeData['proration'];
        $amountDue = $planChangeData['amount_due'];
        $selectedPeriod = $planChangeData['billing_period_months'];
        $selectedDiscount = $plan->getDiscountForPeriod($selectedPeriod);

        // Calculate pricing details for display
        $baseAmount = $plan->price * $selectedPeriod;
        $discountAmount = $baseAmount * ($selectedDiscount / 100);

        // Get payment accounts for display
        $paymentAccounts = \App\Models\PaymentAccount::active()
            ->orderBy('is_primary', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('plan-change.payment', compact(
            'organization',
            'currentPlan',
            'plan',
            'proration',
            'amountDue',
            'planChangeData',
            'selectedPeriod',
            'selectedDiscount',
            'baseAmount',
            'discountAmount',
            'paymentAccounts'
        ));
    }

    /**
     * Process payment for plan change
     */
    public function processPayment(Request $request, Plan $plan)
    {
        $request->validate([
            'payment_method' => 'required|in:bank_transfer,credit_card,paypal,cash',
            'payment_reference' => 'required|string|max:255',
            'payment_amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Convert payment amount from user currency to USD for storage
        $currencyService = app(\App\Services\CurrencyService::class);
        $userCurrency = user_currency();
        $paymentAmountUSD = $userCurrency !== 'USD'
            ? $currencyService->convert($request->payment_amount, $userCurrency, 'USD')
            : $request->payment_amount;

        $organization = Auth::user()->organization;
        $planChangeData = session('plan_change');

        // Validate session data
        if (!$planChangeData || $planChangeData['requested_plan_id'] != $plan->id) {
            return redirect()->route('plan-change.index')
                ->with('error', 'Invalid plan change session. Please try again.');
        }

        DB::beginTransaction();
        try {
            // Create or get subscription for the new plan
            $subscription = $organization->activeSubscription;
            $billingPeriodMonths = $planChangeData['billing_period_months'] ?? 1;

            if (!$subscription) {
                // Create new subscription
                $endDate = now()->addMonths($billingPeriodMonths);

                $subscription = Subscription::create([
                    'organization_id' => $organization->id,
                    'plan_id' => $plan->id,
                    'status' => 'pending', // Will be activated when payment is approved
                    'billing_period_months' => $billingPeriodMonths,
                    'start_date' => now(),
                    'end_date' => $endDate,
                    'amount_paid' => 0,
                    'amount_due' => $paymentAmountUSD,
                    'auto_renew' => true,
                ]);
            } else {
                // Update existing subscription for plan change
                $newEndDate = now()->addMonths($billingPeriodMonths);

                $subscription->update([
                    'plan_id' => $plan->id,
                    'status' => 'pending',
                    'billing_period_months' => $billingPeriodMonths,
                    'end_date' => $newEndDate,
                    'amount_due' => $subscription->amount_due + $paymentAmountUSD,
                ]);
            }

            // Create subscription payment record
            $payment = SubscriptionPayment::create([
                'subscription_id' => $subscription->id,
                'organization_id' => $organization->id,
                'payment_reference' => $request->payment_reference,
                'amount' => $paymentAmountUSD,
                'payment_method' => $request->payment_method,
                'status' => 'pending',
                'notes' => $request->notes . "\n\nPlan Change: " . ($organization->plan ? $organization->plan->name : 'No Plan') . " → " . $plan->name,
                'payment_date' => now(),
            ]);

            // Store plan change details in payment notes for admin reference
            $planChangeNotes = json_encode([
                'type' => 'plan_change',
                'current_plan_id' => $planChangeData['current_plan_id'],
                'requested_plan_id' => $planChangeData['requested_plan_id'],
                'change_type' => $planChangeData['change_type'],
                'proration' => $planChangeData['proration'],
            ]);

            $payment->update([
                'notes' => $payment->notes . "\n\nPlan Change Details: " . $planChangeNotes
            ]);

            // Log the payment submission
            Log::info('Plan change payment submitted', [
                'organization_id' => $organization->id,
                'user_id' => Auth::id(),
                'payment_id' => $payment->id,
                'subscription_id' => $subscription->id,
                'current_plan_id' => $planChangeData['current_plan_id'],
                'requested_plan_id' => $plan->id,
                'amount' => $paymentAmountUSD,
                'payment_method' => $request->payment_method,
            ]);

            DB::commit();

            // Clear session data
            session()->forget('plan_change');

            return redirect()->route('plan-change.index')
                ->with('success', 'Payment submitted successfully! Your plan change will be activated once the payment is approved by admin.');

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Plan change payment failed', [
                'organization_id' => $organization->id,
                'user_id' => Auth::id(),
                'plan_id' => $plan->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'An error occurred while processing payment: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Cancel subscription
     */
    public function cancel(Request $request)
    {
        $request->validate([
            'cancellation_reason' => 'required|string|max:500',
            'confirm' => 'required|accepted',
        ]);

        $organization = Auth::user()->organization;
        $activeSubscription = $organization->activeSubscription;

        if (!$activeSubscription) {
            return redirect()->back()
                ->with('error', 'No active subscription found.');
        }

        DB::beginTransaction();
        try {
            // Calculate refund if applicable
            $refundCalculation = $this->prorationService->calculateCancellationRefund($activeSubscription);

            // Update subscription status
            $activeSubscription->update([
                'status' => 'canceled',
                'cancellation_reason' => $request->cancellation_reason,
                'canceled_at' => now(),
            ]);

            // Log the cancellation
            Log::info('Subscription canceled', [
                'organization_id' => $organization->id,
                'user_id' => Auth::id(),
                'subscription_id' => $activeSubscription->id,
                'reason' => $request->cancellation_reason,
                'refund_amount' => $refundCalculation['refund_amount'],
            ]);

            DB::commit();

            $message = 'Subscription canceled successfully.';
            if ($refundCalculation['refund_amount'] > 0) {
                $message .= " Refund of $" . number_format($refundCalculation['refund_amount'], 2) . " will be processed.";
            }

            return redirect()->route('plan-change.index')
                ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Subscription cancellation failed', [
                'organization_id' => $organization->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to cancel subscription: ' . $e->getMessage());
        }
    }

    /**
     * Reactivate canceled subscription
     */
    public function reactivate(Request $request)
    {
        $organization = Auth::user()->organization;
        $canceledSubscription = $organization->subscriptions()
            ->where('status', 'canceled')
            ->latest()
            ->first();

        if (!$canceledSubscription) {
            return redirect()->back()
                ->with('error', 'No canceled subscription found.');
        }

        DB::beginTransaction();
        try {
            // Reactivate subscription
            $canceledSubscription->update([
                'status' => 'active',
                'cancellation_reason' => null,
                'canceled_at' => null,
                'end_date' => now()->addMonth(), // Extend for one month
            ]);

            // Log the reactivation
            Log::info('Subscription reactivated', [
                'organization_id' => $organization->id,
                'user_id' => Auth::id(),
                'subscription_id' => $canceledSubscription->id,
            ]);

            DB::commit();

            return redirect()->route('plan-change.index')
                ->with('success', 'Subscription reactivated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Subscription reactivation failed', [
                'organization_id' => $organization->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to reactivate subscription: ' . $e->getMessage());
        }
    }
}
