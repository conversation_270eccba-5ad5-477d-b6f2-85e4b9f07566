<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Organization extends Model
{
    use HasFactory;

    protected $fillable = [
        'plan_id',
        'name',
        'is_active',
        'trial_ends_at',
        'email',
        'phone',
        'address',
        'logo',
        'website',
        'description',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'trial_ends_at' => 'datetime',
    ];

    /**
     * Get the branches for the organization.
     */
    public function branches(): HasMany
    {
        return $this->hasMany(Branch::class);
    }

    /**
     * Get the users associated with the organization.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the plan associated with the organization.
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Check if organization has reached user limit.
     */
    public function hasReachedUserLimit(): bool
    {
        if (!$this->plan || $this->plan->user_limit === null) {
            return false; // No limit
        }

        return $this->users()->count() >= $this->plan->user_limit;
    }

    /**
     * Check if organization has reached branch limit.
     */
    public function hasReachedBranchLimit(): bool
    {
        if (!$this->plan || $this->plan->branch_limit === null) {
            return false; // No limit
        }

        return $this->branches()->count() >= $this->plan->branch_limit;
    }

    /**
     * Check if organization can add a new user.
     */
    public function canAddUser(): bool
    {
        return !$this->hasReachedUserLimit();
    }

    /**
     * Check if organization can add a new branch.
     */
    public function canAddBranch(): bool
    {
        return !$this->hasReachedBranchLimit();
    }

    /**
     * Check if organization has reached order limit for current month.
     */
    public function hasReachedOrderLimit(): bool
    {
        // Organizations without a plan cannot create orders
        if (!$this->plan) {
            return true;
        }

        // Plans with no limit (null) allow unlimited orders
        if ($this->plan->order_limit === null) {
            return false;
        }

        $currentMonthOrders = $this->getCurrentMonthOrderCount();
        return $currentMonthOrders >= $this->plan->order_limit;
    }

    /**
     * Check if organization can create a new order.
     */
    public function canCreateOrder(): bool
    {
        return !$this->hasReachedOrderLimit();
    }

    /**
     * Get current month order count for organization.
     */
    public function getCurrentMonthOrderCount(): int
    {
        return $this->orders()
            ->whereYear('created_at', now()->year)
            ->whereMonth('created_at', now()->month)
            ->count();
    }

    /**
     * Get remaining order slots for current month.
     */
    public function getRemainingOrderSlots(): int
    {
        if (!$this->plan || $this->plan->order_limit === null) {
            return PHP_INT_MAX; // Unlimited
        }

        $currentOrders = $this->getCurrentMonthOrderCount();
        return max(0, $this->plan->order_limit - $currentOrders);
    }

    /**
     * Get the active subscription for the organization.
     */
    public function activeSubscription(): HasOne
    {
        return $this->hasOne(Subscription::class)->where('status', 'active')
            ->whereDate('end_date', '>=', now())
            ->latest();
    }

    /**
     * Get the subscriptions for the organization.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the orders for the organization.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the plan change requests for the organization.
     */
    public function planChangeRequests(): HasMany
    {
        return $this->hasMany(PlanChangeRequest::class);
    }

    /**
     * Get the subscription payments for the organization.
     */
    public function subscriptionPayments(): HasMany
    {
        return $this->hasMany(SubscriptionPayment::class);
    }

    /**
     * Check if the organization has an active subscription.
     */
    public function hasActiveSubscription(): bool
    {
        return $this->activeSubscription !== null;
    }

    /**
     * Check if the organization is in grace period.
     */
    public function isInGracePeriod(): bool
    {
        $expiredSubscription = $this->subscriptions()
            ->where('status', 'active')
            ->where('end_date', '<', now())
            ->where('end_date', '>=', now()->subDays(7)) // 7-day grace period
            ->latest()
            ->first();

        return $expiredSubscription !== null;
    }

    /**
     * Get grace period end date.
     */
    public function getGracePeriodEndAttribute()
    {
        $expiredSubscription = $this->subscriptions()
            ->where('status', 'active')
            ->where('end_date', '<', now())
            ->latest()
            ->first();

        if ($expiredSubscription) {
            return $expiredSubscription->end_date->addDays(7);
        }

        return null;
    }

    /**
     * Check if organization has any form of access (active subscription, trial, or grace period).
     */
    public function hasAccess(): bool
    {
        return $this->hasActiveSubscription() ||
               $this->isOnTrial() ||
               $this->isInGracePeriod();
    }

    /**
     * Get the current access status.
     */
    public function getAccessStatusAttribute(): string
    {
        if ($this->hasActiveSubscription()) {
            return 'active';
        } elseif ($this->isOnTrial()) {
            return 'trial';
        } elseif ($this->isInGracePeriod()) {
            return 'grace_period';
        } else {
            return 'expired';
        }
    }

    /**
     * Check if the organization is in trial period.
     */
    public function isOnTrial(): bool
    {
        return $this->trial_ends_at !== null && now()->lt($this->trial_ends_at);
    }

    /**
     * Check if the organization has access to a specific feature.
     */
    public function hasFeature(string $feature): bool
    {
        if (!$this->plan) {
            return false;
        }

        return $this->plan->hasFeature($feature);
    }

    /**
     * Get remaining user slots.
     */
    public function getRemainingUserSlots(): int
    {
        if (!$this->plan || $this->plan->user_limit === null) {
            return PHP_INT_MAX; // Unlimited
        }

        $currentUsers = $this->users()->count();
        return max(0, $this->plan->user_limit - $currentUsers);
    }

    /**
     * Get remaining branch slots.
     */
    public function getRemainingBranchSlots(): int
    {
        if (!$this->plan || $this->plan->branch_limit === null) {
            return PHP_INT_MAX; // Unlimited
        }

        $currentBranches = $this->branches()->count();
        return max(0, $this->plan->branch_limit - $currentBranches);
    }

    /**
     * Check if organization has pending plan change requests.
     */
    public function hasPendingPlanChangeRequest(): bool
    {
        return $this->planChangeRequests()->pending()->exists();
    }

    /**
     * Get the latest pending plan change request.
     */
    public function latestPendingPlanChangeRequest()
    {
        return $this->planChangeRequests()
                    ->pending()
                    ->with(['currentPlan', 'requestedPlan', 'requestedBy'])
                    ->latest()
                    ->first();
    }

    /**
     * Get plan usage summary.
     */
    public function getPlanUsage(): array
    {
        if (!$this->plan) {
            return [
                'plan_name' => 'No Plan',
                'users' => ['current' => 0, 'limit' => 'Unlimited', 'percentage' => 0],
                'branches' => ['current' => 0, 'limit' => 'Unlimited', 'percentage' => 0],
                'orders' => ['current' => 0, 'limit' => 'Unlimited', 'percentage' => 0],
                'data_retention' => ['days' => 'Unlimited'],
            ];
        }

        $currentUsers = $this->users()->count();
        $currentBranches = $this->branches()->count();
        $currentMonthOrders = $this->getCurrentMonthOrderCount();

        return [
            'plan_name' => $this->plan->name,
            'users' => [
                'current' => $currentUsers,
                'limit' => $this->plan->user_limit ?? 'Unlimited',
                'percentage' => $this->plan->user_limit ? round(($currentUsers / $this->plan->user_limit) * 100, 1) : 0,
                'remaining' => $this->plan->user_limit ? max(0, $this->plan->user_limit - $currentUsers) : 'Unlimited',
            ],
            'branches' => [
                'current' => $currentBranches,
                'limit' => $this->plan->branch_limit ?? 'Unlimited',
                'percentage' => $this->plan->branch_limit ? round(($currentBranches / $this->plan->branch_limit) * 100, 1) : 0,
                'remaining' => $this->plan->branch_limit ? max(0, $this->plan->branch_limit - $currentBranches) : 'Unlimited',
            ],
            'orders' => [
                'current' => $currentMonthOrders,
                'limit' => $this->plan->order_limit ?? 'Unlimited',
                'percentage' => $this->plan->order_limit ? round(($currentMonthOrders / $this->plan->order_limit) * 100, 1) : 0,
                'remaining' => $this->plan->order_limit ? max(0, $this->plan->order_limit - $currentMonthOrders) : 'Unlimited',
                'period' => 'This month',
            ],
            'data_retention' => [
                'days' => $this->plan->data_retention_days ?? 'Unlimited',
            ],
        ];
    }

    /**
     * Check if organization has any form of subscription access.
     */
    public function hasAnyAccess(): bool
    {
        return $this->is_active && ($this->hasAccess() || $this->isInGracePeriod());
    }

    /**
     * Get subscription status summary.
     */
    public function getSubscriptionStatus(): array
    {
        if (!$this->is_active) {
            return [
                'status' => 'inactive',
                'message' => 'Organization account is inactive',
                'level' => 'critical'
            ];
        }

        if (!$this->plan) {
            return [
                'status' => 'no_plan',
                'message' => 'No subscription plan selected',
                'level' => 'warning'
            ];
        }

        if (!$this->hasAccess()) {
            return [
                'status' => 'expired',
                'message' => 'Subscription has expired',
                'level' => 'critical'
            ];
        }

        if ($this->isInGracePeriod()) {
            $daysLeft = now()->diffInDays($this->grace_period_end, false);
            return [
                'status' => 'grace_period',
                'message' => "Grace period active ({$daysLeft} days remaining)",
                'level' => 'warning',
                'days_left' => $daysLeft
            ];
        }

        return [
            'status' => 'active',
            'message' => 'Subscription is active',
            'level' => 'success'
        ];
    }

    /**
     * Check if organization needs immediate attention for subscription.
     */
    public function needsSubscriptionAttention(): bool
    {
        return !$this->plan || !$this->hasAccess() || $this->isInGracePeriod();
    }
}


