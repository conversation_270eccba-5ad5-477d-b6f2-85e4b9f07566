<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Order;

return new class extends Migration
{
    public function up()
    {
        // Update all existing orders with new calculation logic
        $orders = Order::all();
        foreach ($orders as $order) {
            // Recalculate all values
            $order->running_cost = $order->calculateRunningCost();
            $order->sales_commission = $order->calculateSalesCommission();
            $order->operators_commission = $order->calculateOperatorsCommission();
            $order->production_amount = $order->calculateProductionAmount();
            $order->save();
        }
    }

    public function down()
    {
        // No need for down method as we're just updating calculations
    }
}; 