<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserBelongsToOrganization
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get the model from the route, if there is one
        $parameters = $request->route()->parameters();

        foreach ($parameters as $parameter) {
            if (is_object($parameter) && property_exists($parameter, 'organization_id')) {
                // Check if the model has an organization ID and it matches the user's
                if ($parameter->organization_id != Auth::user()->organization_id) {
                    if ($request->wantsJson() || $request->ajax() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
                        return response()->json(['message' => 'You do not have permission to access this resource.'], 403);
                    }
                    abort(403, 'You do not have permission to access this resource.');
                }
            }
        }

        return $next($request);
    }
}
