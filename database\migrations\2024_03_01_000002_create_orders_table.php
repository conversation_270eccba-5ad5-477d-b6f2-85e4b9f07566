<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->string('customer_name');
            $table->string('phone_number');
            $table->string('order_title');
            $table->text('job_description');
            $table->string('department');
            $table->string('media')->nullable();
            $table->string('pages')->nullable();
            $table->string('size')->nullable();
            $table->integer('quantity');
            $table->decimal('unit_cost', 10, 2);
            $table->decimal('total_amount', 10, 2);
            $table->decimal('amount_paid', 10, 2);
            $table->decimal('pending_payment', 10, 2);
            $table->string('status');
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            $table->timestamp('status_updated_at')->nullable();
            $table->date('expected_delivery_date');
            $table->string('expected_delivery_time');
            $table->string('receiver_name')->nullable();
            $table->string('receiver_phone')->nullable();
            $table->timestamp('date_delivered')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('orders');
    }
};