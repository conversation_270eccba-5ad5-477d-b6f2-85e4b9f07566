<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckPlanLimits
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $limitType (user|branch)
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, string $limitType): Response
    {
        $user = Auth::user();

        if (!$user || !$user->organization) {
            return redirect()->route('login')->with('error', 'Please log in to access this feature.');
        }

        $organization = $user->organization;

        // Check if organization is active
        if (!$organization->is_active) {
            return redirect()->route('dashboard')->with('error', 'Your organization account is inactive. Please contact support.');
        }

        // Check if organization has any form of access
        if (!$organization->hasAccess()) {
            return redirect()->route('dashboard')->with('error', 'Your subscription has expired and grace period has ended. Please renew to continue.');
        }

        // Show warning if in grace period
        if ($organization->isInGracePeriod()) {
            $gracePeriodEnd = $organization->grace_period_end;
            $daysLeft = now()->diffInDays($gracePeriodEnd, false);
            session()->flash('warning', "Your subscription has expired. You have {$daysLeft} days remaining in your grace period. Please renew to avoid service interruption.");
        }

        // Only block POST requests (actual creation attempts), allow GET requests (viewing forms)
        if ($request->isMethod('POST')) {
            // Check specific limits based on type
            switch ($limitType) {
                case 'user':
                    if ($organization->hasReachedUserLimit()) {
                        return redirect()->back()
                            ->withErrors(['limit_exceeded' => "You have reached your {$organization->plan->name} plan limit of {$organization->plan->user_limit} users. Please upgrade your plan to add more users."])
                            ->withInput()
                            ->with('upgrade_required', true);
                    }
                    break;

                case 'branch':
                    if ($organization->hasReachedBranchLimit()) {
                        return redirect()->back()
                            ->withErrors(['limit_exceeded' => "You have reached your {$organization->plan->name} plan limit of {$organization->plan->branch_limit} branches. Please upgrade your plan to add more branches."])
                            ->withInput()
                            ->with('upgrade_required', true);
                    }
                    break;

                case 'order':
                    if ($organization->hasReachedOrderLimit()) {
                        return redirect()->back()
                            ->withErrors(['limit_exceeded' => "You have reached your {$organization->plan->name} plan limit of {$organization->plan->order_limit} orders this month. Please upgrade your plan to create more orders."])
                            ->withInput()
                            ->with('upgrade_required', true);
                    }
                    break;

                default:
                    return redirect()->back()->with('error', 'Invalid limit type specified.');
            }
        }

        return $next($request);
    }
}
