<?php

namespace App\Listeners;

use Illuminate\Auth\Events\Registered;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;
use App\Mail\WelcomeEmail;
use App\Models\Affiliate;

class SendWelcomeEmail
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        $user = $event->user;
        
        try {
            // Determine user type based on the user's context
            $userType = $this->determineUserType($user);
            
            // Send welcome email
            Mail::to($user->email)->send(new WelcomeEmail($user, $userType));
            
            // Log the welcome email sending
            \Log::info('Welcome email sent', [
                'user_id' => $user->id,
                'email' => $user->email,
                'user_type' => $userType
            ]);
            
        } catch (\Exception $e) {
            // Log the error but don't fail the registration
            \Log::error('Failed to send welcome email', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Determine the user type for welcome email.
     */
    private function determineUserType($user): string
    {
        // Check if user has an affiliate record
        $affiliate = Affiliate::where('user_id', $user->id)->first();
        if ($affiliate) {
            return 'affiliate';
        }
        
        // Check if user has an organization
        if ($user->organization_id) {
            return 'organization';
        }
        
        // Check if user is super admin (you might have a different way to check this)
        if ($user->email === '<EMAIL>' || $user->hasRole('super_admin')) {
            return 'super_admin';
        }
        
        // Default to organization type
        return 'organization';
    }
}
