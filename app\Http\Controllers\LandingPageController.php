<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Plan;

class LandingPageController extends Controller
{
    /**
     * Show the landing page
     */
    public function index()
    {
        // Get featured plans for the landing page
        $featuredPlans = Plan::where('is_active', true)
            ->where('is_featured', true)
            ->orderBy('price')
            ->take(3)
            ->get();

        // If no featured plans, get the first 3 active plans
        if ($featuredPlans->count() === 0) {
            $featuredPlans = Plan::where('is_active', true)
                ->orderBy('price')
                ->take(3)
                ->get();
        }

        return view('landing.index', compact('featuredPlans'));
    }

    /**
     * Show the about page
     */
    public function about()
    {
        return view('landing.about');
    }

    /**
     * Show the features page
     */
    public function features()
    {
        return view('landing.features');
    }

    /**
     * Show the pricing page
     */
    public function pricing()
    {
        $plans = Plan::where('is_active', true)
            ->orderBy('price')
            ->get();

        return view('landing.pricing', compact('plans'));
    }

    /**
     * Show the contact page
     */
    public function contact()
    {
        return view('landing.contact');
    }

    /**
     * Show the affiliate program page
     */
    public function affiliateProgram()
    {
        return view('landing.affiliate-program');
    }

    /**
     * Handle contact form submission
     */
    public function submitContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        // Here you can add logic to send email or store the contact form
        // For now, we'll just return a success message

        return back()->with('success', 'Thank you for your message! We\'ll get back to you soon.');
    }
}
