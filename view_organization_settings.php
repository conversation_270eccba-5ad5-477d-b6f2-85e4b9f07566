<?php
require_once 'vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;

// Get all organizations with their settings
$organizations = DB::table('organizations')
    ->select('organizations.id as org_id', 'organizations.name as org_name')
    ->get();

echo "=== ORGANIZATION SETTINGS ===\n\n";

foreach ($organizations as $org) {
    echo "Organization: {$org->org_name} (ID: {$org->org_id})\n";

    $settings = DB::table('settings')
        ->where('organization_id', $org->org_id)
        ->first();

    if ($settings) {
        echo "  - Settings ID: {$settings->id}\n";
        echo "  - App Name: {$settings->app_name}\n";
        echo "  - Organization Name: {$settings->organization_name}\n";
        echo "  - Company Address: {$settings->company_address}\n";
        echo "  - Company Email: {$settings->company_email}\n";
        echo "  - Company Phone: {$settings->company_phone}\n";
        echo "  - Timezone: {$settings->timezone}\n";
    } else {
        echo "  - No settings found for this organization\n";
    }

    echo "\n";
}

// Check for any settings without organization_id
$orphanedSettings = DB::table('settings')
    ->whereNull('organization_id')
    ->get();

if (count($orphanedSettings) > 0) {
    echo "WARNING: Found " . count($orphanedSettings) . " settings records without an organization ID.\n";
    foreach ($orphanedSettings as $setting) {
        echo "  - Settings ID {$setting->id}: {$setting->app_name} / {$setting->organization_name}\n";
    }
} else {
    echo "All settings are properly associated with organizations.\n";
}
