<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SuperAdmin;
use Illuminate\Support\Facades\Hash;

class CreateSuperAdmin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:super-admin {email} {password} {name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a super admin user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $password = $this->argument('password');
        $name = $this->argument('name');

        try {
            // Check if super admin already exists
            if (SuperAdmin::where('email', $email)->exists()) {
                $this->error("Super admin with email {$email} already exists!");
                return 1;
            }

            // Create super admin
            $superAdmin = SuperAdmin::create([
                'name' => $name,
                'email' => $email,
                'password' => Hash::make($password),
                'role' => 'super_admin',
            ]);

            $this->info('✅ Super admin created successfully!');
            $this->info("Email: {$email}");
            $this->info("Name: {$name}");
            $this->info('You can now login to the super admin dashboard.');

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Error creating super admin: ' . $e->getMessage());
            return 1;
        }
    }
}
