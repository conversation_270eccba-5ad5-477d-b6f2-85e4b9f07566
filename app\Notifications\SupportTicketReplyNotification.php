<?php

namespace App\Notifications;

use App\Models\SupportTicket;
use App\Models\SupportTicketReply;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SupportTicketReplyNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $ticket;
    public $reply;
    public $isFromAdmin;

    /**
     * Create a new notification instance.
     */
    public function __construct(SupportTicket $ticket, SupportTicketReply $reply, bool $isFromAdmin = false)
    {
        $this->ticket = $ticket;
        $this->reply = $reply;
        $this->isFromAdmin = $isFromAdmin;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $subject = $this->isFromAdmin 
            ? "Support Team Reply - Ticket #{$this->ticket->ticket_number}"
            : "New Reply on Ticket #{$this->ticket->ticket_number}";

        $greeting = $this->isFromAdmin
            ? "Hello {$notifiable->name},"
            : "Hello Support Team,";

        $message = $this->isFromAdmin
            ? "Our support team has replied to your ticket."
            : "A user has replied to their support ticket.";

        $actionText = $this->isFromAdmin
            ? "View Ticket & Reply"
            : "View Ticket";

        $actionUrl = $this->isFromAdmin
            ? route('user.support.show', $this->ticket)
            : route('super.support.tickets.show', $this->ticket);

        $mail = (new MailMessage)
            ->subject($subject)
            ->greeting($greeting)
            ->line($message)
            ->line("**Ticket:** #{$this->ticket->ticket_number}")
            ->line("**Subject:** {$this->ticket->title}")
            ->line("**Status:** " . ucfirst(str_replace('_', ' ', $this->ticket->status)))
            ->line("**Priority:** " . ucfirst($this->ticket->priority))
            ->line('')
            ->line("**Reply from " . ($this->isFromAdmin ? 'Support Team' : $this->reply->replier_name) . ":**")
            ->line($this->getFormattedMessage())
            ->action($actionText, $actionUrl);

        if ($this->isFromAdmin) {
            $mail->line('You can reply directly to this ticket through your support center.')
                 ->line('Thank you for using our support system!');
        } else {
            $mail->line('Please review and respond to the user\'s inquiry.')
                 ->line('Response time expectations apply based on ticket priority.');
        }

        return $mail;
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'ticket_id' => $this->ticket->id,
            'ticket_number' => $this->ticket->ticket_number,
            'ticket_title' => $this->ticket->title,
            'reply_id' => $this->reply->id,
            'replier_name' => $this->reply->replier_name,
            'is_from_admin' => $this->isFromAdmin,
            'message_preview' => $this->getMessagePreview(),
            'action_url' => $this->isFromAdmin
                ? route('user.support.show', $this->ticket)
                : route('super.support.tickets.show', $this->ticket),
        ];
    }

    /**
     * Get formatted message for email
     */
    private function getFormattedMessage(): string
    {
        $message = $this->reply->message;
        
        // Limit message length for email
        if (strlen($message) > 500) {
            $message = substr($message, 0, 500) . '...';
        }

        return $message;
    }

    /**
     * Get message preview for database notification
     */
    private function getMessagePreview(): string
    {
        $message = $this->reply->message;
        
        // Limit message length for preview
        if (strlen($message) > 100) {
            $message = substr($message, 0, 100) . '...';
        }

        return $message;
    }

    /**
     * Get notification title for database storage
     */
    public function getTitle(): string
    {
        return $this->isFromAdmin
            ? "Support Team Reply - Ticket #{$this->ticket->ticket_number}"
            : "New User Reply - Ticket #{$this->ticket->ticket_number}";
    }

    /**
     * Get notification icon for UI
     */
    public function getIcon(): string
    {
        return $this->isFromAdmin ? 'fas fa-headset' : 'fas fa-reply';
    }

    /**
     * Get notification color for UI
     */
    public function getColor(): string
    {
        return $this->isFromAdmin ? 'primary' : 'info';
    }
}
