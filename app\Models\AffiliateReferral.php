<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AffiliateReferral extends Model
{
    use HasFactory;

    const STATUS_PENDING = 'pending';
    const STATUS_CONVERTED = 'converted';
    const STATUS_CANCELLED = 'cancelled';

    protected $fillable = [
        'affiliate_id',
        'organization_id',
        'referral_code',
        'registration_date',
        'first_payment_date',
        'status',
        'commission_earned',
        'commission_paid',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'tracking_data',
    ];

    protected $casts = [
        'registration_date' => 'datetime',
        'first_payment_date' => 'datetime',
        'commission_earned' => 'decimal:2',
        'commission_paid' => 'boolean',
        'tracking_data' => 'array',
    ];

    /**
     * Get the affiliate that made this referral
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(Affiliate::class);
    }

    /**
     * Get the organization that was referred
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get all earnings generated from this referral
     */
    public function earnings(): HasMany
    {
        return $this->hasMany(AffiliateEarning::class, 'referral_id');
    }

    /**
     * Scope for converted referrals
     */
    public function scopeConverted($query)
    {
        return $query->where('status', self::STATUS_CONVERTED);
    }

    /**
     * Scope for pending referrals
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Check if referral is converted
     */
    public function isConverted(): bool
    {
        return $this->status === self::STATUS_CONVERTED;
    }

    /**
     * Check if referral is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Mark referral as converted
     */
    public function markAsConverted(): bool
    {
        $this->status = self::STATUS_CONVERTED;
        $this->first_payment_date = now();
        
        return $this->save();
    }

    /**
     * Calculate days since registration
     */
    public function getDaysSinceRegistrationAttribute(): int
    {
        return $this->registration_date->diffInDays(now());
    }

    /**
     * Get the total commission earned from this referral
     */
    public function getTotalCommissionAttribute(): float
    {
        return $this->earnings()->where('status', 'approved')->sum('amount');
    }

    /**
     * Get the pending commission from this referral
     */
    public function getPendingCommissionAttribute(): float
    {
        return $this->earnings()->where('status', 'pending')->sum('amount');
    }

    /**
     * Check if commission has been paid
     */
    public function isCommissionPaid(): bool
    {
        return $this->commission_paid;
    }

    /**
     * Mark commission as paid
     */
    public function markCommissionAsPaid(): bool
    {
        $this->commission_paid = true;
        return $this->save();
    }

    /**
     * Get UTM parameters as string
     */
    public function getUtmStringAttribute(): string
    {
        $utm = [];
        if ($this->utm_source) $utm[] = "utm_source={$this->utm_source}";
        if ($this->utm_medium) $utm[] = "utm_medium={$this->utm_medium}";
        if ($this->utm_campaign) $utm[] = "utm_campaign={$this->utm_campaign}";
        
        return implode('&', $utm);
    }
}
