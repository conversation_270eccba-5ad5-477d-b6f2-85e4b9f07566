@extends('super_admin.layouts.app')

@section('title', 'System Check - Email Testing')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">🔍 System Check - Email Testing</h1>
                <div class="btn-group" role="group">
                    <a href="{{ route('super_admin.email-testing.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Back to Email Testing
                    </a>
                    <a href="{{ route('super_admin.email-testing.account-creation') }}" class="btn btn-outline-success">
                        <i class="fas fa-user-plus"></i> Account Creation Testing
                    </a>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle"></i> 
                    @foreach($errors->all() as $error)
                        {{ $error }}<br>
                    @endforeach
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- System Readiness Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-heartbeat"></i> System Readiness Status
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-3">Comprehensive verification for affiliate account creation with <strong>{{ $testEmail }}</strong></p>
                    
                    @php
                        $systemReady = true;
                        foreach ($checks as $check) {
                            if ($check['status'] === 'error') {
                                $systemReady = false;
                                break;
                            }
                        }
                    @endphp

                    @foreach($checks as $check)
                        <div class="d-flex align-items-center mb-2">
                            @if($check['status'] === 'good')
                                <i class="fas fa-check-circle text-success me-2"></i>
                            @elseif($check['status'] === 'warning')
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                            @else
                                <i class="fas fa-times-circle text-danger me-2"></i>
                            @endif
                            <strong>{{ $check['name'] }}:</strong>
                            <span class="ms-2">{{ $check['details'] }}</span>
                        </div>
                    @endforeach

                    <hr>
                    @if($systemReady)
                        <div class="alert alert-success mb-0">
                            <i class="fas fa-rocket"></i> <strong>System is ready for affiliate account creation!</strong>
                        </div>
                    @else
                        <div class="alert alert-danger mb-0">
                            <i class="fas fa-exclamation-triangle"></i> <strong>Please fix the issues above before creating the affiliate account.</strong>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Email Configuration Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog"></i> Email Configuration Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Mail Driver:</strong></td>
                                    <td>{{ $mailConfig['mailer'] }}</td>
                                </tr>
                                <tr>
                                    <td><strong>SMTP Host:</strong></td>
                                    <td>{{ $mailConfig['host'] }}</td>
                                </tr>
                                <tr>
                                    <td><strong>SMTP Port:</strong></td>
                                    <td>{{ $mailConfig['port'] }}</td>
                                </tr>
                                <tr>
                                    <td><strong>From Address:</strong></td>
                                    <td>{{ $mailConfig['from'] }}</td>
                                </tr>
                                <tr>
                                    <td><strong>From Name:</strong></td>
                                    <td>{{ $mailConfig['from_name'] }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-{{ $mailConfig['host'] === 'live.smtp.mailtrap.io' ? 'success' : 'warning' }} mb-0">
                                <i class="fas fa-{{ $mailConfig['host'] === 'live.smtp.mailtrap.io' ? 'check-circle' : 'exclamation-triangle' }}"></i>
                                @if($mailConfig['host'] === 'live.smtp.mailtrap.io')
                                    Mailtrap configuration detected and ready for testing
                                @else
                                    Mailtrap not configured - please check email settings
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Welcome Messages Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-envelope-open-text"></i> Welcome Messages Status
                    </h5>
                </div>
                <div class="card-body">
                    @if($welcomeMessages->count() > 0)
                        <div class="row">
                            @foreach($welcomeMessages as $message)
                                <div class="col-md-4 mb-3">
                                    <div class="card border-{{ $message->is_active ? 'success' : 'secondary' }}">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ ucfirst($message->user_type) }}</h6>
                                            <p class="card-text small">{{ Str::limit($message->subject, 50) }}</p>
                                            <span class="badge bg-{{ $message->is_active ? 'success' : 'secondary' }}">
                                                {{ $message->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="alert alert-warning mb-0">
                            <i class="fas fa-exclamation-triangle"></i> 
                            No welcome messages found. Please initialize default messages.
                        </div>
                    @endif
                </div>
            </div>

            <!-- Email Testing -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-paper-plane"></i> Email Testing
                    </h5>
                </div>
                <div class="card-body">
                    <p>Test email functionality before creating the affiliate account:</p>
                    <form method="POST" action="{{ route('super_admin.email-testing.send-system-check-test') }}">
                        @csrf
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="test_email" class="form-label">Test Email Address</label>
                                    <input type="email" class="form-control" id="test_email" name="test_email" 
                                           value="{{ $testEmail }}" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-paper-plane"></i> Send System Check Test Email
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <form method="POST" action="{{ route('super_admin.email-testing.send-welcome-test') }}">
                                @csrf
                                <input type="hidden" name="test_email" value="{{ $testEmail }}">
                                <input type="hidden" name="user_type" value="affiliate">
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="fas fa-heart"></i> Test Affiliate Welcome Email
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <a href="{{ route('super_admin.email-testing.auth-emails') }}" class="btn btn-info w-100">
                                <i class="fas fa-shield-alt"></i> Test Authentication Emails
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            @if($existingUser)
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle"></i> Existing Account Found
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <strong>Warning:</strong> An account with email <code>{{ $testEmail }}</code> already exists in the system.
                        </div>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>User ID:</strong></td>
                                <td>{{ $existingUser->id }}</td>
                            </tr>
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>{{ $existingUser->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>{{ $existingUser->email }}</td>
                            </tr>
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>{{ $existingUser->created_at->format('Y-m-d H:i:s') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Email Verified:</strong></td>
                                <td>
                                    @if($existingUser->email_verified_at)
                                        <span class="badge bg-success">Yes</span>
                                    @else
                                        <span class="badge bg-warning">No</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            @endif

            <!-- Next Steps -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-route"></i> Next Steps
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📋 Testing Process</h6>
                            <ol class="small">
                                <li>Send system check test email</li>
                                <li>Test affiliate welcome email template</li>
                                <li>Check Mailtrap inbox for emails</li>
                                <li>Create affiliate account (if email available)</li>
                                <li>Verify welcome email received</li>
                                <li>Test login and dashboard access</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6>🚀 Quick Actions</h6>
                            <div class="d-grid gap-2">
                                <a href="{{ route('super_admin.email-testing.account-creation') }}" class="btn btn-success">
                                    <i class="fas fa-user-plus"></i> Create Test Accounts
                                </a>
                                <a href="{{ url('/affiliate/register') }}" class="btn btn-outline-primary" target="_blank">
                                    <i class="fas fa-external-link-alt"></i> Affiliate Registration Form
                                </a>
                                <a href="{{ route('super_admin.welcome-messages.index') }}" class="btn btn-outline-info">
                                    <i class="fas fa-envelope-open-text"></i> Manage Welcome Messages
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Details for Testing -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clipboard-list"></i> Suggested Account Details for Testing
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Personal Information</h6>
                            <ul class="list-unstyled small">
                                <li><strong>Email:</strong> {{ $testEmail }}</li>
                                <li><strong>Name:</strong> Izzy Kuro</li>
                                <li><strong>Phone:</strong> +**********</li>
                                <li><strong>Password:</strong> SecurePassword123!</li>
                                <li><strong>Bio:</strong> Test affiliate account for email functionality testing</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Additional Information</h6>
                            <ul class="list-unstyled small">
                                <li><strong>Website:</strong> https://example.com</li>
                                <li><strong>Social Media:</strong> @izzykuro01</li>
                                <li><strong>Bank Name:</strong> Test Bank</li>
                                <li><strong>Account Number:</strong> **********</li>
                                <li><strong>Account Name:</strong> Izzy Kuro</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
