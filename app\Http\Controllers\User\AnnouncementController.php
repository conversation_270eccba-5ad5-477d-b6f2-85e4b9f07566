<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Announcement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AnnouncementController extends Controller
{
    /**
     * Get announcements for current user
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            \Log::warning('Announcement API called without authenticated user');
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $audience = $this->getUserAudience($user);

        // Enhanced debug logging
        \Log::info('Announcement API called', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_organization_id' => $user->organization_id,
            'audience' => $audience,
            'request_url' => $request->fullUrl(),
            'user_agent' => $request->userAgent()
        ]);

        // Get announcements with detailed logging
        $query = Announcement::active()
            ->published()
            ->current()
            ->forAudience($audience)
            ->forDashboard();

        // Log the SQL query for debugging
        \Log::info('Announcement query SQL', [
            'sql' => $query->toSql(),
            'bindings' => $query->getBindings()
        ]);

        $announcements = $query->with(['userInteractions' => function($query) use ($user) {
                $query->where('user_id', $user->id);
            }])
            ->orderBy('priority', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        \Log::info('Announcements found', [
            'count' => $announcements->count(),
            'announcement_ids' => $announcements->pluck('id')->toArray()
        ]);

        \Log::info('Announcements found', [
            'count' => $announcements->count(),
            'announcements' => $announcements->pluck('id', 'title')
        ]);

        // Filter out dismissed announcements if they are dismissible
        $announcements = $announcements->filter(function($announcement) use ($user) {
            if (!$announcement->is_dismissible) {
                return true;
            }
            return !$announcement->isDismissedByUser($user->id);
        });

        \Log::info('Announcements after filtering dismissed ones', [
            'count' => $announcements->count(),
            'filtered_announcement_ids' => $announcements->pluck('id')->toArray()
        ]);

        $responseData = [
            'announcements' => $announcements->values()->map(function($announcement) {
                return [
                    'id' => $announcement->id,
                    'title' => $announcement->title,
                    'content' => $announcement->content,
                    'type' => $announcement->type,
                    'priority' => $announcement->priority,
                    'is_dismissible' => $announcement->is_dismissible,
                    'alert_class' => $announcement->alert_class,
                    'icon' => $announcement->icon,
                    'affected_features' => $announcement->affected_features,
                    'starts_at' => $announcement->starts_at,
                    'ends_at' => $announcement->ends_at,
                ];
            })
        ];

        \Log::info('Returning announcement response', [
            'response_count' => count($responseData['announcements']),
            'has_announcements' => !empty($responseData['announcements'])
        ]);

        return response()->json($responseData);
    }

    /**
     * Get announcements for login page
     */
    public function forLogin(Request $request)
    {
        $announcements = Announcement::active()
            ->published()
            ->current()
            ->forAudience('all')
            ->forLogin()
            ->orderBy('priority', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'announcements' => $announcements->map(function($announcement) {
                return [
                    'id' => $announcement->id,
                    'title' => $announcement->title,
                    'content' => $announcement->content,
                    'type' => $announcement->type,
                    'priority' => $announcement->priority,
                    'alert_class' => $announcement->alert_class,
                    'icon' => $announcement->icon,
                ];
            })
        ]);
    }

    /**
     * Mark announcement as read
     */
    public function markAsRead(Request $request, Announcement $announcement)
    {
        $user = Auth::user();

        $announcement->markAsReadByUser($user->id);

        return response()->json(['success' => true]);
    }

    /**
     * Dismiss announcement
     */
    public function dismiss(Request $request, Announcement $announcement)
    {
        $user = Auth::user();

        if (!$announcement->is_dismissible) {
            return response()->json(['error' => 'This announcement cannot be dismissed'], 400);
        }

        $announcement->markAsDismissedByUser($user->id);

        return response()->json(['success' => true]);
    }

    /**
     * Get maintenance status
     */
    public function maintenanceStatus()
    {
        $maintenanceAnnouncements = Announcement::active()
            ->published()
            ->current()
            ->where('type', Announcement::TYPE_MAINTENANCE)
            ->orderBy('priority', 'desc')
            ->get();

        $isInMaintenance = $maintenanceAnnouncements->isNotEmpty();
        $affectedFeatures = $maintenanceAnnouncements->pluck('affected_features')->flatten()->unique();

        return response()->json([
            'is_in_maintenance' => $isInMaintenance,
            'affected_features' => $affectedFeatures,
            'announcements' => $maintenanceAnnouncements->map(function($announcement) {
                return [
                    'id' => $announcement->id,
                    'title' => $announcement->title,
                    'content' => $announcement->content,
                    'starts_at' => $announcement->starts_at,
                    'ends_at' => $announcement->ends_at,
                    'affected_features' => $announcement->affected_features,
                ];
            })
        ]);
    }

    /**
     * Determine user audience type
     */
    private function getUserAudience($user)
    {
        // Determine if user is part of an organization
        if ($user->organization_id) {
            return 'organizations';
        }

        // Default to customers
        return 'customers';
    }
}
