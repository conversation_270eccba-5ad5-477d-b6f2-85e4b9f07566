<?php

namespace App\Services;

use App\Models\CurrencySetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class IpGeolocationService
{
    /**
     * Detect currency based on IP address
     */
    public function detectCurrencyByIp($ipAddress = null)
    {
        if (!CurrencySetting::isAutoCurrencyDetectionEnabled()) {
            return CurrencySetting::getDefaultCurrency();
        }

        $ipAddress = $ipAddress ?: request()->ip();
        
        // Handle local/private IPs
        if ($this->isLocalIp($ipAddress)) {
            return CurrencySetting::getDefaultCurrency();
        }

        // Check cache first
        $cacheKey = "currency_detection_{$ipAddress}";
        $cachedCurrency = Cache::get($cacheKey);
        
        if ($cachedCurrency) {
            return $cachedCurrency;
        }

        $currency = $this->determineCurrencyByIp($ipAddress);
        
        // Cache for 24 hours
        Cache::put($cacheKey, $currency, now()->addHours(24));
        
        return $currency;
    }

    /**
     * Determine currency based on IP address
     */
    protected function determineCurrencyByIp($ipAddress)
    {
        try {
            // First check if IP is in Nigerian ranges
            if ($this->isNigerianIp($ipAddress)) {
                return 'NGN';
            }

            // Try to get country from IP geolocation service
            $country = $this->getCountryFromIp($ipAddress);
            
            if ($country === 'NG' || $country === 'Nigeria') {
                return 'NGN';
            }

            // Default to USD for all other countries
            return 'USD';
            
        } catch (\Exception $e) {
            Log::warning('IP geolocation failed', [
                'ip' => $ipAddress,
                'error' => $e->getMessage()
            ]);
            
            return CurrencySetting::getDefaultCurrency();
        }
    }

    /**
     * Check if IP is in Nigerian IP ranges
     */
    protected function isNigerianIp($ipAddress)
    {
        $nigerianRanges = CurrencySetting::getNigerianIpRanges();
        
        foreach ($nigerianRanges as $range) {
            if ($this->ipInRange($ipAddress, $range)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if IP is in a given range
     */
    protected function ipInRange($ip, $range)
    {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }

        list($subnet, $bits) = explode('/', $range);
        
        if ($bits === null) {
            $bits = 32;
        }

        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;
        
        return ($ip & $mask) === $subnet;
    }

    /**
     * Get country from IP using geolocation service
     */
    protected function getCountryFromIp($ipAddress)
    {
        try {
            // Using ip-api.com (free service)
            $response = Http::timeout(5)->get("http://ip-api.com/json/{$ipAddress}", [
                'fields' => 'status,country,countryCode'
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if ($data['status'] === 'success') {
                    return $data['countryCode'];
                }
            }

            // Fallback to ipinfo.io
            $response = Http::timeout(5)->get("https://ipinfo.io/{$ipAddress}/json");
            
            if ($response->successful()) {
                $data = $response->json();
                return $data['country'] ?? null;
            }

        } catch (\Exception $e) {
            Log::warning('Geolocation API failed', [
                'ip' => $ipAddress,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Check if IP is local/private
     */
    protected function isLocalIp($ipAddress)
    {
        return filter_var(
            $ipAddress,
            FILTER_VALIDATE_IP,
            FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE
        ) === false;
    }

    /**
     * Get user location info
     */
    public function getLocationInfo($ipAddress = null)
    {
        $ipAddress = $ipAddress ?: request()->ip();
        
        if ($this->isLocalIp($ipAddress)) {
            return [
                'ip' => $ipAddress,
                'country' => 'Local',
                'country_code' => 'LOCAL',
                'currency' => CurrencySetting::getDefaultCurrency(),
                'is_local' => true
            ];
        }

        $cacheKey = "location_info_{$ipAddress}";
        $cachedInfo = Cache::get($cacheKey);
        
        if ($cachedInfo) {
            return $cachedInfo;
        }

        try {
            $response = Http::timeout(5)->get("http://ip-api.com/json/{$ipAddress}", [
                'fields' => 'status,country,countryCode,regionName,city,timezone'
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if ($data['status'] === 'success') {
                    $locationInfo = [
                        'ip' => $ipAddress,
                        'country' => $data['country'],
                        'country_code' => $data['countryCode'],
                        'region' => $data['regionName'] ?? null,
                        'city' => $data['city'] ?? null,
                        'timezone' => $data['timezone'] ?? null,
                        'currency' => $this->detectCurrencyByIp($ipAddress),
                        'is_local' => false
                    ];
                    
                    // Cache for 24 hours
                    Cache::put($cacheKey, $locationInfo, now()->addHours(24));
                    
                    return $locationInfo;
                }
            }
        } catch (\Exception $e) {
            Log::warning('Failed to get location info', [
                'ip' => $ipAddress,
                'error' => $e->getMessage()
            ]);
        }

        // Fallback
        return [
            'ip' => $ipAddress,
            'country' => 'Unknown',
            'country_code' => 'UNKNOWN',
            'currency' => $this->detectCurrencyByIp($ipAddress),
            'is_local' => false
        ];
    }
}
