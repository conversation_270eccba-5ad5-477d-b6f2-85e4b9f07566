<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Announcement extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'type',
        'priority',
        'target_audience',
        'is_active',
        'is_dismissible',
        'show_on_login',
        'show_on_dashboard',
        'send_email',
        'affected_features',
        'starts_at',
        'ends_at',
        'published_at',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_dismissible' => 'boolean',
        'show_on_login' => 'boolean',
        'show_on_dashboard' => 'boolean',
        'send_email' => 'boolean',
        'affected_features' => 'array',
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'published_at' => 'datetime',
    ];

    // Type constants
    const TYPE_INFO = 'info';
    const TYPE_WARNING = 'warning';
    const TYPE_SUCCESS = 'success';
    const TYPE_DANGER = 'danger';
    const TYPE_MAINTENANCE = 'maintenance';

    // Priority constants
    const PRIORITY_LOW = 'low';
    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    // Target audience constants
    const AUDIENCE_ALL = 'all';
    const AUDIENCE_CUSTOMERS = 'customers';
    const AUDIENCE_ORGANIZATIONS = 'organizations';
    const AUDIENCE_ADMINS = 'admins';

    /**
     * Get the super admin who created this announcement
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(SuperAdmin::class, 'created_by');
    }

    /**
     * Get user interactions with this announcement
     */
    public function userInteractions(): HasMany
    {
        return $this->hasMany(UserAnnouncementInteraction::class);
    }

    /**
     * Scope for active announcements
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for published announcements
     */
    public function scopePublished($query)
    {
        return $query->whereNotNull('published_at')
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope for current announcements (within date range)
     */
    public function scopeCurrent($query)
    {
        return $query->where(function($q) {
            $q->whereNull('starts_at')
              ->orWhere('starts_at', '<=', now());
        })->where(function($q) {
            $q->whereNull('ends_at')
              ->orWhere('ends_at', '>=', now());
        });
    }

    /**
     * Scope for specific target audience
     */
    public function scopeForAudience($query, $audience)
    {
        return $query->where(function($q) use ($audience) {
            $q->where('target_audience', 'all')
              ->orWhere('target_audience', $audience);
        });
    }

    /**
     * Scope for dashboard display
     */
    public function scopeForDashboard($query)
    {
        return $query->where('show_on_dashboard', true);
    }

    /**
     * Scope for login display
     */
    public function scopeForLogin($query)
    {
        return $query->where('show_on_login', true);
    }

    /**
     * Check if announcement is currently active
     */
    public function isCurrentlyActive(): bool
    {
        if (!$this->is_active || !$this->published_at || $this->published_at > now()) {
            return false;
        }

        if ($this->starts_at && $this->starts_at > now()) {
            return false;
        }

        if ($this->ends_at && $this->ends_at < now()) {
            return false;
        }

        return true;
    }

    /**
     * Check if announcement is scheduled for future
     */
    public function isScheduled(): bool
    {
        return $this->starts_at && $this->starts_at > now();
    }

    /**
     * Check if announcement has expired
     */
    public function hasExpired(): bool
    {
        return $this->ends_at && $this->ends_at < now();
    }

    /**
     * Get type badge HTML
     */
    public function getTypeBadgeAttribute(): string
    {
        $badges = [
            self::TYPE_INFO => '<span class="badge bg-info">Info</span>',
            self::TYPE_WARNING => '<span class="badge bg-warning">Warning</span>',
            self::TYPE_SUCCESS => '<span class="badge bg-success">Success</span>',
            self::TYPE_DANGER => '<span class="badge bg-danger">Alert</span>',
            self::TYPE_MAINTENANCE => '<span class="badge bg-secondary">Maintenance</span>',
        ];

        return $badges[$this->type] ?? '<span class="badge bg-secondary">Unknown</span>';
    }

    /**
     * Get priority badge HTML
     */
    public function getPriorityBadgeAttribute(): string
    {
        $badges = [
            self::PRIORITY_LOW => '<span class="badge bg-light text-dark">Low</span>',
            self::PRIORITY_NORMAL => '<span class="badge bg-primary">Normal</span>',
            self::PRIORITY_HIGH => '<span class="badge bg-warning">High</span>',
            self::PRIORITY_URGENT => '<span class="badge bg-danger">Urgent</span>',
        ];

        return $badges[$this->priority] ?? '<span class="badge bg-secondary">Unknown</span>';
    }

    /**
     * Get status badge HTML
     */
    public function getStatusBadgeAttribute(): string
    {
        if (!$this->is_active) {
            return '<span class="badge bg-secondary">Inactive</span>';
        }

        if (!$this->published_at || $this->published_at > now()) {
            return '<span class="badge bg-warning">Draft</span>';
        }

        if ($this->isScheduled()) {
            return '<span class="badge bg-info">Scheduled</span>';
        }

        if ($this->hasExpired()) {
            return '<span class="badge bg-light text-dark">Expired</span>';
        }

        return '<span class="badge bg-success">Active</span>';
    }

    /**
     * Get CSS class for announcement type
     */
    public function getAlertClassAttribute(): string
    {
        $classes = [
            self::TYPE_INFO => 'alert-info',
            self::TYPE_WARNING => 'alert-warning',
            self::TYPE_SUCCESS => 'alert-success',
            self::TYPE_DANGER => 'alert-danger',
            self::TYPE_MAINTENANCE => 'alert-secondary',
        ];

        return $classes[$this->type] ?? 'alert-info';
    }

    /**
     * Get icon for announcement type
     */
    public function getIconAttribute(): string
    {
        $icons = [
            self::TYPE_INFO => 'fas fa-info-circle',
            self::TYPE_WARNING => 'fas fa-exclamation-triangle',
            self::TYPE_SUCCESS => 'fas fa-check-circle',
            self::TYPE_DANGER => 'fas fa-exclamation-circle',
            self::TYPE_MAINTENANCE => 'fas fa-tools',
        ];

        return $icons[$this->type] ?? 'fas fa-info-circle';
    }

    /**
     * Get type options for forms
     */
    public static function getTypeOptions(): array
    {
        return [
            self::TYPE_INFO => 'Information',
            self::TYPE_WARNING => 'Warning',
            self::TYPE_SUCCESS => 'Success',
            self::TYPE_DANGER => 'Alert/Danger',
            self::TYPE_MAINTENANCE => 'Maintenance',
        ];
    }

    /**
     * Get priority options for forms
     */
    public static function getPriorityOptions(): array
    {
        return [
            self::PRIORITY_LOW => 'Low',
            self::PRIORITY_NORMAL => 'Normal',
            self::PRIORITY_HIGH => 'High',
            self::PRIORITY_URGENT => 'Urgent',
        ];
    }

    /**
     * Get target audience options for forms
     */
    public static function getTargetAudienceOptions(): array
    {
        return [
            self::AUDIENCE_ALL => 'All Users',
            self::AUDIENCE_CUSTOMERS => 'Customers Only',
            self::AUDIENCE_ORGANIZATIONS => 'Organizations Only',
            self::AUDIENCE_ADMINS => 'Admins Only',
        ];
    }

    /**
     * Check if user has dismissed this announcement
     */
    public function isDismissedByUser($userId): bool
    {
        return $this->userInteractions()
                   ->where('user_id', $userId)
                   ->where('is_dismissed', true)
                   ->exists();
    }

    /**
     * Mark as read by user
     */
    public function markAsReadByUser($userId): void
    {
        $this->userInteractions()->updateOrCreate(
            ['user_id' => $userId],
            ['is_read' => true, 'read_at' => now()]
        );
    }

    /**
     * Mark as dismissed by user
     */
    public function markAsDismissedByUser($userId): void
    {
        $this->userInteractions()->updateOrCreate(
            ['user_id' => $userId],
            ['is_dismissed' => true, 'dismissed_at' => now()]
        );
    }
}
