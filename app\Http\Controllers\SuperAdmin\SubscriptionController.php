<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\Organization;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SubscriptionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Subscription::with(['organization', 'plan']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->whereHas('organization', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            })->orWhereHas('plan', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        // Filter by plan
        if ($request->has('plan') && $request->plan) {
            $query->where('plan_id', $request->plan);
        }

        // Filter by date range
        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('start_date', '>=', $request->date_from);
        }
        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('end_date', '<=', $request->date_to);
        }

        $subscriptions = $query->latest()->paginate(15);
        $plans = Plan::where('is_active', true)->get();

        return view('super_admin.subscriptions.index', compact('subscriptions', 'plans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $organizations = Organization::where('is_active', true)->get();
        $plans = Plan::where('is_active', true)->get();

        return view('super_admin.subscriptions.create', compact('organizations', 'plans'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'organization_id' => 'required|exists:organizations,id',
            'plan_id' => 'required|exists:plans,id',
            'status' => 'required|in:active,canceled,past_due,trial,expired',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'trial_ends_at' => 'nullable|date',
            'payment_method' => 'nullable|string|max:50',
            'amount_paid' => 'required|numeric|min:0',
            'auto_renew' => 'boolean',
        ]);

        DB::beginTransaction();
        try {
            $subscription = Subscription::create($validated);

            // Update organization's plan
            $organization = Organization::find($validated['organization_id']);
            $organization->update(['plan_id' => $validated['plan_id']]);

            DB::commit();
            return redirect()->route('super.subscriptions.index')
                ->with('success', 'Subscription created successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to create subscription: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Subscription $subscription)
    {
        $subscription->load(['organization', 'plan']);

        return view('super_admin.subscriptions.show', compact('subscription'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Subscription $subscription)
    {
        $organizations = Organization::where('is_active', true)->get();
        $plans = Plan::where('is_active', true)->get();

        return view('super_admin.subscriptions.edit', compact('subscription', 'organizations', 'plans'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Subscription $subscription)
    {
        $validated = $request->validate([
            'organization_id' => 'required|exists:organizations,id',
            'plan_id' => 'required|exists:plans,id',
            'status' => 'required|in:active,canceled,past_due,trial,expired',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'trial_ends_at' => 'nullable|date',
            'payment_method' => 'nullable|string|max:50',
            'amount_paid' => 'required|numeric|min:0',
            'auto_renew' => 'boolean',
            'cancellation_reason' => 'nullable|string|max:500',
        ]);

        DB::beginTransaction();
        try {
            $subscription->update($validated);

            // Update organization's plan if changed
            if ($subscription->organization_id == $validated['organization_id'] &&
                $subscription->plan_id != $validated['plan_id']) {
                $organization = Organization::find($validated['organization_id']);
                $organization->update(['plan_id' => $validated['plan_id']]);
            }

            DB::commit();
            return redirect()->route('super.subscriptions.show', $subscription)
                ->with('success', 'Subscription updated successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to update subscription: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Subscription $subscription)
    {
        DB::beginTransaction();
        try {
            $subscription->delete();

            DB::commit();
            return redirect()->route('super.subscriptions.index')
                ->with('success', 'Subscription deleted successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to delete subscription: ' . $e->getMessage()]);
        }
    }

    /**
     * Cancel a subscription
     */
    public function cancel(Request $request, Subscription $subscription)
    {
        $request->validate([
            'cancellation_reason' => 'required|string|max:500',
        ]);

        DB::beginTransaction();
        try {
            $subscription->update([
                'status' => 'canceled',
                'cancellation_reason' => $request->cancellation_reason,
                'auto_renew' => false,
            ]);

            DB::commit();
            return back()->with('success', 'Subscription canceled successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to cancel subscription: ' . $e->getMessage()]);
        }
    }

    /**
     * Reactivate a subscription
     */
    public function reactivate(Subscription $subscription)
    {
        DB::beginTransaction();
        try {
            $subscription->update([
                'status' => 'active',
                'cancellation_reason' => null,
                'auto_renew' => true,
                'end_date' => now()->addMonth(), // Extend by one month
            ]);

            DB::commit();
            return back()->with('success', 'Subscription reactivated successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to reactivate subscription: ' . $e->getMessage()]);
        }
    }

    /**
     * Extend a subscription
     */
    public function extend(Request $request, Subscription $subscription)
    {
        $request->validate([
            'extend_months' => 'required|integer|min:1|max:12',
        ]);

        DB::beginTransaction();
        try {
            $currentEndDate = Carbon::parse($subscription->end_date);
            $newEndDate = $currentEndDate->addMonths($request->extend_months);

            $subscription->update([
                'end_date' => $newEndDate,
            ]);

            DB::commit();
            return back()->with('success', "Subscription extended by {$request->extend_months} month(s).");
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to extend subscription: ' . $e->getMessage()]);
        }
    }
}
