<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Branch extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'address',
        'phone',
        'email',
        'description',
        'organization_id',
    ];

    /**
     * Get the organization that owns the branch.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the users associated with this branch.
     */
    public function users(): Has<PERSON>any
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the orders associated with this branch.
     */
    public function orders(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the customers associated with this branch.
     */
    public function customers(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Customer::class);
    }
}
