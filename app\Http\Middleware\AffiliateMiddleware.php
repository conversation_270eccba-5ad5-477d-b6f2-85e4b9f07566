<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Affiliate;
use Illuminate\Support\Facades\Auth;

class AffiliateMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated with affiliate guard
        if (!Auth::guard('affiliate')->check()) {
            return redirect()->route('affiliate.login')
                ->with('error', 'Please login to access the affiliate dashboard.');
        }

        $user = Auth::guard('affiliate')->user();

        // Check if email is verified
        if (!$user->hasVerifiedEmail()) {
            return redirect()->route('affiliate.verification.notice')
                ->with('info', 'Please verify your email address to continue.');
        }

        // Check if user has an affiliate account
        $affiliate = Affiliate::where('user_id', $user->id)->first();

        if (!$affiliate) {
            Auth::guard('affiliate')->logout();
            return redirect()->route('affiliate.register')
                ->with('error', 'You need to register as an affiliate first.');
        }

        // Check if affiliate is pending approval
        if ($affiliate->status === Affiliate::STATUS_PENDING) {
            return redirect()->route('affiliate.pending')
                ->with('info', 'Your affiliate account is pending approval.');
        }

        // Check if affiliate is active
        if ($affiliate->status !== Affiliate::STATUS_ACTIVE) {
            Auth::guard('affiliate')->logout();
            return redirect()->route('affiliate.login')
                ->with('error', 'Your affiliate account is not active. Please contact support.');
        }

        // Add affiliate to request for easy access in controllers
        $request->merge(['affiliate' => $affiliate]);

        return $next($request);
    }
}
