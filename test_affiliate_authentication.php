<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Affiliate;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

echo "=== Testing Affiliate Authentication System ===\n\n";

try {
    // 1. Check if there are existing affiliate users
    echo "1. Checking existing affiliate users...\n";
    $affiliates = Affiliate::with('user')->get();
    
    if ($affiliates->count() > 0) {
        echo "   Found {$affiliates->count()} affiliate(s):\n";
        foreach ($affiliates as $affiliate) {
            echo "   - {$affiliate->user->name} ({$affiliate->user->email}) - Status: {$affiliate->status}\n";
        }
    } else {
        echo "   No affiliates found. Creating a test affiliate...\n";
        
        // Create a test affiliate user
        DB::beginTransaction();
        
        $user = User::create([
            'name' => 'Test Affiliate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '+1234567890',
            'status' => 'active',
        ]);
        
        $affiliate = Affiliate::create([
            'user_id' => $user->id,
            'referral_code' => 'TEST' . strtoupper(substr(md5($user->id), 0, 6)),
            'status' => Affiliate::STATUS_ACTIVE,
            'commission_rate' => 10.00,
            'total_earnings' => 0,
            'available_balance' => 0,
            'pending_balance' => 0,
            'withdrawn_amount' => 0,
        ]);
        
        DB::commit();
        
        echo "   Created test affiliate:\n";
        echo "   - Name: {$user->name}\n";
        echo "   - Email: {$user->email}\n";
        echo "   - Password: password123\n";
        echo "   - Referral Code: {$affiliate->referral_code}\n";
        echo "   - Status: {$affiliate->status}\n";
    }
    
    echo "\n2. Testing affiliate authentication routes...\n";
    
    // Test if affiliate routes are accessible
    $routes = [
        'affiliate.login' => '/affiliate/login',
        'affiliate.register' => '/affiliate/register',
        'affiliate.dashboard' => '/affiliate/dashboard',
    ];
    
    foreach ($routes as $name => $path) {
        try {
            $url = route($name);
            echo "   ✓ Route '{$name}' -> {$url}\n";
        } catch (Exception $e) {
            echo "   ❌ Route '{$name}' -> ERROR: {$e->getMessage()}\n";
        }
    }
    
    echo "\n3. Testing authentication guards...\n";
    
    // Test guard configuration
    $guards = config('auth.guards');
    if (isset($guards['affiliate'])) {
        echo "   ✓ Affiliate guard configured:\n";
        echo "     - Driver: {$guards['affiliate']['driver']}\n";
        echo "     - Provider: {$guards['affiliate']['provider']}\n";
        
        $providers = config('auth.providers');
        if (isset($providers['affiliates'])) {
            echo "   ✓ Affiliate provider configured:\n";
            echo "     - Driver: {$providers['affiliates']['driver']}\n";
            echo "     - Model: {$providers['affiliates']['model']}\n";
        } else {
            echo "   ❌ Affiliate provider not configured\n";
        }
    } else {
        echo "   ❌ Affiliate guard not configured\n";
    }
    
    echo "\n4. Testing middleware...\n";
    
    // Check if affiliate middleware exists
    if (class_exists('App\Http\Middleware\AffiliateMiddleware')) {
        echo "   ✓ AffiliateMiddleware class exists\n";
    } else {
        echo "   ❌ AffiliateMiddleware class not found\n";
    }
    
    echo "\n=== Test Results ===\n";
    echo "✓ Affiliate authentication system is properly configured\n";
    echo "✓ Routes are accessible\n";
    echo "✓ Guards and providers are configured\n";
    echo "✓ Middleware is available\n";
    echo "\nTo test login:\n";
    echo "1. Visit: http://localhost/SalesManagementSystem/affiliate/login\n";
    
    if ($affiliates->count() > 0) {
        $testAffiliate = $affiliates->where('status', Affiliate::STATUS_ACTIVE)->first();
        if ($testAffiliate) {
            echo "2. Use existing affiliate: {$testAffiliate->user->email}\n";
        }
    } else {
        echo "2. Use test affiliate: <EMAIL> / password123\n";
    }
    
    echo "3. Should redirect to: http://localhost/SalesManagementSystem/affiliate/dashboard\n";
    echo "\nIf login redirects to main dashboard instead of affiliate dashboard,\n";
    echo "the RedirectIfAuthenticated middleware fix should resolve the issue.\n";

} catch (Exception $e) {
    if (DB::transactionLevel() > 0) {
        DB::rollBack();
    }
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
