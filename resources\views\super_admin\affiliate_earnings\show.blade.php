@extends('super_admin.layouts.app')

@section('title', 'Earning Details')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Earning Details</h1>
            <p class="text-muted mb-0">View and manage affiliate earning</p>
        </div>
        <div>
            <a href="{{ route('super_admin.affiliate-earnings.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Earnings
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Earning Details -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Earning Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Earning ID</label>
                            <p class="form-control-plaintext">#{{ $earning->id }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Amount</label>
                            <p class="form-control-plaintext">
                                <span class="h5 text-success">{{ format_price($earning->amount) }}</span>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Type</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-info">{{ ucfirst($earning->type) }}</span>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Status</label>
                            <p class="form-control-plaintext">
                                @php
                                    $badgeClass = match($earning->status) {
                                        'pending' => 'bg-warning',
                                        'approved' => 'bg-success',
                                        'paid' => 'bg-primary',
                                        'rejected' => 'bg-danger',
                                        default => 'bg-secondary'
                                    };
                                @endphp
                                <span class="badge {{ $badgeClass }}">{{ ucfirst($earning->status) }}</span>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Earned Date</label>
                            <p class="form-control-plaintext">
                                {{ $earning->earned_at ? $earning->earned_at->format('M j, Y g:i A') : 'N/A' }}
                            </p>
                        </div>
                        @if($earning->approved_at)
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Approved Date</label>
                            <p class="form-control-plaintext">
                                {{ $earning->approved_at->format('M j, Y g:i A') }}
                            </p>
                        </div>
                        @endif
                        @if($earning->description)
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">Description</label>
                            <p class="form-control-plaintext">{{ $earning->description }}</p>
                        </div>
                        @endif
                        @if($earning->notes)
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">Admin Notes</label>
                            <p class="form-control-plaintext">{{ $earning->notes }}</p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Affiliate Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Affiliate Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Affiliate Name</label>
                            <p class="form-control-plaintext">{{ $earning->affiliate->user->name }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Email</label>
                            <p class="form-control-plaintext">{{ $earning->affiliate->user->email }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Affiliate Code</label>
                            <p class="form-control-plaintext">
                                <code>{{ $earning->affiliate->affiliate_code }}</code>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Commission Rate</label>
                            <p class="form-control-plaintext">{{ number_format($earning->affiliate->commission_rate, 2) }}%</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('super_admin.affiliates.show', $earning->affiliate) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-user me-1"></i>View Affiliate Profile
                        </a>
                    </div>
                </div>
            </div>

            <!-- Organization Information -->
            @if($earning->organization)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Organization Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Organization Name</label>
                            <p class="form-control-plaintext">{{ $earning->organization->name }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Email</label>
                            <p class="form-control-plaintext">{{ $earning->organization->email }}</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('super_admin.organizations.show', $earning->organization) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-building me-1"></i>View Organization
                        </a>
                    </div>
                </div>
            </div>
            @endif

            <!-- Referral Information -->
            @if($earning->referral)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Referral Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Referral Code</label>
                            <p class="form-control-plaintext">
                                <code>{{ $earning->referral->referral_code }}</code>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Registration Date</label>
                            <p class="form-control-plaintext">
                                {{ $earning->referral->registration_date->format('M j, Y g:i A') }}
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Referral Status</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-info">{{ ucfirst($earning->referral->status) }}</span>
                            </p>
                        </div>
                        @if($earning->referral->first_payment_date)
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">First Payment Date</label>
                            <p class="form-control-plaintext">
                                {{ $earning->referral->first_payment_date->format('M j, Y g:i A') }}
                            </p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Actions -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                </div>
                <div class="card-body">
                    @if($earning->status === 'pending')
                        <form action="{{ route('super_admin.affiliate-earnings.approve', $earning->id) }}" method="POST" class="mb-2">
                            @csrf
                            <div class="mb-3">
                                <label for="approve_notes" class="form-label">Approval Notes (Optional)</label>
                                <textarea class="form-control" id="approve_notes" name="notes" rows="3"
                                          placeholder="Add any notes for this approval..."></textarea>
                            </div>
                            <button type="submit" class="btn btn-success w-100"
                                    onclick="return confirm('Approve this earning?')">
                                <i class="fas fa-check me-2"></i>
                                Approve Earning
                            </button>
                        </form>

                        <button type="button" class="btn btn-danger w-100" onclick="showRejectModal()">
                            <i class="fas fa-times me-2"></i>
                            Reject Earning
                        </button>
                    @else
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            This earning has already been {{ $earning->status }}.
                            @if($earning->approvedBy)
                                <br><small>By: {{ $earning->approvedBy->name }}</small>
                            @endif
                        </div>
                    @endif
                </div>
            </div>

            <!-- Approval History -->
            @if($earning->approved_at || $earning->approvedBy)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Approval History</h6>
                </div>
                <div class="card-body">
                    @if($earning->approvedBy)
                    <div class="mb-2">
                        <strong>Processed By:</strong> {{ $earning->approvedBy->name }}
                    </div>
                    @endif
                    @if($earning->approved_at)
                    <div class="mb-2">
                        <strong>Processed Date:</strong> {{ $earning->approved_at->format('M j, Y g:i A') }}
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reject Earning</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('super_admin.affiliate-earnings.reject', $earning->id) }}">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">Rejection Reason <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="rejection_reason" name="notes" rows="3" required
                                  placeholder="Please provide a clear reason for rejecting this earning..."></textarea>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This earning will be rejected and removed from affiliate balance.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Reject Earning</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showRejectModal() {
    new bootstrap.Modal(document.getElementById('rejectModal')).show();
}
</script>
@endsection
