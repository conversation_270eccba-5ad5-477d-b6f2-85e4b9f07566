<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    /**
     * Show the profile page
     */
    public function index()
    {
        $superAdmin = Auth::guard('super_admin')->user();
        
        return view('super_admin.profile.index', compact('superAdmin'));
    }

    /**
     * Update profile information
     */
    public function updateProfile(Request $request)
    {
        $superAdmin = Auth::guard('super_admin')->user();

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:super_admins,email,' . $superAdmin->id],
        ]);

        $superAdmin->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);

        return back()->with('success', 'Profile updated successfully!');
    }

    /**
     * Update password
     */
    public function updatePassword(Request $request)
    {
        $superAdmin = Auth::guard('super_admin')->user();

        $request->validate([
            'current_password' => ['required', 'current_password:super_admin'],
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        $superAdmin->update([
            'password' => Hash::make($request->password),
        ]);

        return back()->with('success', 'Password updated successfully!');
    }

    /**
     * Show profile settings
     */
    public function settings()
    {
        $superAdmin = Auth::guard('super_admin')->user();
        
        return view('super_admin.profile.settings', compact('superAdmin'));
    }
}
