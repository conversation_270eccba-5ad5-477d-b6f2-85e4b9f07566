@extends('affiliate.layouts.app')

@section('page-title', 'Profile')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">My Profile</h2>
            <p class="text-muted mb-0">Manage your affiliate account information and settings</p>
        </div>
    </div>

    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user me-2"></i>Profile Information
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('affiliate.profile.update') }}">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <!-- Name -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text"
                                       id="name"
                                       name="name"
                                       value="{{ old('name', $affiliate->user->name) }}"
                                       class="form-control @error('name') is-invalid @enderror"
                                       required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email"
                                       id="email"
                                       name="email"
                                       value="{{ old('email', $affiliate->user->email) }}"
                                       class="form-control @error('email') is-invalid @enderror"
                                       required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <!-- Phone -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel"
                                       id="phone"
                                       name="phone"
                                       value="{{ old('phone', $affiliate->user->phone) }}"
                                       class="form-control @error('phone') is-invalid @enderror"
                                       required>
                                <input type="hidden" id="phone_full" name="phone_full" value="{{ old('phone_full') }}">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                @error('phone_full')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Website -->
                            <div class="col-md-6 mb-3">
                                <label for="website" class="form-label">Website URL</label>
                                <input type="url"
                                       id="website"
                                       name="website"
                                       value="{{ old('website', $affiliate->website) }}"
                                       class="form-control @error('website') is-invalid @enderror"
                                       placeholder="https://example.com">
                                @error('website')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Social Media -->
                        <div class="mb-3">
                            <label for="social_media" class="form-label">Social Media Profiles</label>
                            <input type="text"
                                   id="social_media"
                                   name="social_media"
                                   value="{{ old('social_media', $affiliate->social_media) }}"
                                   class="form-control @error('social_media') is-invalid @enderror"
                                   placeholder="Twitter: @username, Instagram: @username">
                            @error('social_media')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Enter your social media handles (optional)</div>
                        </div>

                        <!-- Bio -->
                        <div class="mb-3">
                            <label for="bio" class="form-label">Bio</label>
                            <textarea id="bio"
                                      name="bio"
                                      rows="4"
                                      class="form-control @error('bio') is-invalid @enderror"
                                      placeholder="Tell us about yourself and your marketing experience...">{{ old('bio', $affiliate->bio) }}</textarea>
                            @error('bio')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Maximum 500 characters</div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Password Change -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-lock me-2"></i>Change Password
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('affiliate.profile.update') }}" id="passwordForm">
                        @csrf
                        @method('PUT')
                        <input type="hidden" name="password_change" value="1">

                        <div class="row">
                            <!-- Current Password -->
                            <div class="col-md-12 mb-3">
                                <label for="current_password" class="form-label">Current Password</label>
                                <input type="password"
                                       id="current_password"
                                       name="current_password"
                                       class="form-control @error('current_password') is-invalid @enderror">
                                @error('current_password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- New Password -->
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">New Password</label>
                                <input type="password"
                                       id="password"
                                       name="password"
                                       class="form-control @error('password') is-invalid @enderror">
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Confirm Password -->
                            <div class="col-md-6 mb-3">
                                <label for="password_confirmation" class="form-label">Confirm New Password</label>
                                <input type="password"
                                       id="password_confirmation"
                                       name="password_confirmation"
                                       class="form-control">
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-key me-2"></i>Change Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Affiliate Information Sidebar -->
        <div class="col-lg-4">
            <!-- Account Status -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Account Status
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <div>
                            @php
                                $statusClass = match($affiliate->status) {
                                    'active' => 'bg-success',
                                    'pending' => 'bg-warning',
                                    'inactive' => 'bg-secondary',
                                    'suspended' => 'bg-danger',
                                    default => 'bg-secondary'
                                };
                            @endphp
                            <span class="badge {{ $statusClass }}">{{ ucfirst($affiliate->status) }}</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Affiliate Code</label>
                        <div class="input-group">
                            <input type="text"
                                   value="{{ $affiliate->affiliate_code }}"
                                   class="form-control"
                                   readonly>
                            <button class="btn btn-outline-secondary"
                                    type="button"
                                    onclick="copyToClipboard('{{ $affiliate->affiliate_code }}')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Commission Rate</label>
                        <div>
                            <span class="h5 text-success">{{ number_format($affiliate->commission_rate, 1) }}%</span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Member Since</label>
                        <div>{{ $affiliate->created_at->format('F j, Y') }}</div>
                    </div>

                    @if($affiliate->approved_at)
                    <div class="mb-0">
                        <label class="form-label">Approved On</label>
                        <div>{{ $affiliate->approved_at->format('F j, Y') }}</div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-2"></i>Quick Stats
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="h4 mb-0 text-primary">{{ format_price($affiliate->total_earnings) }}</div>
                            <div class="small text-muted">Total Earnings</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="h4 mb-0 text-success">{{ format_price($affiliate->available_balance) }}</div>
                            <div class="small text-muted">Available</div>
                        </div>
                        <div class="col-6">
                            <div class="h4 mb-0 text-info">{{ $affiliate->referrals()->count() }}</div>
                            <div class="small text-muted">Referrals</div>
                        </div>
                        <div class="col-6">
                            <div class="h4 mb-0 text-warning">{{ $affiliate->referrals()->where('status', 'converted')->count() }}</div>
                            <div class="small text-muted">Converted</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Referral Link -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-link me-2"></i>Referral Link
                    </h6>
                </div>
                <div class="card-body">
                    <div class="input-group">
                        <input type="text"
                               value="{{ $affiliate->referral_link }}"
                               class="form-control"
                               readonly
                               id="referralLink">
                        <button class="btn btn-outline-primary"
                                type="button"
                                onclick="copyToClipboard('{{ $affiliate->referral_link }}')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <div class="form-text mt-2">Share this link to earn commissions</div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    /* Custom styles for intl-tel-input to match Bootstrap form design */
    .iti {
        width: 100%;
    }
    .iti__flag-container {
        display: flex;
    }
    .iti__selected-flag {
        border-radius: 0.375rem 0 0 0.375rem;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-right: none;
        height: calc(1.5em + 0.75rem + 2px);
    }
    .iti--allow-dropdown input {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
    }
    .iti__country-list {
        z-index: 1050;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize international telephone input
    const phoneInputField = document.querySelector("#phone");
    const phoneInput = window.intlTelInput(phoneInputField, {
        utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js",
        initialCountry: "auto",
        geoIpLookup: function(callback) {
            fetch("https://ipapi.co/json")
              .then(function(res) { return res.json(); })
              .then(function(data) { callback(data.country_code); })
              .catch(function() { callback("us"); });
        },
        preferredCountries: ["us", "gb", "ca", "ng"],
        separateDialCode: true,
        formatOnDisplay: true,
    });

    // If there's an existing phone number, set the country based on it
    const existingPhone = phoneInputField.value;
    if (existingPhone) {
        // Try to set the number and let the plugin detect the country
        phoneInput.setNumber(existingPhone);
    }

    // Store the full number with country code when submitting the form
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function() {
            const fullNumber = phoneInput.getNumber();
            document.getElementById('phone_full').value = fullNumber;
        });
    }
});

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check me-2"></i>Copied to clipboard!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;
        document.body.appendChild(toast);

        // Remove toast after 3 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 3000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
    });
}
</script>
@endpush
@endsection
