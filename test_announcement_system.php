<?php

require_once 'vendor/autoload.php';

use App\Models\Announcement;
use App\Models\SuperAdmin;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Announcement System Diagnostic ===\n\n";

try {
    // 1. Check if announcements table exists and has data
    echo "1. Checking announcements table...\n";
    $announcementCount = Announcement::count();
    echo "   Total announcements in database: {$announcementCount}\n";
    
    if ($announcementCount > 0) {
        $announcements = Announcement::all();
        foreach ($announcements as $announcement) {
            echo "   - ID: {$announcement->id}, Title: {$announcement->title}\n";
            echo "     Target Audience: {$announcement->target_audience}\n";
            echo "     Is Active: " . ($announcement->is_active ? 'Yes' : 'No') . "\n";
            echo "     Published: " . ($announcement->published_at ? $announcement->published_at->format('Y-m-d H:i:s') : 'No') . "\n";
            echo "     Show on Dashboard: " . ($announcement->show_on_dashboard ? 'Yes' : 'No') . "\n";
            echo "     Starts At: " . ($announcement->starts_at ? $announcement->starts_at->format('Y-m-d H:i:s') : 'No limit') . "\n";
            echo "     Ends At: " . ($announcement->ends_at ? $announcement->ends_at->format('Y-m-d H:i:s') : 'No limit') . "\n";
            echo "\n";
        }
    }
    
    // 2. Check if there are any super admins
    echo "2. Checking super admins...\n";
    $superAdminCount = SuperAdmin::count();
    echo "   Total super admins: {$superAdminCount}\n";
    
    if ($superAdminCount === 0) {
        echo "   Creating test super admin...\n";
        $superAdmin = SuperAdmin::create([
            'name' => 'Test Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'super_admin',
            'is_active' => true
        ]);
        echo "   Test super admin created with ID: {$superAdmin->id}\n";
    } else {
        $superAdmin = SuperAdmin::first();
        echo "   Using existing super admin: {$superAdmin->name} (ID: {$superAdmin->id})\n";
    }
    
    // 3. Create a test announcement if none exist
    if ($announcementCount === 0) {
        echo "\n3. Creating test announcement...\n";
        $announcement = Announcement::create([
            'title' => 'Test Announcement for Organizations',
            'content' => 'This is a test announcement to verify the system is working correctly.',
            'type' => 'info',
            'priority' => 'normal',
            'target_audience' => 'organizations',
            'is_active' => true,
            'is_dismissible' => true,
            'show_on_login' => false,
            'show_on_dashboard' => true,
            'send_email' => false,
            'published_at' => now(),
            'created_by' => $superAdmin->id,
        ]);
        echo "   Test announcement created with ID: {$announcement->id}\n";
    }
    
    // 4. Test the query that the API uses
    echo "\n4. Testing API query for organizations...\n";
    $announcements = Announcement::active()
        ->published()
        ->current()
        ->forAudience('organizations')
        ->forDashboard()
        ->orderBy('priority', 'desc')
        ->orderBy('created_at', 'desc')
        ->get();
    
    echo "   Announcements found for organizations: {$announcements->count()}\n";
    foreach ($announcements as $announcement) {
        echo "   - {$announcement->title} (ID: {$announcement->id})\n";
    }
    
    // 5. Test the query for all users
    echo "\n5. Testing API query for all users...\n";
    $allAnnouncements = Announcement::active()
        ->published()
        ->current()
        ->forAudience('all')
        ->forDashboard()
        ->orderBy('priority', 'desc')
        ->orderBy('created_at', 'desc')
        ->get();
    
    echo "   Announcements found for all users: {$allAnnouncements->count()}\n";
    foreach ($allAnnouncements as $announcement) {
        echo "   - {$announcement->title} (ID: {$announcement->id})\n";
    }
    
    // 6. Check if there are organization users
    echo "\n6. Checking organization users...\n";
    $orgUsers = User::whereNotNull('organization_id')->count();
    echo "   Users with organization_id: {$orgUsers}\n";
    
    if ($orgUsers > 0) {
        $sampleUser = User::whereNotNull('organization_id')->first();
        echo "   Sample organization user: {$sampleUser->name} (Org ID: {$sampleUser->organization_id})\n";
        
        // Test the audience determination
        $audience = $sampleUser->organization_id ? 'organizations' : 'customers';
        echo "   Determined audience for this user: {$audience}\n";
    }
    
    echo "\n=== Diagnostic Complete ===\n";
    echo "If announcements are not showing on the frontend, check:\n";
    echo "1. Browser console for JavaScript errors\n";
    echo "2. Network tab to see if /api/announcements is being called\n";
    echo "3. Laravel logs for any errors\n";
    echo "4. Ensure the user is authenticated and has organization_id set\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
