<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Currency
    |--------------------------------------------------------------------------
    |
    | This is the default currency for the application. All prices are stored
    | in this currency in the database and converted to user's currency for display.
    |
    */

    'default' => env('CURRENCY_DEFAULT', 'USD'),

    /*
    |--------------------------------------------------------------------------
    | Supported Currencies
    |--------------------------------------------------------------------------
    |
    | List of currencies supported by the application.
    |
    */

    'supported' => [
        'USD' => [
            'name' => 'US Dollar',
            'symbol' => '$',
            'code' => 'USD',
            'decimal_places' => 2,
        ],
        'NGN' => [
            'name' => 'Nigerian Naira',
            'symbol' => '₦',
            'code' => 'NGN',
            'decimal_places' => 2,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Auto Currency Detection
    |--------------------------------------------------------------------------
    |
    | Enable automatic currency detection based on user's IP address.
    |
    */

    'auto_detection' => env('CURRENCY_AUTO_DETECTION', true),

    /*
    |--------------------------------------------------------------------------
    | Nigerian IP Ranges
    |--------------------------------------------------------------------------
    |
    | IP address ranges that should be detected as Nigerian users (NGN currency).
    | These are in CIDR notation.
    |
    */

    'nigerian_ip_ranges' => [
        '********/8',
        '*********/8',
        '***********/16',
        '*********/8',
        '*********/8',
        '*********/8',
    ],

    /*
    |--------------------------------------------------------------------------
    | Currency Display Settings
    |--------------------------------------------------------------------------
    |
    | Settings for how currencies are displayed throughout the application.
    |
    */

    'display' => [
        'decimal_places' => env('CURRENCY_DECIMAL_PLACES', 2),
        'thousands_separator' => ',',
        'decimal_separator' => '.',
        'symbol_position' => 'before', // 'before' or 'after'
    ],

    /*
    |--------------------------------------------------------------------------
    | Exchange Rate Settings
    |--------------------------------------------------------------------------
    |
    | Settings for exchange rate management and caching.
    |
    */

    'exchange_rates' => [
        'cache_duration' => env('CURRENCY_CACHE_DURATION', 3600), // 1 hour in seconds
        'default_rate' => 1500.0, // Default USD to NGN rate
    ],

    /*
    |--------------------------------------------------------------------------
    | IP Geolocation Settings
    |--------------------------------------------------------------------------
    |
    | Settings for IP geolocation services used for currency detection.
    |
    */

    'geolocation' => [
        'cache_duration' => env('GEOLOCATION_CACHE_DURATION', 86400), // 24 hours
        'timeout' => env('GEOLOCATION_TIMEOUT', 5), // 5 seconds
        'services' => [
            'primary' => 'ip-api',
            'fallback' => 'ipinfo',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Currency Conversion API Settings
    |--------------------------------------------------------------------------
    |
    | Settings for external currency conversion APIs (for future use).
    |
    */

    'api' => [
        'enabled' => env('CURRENCY_API_ENABLED', false),
        'provider' => env('CURRENCY_API_PROVIDER', 'fixer'),
        'key' => env('CURRENCY_API_KEY'),
        'update_frequency' => env('CURRENCY_UPDATE_FREQUENCY', 'daily'),
    ],

];
