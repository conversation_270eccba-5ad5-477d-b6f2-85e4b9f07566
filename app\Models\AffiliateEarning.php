<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AffiliateEarning extends Model
{
    use HasFactory;

    const TYPE_COMMISSION = 'commission';
    const TYPE_BONUS = 'bonus';
    const TYPE_ADJUSTMENT = 'adjustment';
    const TYPE_PENALTY = 'penalty';

    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_PAID = 'paid';
    const STATUS_REJECTED = 'rejected';

    protected $fillable = [
        'affiliate_id',
        'referral_id',
        'organization_id',
        'subscription_payment_id',
        'amount',
        'type',
        'description',
        'status',
        'earned_at',
        'approved_at',
        'approved_by',
        'paid_at',
        'payment_reference',
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'earned_at' => 'datetime',
        'approved_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the affiliate that earned this
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(Affiliate::class);
    }

    /**
     * Get the referral that generated this earning
     */
    public function referral(): BelongsTo
    {
        return $this->belongsTo(AffiliateReferral::class, 'referral_id');
    }

    /**
     * Get the organization that generated this earning
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the subscription payment that triggered this earning
     */
    public function subscriptionPayment(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPayment::class);
    }

    /**
     * Get the super admin who approved this earning
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(SuperAdmin::class, 'approved_by');
    }

    /**
     * Scope for pending earnings
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for approved earnings
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * Scope for paid earnings
     */
    public function scopePaid($query)
    {
        return $query->where('status', self::STATUS_PAID);
    }

    /**
     * Scope for commission type
     */
    public function scopeCommissions($query)
    {
        return $query->where('type', self::TYPE_COMMISSION);
    }

    /**
     * Scope for bonus type
     */
    public function scopeBonuses($query)
    {
        return $query->where('type', self::TYPE_BONUS);
    }

    /**
     * Check if earning is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if earning is approved
     */
    public function isApproved(): bool
    {
        return $this->status === self::STATUS_APPROVED;
    }

    /**
     * Check if earning is paid
     */
    public function isPaid(): bool
    {
        return $this->status === self::STATUS_PAID;
    }

    /**
     * Check if earning is rejected
     */
    public function isRejected(): bool
    {
        return $this->status === self::STATUS_REJECTED;
    }

    /**
     * Approve the earning
     */
    public function approve($approvedBy = null, $notes = null): bool
    {
        $this->status = self::STATUS_APPROVED;
        $this->approved_at = now();
        $this->approved_by = $approvedBy;
        if ($notes) {
            $this->notes = $notes;
        }

        // Update affiliate balance
        $this->affiliate->approvePendingEarnings($this->amount);
        
        return $this->save();
    }

    /**
     * Reject the earning
     */
    public function reject($rejectedBy = null, $notes = null): bool
    {
        $this->status = self::STATUS_REJECTED;
        $this->approved_at = now();
        $this->approved_by = $rejectedBy;
        if ($notes) {
            $this->notes = $notes;
        }

        // Remove from affiliate pending balance
        $affiliate = $this->affiliate;
        $affiliate->pending_balance -= $this->amount;
        $affiliate->save();
        
        return $this->save();
    }

    /**
     * Mark as paid
     */
    public function markAsPaid($paymentReference = null): bool
    {
        $this->status = self::STATUS_PAID;
        $this->paid_at = now();
        if ($paymentReference) {
            $this->payment_reference = $paymentReference;
        }
        
        return $this->save();
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute(): string
    {
        return '$' . number_format($this->amount, 2);
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'bg-yellow-100 text-yellow-800',
            self::STATUS_APPROVED => 'bg-green-100 text-green-800',
            self::STATUS_PAID => 'bg-blue-100 text-blue-800',
            self::STATUS_REJECTED => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get type badge class
     */
    public function getTypeBadgeClassAttribute(): string
    {
        return match($this->type) {
            self::TYPE_COMMISSION => 'bg-blue-100 text-blue-800',
            self::TYPE_BONUS => 'bg-green-100 text-green-800',
            self::TYPE_ADJUSTMENT => 'bg-yellow-100 text-yellow-800',
            self::TYPE_PENALTY => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }
}
