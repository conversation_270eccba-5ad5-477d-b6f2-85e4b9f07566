@extends('super_admin.layouts.app')

@section('title', 'Edit Knowledge Base Article')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Edit Knowledge Base Article</h1>
                    <p class="text-muted mb-0">Update article content and settings</p>
                </div>
                <div>
                    <a href="{{ route('super_admin.knowledge-base.show', $article) }}" class="btn btn-outline-info me-2">
                        <i class="fas fa-eye"></i> View Article
                    </a>
                    <a href="{{ route('super_admin.knowledge-base.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Articles
                    </a>
                </div>
            </div>

            <form method="POST" action="{{ route('super_admin.knowledge-base.update', $article) }}">
                @csrf
                @method('PUT')
                <div class="row">
                    <!-- Main Content -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Article Content</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                    <input type="text" name="title" id="title" 
                                           class="form-control @error('title') is-invalid @enderror" 
                                           value="{{ old('title', $article->title) }}" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="excerpt" class="form-label">Excerpt</label>
                                    <textarea name="excerpt" id="excerpt" 
                                              class="form-control @error('excerpt') is-invalid @enderror" 
                                              rows="3" placeholder="Brief summary of the article (optional)">{{ old('excerpt', $article->excerpt) }}</textarea>
                                    @error('excerpt')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">This will be shown in search results and article previews.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
                                    <textarea name="content" id="content" 
                                              class="form-control @error('content') is-invalid @enderror" 
                                              rows="20" required>{{ old('content', $article->content) }}</textarea>
                                    @error('content')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        You can use Markdown formatting. 
                                        <a href="#" data-bs-toggle="modal" data-bs-target="#markdownHelp">View formatting guide</a>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="tags" class="form-label">Tags</label>
                                    <input type="text" name="tags" id="tags" 
                                           class="form-control @error('tags') is-invalid @enderror" 
                                           value="{{ old('tags', is_array($article->tags) ? implode(', ', $article->tags) : '') }}" 
                                           placeholder="tag1, tag2, tag3">
                                    @error('tags')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Separate tags with commas. Tags help users find related content.</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="col-lg-4">
                        <!-- Publish Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Publish Settings</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select name="status" id="status" 
                                            class="form-control @error('status') is-invalid @enderror" required>
                                        @foreach(\App\Models\KnowledgeBaseArticle::getStatusOptions() as $value => $label)
                                            <option value="{{ $value }}" {{ old('status', $article->status) === $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="visibility" class="form-label">Visibility <span class="text-danger">*</span></label>
                                    <select name="visibility" id="visibility" 
                                            class="form-control @error('visibility') is-invalid @enderror" required>
                                        @foreach(\App\Models\KnowledgeBaseArticle::getVisibilityOptions() as $value => $label)
                                            <option value="{{ $value }}" {{ old('visibility', $article->visibility) === $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('visibility')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-check">
                                    <input type="checkbox" name="featured" id="featured" 
                                           class="form-check-input" value="1" {{ old('featured', $article->featured) ? 'checked' : '' }}>
                                    <label for="featured" class="form-check-label">
                                        Featured Article
                                    </label>
                                    <div class="form-text">Featured articles appear prominently on the knowledge base homepage.</div>
                                </div>
                            </div>
                        </div>

                        <!-- Category -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Category</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                                    <select name="category_id" id="category_id" 
                                            class="form-control @error('category_id') is-invalid @enderror" required>
                                        <option value="">Select Category</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ old('category_id', $article->category_id) == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="text-center">
                                    <a href="{{ route('super_admin.knowledge-base.categories.create') }}" 
                                       class="btn btn-outline-primary btn-sm" target="_blank">
                                        <i class="fas fa-plus"></i> Create New Category
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Article Info -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Article Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <strong>Created:</strong><br>
                                    <small class="text-muted">{{ $article->created_at->format('M d, Y \a\t H:i') }}</small>
                                </div>
                                <div class="mb-2">
                                    <strong>Last Updated:</strong><br>
                                    <small class="text-muted">{{ $article->updated_at->format('M d, Y \a\t H:i') }}</small>
                                </div>
                                @if($article->published_at)
                                <div class="mb-2">
                                    <strong>Published:</strong><br>
                                    <small class="text-muted">{{ $article->published_at->format('M d, Y \a\t H:i') }}</small>
                                </div>
                                @endif
                                <div class="mb-2">
                                    <strong>Views:</strong><br>
                                    <span class="badge bg-info">{{ number_format($article->view_count) }}</span>
                                </div>
                                @if($article->helpfulness_percentage !== null)
                                <div class="mb-2">
                                    <strong>Helpfulness:</strong><br>
                                    <span class="badge bg-success">{{ $article->helpfulness_percentage }}%</span>
                                    <small class="text-muted d-block">
                                        {{ $article->helpful_count }} helpful, {{ $article->not_helpful_count }} not helpful
                                    </small>
                                </div>
                                @endif
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Actions</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Update Article
                                    </button>
                                    <button type="submit" name="status" value="draft" class="btn btn-outline-secondary">
                                        <i class="fas fa-file-alt"></i> Save as Draft
                                    </button>
                                    <a href="{{ route('super_admin.knowledge-base.show', $article) }}" class="btn btn-outline-info">
                                        <i class="fas fa-eye"></i> Preview Article
                                    </a>
                                    <a href="{{ route('super_admin.knowledge-base.index') }}" class="btn btn-outline-danger">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Markdown Help Modal -->
<div class="modal fade" id="markdownHelp" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Markdown Formatting Guide</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Headers</h6>
                        <pre># Header 1
## Header 2
### Header 3</pre>

                        <h6>Text Formatting</h6>
                        <pre>**Bold text**
*Italic text*
`Code text`</pre>

                        <h6>Lists</h6>
                        <pre>- Item 1
- Item 2
- Item 3

1. Numbered item
2. Numbered item</pre>
                    </div>
                    <div class="col-md-6">
                        <h6>Links</h6>
                        <pre>[Link text](https://example.com)</pre>

                        <h6>Code Blocks</h6>
                        <pre>```
Code block
```</pre>

                        <h6>Quotes</h6>
                        <pre>> This is a quote</pre>

                        <h6>Tables</h6>
                        <pre>| Column 1 | Column 2 |
|----------|----------|
| Data 1   | Data 2   |</pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counter for excerpt
    const excerptTextarea = document.getElementById('excerpt');
    if (excerptTextarea) {
        const maxLength = 500;
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        excerptTextarea.parentNode.appendChild(counter);
        
        function updateCounter() {
            const remaining = maxLength - excerptTextarea.value.length;
            counter.textContent = `${remaining} characters remaining`;
            counter.className = remaining < 50 ? 'form-text text-end text-warning' : 'form-text text-end';
        }
        
        excerptTextarea.addEventListener('input', updateCounter);
        updateCounter();
    }

    // Save draft functionality
    const form = document.querySelector('form');
    const draftButton = document.querySelector('button[value="draft"]');
    
    if (draftButton) {
        draftButton.addEventListener('click', function() {
            // Override the status field to draft
            const statusField = document.getElementById('status');
            statusField.value = 'draft';
        });
    }
});
</script>
@endsection
