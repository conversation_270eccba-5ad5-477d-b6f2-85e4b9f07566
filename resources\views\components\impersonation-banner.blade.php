@if(isset($isImpersonating) && $isImpersonating)
<div class="impersonation-banner bg-warning text-dark py-2 px-3 d-flex justify-content-between align-items-center" style="position: sticky; top: 0; z-index: 1050;">
    <div class="d-flex align-items-center">
        <i class="fas fa-user-secret me-2"></i>
        <strong>IMPERSONATION MODE:</strong>
        <span class="ms-2">
            You are impersonating <strong>{{ $impersonationData['target_user_name'] }}</strong>
            ({{ $impersonationData['target_user_email'] }})
        </span>
        <span class="badge bg-dark ms-2">
            <i class="fas fa-clock me-1"></i>
            <span id="impersonation-timer">{{ $impersonationRemainingTime }} min remaining</span>
        </span>
    </div>
    <div class="d-flex align-items-center">
        <small class="me-3">
            Super Admin: {{ $impersonationData['super_admin_name'] }}
        </small>
        <form method="POST" action="{{ route('impersonation.stop') }}" style="display: inline;">
            @csrf
            <button type="submit" class="btn btn-sm btn-dark">
                <i class="fas fa-sign-out-alt me-1"></i>
                Stop Impersonation
            </button>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update impersonation timer every minute
    let remainingMinutes = {{ $impersonationRemainingTime }};
    const timerElement = document.getElementById('impersonation-timer');
    
    const updateTimer = () => {
        if (remainingMinutes <= 0) {
            timerElement.textContent = 'Expired';
            timerElement.parentElement.classList.remove('bg-dark');
            timerElement.parentElement.classList.add('bg-danger');
            
            // Show expiration warning
            if (!document.getElementById('expiration-warning')) {
                const warning = document.createElement('div');
                warning.id = 'expiration-warning';
                warning.className = 'alert alert-danger alert-dismissible fade show position-fixed';
                warning.style.cssText = 'top: 60px; right: 20px; z-index: 1060; max-width: 400px;';
                warning.innerHTML = `
                    <strong>Impersonation Expired!</strong>
                    Your impersonation session has expired. You will be logged out shortly.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(warning);
                
                // Auto-redirect after 10 seconds
                setTimeout(() => {
                    window.location.href = '{{ route('super_admin.login') }}';
                }, 10000);
            }
            return;
        }
        
        const hours = Math.floor(remainingMinutes / 60);
        const minutes = remainingMinutes % 60;
        
        if (hours > 0) {
            timerElement.textContent = `${hours}h ${minutes}m remaining`;
        } else {
            timerElement.textContent = `${minutes} min remaining`;
        }
        
        // Change color when less than 30 minutes remaining
        if (remainingMinutes <= 30) {
            timerElement.parentElement.classList.remove('bg-dark');
            timerElement.parentElement.classList.add('bg-danger');
        }
        
        remainingMinutes--;
    };
    
    // Update immediately and then every minute
    updateTimer();
    setInterval(updateTimer, 60000);
    
    // Check impersonation status every 5 minutes
    setInterval(() => {
        fetch('{{ route('super_admin.impersonation.status') }}')
            .then(response => response.json())
            .then(data => {
                if (!data.impersonating) {
                    // Impersonation stopped, redirect to super admin login
                    window.location.href = '{{ route('super_admin.login') }}';
                }
            })
            .catch(error => {
                console.error('Error checking impersonation status:', error);
            });
    }, 300000); // 5 minutes
});
</script>

<style>
.impersonation-banner {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-bottom: 2px solid #ffc107;
}

.impersonation-banner .badge {
    font-size: 0.75rem;
}

@media (max-width: 768px) {
    .impersonation-banner {
        flex-direction: column;
        text-align: center;
    }
    
    .impersonation-banner > div {
        margin-bottom: 0.5rem;
    }
    
    .impersonation-banner > div:last-child {
        margin-bottom: 0;
    }
}
</style>
@endif
