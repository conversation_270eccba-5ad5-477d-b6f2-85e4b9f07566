<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Role extends Model
{
    use HasFactory;

    protected $fillable = ['name'];

    public static function getRoles()
    {
        return [
            'Organization Owner',
            'Manager',
            'Account',
            'Staff',
            'Operator',
            'Production',
            'Delivery'
        ];
    }

    /**
     * Ensure consistent role name format on creation and updates
     */
    protected static function boot()
    {
        parent::boot();
        
        static::saving(function ($role) {
            $role->name = Str::title(trim($role->name));
        });
    }

    /**
     * The users that belong to the role.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'role_user', 'role_id', 'user_id')
                    ->withTimestamps();
    }

    /**
     * Format role name before comparison
     */
    protected function formatRoleName($name)
    {
        return Str::title(trim($name));
    }

    /**
     * Check if the role matches the given name
     */
    public function is($roleName)
    {
        return $this->name === $this->formatRoleName($roleName);
    }

    /**
     * Scope query to exact role name match
     */
    public function scopeWhereName($query, $name)
    {
        return $query->where('name', $this->formatRoleName($name));
    }

    /**
     * Check if the role matches any of the given names
     */
    public function isAny(array $roleNames)
    {
        return in_array($this->name, array_map([$this, 'formatRoleName'], $roleNames));
    }
}