@extends('layouts.app')

@section('title', 'Daily Expenditures')

@section('content')
@if(auth()->user()->hasAnyRole(['Organization Owner', 'Manager', 'Account']))
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        @if(session('success'))
            <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
        @endif

        @if(session('error'))
            <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ session('error') }}</span>
            </div>
        @endif

        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 bg-white border-b border-gray-200">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold">Daily Expenditures</h2>
                    @if(auth()->user()->hasRole('Account'))
                    <a href="{{ route('expenditures.create') }}"
                        class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        New Application
                    </a>
                    @endif
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Heading</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applicant</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approved By</th>
                                @if(auth()->user()->hasAnyRole(['Organization Owner', 'Manager']))
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                @endif
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($expenditures as $expenditure)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ $expenditure->created_at->format('Y-m-d') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $expenditure->heading }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ format_money($expenditure->amount) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $expenditure->applicant }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                        @if($expenditure->status === 'Approved')
                                            bg-green-100 text-green-800 border border-green-400
                                        @elseif($expenditure->status === 'Rejected')
                                            bg-red-100 text-red-800 border border-red-400
                                        @else
                                            bg-yellow-100 text-yellow-800 border border-yellow-400
                                        @endif">
                                        {{ $expenditure->status }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $expenditure->approved_by ?? '-' }}
                                </td>
                                @if(auth()->user()->hasAnyRole(['Organization Owner', 'Manager']))
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    @if($expenditure->status === 'Pending')
                                    <div class="flex space-x-3">
                                        <form action="{{ route('expenditures.updateStatus', $expenditure) }}" method="POST" class="inline">
                                            @csrf
                                            @method('PATCH')
                                            <input type="hidden" name="status" value="Approved">
                                            <button type="submit"
                                                class="text-green-600 hover:text-green-900 flex items-center"
                                                onclick="return confirm('Are you sure you want to approve this expenditure?')">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                                </svg>
                                                Approve
                                            </button>
                                        </form>
                                        <form action="{{ route('expenditures.updateStatus', $expenditure) }}" method="POST" class="inline">
                                            @csrf
                                            @method('PATCH')
                                            <input type="hidden" name="status" value="Rejected">
                                            <button type="submit"
                                                class="text-red-600 hover:text-red-900 flex items-center"
                                                onclick="return confirm('Are you sure you want to reject this expenditure?')">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                                </svg>
                                                Reject
                                            </button>
                                        </form>
                                    </div>
                                    @endif
                                </td>
                                @endif
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="mt-4">
                    {{ $expenditures->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@else
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-center">
                <p class="text-gray-600">You don't have permission to access this page.</p>
            </div>
        </div>
    </div>
</div>
@endif

@push('scripts')
<script>
function confirmAction(form, action) {
    Swal.fire({
        title: `Confirm ${action}`,
        text: `Are you sure you want to ${action.toLowerCase()} this expenditure?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: action === 'Approve' ? '#3085d6' : '#d33',
        cancelButtonColor: '#6b7280',
        confirmButtonText: `Yes, ${action.toLowerCase()} it!`
    }).then((result) => {
        if (result.isConfirmed) {
            showLoading();
            submitForm(form);
        }
    });
}

function submitForm(form) {
    $.ajax({
        url: $(form).attr('action'),
        method: 'POST',
        data: $(form).serialize(),
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
            hideLoading();
            if (response.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: response.message
                }).then(() => {
                    window.location.reload();
                });
            } else {
                throw new Error(response.message || 'Something went wrong');
            }
        },
        error: function(xhr) {
            hideLoading();
            let message = 'Failed to process request';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            }
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: message
            });
        }
    });
}

function showLoading() {
    $('#loadingOverlay').removeClass('hidden');
}

function hideLoading() {
    $('#loadingOverlay').addClass('hidden');
}
</script>
@endpush

<!-- Loading Overlay -->
<div id="loadingOverlay" class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden">
    <div class="flex items-center justify-center min-h-screen">
        <div class="bg-white p-4 rounded-lg flex items-center space-x-3">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
            <p class="text-gray-900">Processing request...</p>
        </div>
    </div>
</div>

@endsection
