<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use App\Models\User;

class PreventConcurrentEditing
{
    public function handle(Request $request, Closure $next)
    {
        if ($request->route()->getName() === 'users.edit') {
            $user = $request->route('user');
            $lockKey = "user_edit_lock_{$user->id}";
            $currentEditor = Cache::get($lockKey);

            if ($currentEditor && $currentEditor !== auth()->id()) {
                return redirect()->route('users.index')
                    ->with('error', 'This user is currently being edited by another administrator.');
            }

            // Set edit lock for 5 minutes
            Cache::put($lockKey, auth()->id(), now()->addMinutes(5));
        }

        // Release lock when leaving edit page
        if ($request->route()->getName() !== 'users.edit' && $request->route()->getName() !== 'users.update') {
            $previousUrl = url()->previous();
            if (strpos($previousUrl, '/users/') !== false && strpos($previousUrl, '/edit') !== false) {
                $userId = (int) filter_var($previousUrl, FILTER_SANITIZE_NUMBER_INT);
                Cache::forget("user_edit_lock_{$userId}");
            }
        }

        return $next($request);
    }
}