@extends('super_admin.layouts.app')

@section('title', $announcement->title)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{{ $announcement->title }}</h1>
                    <div class="d-flex align-items-center mt-2">
                        {!! $announcement->type_badge !!}
                        {!! $announcement->priority_badge !!}
                        {!! $announcement->status_badge !!}
                        <span class="badge bg-secondary ms-2">
                            {{ \App\Models\Announcement::getTargetAudienceOptions()[$announcement->target_audience] }}
                        </span>
                    </div>
                </div>
                <div>
                    <a href="{{ route('super_admin.announcements.edit', $announcement) }}" class="btn btn-primary me-2">
                        <i class="fas fa-edit"></i> Edit Announcement
                    </a>
                    <a href="{{ route('super_admin.announcements.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Announcements
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="{{ $announcement->icon }} me-2"></i>
                                    Announcement Content
                                </h5>
                                <div>
                                    @if($announcement->isCurrentlyActive())
                                        <span class="badge bg-success">Currently Active</span>
                                    @elseif($announcement->isScheduled())
                                        <span class="badge bg-info">Scheduled</span>
                                    @elseif($announcement->hasExpired())
                                        <span class="badge bg-secondary">Expired</span>
                                    @else
                                        <span class="badge bg-warning">Draft</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Announcement Preview -->
                            <div class="alert {{ $announcement->alert_class }} border-start border-4" role="alert">
                                <div class="d-flex align-items-start">
                                    <i class="{{ $announcement->icon }} fa-lg me-3 mt-1"></i>
                                    <div class="flex-grow-1">
                                        <h5 class="alert-heading mb-2">{{ $announcement->title }}</h5>
                                        <div class="announcement-content">
                                            {!! $announcement->content !!}
                                        </div>
                                        @if($announcement->affected_features && count($announcement->affected_features) > 0)
                                            <div class="mt-3">
                                                <strong>Affected Features:</strong>
                                                @foreach($announcement->affected_features as $feature)
                                                    <span class="badge bg-dark me-1">{{ $feature }}</span>
                                                @endforeach
                                            </div>
                                        @endif
                                        @if($announcement->starts_at || $announcement->ends_at)
                                            <div class="mt-3 small">
                                                @if($announcement->starts_at)
                                                    <div><strong>Start:</strong> {{ $announcement->starts_at->format('M d, Y \a\t H:i') }}</div>
                                                @endif
                                                @if($announcement->ends_at)
                                                    <div><strong>End:</strong> {{ $announcement->ends_at->format('M d, Y \a\t H:i') }}</div>
                                                @endif
                                            </div>
                                        @endif
                                    </div>
                                    @if($announcement->is_dismissible)
                                        <button type="button" class="btn-close" disabled title="Dismissible by users"></button>
                                    @endif
                                </div>
                            </div>

                            <!-- Display Settings -->
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <h6 class="fw-bold">Display Settings</h6>
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-tachometer-alt me-2 {{ $announcement->show_on_dashboard ? 'text-success' : 'text-muted' }}"></i>
                                            Dashboard Display: 
                                            <span class="badge {{ $announcement->show_on_dashboard ? 'bg-success' : 'bg-secondary' }}">
                                                {{ $announcement->show_on_dashboard ? 'Enabled' : 'Disabled' }}
                                            </span>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-sign-in-alt me-2 {{ $announcement->show_on_login ? 'text-success' : 'text-muted' }}"></i>
                                            Login Page Display: 
                                            <span class="badge {{ $announcement->show_on_login ? 'bg-success' : 'bg-secondary' }}">
                                                {{ $announcement->show_on_login ? 'Enabled' : 'Disabled' }}
                                            </span>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-times-circle me-2 {{ $announcement->is_dismissible ? 'text-success' : 'text-muted' }}"></i>
                                            User Dismissible: 
                                            <span class="badge {{ $announcement->is_dismissible ? 'bg-success' : 'bg-secondary' }}">
                                                {{ $announcement->is_dismissible ? 'Yes' : 'No' }}
                                            </span>
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-envelope me-2 {{ $announcement->send_email ? 'text-success' : 'text-muted' }}"></i>
                                            Email Notifications: 
                                            <span class="badge {{ $announcement->send_email ? 'bg-success' : 'bg-secondary' }}">
                                                {{ $announcement->send_email ? 'Enabled' : 'Disabled' }}
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold">Schedule Information</h6>
                                    <ul class="list-unstyled">
                                        @if($announcement->starts_at)
                                            <li class="mb-2">
                                                <i class="fas fa-play me-2 text-info"></i>
                                                <strong>Starts:</strong> {{ $announcement->starts_at->format('M d, Y \a\t H:i') }}
                                                <div class="small text-muted">{{ $announcement->starts_at->diffForHumans() }}</div>
                                            </li>
                                        @else
                                            <li class="mb-2">
                                                <i class="fas fa-play me-2 text-muted"></i>
                                                <strong>Starts:</strong> <span class="text-muted">Immediately when published</span>
                                            </li>
                                        @endif
                                        
                                        @if($announcement->ends_at)
                                            <li class="mb-2">
                                                <i class="fas fa-stop me-2 text-warning"></i>
                                                <strong>Ends:</strong> {{ $announcement->ends_at->format('M d, Y \a\t H:i') }}
                                                <div class="small text-muted">{{ $announcement->ends_at->diffForHumans() }}</div>
                                            </li>
                                        @else
                                            <li class="mb-2">
                                                <i class="fas fa-stop me-2 text-muted"></i>
                                                <strong>Ends:</strong> <span class="text-muted">No end date (permanent)</span>
                                            </li>
                                        @endif
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Interactions -->
                    @if(isset($stats) && ($stats['total_views'] > 0 || $stats['total_dismissals'] > 0))
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    User Engagement
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-md-4">
                                        <h4 class="text-primary">{{ $stats['total_views'] }}</h4>
                                        <p class="text-muted mb-0">Total Views</p>
                                    </div>
                                    <div class="col-md-4">
                                        <h4 class="text-warning">{{ $stats['total_dismissals'] }}</h4>
                                        <p class="text-muted mb-0">Dismissals</p>
                                    </div>
                                    <div class="col-md-4">
                                        <h4 class="text-success">{{ $stats['engagement_rate'] }}%</h4>
                                        <p class="text-muted mb-0">Engagement Rate</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Announcement Details -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Announcement Details</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Type:</strong><br>
                                {!! $announcement->type_badge !!}
                            </div>
                            <div class="mb-3">
                                <strong>Priority:</strong><br>
                                {!! $announcement->priority_badge !!}
                            </div>
                            <div class="mb-3">
                                <strong>Target Audience:</strong><br>
                                <span class="badge bg-secondary">
                                    {{ \App\Models\Announcement::getTargetAudienceOptions()[$announcement->target_audience] }}
                                </span>
                            </div>
                            <div class="mb-3">
                                <strong>Created By:</strong><br>
                                <small class="text-muted">{{ $announcement->creator->name ?? 'Unknown' }}</small>
                            </div>
                            <div class="mb-3">
                                <strong>Created:</strong><br>
                                <small class="text-muted">{{ $announcement->created_at->format('M d, Y \a\t H:i') }}</small>
                            </div>
                            <div class="mb-3">
                                <strong>Last Updated:</strong><br>
                                <small class="text-muted">{{ $announcement->updated_at->format('M d, Y \a\t H:i') }}</small>
                            </div>
                            @if($announcement->published_at)
                                <div class="mb-3">
                                    <strong>Published:</strong><br>
                                    <small class="text-muted">{{ $announcement->published_at->format('M d, Y \a\t H:i') }}</small>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('super_admin.announcements.edit', $announcement) }}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> Edit Announcement
                                </a>
                                
                                @if(!$announcement->published_at || $announcement->published_at > now())
                                    <form method="POST" action="{{ route('super_admin.announcements.publish', $announcement) }}" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-globe"></i> Publish Announcement
                                        </button>
                                    </form>
                                @elseif($announcement->is_active)
                                    <form method="POST" action="{{ route('super_admin.announcements.unpublish', $announcement) }}" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-warning w-100">
                                            <i class="fas fa-eye-slash"></i> Unpublish Announcement
                                        </button>
                                    </form>
                                @endif

                                <a href="{{ route('super_admin.announcements.create') }}?type={{ $announcement->type }}" class="btn btn-outline-info">
                                    <i class="fas fa-copy"></i> Create Similar
                                </a>

                                <form method="POST" action="{{ route('super_admin.announcements.destroy', $announcement) }}" 
                                      onsubmit="return confirm('Are you sure you want to delete this announcement? This action cannot be undone.')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger w-100">
                                        <i class="fas fa-trash"></i> Delete Announcement
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.announcement-content {
    line-height: 1.6;
}

.announcement-content h1,
.announcement-content h2,
.announcement-content h3,
.announcement-content h4,
.announcement-content h5,
.announcement-content h6 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.announcement-content p {
    margin-bottom: 1rem;
}

.announcement-content ul,
.announcement-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.announcement-content blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
}

.announcement-content code {
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}
</style>
@endsection
