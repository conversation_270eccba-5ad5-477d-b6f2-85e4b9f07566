<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use App\Console\Commands\MarkMigrationsCompleted;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\CheckUserRoles::class,
        Commands\DropAllTables::class,
        MarkMigrationsCompleted::class,
    ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Data retention enforcement - daily at 2 AM
        $schedule->command('data:enforce-retention')->dailyAt('02:00');

        // Expiration notifications - daily at 9 AM
        $schedule->command('notifications:send-expiration')->dailyAt('09:00');

        // Process scheduled plan changes - daily at 1 AM
        $schedule->command('subscriptions:process-scheduled-changes')->dailyAt('01:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
