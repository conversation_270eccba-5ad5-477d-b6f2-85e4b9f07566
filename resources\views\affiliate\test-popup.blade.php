@extends('affiliate.layouts.app')

@section('title', 'Test Email Verification Popup')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-test-tube"></i> Test Email Verification Popup
                    </h5>
                </div>
                <div class="card-body">
                    <p>This page is used to test the email verification success popup.</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Test Buttons</h6>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-success" onclick="showTestPopup()">
                                    <i class="fas fa-check-circle me-2"></i>
                                    Show Email Verification Popup
                                </button>
                                <button type="button" class="btn btn-info" onclick="createConfetti()">
                                    <i class="fas fa-star me-2"></i>
                                    Test Confetti Effect
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Popup Features</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i> Animated entrance</li>
                                <li><i class="fas fa-check text-success me-2"></i> Confetti celebration</li>
                                <li><i class="fas fa-check text-success me-2"></i> Auto-close after 15 seconds</li>
                                <li><i class="fas fa-check text-success me-2"></i> Click to close</li>
                                <li><i class="fas fa-check text-success me-2"></i> Mobile responsive</li>
                                <li><i class="fas fa-check text-success me-2"></i> Professional styling</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showTestPopup() {
    // Remove existing popup if any
    const existingPopup = document.getElementById('emailVerificationPopup');
    if (existingPopup) {
        existingPopup.remove();
    }
    
    // Create popup HTML
    const popupHTML = `
        <div id="emailVerificationPopup" class="email-verification-popup">
            <div class="popup-content">
                <div class="popup-header">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3>🎉 Email Verified Successfully!</h3>
                </div>
                <div class="popup-body">
                    <p>Email verified successfully! Welcome to your affiliate dashboard.</p>
                    <p class="text-muted small">You can now access all affiliate features and start earning commissions!</p>
                    <div class="celebration-icons">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                <div class="popup-footer">
                    <button type="button" class="btn btn-success btn-lg" onclick="closeEmailVerificationPopup()">
                        <i class="fas fa-rocket me-2"></i>
                        Get Started!
                    </button>
                </div>
            </div>
            <div class="popup-overlay" onclick="closeEmailVerificationPopup()"></div>
        </div>
    `;
    
    // Add popup to body
    document.body.insertAdjacentHTML('beforeend', popupHTML);
    
    // Add confetti effect
    setTimeout(createConfetti, 500);
}
</script>
@endsection
