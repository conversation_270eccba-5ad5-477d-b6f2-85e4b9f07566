<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OrderStatusUpdateRequest extends FormRequest
{
    public function authorize()
    {
        return $this->user()->hasAnyRole(['Organization Owner', 'Operator', 'Production'])
            && $this->user()->organization_id === $this->route('order')->organization_id;
    }

    public function rules()
    {
        return [
            'status' => 'required|in:Processing,Completed',
            'notes' => 'nullable|string|max:500'
        ];
    }
}