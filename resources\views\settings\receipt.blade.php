@extends('layouts.settings')

@section('title', 'Receipt Settings')

@section('receipt_settings')
<style>
    /* Custom styles for intl-tel-input to match form design */
    .iti {
        width: 100%;
    }
    .iti__flag-container {
        display: flex;
    }
    .iti__selected-flag {
        border-radius: 0.375rem 0 0 0.375rem;
        background-color: #f9fafb;
        border: 1px solid #d1d5db;
        border-right: none;
    }
    .iti--allow-dropdown input {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
    }
</style>
<h2 class="text-2xl font-bold mb-6">Receipt Settings</h2>
<p class="text-gray-600 mb-4">This information will appear on your receipts.</p>

<form action="{{ route('settings.receipt.update') }}" method="POST">
    @csrf
    @method('PUT')

    <div class="mb-6">
        <div class="mb-4">
            <label for="company_address" class="block text-gray-700 text-sm font-bold mb-2">
                Branch Address
            </label>
            <textarea
                name="company_address"
                id="company_address"
                rows="4"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                placeholder="Enter your branch's address (use new lines for formatting)"
            >{{ old('company_address', $setting->company_address) }}</textarea>
            <p class="text-gray-500 text-xs mt-1">Use line breaks for proper formatting on the receipt.</p>
            @error('company_address')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="company_phone" class="block text-gray-700 text-sm font-bold mb-2">
                Contact Phone
            </label>
            <input
                type="tel"
                name="company_phone"
                id="company_phone"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value="{{ old('company_phone', $setting->company_phone) }}"
                placeholder="Enter phone number"
            >
            <input type="hidden" name="company_phone_full" id="company_phone_full">
            <p class="text-gray-500 text-xs mt-1">Include country code for international customers.</p>
            @error('company_phone')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="company_email" class="block text-gray-700 text-sm font-bold mb-2">
                Company Email
            </label>
            <input
                type="email"
                name="company_email"
                id="company_email"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                value="{{ old('company_email', $setting->company_email) }}"
                placeholder="e.g., <EMAIL>"
            >
            @error('company_email')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
    </div>

    <div class="flex items-center justify-end pt-6">
        <button
            type="submit"
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded focus:outline-none focus:shadow-outline"
        >
            Save Receipt Settings
        </button>
    </div>
</form>

<div class="bg-white shadow-md rounded-lg p-6 mt-6">
    <h3 class="text-xl font-semibold mb-4">Receipt Preview</h3>
    <p class="text-gray-600 mb-4">Below is how your company information will appear on receipts:</p>

    <div class="border rounded-lg p-4 font-mono text-sm">
        <div class="text-center border-b pb-2 mb-2">
            <div class="font-bold">{{ $setting->organization_name ?: config('app.name', 'Kadmon Printing Company Ltd.') }}</div>
            <div style="white-space: pre-line">{{ $setting->company_address }}</div>
            <div>Sales Receipt</div>
        </div>
        <div class="mb-2">
            <strong>Receipt #:</strong> RCP-EXAMPLE<br>
            <strong>Date:</strong> {{ now()->format('M d, Y h:i A') }}<br>
            <strong>Customer:</strong> Sample Customer<br>
            <strong>Phone:</strong> 08012345678
        </div>
        <div class="border-t pt-2 text-center">
            <div>Thank you for your patronage!</div>
            <div class="text-xs mt-1">
                For Enquiries: {{ $setting->company_phone ?? '080, 081' }}
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize and configure the international telephone input
    const phoneInputField = document.querySelector("#company_phone");
    const phoneInput = window.intlTelInput(phoneInputField, {
        utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js",
        initialCountry: "auto",
        geoIpLookup: function(callback) {
            fetch("https://ipapi.co/json")
              .then(function(res) { return res.json(); })
              .then(function(data) { callback(data.country_code); })
              .catch(function() { callback("ng"); }); // Default to Nigeria
        },
        preferredCountries: ["ng", "us", "gb"],
        separateDialCode: true,
        formatOnDisplay: true,
    });

    // Store the full number with country code when submitting the form
    document.querySelector('form').addEventListener('submit', function() {
        const fullNumber = phoneInput.getNumber();
        document.getElementById('company_phone_full').value = fullNumber;
    });
});
</script>
@endpush
