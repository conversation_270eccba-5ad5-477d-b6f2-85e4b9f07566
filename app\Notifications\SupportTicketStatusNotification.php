<?php

namespace App\Notifications;

use App\Models\SupportTicket;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SupportTicketStatusNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $ticket;
    public $oldStatus;
    public $newStatus;
    public $adminName;

    /**
     * Create a new notification instance.
     */
    public function __construct(SupportTicket $ticket, string $oldStatus, string $newStatus, string $adminName = null)
    {
        $this->ticket = $ticket;
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;
        $this->adminName = $adminName;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $subject = "Ticket Status Update - #{$this->ticket->ticket_number}";
        $statusMessage = $this->getStatusMessage();
        $actionUrl = route('user.support.show', $this->ticket);

        $mail = (new MailMessage)
            ->subject($subject)
            ->greeting("Hello {$notifiable->name},")
            ->line($statusMessage)
            ->line("**Ticket:** #{$this->ticket->ticket_number}")
            ->line("**Subject:** {$this->ticket->title}")
            ->line("**Previous Status:** " . ucfirst(str_replace('_', ' ', $this->oldStatus)))
            ->line("**Current Status:** " . ucfirst(str_replace('_', ' ', $this->newStatus)));

        if ($this->adminName) {
            $mail->line("**Updated by:** {$this->adminName}");
        }

        $mail->action('View Ticket Details', $actionUrl);

        // Add status-specific messages
        switch ($this->newStatus) {
            case 'resolved':
                $mail->line('Your issue has been resolved. If you\'re satisfied with the solution, no further action is needed.')
                     ->line('If you need additional help, you can reply to the ticket or create a new one.');
                break;
            case 'closed':
                $mail->line('This ticket has been closed. If you need further assistance, please create a new support ticket.');
                break;
            case 'in_progress':
                $mail->line('Our support team is actively working on your issue. We\'ll keep you updated on our progress.');
                break;
            case 'waiting_customer':
                $mail->line('We\'re waiting for your response. Please check the ticket and provide any additional information requested.');
                break;
        }

        $mail->line('Thank you for using our support system!');

        return $mail;
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'ticket_id' => $this->ticket->id,
            'ticket_number' => $this->ticket->ticket_number,
            'ticket_title' => $this->ticket->title,
            'old_status' => $this->oldStatus,
            'new_status' => $this->newStatus,
            'admin_name' => $this->adminName,
            'status_message' => $this->getStatusMessage(),
            'action_url' => route('user.support.show', $this->ticket),
        ];
    }

    /**
     * Get status change message
     */
    private function getStatusMessage(): string
    {
        $oldStatusFormatted = ucfirst(str_replace('_', ' ', $this->oldStatus));
        $newStatusFormatted = ucfirst(str_replace('_', ' ', $this->newStatus));

        switch ($this->newStatus) {
            case 'resolved':
                return "Great news! Your support ticket has been resolved.";
            case 'closed':
                return "Your support ticket has been closed.";
            case 'in_progress':
                return "Your support ticket is now being actively worked on.";
            case 'waiting_customer':
                return "We need additional information from you to continue with your support ticket.";
            case 'waiting_admin':
                return "Your support ticket has been updated and is waiting for our team's response.";
            default:
                return "Your support ticket status has been updated from {$oldStatusFormatted} to {$newStatusFormatted}.";
        }
    }

    /**
     * Get notification title for database storage
     */
    public function getTitle(): string
    {
        return "Ticket Status Update - #{$this->ticket->ticket_number}";
    }

    /**
     * Get notification icon for UI
     */
    public function getIcon(): string
    {
        switch ($this->newStatus) {
            case 'resolved':
                return 'fas fa-check-circle';
            case 'closed':
                return 'fas fa-times-circle';
            case 'in_progress':
                return 'fas fa-cog';
            case 'waiting_customer':
                return 'fas fa-clock';
            default:
                return 'fas fa-info-circle';
        }
    }

    /**
     * Get notification color for UI
     */
    public function getColor(): string
    {
        switch ($this->newStatus) {
            case 'resolved':
                return 'success';
            case 'closed':
                return 'secondary';
            case 'in_progress':
                return 'primary';
            case 'waiting_customer':
                return 'warning';
            default:
                return 'info';
        }
    }
}
