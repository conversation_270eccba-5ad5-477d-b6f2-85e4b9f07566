<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Receipt - {{ $orders[0]->customer_name }}</title>
    <style>
        @page {
            margin: 0;
            size: 80mm 300mm;
        }
        @font-face {
            font-family: 'DejaVu Sans';
            src: url('{{ storage_path('fonts/DejaVuSans.ttf') }}') format('truetype');
            font-weight: normal;
            font-style: normal;
        }
        body {
            font-family: 'DejaVu Sans', sans-serif;
            margin: 2mm;
            padding: 0;
            font-size: 10pt;
            line-height: 1.2;
            width: 76mm;
        }
        .header {
            text-align: center;
            margin-bottom: 3mm;
            border-bottom: 1px dashed #000;
            padding-bottom: 2mm;
        }
        .header h1 {
            margin: 0;
            font-size: 14pt;
            font-weight: bold;
        }
        .header .address {
            margin: 2mm auto;
            font-size: 8.5pt;
            line-height: 1.4;
            text-align: center;
            padding: 1mm 0;
        }
        .receipt-info {
            margin-bottom: 3mm;
            font-size: 9pt;
        }
        .receipt-info p {
            margin: 1mm 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 2mm 0;
            font-size: 9pt;
        }
        th, td {
            padding: 1mm;
            text-align: left;
            border-bottom: 1px dotted #000;
        }
        th {
            font-weight: bold;
        }
        .totals {
            margin-top: 2mm;
            text-align: right;
            font-size: 9pt;
            border-top: 1px dashed #000;
            padding-top: 2mm;
        }
        .totals p {
            margin: 1mm 0;
        }
        .footer {
            margin-top: 5mm;
            text-align: center;
            font-size: 9pt;
            border-top: 1px dashed #000;
            padding-top: 2mm;
        }
        .order-separator {
            border-top: 1px dashed #000;
            margin: 3mm 0;
        }
        .contact-info {
            margin-top: 2mm;
            padding: 2mm 0;
            border-top: 1px dotted #000;
            font-size: 8pt;
        }
        .developer-info {
            margin-top: 2mm;
            font-size: 8pt;
        }
        .cut-line {
            border-bottom: 1px dashed #000;
            margin: 5mm 0;
            position: relative;
        }
        .cut-line:after {
            content: '✂';
            position: absolute;
            right: -4mm;
            top: -2mm;
            font-size: 8pt;
        }
        .amount {
            font-family: 'DejaVu Sans', sans-serif;
            font-weight: bold;
        }
        .small-text {
            font-size: 8pt;
        }
        .bold {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            @php
                $settings = \App\Models\Setting::where('organization_id', $orders[0]->organization_id)->first();
                $organizationName = $settings && $settings->organization_name ? $settings->organization_name : config('app.name', 'Name of the Application.');

                // Get branch-specific information if order has a branch
                $branch = null;
                if ($orders[0]->branch_id) {
                    $branch = \App\Models\Branch::find($orders[0]->branch_id);
                }
            @endphp
            {{ $organizationName }}
        </h1>
        <p class="address">
            @php
                // Use branch address if available, otherwise fall back to organization-wide settings
                $address = $branch && $branch->address ? $branch->address :
                          ($settings && $settings->company_address ? $settings->company_address :
                           '[Address here]
						   Go to Settings using the organization owner account, find the Receipt tab, update your address						   .');

                // Use branch phone if available, otherwise fall back to organization-wide settings
                $phone = $branch && $branch->phone ? $branch->phone :
                        ($settings && $settings->company_phone ? $settings->company_phone :
                         '**********, **********');

                // Get branch-specific email
                $email = $branch && $branch->email ? $branch->email :
                        ($settings && $settings->company_email ? $settings->company_email : '');
            @endphp
            {!! nl2br(e($address)) !!}
        </p>
        <p>Sales Receipt</p>
    </div>

    <div class="receipt-info">
        <p><strong>Receipt #:</strong> RCP-{{ Str::random(8) }}</p>
        <p><strong>Date:</strong> {{ now()->format('M d, Y h:i A') }}</p>
        <p><strong>Customer:</strong> {{ $orders[0]->customer_name }}</p>
        <p><strong>Phone:</strong> {{ $orders[0]->phone_number }}</p>
        @if($branch)
        <p><strong>Branch:</strong> {{ $branch->name }}</p>
        @endif
    </div>

    @php
        $grandTotal = 0;
        $grandPaid = 0;
        $grandPending = 0;
    @endphp

    @foreach($orders as $order)
        @if(!$loop->first)
            <div class="cut-line"></div>
        @endif

        <div class="order-details">
            <p class="bold">Order #{{ $order->order_number }}</p>
            <table>
                <tr>
                    <td colspan="2" class="bold">{{ $order->order_title }}</td>
                </tr>
                <tr>
                    <td colspan="2" class="small-text">{{ $order->job_description }}</td>
                </tr>
                <tr>
                    <td>Department:</td>
                    <td>{{ $order->department }}</td>
                </tr>
                <tr>
                    <td>Quantity:</td>
                    <td>{{ number_format($order->quantity) }}</td>
                </tr>
                <tr>
                    <td>Unit Price:</td>
                    <td class="amount">{{ currency_symbol() }}{{ number_format($order->unit_cost, 2) }}</td>
                </tr>
                @if($order->media || $order->pages || $order->size)
                <tr>
                    <td colspan="2" class="small-text">
                        @if($order->media)Media: {{ $order->media }}@endif
                        @if($order->pages)• Pages: {{ $order->pages }}@endif
                        @if($order->size)• Size: {{ $order->size }}@endif
                    </td>
                </tr>
                @endif
            </table>

            <div class="totals">
                <p>Total Amount: <span class="amount">{{ currency_symbol() }}{{ number_format($order->total_amount, 2) }}</span></p>
                <p>Amount Paid: <span class="amount">{{ currency_symbol() }}{{ number_format($order->amount_paid, 2) }}</span></p>
                @if($order->pending_payment > 0)
                    <p>Balance: <span class="amount">{{ currency_symbol() }}{{ number_format($order->pending_payment, 2) }}</span></p>
                @endif
            </div>

            <p class="small-text"><strong>Delivery:</strong> {{ $order->expected_delivery_date->format('M d, Y') }} at {{ $order->expected_delivery_time }}</p>
        </div>

        @php
            $grandTotal += $order->total_amount;
            $grandPaid += $order->amount_paid;
            $grandPending += $order->pending_payment;
        @endphp
    @endforeach

    @if(count($orders) > 1)
        <div class="cut-line"></div>
        <div class="totals">
            <p class="bold">Grand Totals</p>
            <p>Total Amount: <span class="amount">{{ currency_symbol() }}{{ number_format($grandTotal, 2) }}</span></p>
            <p>Total Paid: <span class="amount">{{ currency_symbol() }}{{ number_format($grandPaid, 2) }}</span></p>
            @if($grandPending > 0)
                <p>Balance: <span class="amount">{{ currency_symbol() }}{{ number_format($grandPending, 2) }}</span></p>
            @endif
        </div>
    @endif

    <div class="footer">
        <p>Thank you for your petronage!</p>
        <div class="contact-info">
            <p>For Enquiries: {{ $phone }}</p>
            @if($email)
            <p>Email: {{ $email }}</p>
            @endif
        </div>
        <div class="developer-info">
            <p>Developed By: Kuronicz Tech</p>
			<p>Social Media: Kuronicz Tech</p>
            <p class="small-text">{{ now()->format('Y-m-d H:i:s') }}</p>
        </div>
    </div>
</body>
</html>
