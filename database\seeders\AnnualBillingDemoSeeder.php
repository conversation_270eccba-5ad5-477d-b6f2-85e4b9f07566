<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Plan;

class AnnualBillingDemoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing plans for demo
        Plan::truncate();
        
        // Create sample plans with annual billing
        $plans = [
            [
                'name' => 'Starter',
                'slug' => 'starter',
                'description' => 'Perfect for small businesses just getting started with order management',
                'price' => 29.99,
                'annual_price' => 305.89, // 15% discount
                'billing_period' => 'both',
                'annual_discount_percentage' => 15,
                'branch_limit' => 1,
                'user_limit' => 5,
                'data_retention_days' => 30,
                'order_limit' => 100,
                'thermal_printing' => false,
                'advanced_reporting' => false,
                'api_access' => false,
                'white_label' => false,
                'custom_branding' => false,
                'is_active' => true,
                'is_featured' => false,
            ],
            [
                'name' => 'Professional',
                'slug' => 'professional',
                'description' => 'Ideal for growing businesses with advanced reporting and multiple locations',
                'price' => 59.99,
                'annual_price' => 611.89, // 15% discount
                'billing_period' => 'both',
                'annual_discount_percentage' => 15,
                'branch_limit' => 5,
                'user_limit' => 25,
                'data_retention_days' => 90,
                'order_limit' => 500,
                'thermal_printing' => true,
                'advanced_reporting' => true,
                'api_access' => false,
                'white_label' => false,
                'custom_branding' => false,
                'is_active' => true,
                'is_featured' => true,
            ],
            [
                'name' => 'Business',
                'slug' => 'business',
                'description' => 'Comprehensive solution for established businesses with API access',
                'price' => 99.99,
                'annual_price' => 959.90, // 20% discount (better deal)
                'billing_period' => 'both',
                'annual_discount_percentage' => 20,
                'branch_limit' => 15,
                'user_limit' => 100,
                'data_retention_days' => 180,
                'order_limit' => 2000,
                'thermal_printing' => true,
                'advanced_reporting' => true,
                'api_access' => true,
                'white_label' => false,
                'custom_branding' => true,
                'is_active' => true,
                'is_featured' => false,
            ],
            [
                'name' => 'Enterprise',
                'slug' => 'enterprise',
                'description' => 'Complete white-label solution for large organizations and resellers',
                'price' => 199.99,
                'annual_price' => 1919.90, // 20% discount
                'billing_period' => 'both',
                'annual_discount_percentage' => 20,
                'branch_limit' => 999,
                'user_limit' => 999,
                'data_retention_days' => 999,
                'order_limit' => null, // Unlimited
                'thermal_printing' => true,
                'advanced_reporting' => true,
                'api_access' => true,
                'white_label' => true,
                'custom_branding' => true,
                'is_active' => true,
                'is_featured' => false,
            ],
            [
                'name' => 'Basic Monthly',
                'slug' => 'basic-monthly',
                'description' => 'Simple monthly-only plan for budget-conscious users',
                'price' => 19.99,
                'annual_price' => null,
                'billing_period' => 'monthly',
                'annual_discount_percentage' => 0,
                'branch_limit' => 1,
                'user_limit' => 3,
                'data_retention_days' => 30,
                'order_limit' => 50,
                'thermal_printing' => false,
                'advanced_reporting' => false,
                'api_access' => false,
                'white_label' => false,
                'custom_branding' => false,
                'is_active' => true,
                'is_featured' => false,
            ],
            [
                'name' => 'Annual Special',
                'slug' => 'annual-special',
                'description' => 'Special annual-only plan with maximum savings',
                'price' => 79.99, // Monthly equivalent
                'annual_price' => 799.90, // ~17% discount
                'billing_period' => 'annual',
                'annual_discount_percentage' => 17,
                'branch_limit' => 10,
                'user_limit' => 50,
                'data_retention_days' => 365,
                'order_limit' => 1000,
                'thermal_printing' => true,
                'advanced_reporting' => true,
                'api_access' => true,
                'white_label' => false,
                'custom_branding' => true,
                'is_active' => true,
                'is_featured' => false,
            ],
        ];
        
        foreach ($plans as $planData) {
            $plan = Plan::create($planData);
            
            // Calculate and display savings for demo
            if ($plan->supportsAnnualBilling()) {
                $savings = $plan->getAnnualSavings();
                $savingsPercentage = $plan->getAnnualSavingsPercentage();
                echo "Created {$plan->name}:\n";
                echo "  Monthly: \${$plan->price}\n";
                echo "  Annual: \${$plan->getEffectiveAnnualPrice()}\n";
                echo "  Savings: \${$savings} ({$savingsPercentage}%)\n";
                echo "  Billing: {$plan->billing_period}\n\n";
            } else {
                echo "Created {$plan->name} (Monthly only): \${$plan->price}\n\n";
            }
        }
        
        echo "Demo plans created successfully!\n";
        echo "Features demonstrated:\n";
        echo "✓ Both monthly and annual billing options\n";
        echo "✓ Different discount percentages (15%, 17%, 20%)\n";
        echo "✓ Monthly-only and annual-only plans\n";
        echo "✓ Explicit annual pricing vs calculated discounts\n";
        echo "✓ Featured plan highlighting\n";
        echo "✓ Various plan limits and features\n";
        echo "\nVisit the plan selection page to see the annual billing in action!\n";
    }
}
