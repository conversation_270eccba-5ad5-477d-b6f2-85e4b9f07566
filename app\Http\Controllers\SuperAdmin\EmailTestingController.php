<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Mail\WelcomeEmail;
use App\Mail\CustomVerifyEmail;
use App\Models\User;
use App\Models\Organization;
use App\Models\Affiliate;
use App\Models\WelcomeMessage;
use App\Models\AffiliateSetting;

class EmailTestingController extends Controller
{
    /**
     * Display the main email testing dashboard.
     */
    public function index()
    {
        $welcomeMessages = WelcomeMessage::all();
        $mailConfig = [
            'mailer' => config('mail.default'),
            'host' => config('mail.mailers.smtp.host'),
            'port' => config('mail.mailers.smtp.port'),
            'from' => config('mail.from.address'),
            'from_name' => config('mail.from.name')
        ];

        return view('super_admin.email_testing.index', compact('welcomeMessages', 'mailConfig'));
    }

    /**
     * Send basic test email.
     */
    public function sendBasicTest(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            Mail::raw('This is a basic test email from Sales Management System to verify Mailtrap integration. Sent at: ' . now(), function($message) use ($request) {
                $message->to($request->test_email)
                        ->subject('Basic Email Test - Sales Management System');
            });

            return back()->with('success', 'Basic test email sent successfully to ' . $request->test_email);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to send basic test email: ' . $e->getMessage()]);
        }
    }

    /**
     * Send welcome email test.
     */
    public function sendWelcomeTest(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email',
            'user_type' => 'required|in:organization,affiliate,super_admin'
        ]);

        try {
            // Check if welcome message exists for this user type
            $welcomeMessage = WelcomeMessage::where('user_type', $request->user_type)
                                          ->where('is_active', true)
                                          ->first();

            if (!$welcomeMessage) {
                return back()->withErrors(['error' => 'No active welcome message found for user type: ' . $request->user_type . '. Please create one first.']);
            }

            $dummyUser = new User([
                'name' => 'Test User',
                'email' => $request->test_email
            ]);

            // Send email immediately (not queued)
            Mail::to($request->test_email)->send(new WelcomeEmail($dummyUser, $request->user_type));

            return back()->with('success', 'Welcome email (' . ucfirst($request->user_type) . ') sent successfully to ' . $request->test_email)
                        ->with('welcome_message_info', [
                            'subject' => $welcomeMessage->subject,
                            'user_type' => $request->user_type,
                            'message_id' => $welcomeMessage->id
                        ]);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to send welcome email: ' . $e->getMessage() . ' (Line: ' . $e->getLine() . ')']);
        }
    }

    /**
     * Send all email types test.
     */
    public function sendAllTests(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email'
        ]);

        $results = [];

        // Test basic email
        try {
            Mail::raw('Comprehensive test - Basic email', function($message) use ($request) {
                $message->to($request->test_email)->subject('Comprehensive Test - Basic Email');
            });
            $results[] = ['type' => 'Basic Email', 'status' => 'success', 'message' => 'Sent successfully'];
        } catch (\Exception $e) {
            $results[] = ['type' => 'Basic Email', 'status' => 'error', 'message' => $e->getMessage()];
        }

        // Test welcome emails for each type
        $userTypes = ['organization', 'affiliate', 'super_admin'];
        foreach ($userTypes as $userType) {
            try {
                $dummyUser = new User(['name' => 'Test User', 'email' => $request->test_email]);
                Mail::to($request->test_email)->send(new WelcomeEmail($dummyUser, $userType));
                $results[] = ['type' => ucfirst($userType) . ' Welcome', 'status' => 'success', 'message' => 'Sent successfully'];
            } catch (\Exception $e) {
                $results[] = ['type' => ucfirst($userType) . ' Welcome', 'status' => 'error', 'message' => $e->getMessage()];
            }
        }

        return back()->with('test_results', $results);
    }

    /**
     * Initialize default welcome messages.
     */
    public function initializeDefaults()
    {
        try {
            WelcomeMessage::createDefaults(auth('super_admin')->id());
            return back()->with('success', 'Default welcome messages created successfully!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create default messages: ' . $e->getMessage()]);
        }
    }

    /**
     * Display authentication email testing page.
     */
    public function authEmails()
    {
        return view('super_admin.email_testing.auth_emails');
    }

    /**
     * Send email verification test.
     */
    public function sendVerificationTest(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            // Create a temporary user in database for testing
            $testUser = User::create([
                'name' => 'Email Verification Test User',
                'email' => $request->test_email,
                'password' => Hash::make('temporary_password'),
                'email_verified_at' => null,
            ]);

            // Send verification notification
            $testUser->sendEmailVerificationNotification();

            return back()->with('success', 'Email verification email sent successfully to ' . $request->test_email)
                        ->with('test_user_info', [
                            'user_id' => $testUser->id,
                            'note' => 'Test user created for verification testing. You can delete this user after testing.'
                        ]);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to send verification email: ' . $e->getMessage() . ' (Line: ' . $e->getLine() . ')']);
        }
    }

    /**
     * Test affiliate verification email specifically.
     */
    public function testAffiliateVerification(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            // Create a temporary user and affiliate for testing
            $testUser = User::create([
                'name' => 'Affiliate Verification Test User',
                'email' => $request->test_email,
                'password' => Hash::make('temporary_password'),
                'email_verified_at' => null,
            ]);

            // Create affiliate record
            $affiliate = Affiliate::create([
                'user_id' => $testUser->id,
                'referral_code' => 'TEST' . strtoupper(substr(md5($testUser->email), 0, 6)),
                'status' => Affiliate::STATUS_PENDING,
                'payment_details' => json_encode([
                    'method' => 'bank_transfer',
                    'bank_name' => 'Test Bank',
                    'account_number' => '**********',
                    'account_name' => 'Test User'
                ])
            ]);

            // Send verification notification
            $testUser->sendEmailVerificationNotification();

            return back()->with('success', 'Affiliate verification email sent successfully to ' . $request->test_email)
                        ->with('test_user_info', [
                            'user_id' => $testUser->id,
                            'affiliate_id' => $affiliate->id,
                            'referral_code' => $affiliate->referral_code,
                            'note' => 'Test affiliate user created. Verification link will use affiliate routes.'
                        ]);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to send affiliate verification email: ' . $e->getMessage() . ' (Line: ' . $e->getLine() . ')']);
        }
    }

    /**
     * Send password reset test.
     */
    public function sendPasswordResetTest(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            $status = Password::sendResetLink(['email' => $request->test_email]);

            if ($status === Password::RESET_LINK_SENT) {
                return back()->with('success', 'Password reset email sent successfully to ' . $request->test_email);
            } else {
                return back()->with('warning', 'Password reset status: ' . $status);
            }
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to send password reset email: ' . $e->getMessage()]);
        }
    }

    /**
     * Display account creation testing page.
     */
    public function accountCreation()
    {
        $testUsers = User::where('email', 'like', '%test%')
                        ->orWhere('email', 'like', '%example%')
                        ->orWhere('name', 'like', '%Test%')
                        ->with('roles')
                        ->get();

        return view('super_admin.email_testing.account_creation', compact('testUsers'));
    }

    /**
     * Create test organization account.
     */
    public function createOrganizationAccount(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            DB::beginTransaction();

            $organization = Organization::create([
                'name' => 'Test Organization ' . now()->format('Y-m-d H:i:s'),
                'email' => $request->test_email,
                'phone' => '+**********',
                'address' => '123 Test Street, Test City',
                'is_active' => true,
            ]);

            $user = User::create([
                'name' => 'Test Organization Owner',
                'email' => $request->test_email,
                'password' => Hash::make('password123'),
                'organization_id' => $organization->id,
                'status' => 'active',
            ]);

            $user->assignRole('Organization Owner');

            DB::commit();

            return back()->with('success', 'Organization account created successfully! Welcome email should be sent to ' . $request->test_email)
                        ->with('account_details', [
                            'type' => 'Organization',
                            'organization_id' => $organization->id,
                            'user_id' => $user->id,
                            'email' => $user->email,
                            'password' => 'password123',
                            'role' => 'Organization Owner'
                        ]);
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to create organization account: ' . $e->getMessage()]);
        }
    }

    /**
     * Create test affiliate account.
     */
    public function createAffiliateAccount(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            DB::beginTransaction();

            $user = User::create([
                'name' => 'Test Affiliate User',
                'email' => $request->test_email,
                'password' => Hash::make('password123'),
                'status' => 'active',
            ]);

            $affiliate = Affiliate::create([
                'user_id' => $user->id,
                'referral_code' => 'TEST' . strtoupper(substr(md5($request->test_email), 0, 6)),
                'commission_rate' => 10.00,
                'status' => 'active',
                'phone' => '+**********',
                'address' => '123 Affiliate Street, Test City',
                'bank_name' => 'Test Bank',
                'account_number' => '**********',
                'account_name' => 'Test Affiliate User',
            ]);

            $user->assignRole('Affiliate');

            DB::commit();

            return back()->with('success', 'Affiliate account created successfully! Welcome email should be sent to ' . $request->test_email)
                        ->with('account_details', [
                            'type' => 'Affiliate',
                            'affiliate_id' => $affiliate->id,
                            'user_id' => $user->id,
                            'email' => $user->email,
                            'password' => 'password123',
                            'referral_code' => $affiliate->referral_code,
                            'role' => 'Affiliate'
                        ]);
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to create affiliate account: ' . $e->getMessage()]);
        }
    }

    /**
     * Cleanup test accounts.
     */
    public function cleanupTestAccounts()
    {
        try {
            DB::beginTransaction();

            $deletedUsers = 0;
            $deletedOrganizations = 0;
            $deletedAffiliates = 0;

            $testUsers = User::where('email', 'like', '%test%')
                           ->orWhere('email', 'like', '%example%')
                           ->orWhere('name', 'like', '%Test%')
                           ->get();

            foreach ($testUsers as $user) {
                // Delete related affiliates
                $affiliate = Affiliate::where('user_id', $user->id)->first();
                if ($affiliate) {
                    $affiliate->delete();
                    $deletedAffiliates++;
                }

                // Delete related organizations
                if ($user->organization_id) {
                    $organization = Organization::find($user->organization_id);
                    if ($organization && strpos($organization->name, 'Test') !== false) {
                        $organization->delete();
                        $deletedOrganizations++;
                    }
                }

                $user->delete();
                $deletedUsers++;
            }

            DB::commit();

            return back()->with('success', 'Test account cleanup completed!')
                        ->with('cleanup_results', [
                            'deleted_users' => $deletedUsers,
                            'deleted_organizations' => $deletedOrganizations,
                            'deleted_affiliates' => $deletedAffiliates
                        ]);
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to cleanup test accounts: ' . $e->getMessage()]);
        }
    }

    /**
     * Display system check page.
     */
    public function systemCheck()
    {
        $testEmail = '<EMAIL>';

        // Perform system checks
        $checks = $this->performSystemChecks($testEmail);

        $mailConfig = [
            'mailer' => config('mail.default'),
            'host' => config('mail.mailers.smtp.host'),
            'port' => config('mail.mailers.smtp.port'),
            'from' => config('mail.from.address'),
            'from_name' => config('mail.from.name')
        ];

        $welcomeMessages = WelcomeMessage::all();
        $existingUser = User::where('email', $testEmail)->first();

        return view('super_admin.email_testing.system_check', compact(
            'testEmail', 'checks', 'mailConfig', 'welcomeMessages', 'existingUser'
        ));
    }

    /**
     * Send system check test email.
     */
    public function sendSystemCheckTest(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            Mail::raw('System check test email for affiliate account creation. Sent at: ' . now(), function($message) use ($request) {
                $message->to($request->test_email)
                        ->subject('System Check - Affiliate Account Test');
            });

            return back()->with('success', 'System check test email sent successfully to ' . $request->test_email);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to send system check test email: ' . $e->getMessage()]);
        }
    }

    /**
     * Display Mailtrap configuration diagnostics.
     */
    public function mailtrapDiagnostics()
    {
        $config = [
            'mailer' => config('mail.default'),
            'host' => config('mail.mailers.smtp.host'),
            'port' => config('mail.mailers.smtp.port'),
            'username' => config('mail.mailers.smtp.username'),
            'password' => config('mail.mailers.smtp.password'),
            'encryption' => config('mail.mailers.smtp.encryption'),
            'from_address' => config('mail.from.address'),
            'from_name' => config('mail.from.name'),
        ];

        return view('super_admin.email_testing.mailtrap_diagnostics', compact('config'));
    }

    /**
     * Send diagnostic email with detailed headers.
     */
    public function sendDiagnosticEmail(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            $diagnosticInfo = [
                'timestamp' => now()->toISOString(),
                'environment' => app()->environment(),
                'mailtrap_host' => config('mail.mailers.smtp.host'),
                'from_address' => config('mail.from.address'),
                'to_address' => $request->test_email,
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
            ];

            Mail::raw(
                "Mailtrap Diagnostic Email\n\n" .
                "This email is sent to diagnose Mailtrap configuration issues.\n\n" .
                "Diagnostic Information:\n" .
                json_encode($diagnosticInfo, JSON_PRETTY_PRINT),
                function($message) use ($request, $diagnosticInfo) {
                    $message->to($request->test_email)
                            ->subject('Mailtrap Diagnostic - ' . $diagnosticInfo['timestamp'])
                            ->priority(1);
                }
            );

            return back()->with('success', 'Diagnostic email sent successfully to ' . $request->test_email)
                        ->with('diagnostic_info', $diagnosticInfo);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to send diagnostic email: ' . $e->getMessage()]);
        }
    }

    /**
     * Diagnose welcome email issues.
     */
    public function diagnoseWelcomeEmail(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email',
            'user_type' => 'required|in:organization,affiliate,super_admin'
        ]);

        $diagnostics = [];
        $userType = $request->user_type;

        try {
            // Check 1: Welcome messages exist
            $totalMessages = WelcomeMessage::count();
            $diagnostics[] = [
                'check' => 'Total Welcome Messages',
                'result' => $totalMessages > 0 ? 'PASS' : 'FAIL',
                'details' => "Found {$totalMessages} welcome messages"
            ];

            // Check 2: Specific user type message exists
            $userTypeMessage = WelcomeMessage::where('user_type', $userType)->first();
            $diagnostics[] = [
                'check' => ucfirst($userType) . ' Welcome Message Exists',
                'result' => $userTypeMessage ? 'PASS' : 'FAIL',
                'details' => $userTypeMessage ? "Found message ID: {$userTypeMessage->id}" : 'No message found'
            ];

            // Check 3: Message is active
            if ($userTypeMessage) {
                $diagnostics[] = [
                    'check' => ucfirst($userType) . ' Message Active',
                    'result' => $userTypeMessage->is_active ? 'PASS' : 'FAIL',
                    'details' => $userTypeMessage->is_active ? 'Message is active' : 'Message is inactive'
                ];
            }

            // Check 4: Template exists
            $templatePath = resource_path('views/emails/welcome.blade.php');
            $templateExists = file_exists($templatePath);
            $diagnostics[] = [
                'check' => 'Welcome Email Template',
                'result' => $templateExists ? 'PASS' : 'FAIL',
                'details' => $templateExists ? 'Template file exists' : 'Template file missing'
            ];

            // Check 5: Try to create dummy user and send email
            try {
                $dummyUser = new User([
                    'name' => 'Diagnostic Test User',
                    'email' => $request->test_email
                ]);

                $welcomeEmail = new WelcomeEmail($dummyUser, $userType);

                $diagnostics[] = [
                    'check' => 'WelcomeEmail Class Instantiation',
                    'result' => 'PASS',
                    'details' => 'WelcomeEmail object created successfully'
                ];

                // Try to send the email
                Mail::to($request->test_email)->send($welcomeEmail);

                $diagnostics[] = [
                    'check' => 'Email Sending',
                    'result' => 'PASS',
                    'details' => 'Welcome email sent successfully'
                ];

            } catch (\Exception $e) {
                $diagnostics[] = [
                    'check' => 'Email Sending',
                    'result' => 'FAIL',
                    'details' => 'Error: ' . $e->getMessage() . ' (Line: ' . $e->getLine() . ')'
                ];
            }

            return back()->with('success', 'Welcome email diagnostics completed')
                        ->with('welcome_diagnostics', $diagnostics);

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Diagnostic failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Perform comprehensive system checks.
     */
    private function performSystemChecks($testEmail)
    {
        $checks = [];

        // Email Configuration Check
        $mailHost = config('mail.mailers.smtp.host');
        $checks[] = [
            'name' => 'Mailtrap Configuration',
            'status' => $mailHost === 'live.smtp.mailtrap.io' ? 'good' : 'error',
            'details' => $mailHost === 'live.smtp.mailtrap.io' ? 'Using Mailtrap Email Sending (live.smtp.mailtrap.io)' : 'Not configured for Mailtrap'
        ];

        // Email Sending Environment Check
        $fromAddress = config('mail.from.address');
        $checks[] = [
            'name' => 'Email Sending Domain',
            'status' => $fromAddress === '<EMAIL>' ? 'warning' : 'good',
            'details' => $fromAddress === '<EMAIL>' ?
                'Using sms.macivergroup.com - verify domain is verified in Mailtrap' :
                'From address: ' . $fromAddress
        ];

        // Mailtrap Environment Check
        $checks[] = [
            'name' => 'Mailtrap Environment',
            'status' => 'warning',
            'details' => 'Using Email Sending environment - emails should be delivered to real addresses if domain is verified'
        ];

        // Welcome Messages Check
        $affiliateMessage = WelcomeMessage::where('user_type', 'affiliate')->where('is_active', true)->first();
        $checks[] = [
            'name' => 'Affiliate Welcome Message',
            'status' => $affiliateMessage ? 'good' : 'error',
            'details' => $affiliateMessage ? 'Active affiliate welcome message found' : 'No active affiliate welcome message'
        ];

        // Email Availability Check
        $existingUser = User::where('email', $testEmail)->first();
        $checks[] = [
            'name' => 'Email Availability',
            'status' => $existingUser ? 'warning' : 'good',
            'details' => $existingUser ? 'Email already exists in system' : 'Email available for new account'
        ];

        // Affiliate Settings Check
        try {
            $affiliateSettings = AffiliateSetting::getInstance();
            $checks[] = [
                'name' => 'Affiliate Program Status',
                'status' => $affiliateSettings->program_active ? 'good' : 'error',
                'details' => $affiliateSettings->program_active ? 'Affiliate program is active' : 'Affiliate program is disabled'
            ];
        } catch (\Exception $e) {
            $checks[] = [
                'name' => 'Affiliate Program Status',
                'status' => 'error',
                'details' => 'Error checking affiliate settings: ' . $e->getMessage()
            ];
        }

        // Database Connection Check
        try {
            DB::connection()->getPdo();
            $checks[] = [
                'name' => 'Database Connection',
                'status' => 'good',
                'details' => 'Database connection successful'
            ];
        } catch (\Exception $e) {
            $checks[] = [
                'name' => 'Database Connection',
                'status' => 'error',
                'details' => 'Database connection failed'
            ];
        }

        return $checks;
    }

    /**
     * Check affiliate registration email flow.
     */
    public function checkAffiliateRegistration(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        $email = $request->email;
        $diagnostics = [];

        try {
            // Check if user exists
            $user = User::where('email', $email)->first();
            if ($user) {
                $diagnostics[] = [
                    'check' => 'User Account',
                    'result' => 'FOUND',
                    'details' => "User ID: {$user->id}, Name: {$user->name}, Created: {$user->created_at}"
                ];

                // Check email verification status
                $diagnostics[] = [
                    'check' => 'Email Verification',
                    'result' => $user->email_verified_at ? 'VERIFIED' : 'PENDING',
                    'details' => $user->email_verified_at ? "Verified at: {$user->email_verified_at}" : 'Email not verified'
                ];

                // Check affiliate record
                $affiliate = Affiliate::where('user_id', $user->id)->first();
                if ($affiliate) {
                    $diagnostics[] = [
                        'check' => 'Affiliate Record',
                        'result' => 'FOUND',
                        'details' => "Referral Code: {$affiliate->referral_code}, Status: {$affiliate->status}"
                    ];
                } else {
                    $diagnostics[] = [
                        'check' => 'Affiliate Record',
                        'result' => 'NOT FOUND',
                        'details' => 'No affiliate record exists for this user'
                    ];
                }

                // Check welcome message for affiliate
                $welcomeMessage = WelcomeMessage::where('user_type', 'affiliate')->where('is_active', true)->first();
                $diagnostics[] = [
                    'check' => 'Affiliate Welcome Message',
                    'result' => $welcomeMessage ? 'ACTIVE' : 'MISSING',
                    'details' => $welcomeMessage ? "Subject: {$welcomeMessage->subject}" : 'No active affiliate welcome message'
                ];

                // Test sending welcome email manually
                try {
                    Mail::to($email)->send(new WelcomeEmail($user, 'affiliate'));
                    $diagnostics[] = [
                        'check' => 'Manual Welcome Email Send',
                        'result' => 'SUCCESS',
                        'details' => 'Welcome email sent successfully'
                    ];
                } catch (\Exception $e) {
                    $diagnostics[] = [
                        'check' => 'Manual Welcome Email Send',
                        'result' => 'FAILED',
                        'details' => 'Error: ' . $e->getMessage()
                    ];
                }

                // Check if email verification is enabled
                $verificationEnabled = config('auth.verification', false);
                $diagnostics[] = [
                    'check' => 'Email Verification Enabled',
                    'result' => $verificationEnabled ? 'ENABLED' : 'DISABLED',
                    'details' => $verificationEnabled ? 'Email verification is required' : 'Email verification is not required'
                ];

            } else {
                $diagnostics[] = [
                    'check' => 'User Account',
                    'result' => 'NOT FOUND',
                    'details' => 'No user found with this email address'
                ];
            }

            return back()->with('success', 'Affiliate registration check completed for ' . $email)
                        ->with('registration_diagnostics', $diagnostics);

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Registration check failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Debug verification URL generation.
     */
    public function debugVerificationUrl(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        try {
            // Find existing user or create test user
            $user = User::where('email', $request->email)->first();

            if (!$user) {
                $user = User::create([
                    'name' => 'Debug Test User',
                    'email' => $request->email,
                    'password' => Hash::make('temporary_password'),
                    'email_verified_at' => null,
                ]);
            }

            // Check if user is affiliate
            $affiliate = Affiliate::where('user_id', $user->id)->first();

            // Generate verification URL manually
            $routeName = $affiliate ? 'affiliate.verification.verify' : 'verification.verify';

            $verificationUrl = \Illuminate\Support\Facades\URL::temporarySignedRoute(
                $routeName,
                \Illuminate\Support\Carbon::now()->addMinutes(60),
                [
                    'id' => $user->getKey(),
                    'hash' => sha1($user->getEmailForVerification()),
                ]
            );

            return back()->with('success', 'Verification URL generated successfully')
                        ->with('debug_info', [
                            'user_id' => $user->id,
                            'email' => $user->email,
                            'is_affiliate' => $affiliate ? 'Yes' : 'No',
                            'route_name' => $routeName,
                            'verification_url' => $verificationUrl,
                            'url_parts' => parse_url($verificationUrl)
                        ]);

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Debug failed: ' . $e->getMessage() . ' (Line: ' . $e->getLine() . ')']);
        }
    }

    /**
     * Test affiliate password reset email.
     */
    public function testAffiliatePasswordReset(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        try {
            // Find or create affiliate user
            $user = User::where('email', $request->email)->first();

            if (!$user) {
                $user = User::create([
                    'name' => 'Test Affiliate User',
                    'email' => $request->email,
                    'password' => Hash::make('temporary_password'),
                    'email_verified_at' => now(),
                ]);
            }

            // Ensure user has affiliate record
            $affiliate = Affiliate::where('user_id', $user->id)->first();
            if (!$affiliate) {
                $affiliate = Affiliate::create([
                    'user_id' => $user->id,
                    'referral_code' => 'TEST' . strtoupper(substr(md5($user->email), 0, 6)),
                    'status' => 'active',
                    'payment_details' => json_encode([
                        'method' => 'bank_transfer',
                        'bank_name' => 'Test Bank',
                        'account_number' => '**********',
                        'account_name' => 'Test User'
                    ])
                ]);
            }

            // Send password reset using affiliate broker
            $status = \Illuminate\Support\Facades\Password::broker('affiliates')->sendResetLink([
                'email' => $request->email
            ]);

            if ($status === \Illuminate\Support\Facades\Password::RESET_LINK_SENT) {
                return back()->with('success', 'Affiliate password reset email sent successfully to ' . $request->email);
            } else {
                return back()->withErrors(['error' => 'Failed to send password reset email: ' . __($status)]);
            }

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Password reset test failed: ' . $e->getMessage()]);
        }
    }
}
