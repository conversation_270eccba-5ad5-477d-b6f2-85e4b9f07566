<?php

// Simple script to create test announcements
// Run this from the browser: http://localhost/SalesManagementSystem/public/../create_test_announcement.php

require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    // Create test announcements using raw SQL to avoid any model issues
    $pdo = new PDO(
        'mysql:host=localhost;dbname=sales_management_system',
        'root',
        '',
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // Delete existing test announcements
    $pdo->exec("DELETE FROM announcements WHERE title LIKE 'Test%' OR title LIKE 'Welcome to Order Flow Pro%'");
    
    // Create announcement for organizations
    $stmt = $pdo->prepare("
        INSERT INTO announcements (
            title, content, type, priority, target_audience, 
            is_active, is_dismissible, show_on_login, show_on_dashboard, 
            send_email, published_at, created_by, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), 1, NOW(), NOW())
    ");
    
    $stmt->execute([
        'Welcome to Order Flow Pro!',
        'This is a test announcement to verify that the announcement system is working properly for organization users. You should see this message on your dashboard.',
        'info',
        'normal',
        'organizations',
        1, // is_active
        1, // is_dismissible
        0, // show_on_login
        1, // show_on_dashboard
        0  // send_email
    ]);
    
    echo "✅ Created announcement for organizations (ID: " . $pdo->lastInsertId() . ")<br>";
    
    // Create announcement for all users
    $stmt->execute([
        'System Maintenance Notice',
        'We will be performing scheduled maintenance on the system this weekend. Please save your work frequently.',
        'warning',
        'high',
        'all',
        1, // is_active
        1, // is_dismissible
        0, // show_on_login
        1, // show_on_dashboard
        0  // send_email
    ]);
    
    echo "✅ Created announcement for all users (ID: " . $pdo->lastInsertId() . ")<br>";
    
    // Check what announcements exist
    $stmt = $pdo->query("
        SELECT id, title, target_audience, is_active, show_on_dashboard, published_at 
        FROM announcements 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    
    echo "<br><h3>Current Announcements:</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Title</th><th>Audience</th><th>Active</th><th>Dashboard</th><th>Published</th></tr>";
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['title'] . "</td>";
        echo "<td>" . $row['target_audience'] . "</td>";
        echo "<td>" . ($row['is_active'] ? 'Yes' : 'No') . "</td>";
        echo "<td>" . ($row['show_on_dashboard'] ? 'Yes' : 'No') . "</td>";
        echo "<td>" . ($row['published_at'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<br><p>✅ Test announcements created successfully!</p>";
    echo "<p>Now go to the dashboard to see if they appear: <a href='public/dashboard'>Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
}
