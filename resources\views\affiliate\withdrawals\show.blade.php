@extends('affiliate.layouts.app')

@section('page-title', 'Withdrawal Details')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">Withdrawal Request #{{ $withdrawal->id }}</h2>
            <p class="text-muted mb-0">View details of your withdrawal request</p>
        </div>
        <div>
            <a href="{{ route('affiliate.withdrawals') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Withdrawals
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Withdrawal Details -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Withdrawal Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Request ID</label>
                            <p class="form-control-plaintext">#{{ $withdrawal->id }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Status</label>
                            <p class="form-control-plaintext">
                                @php
                                    $badgeClass = match($withdrawal->status) {
                                        'pending' => 'bg-warning',
                                        'approved' => 'bg-info',
                                        'paid' => 'bg-success',
                                        'rejected' => 'bg-danger',
                                        'cancelled' => 'bg-secondary',
                                        default => 'bg-secondary'
                                    };
                                @endphp
                                <span class="badge {{ $badgeClass }}">{{ ucfirst($withdrawal->status) }}</span>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Requested Date</label>
                            <p class="form-control-plaintext">
                                {{ $withdrawal->requested_at->format('M j, Y g:i A') }}
                            </p>
                        </div>
                        @if($withdrawal->processed_at)
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Processed Date</label>
                            <p class="form-control-plaintext">
                                {{ $withdrawal->processed_at->format('M j, Y g:i A') }}
                            </p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Amount Details -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Amount Breakdown</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Requested Amount:</strong></td>
                                <td class="text-end">${{ number_format($withdrawal->amount, 2) }}</td>
                            </tr>
                            @if($withdrawal->fee_amount > 0)
                                <tr>
                                    <td><strong>Processing Fee:</strong></td>
                                    <td class="text-end text-muted">-${{ number_format($withdrawal->fee_amount, 2) }}</td>
                                </tr>
                                <tr class="border-top">
                                    <td><strong>Net Amount:</strong></td>
                                    <td class="text-end"><strong>${{ number_format($withdrawal->net_amount, 2) }}</strong></td>
                                </tr>
                            @else
                                <tr class="border-top">
                                    <td><strong>Net Amount:</strong></td>
                                    <td class="text-end"><strong>${{ number_format($withdrawal->amount, 2) }}</strong></td>
                                </tr>
                            @endif
                        </table>
                    </div>
                </div>
            </div>

            <!-- Payment Details -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Payment Details</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Payment Method</label>
                            <p class="form-control-plaintext">
                                <span class="badge bg-info">{{ ucfirst(str_replace('_', ' ', $withdrawal->payment_method)) }}</span>
                            </p>
                        </div>
                        @if($withdrawal->transaction_reference)
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Transaction Reference</label>
                            <p class="form-control-plaintext">
                                <code>{{ $withdrawal->transaction_reference }}</code>
                            </p>
                        </div>
                        @endif
                    </div>

                    @if($withdrawal->payment_details)
                        <div class="mt-3">
                            <h6 class="fw-bold">Payment Information:</h6>
                            @if($withdrawal->payment_method === 'bank_transfer')
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Bank Name:</strong> {{ $withdrawal->payment_details['bank_name'] ?? 'N/A' }}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Account Name:</strong> {{ $withdrawal->payment_details['account_name'] ?? 'N/A' }}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Account Number:</strong> ****{{ substr($withdrawal->payment_details['account_number'] ?? '', -4) }}
                                    </div>
                                    @if(isset($withdrawal->payment_details['routing_number']) && $withdrawal->payment_details['routing_number'])
                                    <div class="col-md-6">
                                        <strong>Routing Number:</strong> {{ $withdrawal->payment_details['routing_number'] }}
                                    </div>
                                    @endif
                                </div>
                            @elseif($withdrawal->payment_method === 'paypal')
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>PayPal Email:</strong> {{ $withdrawal->payment_details['paypal_email'] ?? 'N/A' }}
                                    </div>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>
            </div>

            <!-- Rejection Reason -->
            @if($withdrawal->status === 'rejected' && $withdrawal->rejection_reason)
            <div class="card shadow mb-4">
                <div class="card-header py-3 bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-times-circle me-2"></i>
                        Rejection Reason
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger rejection-reason-alert" id="rejectionReasonAlert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Your withdrawal request was rejected for the following reason:</strong>
                        <hr>
                        <div class="rejection-reason-text">
                            {{ $withdrawal->rejection_reason }}
                        </div>
                    </div>
                    @if($withdrawal->processed_at)
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            Rejected on {{ $withdrawal->processed_at->format('M j, Y g:i A') }}
                        </small>
                    @endif
                </div>
            </div>
            @endif

            <!-- Notes -->
            @if($withdrawal->notes)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Additional Notes</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="fw-bold">Your Notes:</h6>
                        <p class="text-muted">{{ $withdrawal->notes }}</p>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Status & Actions -->
        <div class="col-lg-4">
            <!-- Status Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Status Information</h6>
                </div>
                <div class="card-body text-center">
                    @php
                        $statusIcon = match($withdrawal->status) {
                            'pending' => 'fas fa-clock text-warning',
                            'approved' => 'fas fa-check-circle text-info',
                            'paid' => 'fas fa-check-double text-success',
                            'rejected' => 'fas fa-times-circle text-danger',
                            'cancelled' => 'fas fa-ban text-secondary',
                            default => 'fas fa-question-circle text-muted'
                        };

                        $statusMessage = match($withdrawal->status) {
                            'pending' => 'Your withdrawal request is being reviewed by our team.',
                            'approved' => 'Your withdrawal has been approved and will be processed soon.',
                            'paid' => 'Your withdrawal has been successfully processed and paid.',
                            'rejected' => $withdrawal->rejection_reason ?
                                'Your withdrawal request has been rejected. Please see the rejection reason below for details.' :
                                'Your withdrawal request has been rejected.',
                            'cancelled' => 'This withdrawal request has been cancelled.',
                            default => 'Status unknown.'
                        };
                    @endphp

                    <i class="{{ $statusIcon }}" style="font-size: 3rem;"></i>
                    <h4 class="mt-3">{{ ucfirst($withdrawal->status) }}</h4>
                    <p class="text-muted">{{ $statusMessage }}</p>

                    @if($withdrawal->status === 'pending')
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Processing typically takes 1-3 business days
                            </small>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Actions -->
            @if($withdrawal->status === 'pending')
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('affiliate.withdrawals.cancel', $withdrawal) }}" method="POST" onsubmit="return confirm('Are you sure you want to cancel this withdrawal request? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="fas fa-times me-2"></i>
                            Cancel Request
                        </button>
                    </form>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            Cancelling will return the amount to your available balance
                        </small>
                    </div>
                </div>
            </div>
            @endif

            <!-- Timeline -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Request Submitted</h6>
                                <small class="text-muted">{{ $withdrawal->requested_at->format('M j, Y g:i A') }}</small>
                            </div>
                        </div>

                        @if($withdrawal->status !== 'pending')
                        <div class="timeline-item">
                            <div class="timeline-marker {{ $withdrawal->status === 'rejected' || $withdrawal->status === 'cancelled' ? 'bg-danger' : 'bg-success' }}"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">{{ ucfirst($withdrawal->status) }}</h6>
                                @if($withdrawal->processed_at)
                                    <small class="text-muted">{{ $withdrawal->processed_at->format('M j, Y g:i A') }}</small>
                                @endif
                                @if($withdrawal->status === 'rejected' && $withdrawal->rejection_reason)
                                    <br>
                                    <small class="text-danger">
                                        <i class="fas fa-info-circle me-1"></i>
                                        See rejection details below
                                    </small>
                                @endif
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e3e6f0;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-weight: 600;
}

/* Ensure rejection reason alert never auto-hides */
.rejection-reason-alert {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    position: relative;
}

.rejection-reason-alert.show {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

.rejection-reason-alert.fade {
    opacity: 1 !important;
}

.rejection-reason-alert.fade.show {
    opacity: 1 !important;
}

.rejection-reason-text {
    font-size: 15px;
    line-height: 1.5;
    margin-top: 10px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    word-wrap: break-word;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Ensure rejection reason alert never auto-hides
    const rejectionAlert = document.getElementById('rejectionReasonAlert');

    if (rejectionAlert) {
        // Force visibility immediately
        rejectionAlert.style.display = 'block';
        rejectionAlert.style.opacity = '1';
        rejectionAlert.style.visibility = 'visible';

        // Remove any Bootstrap auto-hide attributes
        rejectionAlert.removeAttribute('data-bs-dismiss');
        rejectionAlert.removeAttribute('data-dismiss');

        // Override Bootstrap's Alert class methods to prevent auto-hiding
        if (window.bootstrap && window.bootstrap.Alert) {
            try {
                const alertInstance = window.bootstrap.Alert.getOrCreateInstance(rejectionAlert);
                if (alertInstance) {
                    // Override the close method to prevent any closing
                    alertInstance.close = function() {
                        // Do nothing - prevent closing
                        console.log('Rejection reason alert close prevented');
                    };
                }
            } catch (e) {
                console.log('Bootstrap Alert override failed, but alert should still be visible');
            }
        }

        // Prevent any automatic hiding by overriding common auto-hide triggers
        rejectionAlert.addEventListener('show.bs.alert', function(e) {
            e.preventDefault();
        });

        rejectionAlert.addEventListener('hide.bs.alert', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        rejectionAlert.addEventListener('close.bs.alert', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        rejectionAlert.addEventListener('closed.bs.alert', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        // Continuously ensure visibility (fallback)
        setInterval(function() {
            if (rejectionAlert.style.display === 'none' || rejectionAlert.style.opacity === '0') {
                rejectionAlert.style.display = 'block';
                rejectionAlert.style.opacity = '1';
                rejectionAlert.style.visibility = 'visible';
            }
        }, 1000);
    }
});
</script>
@endsection
