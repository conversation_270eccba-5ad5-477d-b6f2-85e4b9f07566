<?php

namespace App\Http\Controllers;

use App\Models\Expenditure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;

class ExpenditureController extends Controller
{
    public function __construct()
    {
        // Account users can only create and store expenditures
        $this->middleware('role:Account')->only(['create', 'store']);

        // Organization Owner and Manager can approve/reject expenditures
        $this->middleware('role:Organization Owner|Manager')->only(['updateStatus']);

        // All three roles can view expenditures
        $this->middleware('role:Organization Owner|Manager|Account')->only(['index']);
    }

    public function index()
    {
        $query = Expenditure::query();

        // Check if organization_id and branch_id columns exist
        $hasOrganizationId = Schema::hasColumn('expenditures', 'organization_id');
        $hasBranchId = Schema::hasColumn('expenditures', 'branch_id');

        // Add organization filter if column exists
        if ($hasOrganizationId) {
            $query->where('organization_id', Auth::user()->organization_id);
        }

        // Filter by branch if user is assigned to a branch and column exists
        if (Auth::user()->branch_id && $hasBranchId) {
            $query->where('branch_id', Auth::user()->branch_id);
        }

        $expenditures = $query->latest()->paginate(10);
        return view('expenditures.index', compact('expenditures'));
    }

    public function create()
    {
        return view('expenditures.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'heading' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
        ]);

        $expenditure = new Expenditure($validated);
        $expenditure->applicant = Auth::user()->name;
        $expenditure->status = 'Pending';

        // Check if organization_id and branch_id columns exist
        $hasOrganizationId = Schema::hasColumn('expenditures', 'organization_id');
        $hasBranchId = Schema::hasColumn('expenditures', 'branch_id');

        // Set organization_id if column exists
        if ($hasOrganizationId) {
            $expenditure->organization_id = Auth::user()->organization_id;
        }

        // Set branch_id if column exists and user has a branch
        if ($hasBranchId && Auth::user()->branch_id) {
            $expenditure->branch_id = Auth::user()->branch_id;
        }

        $expenditure->save();

        return redirect()->route('expenditures.index')
            ->with('success', 'Expenditure application submitted successfully.');
    }

    public function show(Expenditure $expenditure)
    {
        // Ensure the expenditure belongs to the user's organization
        if (Schema::hasColumn('expenditures', 'organization_id') &&
            $expenditure->organization_id !== Auth::user()->organization_id) {
            abort(403, 'Unauthorized action.');
        }

        return view('expenditures.show', compact('expenditure'));
    }

    public function updateStatus(Expenditure $expenditure, Request $request)
    {
        // Ensure the expenditure belongs to the user's organization
        if (Schema::hasColumn('expenditures', 'organization_id') &&
            $expenditure->organization_id !== Auth::user()->organization_id) {
            abort(403, 'Unauthorized action.');
        }

        try {
            $request->validate([
                'status' => 'required|in:Approved,Rejected'
            ]);

            $expenditure->update([
                'status' => $request->status,
                'approved_by' => Auth::user()->name,
                'approved_at' => now()
            ]);

            return redirect()->route('expenditures.index')
                ->with('success', "Expenditure has been {$request->status}!");

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', "Error: " . $e->getMessage());
        }
    }
}
