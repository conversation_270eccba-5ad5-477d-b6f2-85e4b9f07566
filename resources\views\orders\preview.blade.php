<div class="space-y-6 text-left">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <h4 class="font-medium text-gray-700">Customer Information</h4>
            <p>Name: {{ $data['customer_name'] }}</p>
            <p>Phone: {{ $data['phone_number'] }}</p>
            <p class="mt-2">Description: {{ $data['job_description'] }}</p>
        </div>
        
        <div>
            <h4 class="font-medium text-gray-700">Order Details</h4>
            <p>Department: {{ $data['department'] }}</p>
            @if($data['media'])
                <p>Media: {{ $data['media'] }}</p>
            @endif
            @if($data['pages'])
                <p>Pages: {{ $data['pages'] }}</p>
            @endif
            @if($data['size'])
                <p>Size: {{ $data['size'] }}</p>
            @endif
            <p>Quantity: {{ $data['quantity'] }}</p>
            <p>Unit Cost: ₦{{ number_format($data['unit_cost'], 2) }}</p>
        </div>
    </div>

    <div>
        <h4 class="font-medium text-gray-700">Financial Details</h4>
        <p>Total Amount: ₦{{ number_format($data['total_amount'], 2) }}</p>
        <p>Amount Paid: ₦{{ number_format($data['amount_paid'], 2) }}</p>
        <p>Pending Payment: ₦{{ number_format($data['pending_payment'], 2) }}</p>
    </div>

    <div>
        <h4 class="font-medium text-gray-700">Delivery Information</h4>
        <p>Expected Delivery: {{ \Carbon\Carbon::parse($data['expected_delivery_date'])->format('M d, Y') }} at {{ $data['expected_delivery_time'] }}</p>
    </div>
</div>