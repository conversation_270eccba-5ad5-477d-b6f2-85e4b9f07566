@extends('super_admin.layouts.app')

@section('title', 'Edit Payment Account')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Edit Payment Account</h1>
                <a href="{{ route('super_admin.payment-accounts.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Payment Accounts
                </a>
            </div>

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Payment Account Details</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('super_admin.payment-accounts.update', $paymentAccount) }}">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_name" class="form-label">Account Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('account_name') is-invalid @enderror"
                                           id="account_name" name="account_name" value="{{ old('account_name', $paymentAccount->account_name) }}" required>
                                    @error('account_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bank_name" class="form-label">Bank Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('bank_name') is-invalid @enderror"
                                           id="bank_name" name="bank_name" value="{{ old('bank_name', $paymentAccount->bank_name) }}" required>
                                    @error('bank_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_number" class="form-label">Account Number <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('account_number') is-invalid @enderror"
                                           id="account_number" name="account_number" value="{{ old('account_number', $paymentAccount->account_number) }}" required>
                                    @error('account_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_type" class="form-label">Account Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('account_type') is-invalid @enderror"
                                            id="account_type" name="account_type" required>
                                        <option value="">Select Account Type</option>
                                        <option value="savings" {{ old('account_type', $paymentAccount->account_type) == 'savings' ? 'selected' : '' }}>Savings</option>
                                        <option value="current" {{ old('account_type', $paymentAccount->account_type) == 'current' ? 'selected' : '' }}>Current</option>
                                        <option value="checking" {{ old('account_type', $paymentAccount->account_type) == 'checking' ? 'selected' : '' }}>Checking</option>
                                        <option value="business" {{ old('account_type', $paymentAccount->account_type) == 'business' ? 'selected' : '' }}>Business</option>
                                    </select>
                                    @error('account_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="routing_number" class="form-label">Routing Number</label>
                                    <input type="text" class="form-control @error('routing_number') is-invalid @enderror"
                                           id="routing_number" name="routing_number" value="{{ old('routing_number', $paymentAccount->routing_number) }}">
                                    <div class="form-text">For domestic transfers (optional)</div>
                                    @error('routing_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="swift_code" class="form-label">SWIFT Code</label>
                                    <input type="text" class="form-control @error('swift_code') is-invalid @enderror"
                                           id="swift_code" name="swift_code" value="{{ old('swift_code', $paymentAccount->swift_code) }}">
                                    <div class="form-text">For international transfers (optional)</div>
                                    @error('swift_code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="additional_instructions" class="form-label">Additional Instructions</label>
                            <textarea class="form-control @error('additional_instructions') is-invalid @enderror"
                                      id="additional_instructions" name="additional_instructions" rows="3">{{ old('additional_instructions', $paymentAccount->additional_instructions) }}</textarea>
                            <div class="form-text">Any special instructions for customers making payments</div>
                            @error('additional_instructions')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1"
                                               {{ old('is_active', $paymentAccount->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active Account
                                        </label>
                                    </div>
                                    <div class="form-text">Only active accounts will be shown to customers</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_primary" name="is_primary" value="1"
                                               {{ old('is_primary', $paymentAccount->is_primary) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_primary">
                                            Primary Account
                                        </label>
                                    </div>
                                    <div class="form-text">Primary account will be displayed first to customers</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('super_admin.payment-accounts.index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Payment Account
                            </button>
                        </div>
                    </form>

                    <!-- Delete Form (separate from edit form) -->
                    @if(!$paymentAccount->is_primary)
                        <div class="mt-3 pt-3 border-top">
                            <h6 class="text-danger">Danger Zone</h6>
                            <p class="text-muted small">Once you delete this payment account, there is no going back. Please be certain.</p>
                            <form method="POST" action="{{ route('super_admin.payment-accounts.destroy', $paymentAccount) }}"
                                  onsubmit="return confirm('Are you sure you want to delete this payment account? This action cannot be undone.')"
                                  class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> Delete Account
                                </button>
                            </form>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
