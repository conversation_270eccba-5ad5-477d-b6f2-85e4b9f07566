<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Organization;
use App\Models\Branch;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InitialOrganizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Start transaction to ensure data consistency
        DB::beginTransaction();

        try {
            // Check for existing organization
            $existingOrg = Organization::first();

            if (!$existingOrg) {
                // Create default organization
                $organization = Organization::create([
                    'name' => 'Default Organization',
                    'email' => '<EMAIL>',
                    'description' => 'Initial organization created during migration',
                ]);

                $this->command->info('Default organization created successfully.');

                // Create a default branch
                $branch = Branch::create([
                    'name' => 'Main Branch',
                    'organization_id' => $organization->id,
                    'description' => 'Main branch of the organization',
                ]);

                $this->command->info('Default branch created successfully.');

                // Update existing admin user with organization and branch
                $adminUser = User::where('email', '<EMAIL>')->first();

                if ($adminUser) {
                    $adminUser->update([
                        'organization_id' => $organization->id,
                        'branch_id' => $branch->id,
                    ]);

                    // Ensure admin has Organization Owner role
                    $ownerRole = Role::where('name', 'Organization Owner')->first();
                    if ($ownerRole && !$adminUser->hasRole('Organization Owner')) {
                        $adminUser->roles()->syncWithoutDetaching([$ownerRole->id]);
                    }

                    $this->command->info('Admin user updated with organization and branch.');
                } else {
                    $this->command->warn('Admin user not found. No user was updated.');
                }

                // Commit transaction
                DB::commit();

                $this->command->info('Initial organization setup completed successfully.');
            } else {
                // Organization already exists
                $this->command->info('Organization already exists. No changes made.');
                DB::rollBack();
            }
        } catch (\Exception $e) {
            // Rollback on error
            DB::rollBack();
            Log::error('Failed to create initial organization: ' . $e->getMessage());
            $this->command->error('Failed to create initial organization: ' . $e->getMessage());
        }
    }
}
