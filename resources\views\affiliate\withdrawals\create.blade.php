@extends('affiliate.layouts.app')

@section('page-title', 'Request Withdrawal')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="mb-4">
        <h2 class="mb-1">Request Withdrawal</h2>
        <p class="text-muted mb-0">Submit a withdrawal request for your available earnings</p>
    </div>

    <!-- Balance Info -->
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-start">
            <i class="fas fa-info-circle me-3 mt-1"></i>
            <div>
                <h5 class="alert-heading">Available Balance: {{ format_price($affiliate->available_balance) }}</h5>
                <p class="mb-1">Minimum withdrawal: {{ $settings->formatted_minimum_withdrawal }}</p>
                @if($settings->withdrawal_fee_percentage > 0 || $settings->withdrawal_fee_fixed > 0)
                    <p class="mb-0">Withdrawal fees:
                        @if($settings->withdrawal_fee_percentage > 0)
                            {{ number_format($settings->withdrawal_fee_percentage, 2) }}%
                        @endif
                        @if($settings->withdrawal_fee_fixed > 0)
                            @if($settings->withdrawal_fee_percentage > 0) + @endif
                            {{ format_price($settings->withdrawal_fee_fixed) }}
                        @endif
                    </p>
                @endif
            </div>
        </div>
    </div>

    <!-- Withdrawal Form -->
    <div class="card shadow">
        <div class="card-body">
            <form method="POST" action="{{ route('affiliate.withdrawals.store') }}">
                @csrf

                <!-- Amount -->
                <div class="mb-3">
                    <label for="amount" class="form-label">Withdrawal Amount</label>
                    <div class="input-group">
                        <span class="input-group-text">{{ user_currency_symbol() }}</span>
                        <input type="number"
                               id="amount"
                               name="amount"
                               step="0.01"
                               min="{{ $settings->minimum_withdrawal }}"
                               max="{{ $affiliate->available_balance }}"
                               value="{{ old('amount') }}"
                               class="form-control @error('amount') is-invalid @enderror"
                               placeholder="0.00"
                               required>
                    </div>
                    @error('amount')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <div class="form-text">
                        Enter amount between {{ format_price($settings->minimum_withdrawal) }} and {{ format_price($affiliate->available_balance) }}
                    </div>
                </div>

                <!-- Payment Method -->
                <div class="mb-3">
                    <label for="payment_method" class="form-label">Payment Method</label>
                    <select id="payment_method" name="payment_method" class="form-select @error('payment_method') is-invalid @enderror" required>
                        <option value="">Select Payment Method</option>
                        @foreach($settings->getAvailablePaymentMethods() as $method)
                            <option value="{{ $method }}" {{ old('payment_method') == $method ? 'selected' : '' }}>
                                {{ ucfirst(str_replace('_', ' ', $method)) }}
                            </option>
                        @endforeach
                    </select>
                    @error('payment_method')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Bank Transfer Fields -->
                <div id="bank_fields" class="d-none mb-3">
                    <h5 class="mb-3">Bank Transfer Details</h5>

                    <div class="mb-3">
                        <label for="bank_name" class="form-label">Bank Name</label>
                        <input type="text" id="bank_name" name="bank_name" value="{{ old('bank_name') }}" class="form-control @error('bank_name') is-invalid @enderror" disabled>
                        @error('bank_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="account_name" class="form-label">Account Holder Name</label>
                        <input type="text" id="account_name" name="account_name" value="{{ old('account_name') }}" class="form-control @error('account_name') is-invalid @enderror" disabled>
                        @error('account_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="account_number" class="form-label">Account Number</label>
                        <input type="text" id="account_number" name="account_number" value="{{ old('account_number') }}" class="form-control @error('account_number') is-invalid @enderror" disabled>
                        @error('account_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="routing_number" class="form-label">Routing Number (Optional)</label>
                        <input type="text" id="routing_number" name="routing_number" value="{{ old('routing_number') }}" class="form-control @error('routing_number') is-invalid @enderror" disabled>
                        @error('routing_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- PayPal Fields -->
                <div id="paypal_fields" class="d-none mb-3">
                    <h5 class="mb-3">PayPal Details</h5>

                    <div class="mb-3">
                        <label for="paypal_email" class="form-label">PayPal Email Address</label>
                        <input type="email" id="paypal_email" name="paypal_email" value="{{ old('paypal_email') }}" class="form-control @error('paypal_email') is-invalid @enderror" disabled>
                        @error('paypal_email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">Enter the email address associated with your PayPal account</div>
                    </div>
                </div>

                <!-- Notes -->
                <div class="mb-3">
                    <label for="notes" class="form-label">Additional Notes (Optional)</label>
                    <textarea id="notes" name="notes" rows="3" class="form-control @error('notes') is-invalid @enderror" placeholder="Any additional information or special instructions">{{ old('notes') }}</textarea>
                    @error('notes')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Fee Calculation Display -->
                <div id="fee_calculation" class="d-none mb-3 p-3 bg-light rounded">
                    <h6 class="mb-2">Withdrawal Summary</h6>
                    <div class="small">
                        <div class="d-flex justify-content-between">
                            <span>Withdrawal Amount:</span>
                            <span id="withdrawal_amount">{{ user_currency_symbol() }}0.00</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Processing Fee:</span>
                            <span id="processing_fee">{{ user_currency_symbol() }}0.00</span>
                        </div>
                        <div class="d-flex justify-content-between fw-bold border-top pt-1">
                            <span>Net Amount:</span>
                            <span id="net_amount">{{ user_currency_symbol() }}0.00</span>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-between">
                    <a href="{{ route('affiliate.withdrawals') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Cancel
                    </a>

                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-paper-plane me-2"></i>
                        Submit Withdrawal Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethodSelect = document.getElementById('payment_method');
    const bankFields = document.getElementById('bank_fields');
    const paypalFields = document.getElementById('paypal_fields');
    const amountInput = document.getElementById('amount');
    const feeCalculation = document.getElementById('fee_calculation');

    const withdrawalFeePercentage = {{ $settings->withdrawal_fee_percentage }};
    const withdrawalFeeFixed = {{ $settings->withdrawal_fee_fixed }};
    const currencySymbol = '{{ user_currency_symbol() }}';

    function togglePaymentFields() {
        const selectedMethod = paymentMethodSelect.value;

        // Get all form fields
        const paypalEmailField = document.getElementById('paypal_email');
        const bankNameField = document.getElementById('bank_name');
        const accountNameField = document.getElementById('account_name');
        const accountNumberField = document.getElementById('account_number');
        const routingNumberField = document.getElementById('routing_number');

        if (selectedMethod === 'bank_transfer') {
            bankFields.classList.remove('d-none');
            paypalFields.classList.add('d-none');

            // Enable bank fields and disable PayPal fields
            bankNameField.disabled = false;
            accountNameField.disabled = false;
            accountNumberField.disabled = false;
            routingNumberField.disabled = false;
            paypalEmailField.disabled = true;

            // Clear PayPal fields when bank transfer is selected
            paypalEmailField.value = '';
        } else if (selectedMethod === 'paypal') {
            paypalFields.classList.remove('d-none');
            bankFields.classList.add('d-none');

            // Enable PayPal fields and disable bank fields
            paypalEmailField.disabled = false;
            bankNameField.disabled = true;
            accountNameField.disabled = true;
            accountNumberField.disabled = true;
            routingNumberField.disabled = true;

            // Clear bank fields when PayPal is selected
            bankNameField.value = '';
            accountNameField.value = '';
            accountNumberField.value = '';
            routingNumberField.value = '';
        } else {
            bankFields.classList.add('d-none');
            paypalFields.classList.add('d-none');

            // Disable all payment fields when no method is selected
            paypalEmailField.disabled = true;
            bankNameField.disabled = true;
            accountNameField.disabled = true;
            accountNumberField.disabled = true;
            routingNumberField.disabled = true;

            // Clear all payment fields when no method is selected
            paypalEmailField.value = '';
            bankNameField.value = '';
            accountNameField.value = '';
            accountNumberField.value = '';
            routingNumberField.value = '';
        }
    }

    function calculateFees() {
        const amount = parseFloat(amountInput.value) || 0;

        if (amount > 0) {
            const percentageFee = (amount * withdrawalFeePercentage) / 100;
            const totalFee = percentageFee + withdrawalFeeFixed;
            const netAmount = amount - totalFee;

            document.getElementById('withdrawal_amount').textContent = currencySymbol + amount.toFixed(2);
            document.getElementById('processing_fee').textContent = currencySymbol + totalFee.toFixed(2);
            document.getElementById('net_amount').textContent = currencySymbol + netAmount.toFixed(2);

            feeCalculation.classList.remove('d-none');
        } else {
            feeCalculation.classList.add('d-none');
        }
    }

    paymentMethodSelect.addEventListener('change', togglePaymentFields);
    amountInput.addEventListener('input', calculateFees);

    // Initialize on page load
    togglePaymentFields();
    calculateFees();
});
</script>
@endsection
