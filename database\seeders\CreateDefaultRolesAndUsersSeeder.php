<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

class CreateDefaultRolesAndUsersSeeder extends Seeder
{
    public function run(): void
    {
        // Create default roles if they don't exist
        $roles = [
            'Organization Owner',
            'Manager',
            'Account',
            'Staff',
            'Operator',
            'Production',
            'Delivery'
        ];

        foreach ($roles as $roleName) {
            Role::firstOrCreate(['name' => $roleName]);
        }

        // Create default admin user
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'status' => 'active'
            ]
        );

        // Assign Organization Owner role
        $ownerRole = Role::where('name', 'Organization Owner')->first();
        if (!$adminUser->hasRole('Organization Owner')) {
            $adminUser->roles()->attach($ownerRole);
        }
        
        // Create default staff user
        $staffUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Staff User',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'status' => 'active'
            ]
        );

        // Assign Staff role
        $staffRole = Role::where('name', 'Staff')->first();
        if (!$staffUser->hasRole('Staff')) {
            $staffUser->roles()->attach($staffRole);
        }

        // Create default operator user
        $operatorUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Operator User',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'status' => 'active'
            ]
        );

        // Assign Operator role
        $operatorRole = Role::where('name', 'Operator')->first();
        if (!$operatorUser->hasRole('Operator')) {
            $operatorUser->roles()->attach($operatorRole);
        }
    }
}