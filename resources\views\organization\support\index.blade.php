@extends('layouts.app')

@section('title', 'Organization Support Dashboard')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Organization Support Dashboard</h1>
                    <p class="text-muted mb-0">Manage support tickets for {{ auth()->user()->organization->name }}</p>
                </div>
                <div>
                    <a href="{{ route('user.support.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Ticket
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['total'] }}</h4>
                                    <p class="mb-0">Total Tickets</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-ticket-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['open'] }}</h4>
                                    <p class="mb-0">Open Tickets</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['in_progress'] }}</h4>
                                    <p class="mb-0">In Progress</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-cog fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['resolved'] }}</h4>
                                    <p class="mb-0">Resolved</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('organization.support.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select name="status" id="status" class="form-control">
                                    <option value="">All Statuses</option>
                                    <option value="open" {{ request('status') === 'open' ? 'selected' : '' }}>Open</option>
                                    <option value="in_progress" {{ request('status') === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                    <option value="waiting_customer" {{ request('status') === 'waiting_customer' ? 'selected' : '' }}>Waiting for Customer</option>
                                    <option value="waiting_admin" {{ request('status') === 'waiting_admin' ? 'selected' : '' }}>Waiting for Admin</option>
                                    <option value="resolved" {{ request('status') === 'resolved' ? 'selected' : '' }}>Resolved</option>
                                    <option value="closed" {{ request('status') === 'closed' ? 'selected' : '' }}>Closed</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="priority" class="form-label">Priority</label>
                                <select name="priority" id="priority" class="form-control">
                                    <option value="">All Priorities</option>
                                    <option value="low" {{ request('priority') === 'low' ? 'selected' : '' }}>Low</option>
                                    <option value="normal" {{ request('priority') === 'normal' ? 'selected' : '' }}>Normal</option>
                                    <option value="high" {{ request('priority') === 'high' ? 'selected' : '' }}>High</option>
                                    <option value="urgent" {{ request('priority') === 'urgent' ? 'selected' : '' }}>Urgent</option>
                                    <option value="critical" {{ request('priority') === 'critical' ? 'selected' : '' }}>Critical</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" name="search" id="search" class="form-control" 
                                       placeholder="Search tickets..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tickets Table -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            Organization Support Tickets
                        </h5>
                        <div>
                            <a href="{{ route('organization.support.export') }}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-download"></i> Export CSV
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if($tickets->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Ticket #</th>
                                        <th>Title</th>
                                        <th>User</th>
                                        <th>Status</th>
                                        <th>Priority</th>
                                        <th>Category</th>
                                        <th>Created</th>
                                        <th>Last Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($tickets as $ticket)
                                    <tr>
                                        <td>
                                            <code>{{ $ticket->ticket_number }}</code>
                                        </td>
                                        <td>
                                            <a href="{{ route('user.support.show', $ticket) }}" class="text-decoration-none">
                                                {{ $ticket->title }}
                                            </a>
                                            @if($ticket->replies()->where('is_internal', false)->count() > 0)
                                                <span class="badge bg-info ms-1">
                                                    {{ $ticket->replies()->where('is_internal', false)->count() }} replies
                                                </span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" 
                                                     style="width: 30px; height: 30px; font-size: 12px;">
                                                    {{ substr($ticket->user->name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $ticket->user->name }}</div>
                                                    <small class="text-muted">{{ $ticket->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{!! $ticket->status_badge !!}</td>
                                        <td>{!! $ticket->priority_badge !!}</td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                {{ ucfirst(str_replace('_', ' ', $ticket->category)) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div>{{ $ticket->created_at->format('M d, Y') }}</div>
                                            <small class="text-muted">{{ $ticket->created_at->format('H:i') }}</small>
                                        </td>
                                        <td>
                                            <div>{{ $ticket->updated_at->format('M d, Y') }}</div>
                                            <small class="text-muted">{{ $ticket->updated_at->diffForHumans() }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('user.support.show', $ticket) }}" 
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if($ticket->status !== 'closed')
                                                    <a href="{{ route('user.support.create') }}?reply_to={{ $ticket->id }}" 
                                                       class="btn btn-outline-success btn-sm">
                                                        <i class="fas fa-reply"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if($tickets->hasPages())
                            <div class="d-flex justify-content-center mt-4">
                                {{ $tickets->links() }}
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Support Tickets Found</h4>
                            <p class="text-muted">
                                @if(request()->hasAny(['status', 'priority', 'search']))
                                    No tickets match your current filters. Try adjusting your search criteria.
                                @else
                                    Your organization hasn't created any support tickets yet.
                                @endif
                            </p>
                            <div class="mt-4">
                                @if(request()->hasAny(['status', 'priority', 'search']))
                                    <a href="{{ route('organization.support.index') }}" class="btn btn-outline-secondary me-2">
                                        <i class="fas fa-times"></i> Clear Filters
                                    </a>
                                @endif
                                <a href="{{ route('user.support.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Create First Ticket
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-plus-circle fa-2x text-primary mb-3"></i>
                            <h5>Create New Ticket</h5>
                            <p class="text-muted">Submit a new support request for your team</p>
                            <a href="{{ route('user.support.create') }}" class="btn btn-primary">
                                Create Ticket
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-book fa-2x text-info mb-3"></i>
                            <h5>Knowledge Base</h5>
                            <p class="text-muted">Browse help articles and documentation</p>
                            <a href="{{ route('user.support.knowledge-base') }}" class="btn btn-info">
                                Browse Articles
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-bar fa-2x text-success mb-3"></i>
                            <h5>Support Analytics</h5>
                            <p class="text-muted">View detailed support metrics and reports</p>
                            <a href="{{ route('organization.support.analytics') }}" class="btn btn-success">
                                View Analytics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-group .btn {
    border-radius: 0.25rem;
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>
@endsection
