@extends('layouts.settings')

@section('title', 'Notification Settings')

@section('notification_settings')
<h2 class="text-2xl font-bold mb-6">Notification Settings</h2>
<p class="text-gray-600 mb-4">Configure which automated notifications are sent to customers.</p>

<form action="{{ route('settings.notifications.update') }}" method="POST">
    @csrf
    @method('PUT')

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Email Notifications</h3>

        <div class="space-y-4">
            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input
                        id="order_confirmation_emails"
                        name="order_confirmation_emails"
                        type="checkbox"
                        value="1"
                        {{ old('order_confirmation_emails', $setting->order_confirmation_emails) ? 'checked' : '' }}
                        class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                    >
                </div>
                <div class="ml-3 text-sm">
                    <label for="order_confirmation_emails" class="font-medium text-gray-700">Order Confirmation Emails</label>
                    <p class="text-gray-500">Send an email to customers when their order is created.</p>
                </div>
            </div>

            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input
                        id="order_status_update_emails"
                        name="order_status_update_emails"
                        type="checkbox"
                        value="1"
                        {{ old('order_status_update_emails', $setting->order_status_update_emails) ? 'checked' : '' }}
                        class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                    >
                </div>
                <div class="ml-3 text-sm">
                    <label for="order_status_update_emails" class="font-medium text-gray-700">Order Status Update Emails</label>
                    <p class="text-gray-500">Send an email to customers when their order status changes.</p>
                </div>
            </div>

            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input
                        id="payment_reminder_emails"
                        name="payment_reminder_emails"
                        type="checkbox"
                        value="1"
                        {{ old('payment_reminder_emails', $setting->payment_reminder_emails) ? 'checked' : '' }}
                        class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                    >
                </div>
                <div class="ml-3 text-sm">
                    <label for="payment_reminder_emails" class="font-medium text-gray-700">Payment Reminder Emails</label>
                    <p class="text-gray-500">Send payment reminder emails for orders with pending payments.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="flex items-center justify-end">
        <button
            type="submit"
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded focus:outline-none focus:shadow-outline"
        >
            Save Notification Settings
        </button>
    </div>
</form>
@endsection
