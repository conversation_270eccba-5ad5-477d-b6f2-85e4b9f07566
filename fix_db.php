<?php
// Simple database fix script - place in root directory
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Database Fix Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        .warning { color: #ffc107; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
        .button { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .button:hover { background: #0056b3; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #28a745; background: #f8fff9; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Sales Management System - Database Fix Tool</h1>
        
        <?php
        if (isset($_POST['fix_database'])) {
            echo "<h2>🚀 Fixing Database Issues...</h2>";
            
            // Database configuration
            $host = '127.0.0.1';
            $port = '4306';
            $database = 'ofp_pro';
            $username = 'root';
            $password = '';
            
            try {
                // Connect to database
                $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
                $pdo = new PDO($dsn, $username, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                ]);
                
                echo "<p class='success'>✅ Connected to database: <strong>{$database}</strong></p>";
                
                // Fix sessions table
                $stmt = $pdo->query("SHOW TABLES LIKE 'sessions'");
                if ($stmt->rowCount() == 0) {
                    echo "<p class='info'>📝 Creating sessions table...</p>";
                    
                    $createSessionsTable = "
                    CREATE TABLE `sessions` (
                        `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                        `user_id` bigint unsigned DEFAULT NULL,
                        `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                        `user_agent` text COLLATE utf8mb4_unicode_ci,
                        `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
                        `last_activity` int NOT NULL,
                        PRIMARY KEY (`id`),
                        KEY `sessions_user_id_index` (`user_id`),
                        KEY `sessions_last_activity_index` (`last_activity`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                    ";
                    
                    $pdo->exec($createSessionsTable);
                    echo "<p class='success'>✅ Sessions table created successfully</p>";
                } else {
                    echo "<p class='success'>✅ Sessions table already exists</p>";
                }
                
                // Check super_admins table
                $stmt = $pdo->query("SHOW TABLES LIKE 'super_admins'");
                if ($stmt->rowCount() > 0) {
                    echo "<p class='success'>✅ Super admins table exists</p>";
                    
                    // Check if there are any super admins
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM super_admins");
                    $result = $stmt->fetch();
                    
                    if ($result['count'] == 0) {
                        echo "<p class='info'>👤 Creating default super admin...</p>";
                        
                        $stmt = $pdo->prepare("
                            INSERT INTO super_admins (name, email, password, role, is_active, created_at, updated_at) 
                            VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                        ");
                        
                        $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
                        $stmt->execute([
                            'Super Admin',
                            '<EMAIL>',
                            $hashedPassword,
                            'super_admin',
                            1
                        ]);
                        
                        echo "<p class='success'>✅ Default super admin created</p>";
                        echo "<div class='step'>";
                        echo "<h4>🔑 Super Admin Login Details:</h4>";
                        echo "<strong>Email:</strong> <EMAIL><br>";
                        echo "<strong>Password:</strong> password";
                        echo "</div>";
                    } else {
                        echo "<p class='success'>✅ Super admin(s) already exist (count: {$result['count']})</p>";
                    }
                } else {
                    echo "<p class='warning'>⚠️ Super admins table does not exist - you may need to run migrations</p>";
                }
                
                // Check announcements table
                $stmt = $pdo->query("SHOW TABLES LIKE 'announcements'");
                if ($stmt->rowCount() > 0) {
                    echo "<p class='success'>✅ Announcements table exists</p>";
                    
                    // Check if there are any announcements
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM announcements");
                    $result = $stmt->fetch();
                    
                    if ($result['count'] == 0) {
                        echo "<p class='info'>📢 Creating test announcements...</p>";
                        
                        // Get super admin ID
                        $stmt = $pdo->query("SELECT id FROM super_admins LIMIT 1");
                        $superAdmin = $stmt->fetch();
                        
                        if ($superAdmin) {
                            $superAdminId = $superAdmin['id'];
                            
                            $stmt = $pdo->prepare("
                                INSERT INTO announcements 
                                (title, content, type, priority, target_audience, is_active, is_dismissible, show_on_login, show_on_dashboard, send_email, published_at, created_by, created_at, updated_at) 
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, NOW(), NOW())
                            ");
                            
                            // Test announcement for organizations
                            $stmt->execute([
                                'Welcome to the System!',
                                'This is a test announcement for organization users. The announcement system is now working correctly.',
                                'info', 'normal', 'organizations',
                                1, 1, 0, 1, 0, $superAdminId
                            ]);
                            
                            // Test announcement for all users
                            $stmt->execute([
                                'System Maintenance Notice',
                                'Please be aware that we may perform system maintenance during off-peak hours. You will be notified in advance of any scheduled downtime.',
                                'warning', 'high', 'all',
                                1, 1, 0, 1, 0, $superAdminId
                            ]);
                            
                            echo "<p class='success'>✅ Test announcements created</p>";
                        } else {
                            echo "<p class='warning'>⚠️ No super admin found to assign announcements to</p>";
                        }
                    } else {
                        echo "<p class='success'>✅ Announcements already exist (count: {$result['count']})</p>";
                    }
                } else {
                    echo "<p class='warning'>⚠️ Announcements table does not exist - you may need to run migrations</p>";
                }
                
                echo "<div class='step'>";
                echo "<h3 class='success'>🎉 Database Fix Complete!</h3>";
                echo "<h4>📋 Next Steps:</h4>";
                echo "<ol>";
                echo "<li><strong>Login as Super Admin:</strong><br>";
                echo "   URL: <a href='/SalesManagementSystem/super-admin/login' target='_blank'>/super-admin/login</a><br>";
                echo "   Email: <EMAIL><br>";
                echo "   Password: password</li>";
                echo "<li><strong>Manage Announcements:</strong><br>";
                echo "   URL: <a href='/SalesManagementSystem/super-admin/announcements' target='_blank'>/super-admin/announcements</a></li>";
                echo "<li><strong>Test User Side:</strong><br>";
                echo "   Login as an organization user and check the dashboard for announcements</li>";
                echo "</ol>";
                echo "</div>";
                
            } catch (PDOException $e) {
                echo "<p class='error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
                echo "<p class='info'>💡 <strong>Troubleshooting:</strong></p>";
                echo "<ul>";
                echo "<li>Make sure XAMPP MySQL is running</li>";
                echo "<li>Check if MySQL is running on port 4306</li>";
                echo "<li>Verify database name 'ofp_pro' exists</li>";
                echo "<li>Check database credentials in .env file</li>";
                echo "</ul>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        } else {
            ?>
            <p>This tool will fix the database issues preventing the announcement system from working.</p>
            
            <div class="step">
                <h3>🎯 What this tool will do:</h3>
                <ul>
                    <li>✅ Create the missing 'sessions' table (fixes authentication issues)</li>
                    <li>✅ Create a default super admin account if none exists</li>
                    <li>✅ Create test announcements to verify the system works</li>
                    <li>✅ Provide next steps for testing</li>
                </ul>
            </div>
            
            <div class="step">
                <h3>📋 Database Configuration:</h3>
                <pre>Host: 127.0.0.1
Port: 4306
Database: ofp_pro
Username: root
Password: (empty)</pre>
            </div>
            
            <form method="post">
                <button type="submit" name="fix_database" class="button">🚀 Fix Database Issues</button>
            </form>
            <?php
        }
        ?>
    </div>
</body>
</html>
