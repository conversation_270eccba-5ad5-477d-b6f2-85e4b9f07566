const CACHE_NAME = 'printworks-cache-v1';
const urlsToCache = [
    '/',
    '/css/app.css',
    '/js/app.js',
    '/images/logo.png',
    '/offline.html',
    // Vendor resources
    '/vendor/css/tailwind.min.css',
    '/vendor/css/fontawesome.min.css',
    '/vendor/css/sweetalert2.min.css',
    '/vendor/js/sweetalert2.min.js',
    '/vendor/webfonts/fa-solid-900.woff2',
    '/vendor/webfonts/fa-regular-400.woff2',
    '/vendor/webfonts/fa-brands-400.woff2',
    '/fonts/inter-var.woff2',
];

self.addEventListener('install', event => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => cache.addAll(urlsToCache))
    );
});

self.addEventListener('fetch', event => {
    if (event.request.url.includes('/api/')) {
        event.respondWith(
            fetch(event.request)
                .catch(() => caches.match(event.request))
        );
        return;
    }

    event.respondWith(
        caches.match(event.request)
            .then(response => {
                if (response) {
                    return response;
                }

                return fetch(event.request)
                    .then(response => {
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }

                        const responseToCache = response.clone();
                        caches.open(CACHE_NAME)
                            .then(cache => {
                                cache.put(event.request, responseToCache);
                            });

                        return response;
                    })
                    .catch(() => {
                        if (event.request.mode === 'navigate') {
                            return caches.match('/offline.html');
                        }
                        // Return fallback font or CSS if the request was for those
                        if (event.request.url.includes('.woff2')) {
                            return caches.match('/fonts/inter-var.woff2');
                        }
                        if (event.request.url.includes('css')) {
                            return caches.match('/css/app.css');
                        }
                    });
            })
    );
}); 