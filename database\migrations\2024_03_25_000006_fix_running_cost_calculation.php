<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Order;

return new class extends Migration
{
    public function up()
    {
        // First update all existing orders
        $orders = Order::all();
        foreach ($orders as $order) {
            $order->running_cost = $order->total_amount * 0.15; // 15% Running Cost
            $order->production_amount = $order->total_amount - ($order->sales_commission + $order->operators_commission + $order->running_cost);
            $order->save();
        }
    }

    public function down()
    {
        // No need for down method as we're just fixing calculations
    }
}; 