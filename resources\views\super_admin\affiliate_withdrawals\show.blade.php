@extends('super_admin.layouts.app')

@section('title', 'Withdrawal Details')
@section('page-title', 'Withdrawal Details')

@section('content')
<!-- Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 mb-1">Withdrawal Request #{{ $withdrawal->id }}</h1>
        @if($withdrawal->affiliate && $withdrawal->affiliate->user)
            <p class="text-muted">{{ $withdrawal->affiliate->user->name }} - {{ $withdrawal->affiliate->affiliate_code }}</p>
        @else
            <p class="text-muted text-danger">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Affiliate information not available
            </p>
        @endif
    </div>
    <div>
        <a href="{{ route('super_admin.affiliate-withdrawals.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Back to Withdrawals
        </a>
    </div>
</div>

<!-- Status Badge -->
<div class="mb-4">
    @if($withdrawal->status === 'pending')
        <span class="badge bg-warning fs-6">
            <i class="fas fa-clock me-1"></i>
            Pending Review
        </span>
    @elseif($withdrawal->status === 'approved')
        <span class="badge bg-info fs-6">
            <i class="fas fa-check me-1"></i>
            Approved - Ready for Payment
        </span>
    @elseif($withdrawal->status === 'paid')
        <span class="badge bg-success fs-6">
            <i class="fas fa-money-bill-wave me-1"></i>
            Paid
        </span>
    @elseif($withdrawal->status === 'rejected')
        <span class="badge bg-danger fs-6">
            <i class="fas fa-times me-1"></i>
            Rejected
        </span>
    @endif
</div>

<!-- Quick Payment Summary for Bank Transfers - ALWAYS VISIBLE -->
@if($withdrawal->payment_method === 'bank_transfer' && $withdrawal->status === 'approved')
<div class="alert alert-success border-success mb-4 bank-transfer-alert" id="bankTransferAlert" style="border-width: 3px !important;">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h5 class="mb-2">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong>READY FOR BANK TRANSFER</strong>
            </h5>
            <div class="alert alert-info mb-2 py-2">
                <small class="text-dark">
                    <i class="fas fa-info-circle me-1"></i>
                    <strong>Important:</strong> This notification will remain visible until payment is processed. Use the details below for your bank transfer.
                </small>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <p class="mb-1"><strong>Amount:</strong>
                        <span class="badge bg-warning text-dark fs-6 p-2">{{ format_price($withdrawal->net_amount ?: $withdrawal->amount) }}</span>
                    </p>
                    <p class="mb-1"><strong>Account:</strong> {{ $withdrawal->payment_details['account_number'] ?? 'Not provided' }}</p>
                </div>
                <div class="col-sm-6">
                    <p class="mb-1"><strong>Bank:</strong> {{ $withdrawal->payment_details['bank_name'] ?? 'Not provided' }}</p>
                    <p class="mb-1"><strong>Beneficiary:</strong> {{ $withdrawal->payment_details['account_name'] ?? 'Not provided' }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-success btn-lg" data-bs-toggle="modal" data-bs-target="#markAsPaidModal">
                <i class="fas fa-check-circle me-2"></i>
                Mark as Paid
            </button>
        </div>
    </div>
</div>
@endif

<!-- Main Content -->
<div class="row">
    <!-- Withdrawal Details -->
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Withdrawal Information</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-gray-800">Amount Details</h6>
                        <!-- Debug Info -->
                        <div class="alert alert-info" style="font-size: 12px;">
                            <strong>Debug Info:</strong><br>
                            Raw Amount: {{ var_export($withdrawal->amount) }}<br>
                            Raw Fee: {{ var_export($withdrawal->fee_amount) }}<br>
                            Raw Net: {{ var_export($withdrawal->net_amount) }}<br>
                            Payment Method: {{ var_export($withdrawal->payment_method) }}<br>
                            Payment Details: {{ var_export($withdrawal->payment_details) }}<br>
                            Affiliate ID: {{ var_export($withdrawal->affiliate_id) }}<br>
                            Affiliate Exists: {{ var_export($withdrawal->affiliate ? true : false) }}<br>
                            @if($withdrawal->affiliate)
                                Affiliate User Exists: {{ var_export($withdrawal->affiliate->user ? true : false) }}<br>
                                @if($withdrawal->affiliate->user)
                                    Affiliate Name: {{ var_export($withdrawal->affiliate->user->name) }}<br>
                                @endif
                            @endif
                            Requested At: {{ var_export($withdrawal->requested_at) }}<br>
                            Status: {{ var_export($withdrawal->status) }}
                        </div>
                        <table class="table table-striped">
                            <tr>
                                <td style="width: 60%;"><strong>Requested Amount:</strong></td>
                                <td style="text-align: right; font-size: 16px; color: #333;"><strong>{{ format_price($withdrawal->amount) }}</strong></td>
                            </tr>
                            @if($withdrawal->fee_amount > 0)
                                <tr>
                                    <td><strong>Processing Fee:</strong></td>
                                    <td style="text-align: right; color: #6c757d;">-{{ format_price($withdrawal->fee_amount) }}</td>
                                </tr>
                                <tr style="border-top: 2px solid #dee2e6;">
                                    <td><strong>Net Amount:</strong></td>
                                    <td style="text-align: right; font-size: 18px; color: #28a745;"><strong>{{ format_price($withdrawal->net_amount) }}</strong></td>
                                </tr>
                            @else
                                <tr style="border-top: 2px solid #dee2e6;">
                                    <td><strong>Net Amount:</strong></td>
                                    <td style="text-align: right; font-size: 18px; color: #28a745;"><strong>{{ format_price($withdrawal->amount) }}</strong></td>
                                </tr>
                            @endif
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-gray-800">Request Details</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Request Date:</strong></td>
                                <td>{{ $withdrawal->requested_at ? $withdrawal->requested_at->format('M d, Y h:i A') : 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    @if($withdrawal->status === 'pending')
                                        <span class="badge bg-warning">Pending</span>
                                    @elseif($withdrawal->status === 'approved')
                                        <span class="badge bg-info">Approved</span>
                                    @elseif($withdrawal->status === 'paid')
                                        <span class="badge bg-success">Paid</span>
                                    @elseif($withdrawal->status === 'rejected')
                                        <span class="badge bg-danger">Rejected</span>
                                    @endif
                                </td>
                            </tr>
                            @if($withdrawal->processed_at)
                                <tr>
                                    <td><strong>Processed Date:</strong></td>
                                    <td>{{ $withdrawal->processed_at->format('M d, Y h:i A') }}</td>
                                </tr>
                            @endif
                            @if($withdrawal->processedBy)
                                <tr>
                                    <td><strong>Processed By:</strong></td>
                                    <td>{{ $withdrawal->processedBy->name }}</td>
                                </tr>
                            @endif
                        </table>
                    </div>
                </div>

                @if($withdrawal->notes)
                    <div class="mt-3">
                        <h6 class="text-gray-800">Notes</h6>
                        <div class="alert alert-info">
                            {{ $withdrawal->notes }}
                        </div>
                    </div>
                @endif

                @if($withdrawal->admin_notes)
                    <div class="mt-3">
                        <h6 class="text-gray-800">Admin Notes</h6>
                        <div class="alert alert-warning">
                            {{ $withdrawal->admin_notes }}
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Payment Details -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Payment Details</h6>
            </div>
            <div class="card-body">
                <!-- Debug Payment Details -->
                <div class="alert alert-secondary mb-3" style="font-size: 12px;">
                    <strong>Payment Debug:</strong><br>
                    Method: {{ var_export($withdrawal->payment_method) }}<br>
                    Details: {{ var_export($withdrawal->payment_details) }}
                </div>

                @if($withdrawal->payment_method === 'paypal')
                    <div class="d-flex align-items-center mb-3">
                        <i class="fab fa-paypal fa-2x text-primary me-3"></i>
                        <div>
                            <h6 class="mb-1">PayPal Payment</h6>
                            <p class="text-muted mb-0">
                                @if(is_array($withdrawal->payment_details) && isset($withdrawal->payment_details['paypal_email']))
                                    <strong>{{ $withdrawal->payment_details['paypal_email'] }}</strong>
                                @else
                                    <span class="text-danger">PayPal email not provided</span>
                                @endif
                            </p>
                        </div>
                    </div>

                    @if(is_array($withdrawal->payment_details) && isset($withdrawal->payment_details['paypal_email']))
                        <div class="alert alert-info">
                            <h6><i class="fab fa-paypal me-2"></i>Payment Instructions</h6>
                            <p class="mb-2"><strong>Send payment to:</strong> {{ $withdrawal->payment_details['paypal_email'] }}</p>
                            <p class="mb-2"><strong>Amount:</strong> ${{ number_format($withdrawal->net_amount ?: $withdrawal->amount, 2) }}</p>
                            <p class="mb-0"><strong>Reference:</strong> Withdrawal #{{ $withdrawal->id }}</p>
                        </div>
                    @endif

                @elseif($withdrawal->payment_method === 'bank_transfer')
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-university fa-2x text-info me-3"></i>
                        <div>
                            <h6 class="mb-1">Bank Transfer</h6>
                            <p class="text-muted mb-0">
                                @if(is_array($withdrawal->payment_details) && isset($withdrawal->payment_details['bank_name']))
                                    {{ $withdrawal->payment_details['bank_name'] }}
                                @else
                                    <span class="text-danger">Bank information not provided</span>
                                @endif
                            </p>
                        </div>
                    </div>

                    @if(is_array($withdrawal->payment_details))
                        <!-- BANK TRANSFER PAYMENT INSTRUCTIONS - HIGHLIGHTED FOR MANUAL PROCESSING -->
                        <div class="alert alert-primary border-primary" style="border-width: 3px !important;">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-university me-2"></i>
                                    <strong>BANK TRANSFER DETAILS</strong>
                                </h5>
                                <button class="btn btn-sm btn-outline-primary" onclick="copyBankDetails()">
                                    <i class="fas fa-copy me-1"></i>Copy Details
                                </button>
                            </div>

                            <div class="row">
                                <!-- Recipient Bank Details -->
                                <div class="col-md-6">
                                    <div class="card bg-light border-0">
                                        <div class="card-body p-3">
                                            <h6 class="text-primary mb-3">
                                                <i class="fas fa-building me-2"></i>Recipient Bank Details
                                            </h6>
                                            <table class="table table-sm table-borderless mb-0">
                                                <tr>
                                                    <td style="width: 40%;"><strong>Bank Name:</strong></td>
                                                    <td>
                                                        @if(isset($withdrawal->payment_details['bank_name']) && $withdrawal->payment_details['bank_name'])
                                                            <span class="badge bg-info fs-6">{{ $withdrawal->payment_details['bank_name'] }}</span>
                                                        @else
                                                            <span class="text-danger">❌ Not provided</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Account Name:</strong></td>
                                                    <td>
                                                        @if(isset($withdrawal->payment_details['account_name']) && $withdrawal->payment_details['account_name'])
                                                            <strong class="text-dark">{{ $withdrawal->payment_details['account_name'] }}</strong>
                                                        @else
                                                            <span class="text-danger">❌ Not provided</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Account Number:</strong></td>
                                                    <td>
                                                        @if(isset($withdrawal->payment_details['account_number']) && $withdrawal->payment_details['account_number'])
                                                            <code class="fs-6 bg-warning text-dark p-2 rounded">{{ $withdrawal->payment_details['account_number'] }}</code>
                                                        @else
                                                            <span class="text-danger">❌ Not provided</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Routing Number:</strong></td>
                                                    <td>
                                                        @if(isset($withdrawal->payment_details['routing_number']) && $withdrawal->payment_details['routing_number'])
                                                            <code class="fs-6 bg-info text-white p-2 rounded">{{ $withdrawal->payment_details['routing_number'] }}</code>
                                                        @else
                                                            <span class="text-muted">N/A</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Transfer Instructions -->
                                <div class="col-md-6">
                                    <div class="card bg-success text-white border-0">
                                        <div class="card-body p-3">
                                            <h6 class="text-white mb-3">
                                                <i class="fas fa-money-bill-wave me-2"></i>Transfer Instructions
                                            </h6>
                                            <table class="table table-sm table-borderless mb-0 text-white">
                                                <tr>
                                                    <td style="width: 50%;"><strong>Amount to Send:</strong></td>
                                                    <td>
                                                        <span class="badge bg-warning text-dark fs-5 p-2">
                                                            {{ format_price($withdrawal->net_amount ?: $withdrawal->amount) }}
                                                        </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Reference:</strong></td>
                                                    <td><code class="bg-light text-dark p-1 rounded">WD-{{ $withdrawal->id }}</code></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Beneficiary:</strong></td>
                                                    <td><strong>{{ $withdrawal->affiliate->user->name ?? 'Unknown' }}</strong></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Purpose:</strong></td>
                                                    <td>Affiliate Commission Withdrawal</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="mt-3 d-flex flex-wrap gap-2">
                                <button class="btn btn-success" onclick="copyBankDetails()">
                                    <i class="fas fa-copy me-2"></i>Copy Bank Details
                                </button>
                                <button class="btn btn-info" onclick="copyTransferAmount()">
                                    <i class="fas fa-dollar-sign me-2"></i>Copy Amount
                                </button>
                                <button class="btn btn-warning" onclick="copyReference()">
                                    <i class="fas fa-hashtag me-2"></i>Copy Reference
                                </button>
                                @if($withdrawal->status === 'approved')
                                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#markAsPaidModal">
                                        <i class="fas fa-check-circle me-2"></i>Mark as Paid
                                    </button>
                                @endif
                            </div>
                        </div>
                    @else
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Error:</strong> Bank transfer details are missing or corrupted.
                        </div>
                    @endif

                @else
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Unknown Payment Method:</strong> {{ ucfirst($withdrawal->payment_method ?? 'Not specified') }}
                        @if(!$withdrawal->payment_method)
                            <br><small>This withdrawal request is missing payment method information.</small>
                        @endif
                    </div>
                @endif
            </div>
        </div>

        <!-- Affiliate Information -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Affiliate Information</h6>
            </div>
            <div class="card-body">
                @if($withdrawal->affiliate && $withdrawal->affiliate->user)
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ $withdrawal->affiliate->user->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ $withdrawal->affiliate->user->email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>{{ $withdrawal->affiliate->user->phone ?? 'Not provided' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Affiliate Code:</strong></td>
                                    <td><code>{{ $withdrawal->affiliate->affiliate_code }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>Commission Rate:</strong></td>
                                    <td>{{ number_format($withdrawal->affiliate->commission_rate, 1) }}%</td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if($withdrawal->affiliate->status === 'active')
                                            <span class="badge bg-success">Active</span>
                                        @else
                                            <span class="badge bg-secondary">{{ ucfirst($withdrawal->affiliate->status) }}</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="mt-3">
                        <a href="{{ route('super_admin.affiliates.show', $withdrawal->affiliate) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-user me-2"></i>
                            View Affiliate Details
                        </a>
                    </div>
                @else
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> Affiliate information is not available for this withdrawal request.
                        @if($withdrawal->affiliate_id)
                            <br><small>Affiliate ID: {{ $withdrawal->affiliate_id }} (Record may have been deleted)</small>
                        @else
                            <br><small>No affiliate ID associated with this withdrawal.</small>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
            </div>
            <div class="card-body">
                @if($withdrawal->status === 'pending')
                    <form action="{{ route('super_admin.affiliate-withdrawals.approve', $withdrawal) }}" method="POST" class="mb-2">
                        @csrf
                        <button type="submit" class="btn btn-success btn-sm w-100"
                                onclick="return confirm('Approve this withdrawal request?')">
                            <i class="fas fa-check me-2"></i>
                            Approve Withdrawal
                        </button>
                    </form>

                    <button type="button" class="btn btn-danger btn-sm w-100 mb-2" data-bs-toggle="modal" data-bs-target="#rejectModal">
                        <i class="fas fa-times me-2"></i>
                        Reject Withdrawal
                    </button>
                @elseif($withdrawal->status === 'approved')
                    <button type="button" class="btn btn-info btn-sm w-100 mb-2" data-bs-toggle="modal" data-bs-target="#markAsPaidModal">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        Mark as Paid
                    </button>
                @endif

                <hr>

                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        Requested: {{ $withdrawal->requested_at ? $withdrawal->requested_at->format('M d, Y') : 'N/A' }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mark as Paid Modal -->
<div class="modal fade" id="markAsPaidModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('super_admin.affiliate-withdrawals.mark-as-paid', $withdrawal) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Mark Withdrawal as Paid</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Payment Summary</h6>
                        <p class="mb-1"><strong>Amount to Pay:</strong> ${{ number_format($withdrawal->net_amount ?: $withdrawal->amount, 2) }}</p>
                        <p class="mb-1"><strong>Affiliate:</strong> {{ $withdrawal->affiliate->user->name ?? 'Unknown' }}</p>
                        <p class="mb-0"><strong>Payment Method:</strong> {{ $withdrawal->payment_method_display }}</p>
                    </div>

                    <div class="mb-3">
                        <label for="transaction_reference" class="form-label">Transaction Reference</label>
                        <input type="text" class="form-control" id="transaction_reference" name="transaction_reference"
                               placeholder="Enter bank transaction ID, PayPal transaction ID, etc." required>
                        <div class="form-text">Enter the reference number from your payment system (e.g., bank transfer ID, PayPal transaction ID)</div>
                    </div>

                    <div class="mb-3">
                        <label for="payment_notes" class="form-label">Payment Notes (Optional)</label>
                        <textarea class="form-control" id="payment_notes" name="notes" rows="3"
                                  placeholder="Any additional notes about the payment..."></textarea>
                    </div>

                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Confirm:</strong> I have successfully transferred the payment and want to mark this withdrawal as paid.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>Mark as Paid
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('super_admin.affiliate-withdrawals.reject', $withdrawal) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Reject Withdrawal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">Rejection Reason</label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="4"
                                  placeholder="Please provide a reason for rejecting this withdrawal request..." required></textarea>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This action cannot be undone. The affiliate will be notified of the rejection.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Reject Withdrawal</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* Ensure bank transfer alert never auto-hides and stays visible */
.bank-transfer-alert {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    position: relative;
}

.bank-transfer-alert.show {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

.bank-transfer-alert.fade {
    opacity: 1 !important;
}

.bank-transfer-alert.fade.show {
    opacity: 1 !important;
}

/* Add a subtle animation to draw attention */
@keyframes bank-transfer-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0.1);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.bank-transfer-alert {
    animation: bank-transfer-pulse 3s ease-in-out infinite;
}

/* Ensure the alert content is always readable */
.bank-transfer-alert .alert-info {
    background-color: rgba(13, 202, 240, 0.1) !important;
    border: 1px solid rgba(13, 202, 240, 0.3) !important;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Ensure bank transfer alert never auto-hides
    const bankTransferAlert = document.getElementById('bankTransferAlert');

    if (bankTransferAlert) {
        // Force visibility immediately and continuously
        bankTransferAlert.style.display = 'block';
        bankTransferAlert.style.opacity = '1';
        bankTransferAlert.style.visibility = 'visible';

        // Remove any Bootstrap auto-hide attributes
        bankTransferAlert.removeAttribute('data-bs-dismiss');
        bankTransferAlert.removeAttribute('data-dismiss');
        bankTransferAlert.removeAttribute('data-bs-autohide');

        // Override Bootstrap's Alert class methods to prevent any hiding
        if (window.bootstrap && window.bootstrap.Alert) {
            try {
                const alertInstance = window.bootstrap.Alert.getOrCreateInstance(bankTransferAlert);
                if (alertInstance) {
                    // Override the close method to prevent any closing
                    alertInstance.close = function() {
                        console.log('Bank transfer alert close prevented - payment details must remain visible');
                        return false;
                    };
                }
            } catch (e) {
                console.log('Bootstrap Alert override failed, but alert should still be visible');
            }
        }

        // Prevent any automatic hiding by overriding all Bootstrap alert events
        ['show.bs.alert', 'hide.bs.alert', 'close.bs.alert', 'closed.bs.alert'].forEach(function(eventName) {
            bankTransferAlert.addEventListener(eventName, function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Bank transfer alert event prevented:', eventName);
            });
        });

        // Continuously ensure visibility (every 2 seconds)
        setInterval(function() {
            if (bankTransferAlert.style.display === 'none' ||
                bankTransferAlert.style.opacity === '0' ||
                bankTransferAlert.style.visibility === 'hidden') {

                console.log('Bank transfer alert visibility restored');
                bankTransferAlert.style.display = 'block';
                bankTransferAlert.style.opacity = '1';
                bankTransferAlert.style.visibility = 'visible';
            }
        }, 2000);

        // Add a warning if someone tries to hide it
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' &&
                    (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {

                    if (bankTransferAlert.style.display === 'none' ||
                        bankTransferAlert.classList.contains('d-none')) {

                        console.warn('Bank transfer alert visibility was modified - restoring...');
                        bankTransferAlert.style.display = 'block';
                        bankTransferAlert.classList.remove('d-none');
                    }
                }
            });
        });

        observer.observe(bankTransferAlert, {
            attributes: true,
            attributeFilter: ['style', 'class']
        });
    }
});

function copyBankDetails() {
    const bankName = '{{ $withdrawal->payment_details["bank_name"] ?? "" }}';
    const accountName = '{{ $withdrawal->payment_details["account_name"] ?? "" }}';
    const accountNumber = '{{ $withdrawal->payment_details["account_number"] ?? "" }}';
    const routingNumber = '{{ $withdrawal->payment_details["routing_number"] ?? "" }}';
    const amount = '{{ format_price($withdrawal->net_amount ?: $withdrawal->amount) }}';
    const reference = 'WD-{{ $withdrawal->id }}';

    const bankDetails = `Bank Transfer Details:
Bank Name: ${bankName}
Account Name: ${accountName}
Account Number: ${accountNumber}
Routing Number: ${routingNumber}
Amount: ${amount}
Reference: ${reference}
Beneficiary: {{ $withdrawal->affiliate->user->name ?? "Unknown" }}`;

    navigator.clipboard.writeText(bankDetails).then(function() {
        showToast('Bank details copied to clipboard!', 'success');
    }).catch(function() {
        showToast('Failed to copy bank details', 'error');
    });
}

function copyTransferAmount() {
    const amount = '{{ format_price($withdrawal->net_amount ?: $withdrawal->amount) }}';
    navigator.clipboard.writeText(amount).then(function() {
        showToast('Amount copied: ' + amount, 'success');
    }).catch(function() {
        showToast('Failed to copy amount', 'error');
    });
}

function copyReference() {
    const reference = 'WD-{{ $withdrawal->id }}';
    navigator.clipboard.writeText(reference).then(function() {
        showToast('Reference copied: ' + reference, 'success');
    }).catch(function() {
        showToast('Failed to copy reference', 'error');
    });
}

function showToast(message, type) {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
        ${message}
    `;

    // Add to page
    document.body.appendChild(toast);

    // Remove after 3 seconds
    setTimeout(function() {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}
</script>
@endpush
