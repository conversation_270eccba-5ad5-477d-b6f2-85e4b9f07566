@extends('super_admin.layouts.app')

@section('title', $article->title)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">{{ $article->title }}</h1>
                    <div class="d-flex align-items-center mt-2">
                        {!! $article->status_badge !!}
                        {!! $article->visibility_badge !!}
                        @if($article->featured)
                            <span class="badge bg-warning ms-2">
                                <i class="fas fa-star"></i> Featured
                            </span>
                        @endif
                    </div>
                </div>
                <div>
                    <a href="{{ route('super_admin.knowledge-base.edit', $article) }}" class="btn btn-primary me-2">
                        <i class="fas fa-edit"></i> Edit Article
                    </a>
                    <a href="{{ route('super_admin.knowledge-base.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Articles
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">Article Content</h5>
                                <div>
                                    @if($article->status === 'published')
                                        <a href="{{ route('user.support.article', $article) }}" 
                                           class="btn btn-outline-info btn-sm" target="_blank">
                                            <i class="fas fa-external-link-alt"></i> View Live
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            @if($article->excerpt)
                                <div class="alert alert-light border-start border-primary border-4 mb-4">
                                    <h6 class="fw-bold mb-2">Article Summary</h6>
                                    <p class="mb-0">{{ $article->excerpt }}</p>
                                </div>
                            @endif

                            <div class="article-content">
                                {!! \Illuminate\Support\Str::markdown($article->content) !!}
                            </div>

                            @if($article->tags && count($article->tags) > 0)
                                <div class="mt-4 pt-4 border-top">
                                    <h6 class="fw-bold mb-2">Tags</h6>
                                    @foreach($article->tags as $tag)
                                        <span class="badge bg-secondary me-1">{{ $tag }}</span>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- User Feedback -->
                    @if($article->feedback->count() > 0)
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-comments me-2"></i>
                                    User Feedback ({{ $article->feedback->count() }})
                                </h5>
                            </div>
                            <div class="card-body">
                                @foreach($article->feedback->take(10) as $feedback)
                                    <div class="d-flex align-items-start mb-3 {{ !$loop->last ? 'border-bottom pb-3' : '' }}">
                                        <div class="me-3">
                                            @if($feedback->is_helpful)
                                                <span class="badge bg-success">
                                                    <i class="fas fa-thumbs-up"></i> Helpful
                                                </span>
                                            @else
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-thumbs-down"></i> Not Helpful
                                                </span>
                                            @endif
                                        </div>
                                        <div class="flex-grow-1">
                                            @if($feedback->comment)
                                                <p class="mb-1">{{ $feedback->comment }}</p>
                                            @endif
                                            <small class="text-muted">
                                                @if($feedback->user)
                                                    by {{ $feedback->user->name }}
                                                @else
                                                    by Anonymous
                                                @endif
                                                on {{ $feedback->created_at->format('M d, Y \a\t H:i') }}
                                            </small>
                                        </div>
                                    </div>
                                @endforeach

                                @if($article->feedback->count() > 10)
                                    <div class="text-center mt-3">
                                        <small class="text-muted">
                                            Showing 10 of {{ $article->feedback->count() }} feedback entries
                                        </small>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Article Statistics -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Article Statistics</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary mb-0">{{ number_format($article->view_count) }}</h4>
                                        <small class="text-muted">Views</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success mb-0">
                                        {{ $article->helpfulness_percentage ?? 'N/A' }}{{ $article->helpfulness_percentage ? '%' : '' }}
                                    </h4>
                                    <small class="text-muted">Helpfulness</small>
                                </div>
                            </div>

                            @if($article->helpful_count > 0 || $article->not_helpful_count > 0)
                                <div class="mt-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="text-success">
                                            <i class="fas fa-thumbs-up"></i> Helpful
                                        </span>
                                        <span class="badge bg-success">{{ $article->helpful_count }}</span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-danger">
                                            <i class="fas fa-thumbs-down"></i> Not Helpful
                                        </span>
                                        <span class="badge bg-danger">{{ $article->not_helpful_count }}</span>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Article Details -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Article Details</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Category:</strong><br>
                                <span class="badge bg-secondary">{{ $article->category->name ?? 'Uncategorized' }}</span>
                            </div>
                            <div class="mb-3">
                                <strong>Author:</strong><br>
                                <small class="text-muted">{{ $article->author->name ?? 'Unknown' }}</small>
                            </div>
                            <div class="mb-3">
                                <strong>Created:</strong><br>
                                <small class="text-muted">{{ $article->created_at->format('M d, Y \a\t H:i') }}</small>
                            </div>
                            <div class="mb-3">
                                <strong>Last Updated:</strong><br>
                                <small class="text-muted">{{ $article->updated_at->format('M d, Y \a\t H:i') }}</small>
                            </div>
                            @if($article->published_at)
                                <div class="mb-3">
                                    <strong>Published:</strong><br>
                                    <small class="text-muted">{{ $article->published_at->format('M d, Y \a\t H:i') }}</small>
                                </div>
                            @endif
                            <div class="mb-3">
                                <strong>Slug:</strong><br>
                                <code class="small">{{ $article->slug }}</code>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('super_admin.knowledge-base.edit', $article) }}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> Edit Article
                                </a>
                                
                                @if($article->status === 'draft')
                                    <form method="POST" action="{{ route('super_admin.knowledge-base.update', $article) }}" class="d-inline">
                                        @csrf
                                        @method('PUT')
                                        <input type="hidden" name="status" value="published">
                                        <input type="hidden" name="title" value="{{ $article->title }}">
                                        <input type="hidden" name="content" value="{{ $article->content }}">
                                        <input type="hidden" name="category_id" value="{{ $article->category_id }}">
                                        <input type="hidden" name="visibility" value="{{ $article->visibility }}">
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-globe"></i> Publish Article
                                        </button>
                                    </form>
                                @elseif($article->status === 'published')
                                    <form method="POST" action="{{ route('super_admin.knowledge-base.update', $article) }}" class="d-inline">
                                        @csrf
                                        @method('PUT')
                                        <input type="hidden" name="status" value="draft">
                                        <input type="hidden" name="title" value="{{ $article->title }}">
                                        <input type="hidden" name="content" value="{{ $article->content }}">
                                        <input type="hidden" name="category_id" value="{{ $article->category_id }}">
                                        <input type="hidden" name="visibility" value="{{ $article->visibility }}">
                                        <button type="submit" class="btn btn-warning w-100">
                                            <i class="fas fa-eye-slash"></i> Unpublish Article
                                        </button>
                                    </form>
                                @endif

                                @if($article->status === 'published')
                                    <a href="{{ route('user.support.article', $article) }}" 
                                       class="btn btn-outline-info" target="_blank">
                                        <i class="fas fa-external-link-alt"></i> View Live Article
                                    </a>
                                @endif

                                <form method="POST" action="{{ route('super_admin.knowledge-base.destroy', $article) }}" 
                                      onsubmit="return confirm('Are you sure you want to delete this article? This action cannot be undone.')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger w-100">
                                        <i class="fas fa-trash"></i> Delete Article
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.article-content {
    line-height: 1.6;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.article-content h1 { font-size: 1.75rem; }
.article-content h2 { font-size: 1.5rem; }
.article-content h3 { font-size: 1.25rem; }
.article-content h4 { font-size: 1.1rem; }

.article-content p {
    margin-bottom: 1rem;
}

.article-content ul,
.article-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.article-content li {
    margin-bottom: 0.25rem;
}

.article-content blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 1rem;
}

.article-content code {
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.article-content pre {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.25rem;
    overflow-x: auto;
    margin: 1rem 0;
}

.article-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.article-content table th,
.article-content table td {
    border: 1px solid #dee2e6;
    padding: 0.5rem;
    text-align: left;
}

.article-content table th {
    background-color: #f8f9fa;
    font-weight: 600;
}
</style>
@endsection
