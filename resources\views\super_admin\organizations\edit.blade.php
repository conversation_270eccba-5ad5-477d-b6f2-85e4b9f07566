@extends('super_admin.layouts.app')

@section('title', 'Edit Organization - ' . $organization->name)
@section('page-title', 'Edit Organization')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Edit Organization: {{ $organization->name }}</h1>
            <p class="text-muted">Update organization details</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('super_admin.organizations.show', $organization) }}" class="btn btn-info">
                <i class="fas fa-eye me-2"></i>View Organization
            </a>
            <a href="{{ route('super_admin.organizations.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Organizations
            </a>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Organization Details</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('super_admin.organizations.update', $organization) }}" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Organization Name *</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $organization->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email', $organization->email) }}" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" name="phone" value="{{ old('phone', $organization->phone) }}">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="website" class="form-label">Website</label>
                                <input type="url" class="form-control @error('website') is-invalid @enderror" 
                                       id="website" name="website" value="{{ old('website', $organization->website) }}" 
                                       placeholder="https://example.com">
                                @error('website')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control @error('address') is-invalid @enderror" 
                                      id="address" name="address" rows="3">{{ old('address', $organization->address) }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Brief description of the organization">{{ old('description', $organization->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Plan Selection -->
                        <div class="mb-4">
                            <label for="plan_id" class="form-label">Subscription Plan *</label>
                            <select class="form-select @error('plan_id') is-invalid @enderror" 
                                    id="plan_id" name="plan_id" required>
                                <option value="">Select a Plan</option>
                                @foreach($plans as $plan)
                                    <option value="{{ $plan->id }}" 
                                            data-price="{{ $plan->price }}"
                                            data-features="{{ json_encode([
                                                'branches' => $plan->branch_limit,
                                                'users' => $plan->user_limit,
                                                'retention' => $plan->data_retention_days,
                                                'thermal_printing' => $plan->thermal_printing,
                                                'advanced_reporting' => $plan->advanced_reporting,
                                                'api_access' => $plan->api_access
                                            ]) }}"
                                            {{ old('plan_id', $organization->plan_id) == $plan->id ? 'selected' : '' }}>
                                        {{ $plan->name }} - ${{ number_format($plan->price, 2) }}/month
                                    </option>
                                @endforeach
                            </select>
                            @error('plan_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Trial Period -->
                        <div class="mb-4">
                            <label for="trial_ends_at" class="form-label">Trial End Date</label>
                            <input type="date" class="form-control @error('trial_ends_at') is-invalid @enderror" 
                                   id="trial_ends_at" name="trial_ends_at" 
                                   value="{{ old('trial_ends_at', $organization->trial_ends_at ? $organization->trial_ends_at->format('Y-m-d') : '') }}">
                            <small class="text-muted">Leave empty if no trial period</small>
                            @error('trial_ends_at')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Logo Upload -->
                        <div class="mb-4">
                            <label for="logo" class="form-label">Organization Logo</label>
                            @if($organization->logo)
                                <div class="mb-2">
                                    <img src="{{ asset('storage/logos/' . $organization->logo) }}" 
                                         alt="Current Logo" class="rounded" width="100" height="100" style="object-fit: cover;">
                                    <div class="form-check mt-2">
                                        <input type="checkbox" class="form-check-input" id="remove_logo" name="remove_logo" value="1">
                                        <label class="form-check-label" for="remove_logo">
                                            Remove current logo
                                        </label>
                                    </div>
                                </div>
                            @endif
                            <input type="file" class="form-control @error('logo') is-invalid @enderror" 
                                   id="logo" name="logo" accept="image/*">
                            <small class="text-muted">Supported formats: JPEG, PNG, JPG, GIF. Max size: 2MB</small>
                            @error('logo')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Status -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" 
                                       name="is_active" value="1" {{ old('is_active', $organization->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    <strong>Active Organization</strong>
                                    <br><small class="text-muted">Organization can access the system when active</small>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('super_admin.organizations.show', $organization) }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Organization
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Current Organization Info -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Organization</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            @if($organization->logo)
                                <img src="{{ asset('storage/logos/' . $organization->logo) }}" 
                                     alt="{{ $organization->name }}" 
                                     class="rounded-circle" width="80" height="80" style="object-fit: cover;">
                            @else
                                <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" 
                                     style="width: 80px; height: 80px;">
                                    <span class="text-white font-weight-bold fs-3">
                                        {{ substr($organization->name, 0, 1) }}
                                    </span>
                                </div>
                            @endif
                        </div>
                        <h5>{{ $organization->name }}</h5>
                        <p class="text-muted">{{ $organization->email }}</p>
                        <div class="text-start">
                            <small class="text-muted">
                                <strong>Current Plan:</strong> 
                                @if($organization->plan)
                                    {{ $organization->plan->name }} (${{ number_format($organization->plan->price, 2) }}/month)
                                @else
                                    No plan assigned
                                @endif
                                <br><br>
                                <strong>Status:</strong> 
                                @if($organization->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-danger">Inactive</span>
                                @endif
                                <br><br>
                                <strong>Trial:</strong> 
                                @if($organization->trial_ends_at)
                                    @if($organization->trial_ends_at->isFuture())
                                        <span class="badge bg-info">{{ $organization->trial_ends_at->diffForHumans() }}</span>
                                    @else
                                        <span class="badge bg-warning">Expired</span>
                                    @endif
                                @else
                                    No trial
                                @endif
                                <br><br>
                                <strong>Users:</strong> {{ $organization->users->count() }}<br>
                                <strong>Branches:</strong> {{ $organization->branches->count() }}<br>
                                <strong>Subscriptions:</strong> {{ $organization->subscriptions->count() }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Plan Features -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Plan Features</h6>
                </div>
                <div class="card-body" id="current-plan-features">
                    @if($organization->plan)
                        <div class="row">
                            <div class="col-12 mb-2"><small><strong>Limits:</strong></small></div>
                            <div class="col-12"><small>• {{ $organization->plan->branch_limit == 999 ? 'Unlimited' : $organization->plan->branch_limit }} Branches</small></div>
                            <div class="col-12"><small>• {{ $organization->plan->user_limit == 999 ? 'Unlimited' : $organization->plan->user_limit }} Users</small></div>
                            <div class="col-12 mb-2"><small>• {{ $organization->plan->data_retention_days == 999 ? 'Forever' : $organization->plan->data_retention_days . ' Days' }} Data Retention</small></div>
                            <div class="col-12 mb-2"><small><strong>Features:</strong></small></div>
                            @if($organization->plan->thermal_printing) <div class="col-12"><small>• Thermal Printing</small></div> @endif
                            @if($organization->plan->advanced_reporting) <div class="col-12"><small>• Advanced Reporting</small></div> @endif
                            @if($organization->plan->api_access) <div class="col-12"><small>• API Access</small></div> @endif
                            @if($organization->plan->white_label) <div class="col-12"><small>• White Label</small></div> @endif
                            @if($organization->plan->custom_branding) <div class="col-12"><small>• Custom Branding</small></div> @endif
                        </div>
                    @else
                        <p class="text-muted">No plan assigned</p>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="setTrialPeriod()">
                            <i class="fas fa-clock me-2"></i>Set 30-day Trial
                        </button>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="clearTrial()">
                            <i class="fas fa-times me-2"></i>Clear Trial
                        </button>
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="toggleStatus()">
                            <i class="fas fa-toggle-on me-2"></i>Toggle Status
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    const phoneInput = document.getElementById('phone');
    const websiteInput = document.getElementById('website');
    const planSelect = document.getElementById('plan_id');
    const logoInput = document.getElementById('logo');

    // Handle logo preview
    logoInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // You could add a preview here if needed
                console.log('New logo selected');
            };
            reader.readAsDataURL(file);
        }
    });

    // Update plan features when plan changes
    planSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            const features = JSON.parse(selectedOption.dataset.features || '{}');
            
            let featuresHtml = '<div class="row">';
            featuresHtml += `<div class="col-12 mb-2"><small><strong>New Plan Limits:</strong></small></div>`;
            featuresHtml += `<div class="col-12"><small>• ${features.branches == 999 ? 'Unlimited' : features.branches} Branches</small></div>`;
            featuresHtml += `<div class="col-12"><small>• ${features.users == 999 ? 'Unlimited' : features.users} Users</small></div>`;
            featuresHtml += `<div class="col-12 mb-2"><small>• ${features.retention == 999 ? 'Forever' : features.retention + ' Days'} Data Retention</small></div>`;
            featuresHtml += `<div class="col-12 mb-2"><small><strong>New Plan Features:</strong></small></div>`;
            if (features.thermal_printing) featuresHtml += `<div class="col-12"><small>• Thermal Printing</small></div>`;
            if (features.advanced_reporting) featuresHtml += `<div class="col-12"><small>• Advanced Reporting</small></div>`;
            if (features.api_access) featuresHtml += `<div class="col-12"><small>• API Access</small></div>`;
            featuresHtml += '</div>';

            document.getElementById('current-plan-features').innerHTML = featuresHtml;
        }
    });
});

// Quick action functions
function setTrialPeriod() {
    const today = new Date();
    const trialEnd = new Date(today);
    trialEnd.setDate(today.getDate() + 30);
    document.getElementById('trial_ends_at').value = trialEnd.toISOString().split('T')[0];
}

function clearTrial() {
    document.getElementById('trial_ends_at').value = '';
}

function toggleStatus() {
    const statusCheckbox = document.getElementById('is_active');
    statusCheckbox.checked = !statusCheckbox.checked;
}
</script>
@endsection
