<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Affiliate;
use Symfony\Component\HttpFoundation\Response;

class PreventCrossAuthentication
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $userType = 'organization'): Response
    {
        if ($userType === 'organization') {
            // For organization routes, ensure user is not an affiliate
            if (Auth::check()) {
                $user = Auth::user();
                $affiliate = Affiliate::where('user_id', $user->id)->first();
                
                if ($affiliate) {
                    Auth::logout();
                    return redirect()->route('affiliate.login')
                        ->with('error', 'This account is registered as an affiliate. Please use the affiliate login.');
                }
                
                // Ensure user has organization access
                if (!$user->organization_id) {
                    Auth::logout();
                    return redirect()->route('organization.login')
                        ->with('error', 'This account is not associated with any organization.');
                }
            }
        } elseif ($userType === 'affiliate') {
            // For affiliate routes, ensure user is not an organization user
            if (Auth::guard('affiliate')->check()) {
                $user = Auth::guard('affiliate')->user();
                
                if ($user->organization_id) {
                    Auth::guard('affiliate')->logout();
                    return redirect()->route('organization.login')
                        ->with('error', 'This account is registered as an organization user. Please use the organization login.');
                }
                
                // Ensure user has affiliate account
                $affiliate = Affiliate::where('user_id', $user->id)->first();
                if (!$affiliate) {
                    Auth::guard('affiliate')->logout();
                    return redirect()->route('affiliate.register')
                        ->with('error', 'You need to register as an affiliate first.');
                }
            }
        }

        return $next($request);
    }
}
