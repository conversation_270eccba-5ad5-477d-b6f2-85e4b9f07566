<?php

namespace App\Http\Controllers;

use App\Models\Organization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class OrganizationController extends Controller
{
    /**
     * Display the organization details.
     */
    public function show()
    {
        $organization = Auth::user()->organization;

        return view('organization.show', compact('organization'));
    }

    /**
     * Show the form for editing the organization.
     */
    public function edit()
    {
        $organization = Auth::user()->organization;

        return view('organization.edit', compact('organization'));
    }

    /**
     * Update the specified organization in storage.
     */
    public function update(Request $request)
    {
        $organization = Auth::user()->organization;

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['nullable', 'string', 'email', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:255'],
            'website' => ['nullable', 'string', 'max:255', 'url'],
            'description' => ['nullable', 'string'],
            'logo' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif,svg', 'max:2048'],
        ]);

        try {
            $data = $request->except('logo');

            // Handle logo upload
            if ($request->hasFile('logo')) {
                // Delete old logo if exists
                if ($organization->logo && Storage::exists('public/logos/' . $organization->logo)) {
                    Storage::delete('public/logos/' . $organization->logo);
                }

                // Store new logo
                $logoName = time() . '.' . $request->logo->extension();
                $request->logo->storeAs('public/logos', $logoName);
                $data['logo'] = $logoName;
            }

            $organization->update($data);

            return redirect()->route('organization.show')
                ->with('success', 'Organization updated successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update organization: ' . $e->getMessage()])->withInput();
        }
    }
}
