@extends('emails.layout')

@section('title', $welcomeMessage ? $welcomeMessage->subject : 'Welcome to Sederly.com!')

@section('header-title', 'Welcome!')
@section('header-tagline', 'Thank you for joining our platform')

@section('content')
    <div class="greeting">
        {{ $welcomeMessage ? $welcomeMessage->greeting : 'Hello ' . $user->name . '!' }}
    </div>

    @if($welcomeMessage && $welcomeMessage->content)
        @foreach(explode("\n", $welcomeMessage->content) as $paragraph)
            @if(trim($paragraph))
                <p>{{ trim($paragraph) }}</p>
            @endif
        @endforeach
    @else
        <p>Thank you for joining Sederly.com! We're excited to help you streamline your business operations and boost your productivity.</p>
        
        @if($userType === 'organization')
            <div class="info-box">
                <h3>Getting Started</h3>
                <p>To make the most of your account, we recommend:</p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Complete your organization profile</li>
                    <li>Set up your first branch</li>
                    <li>Add team members</li>
                    <li>Start managing your orders</li>
                </ul>
            </div>
        @elseif($userType === 'affiliate')
            <div class="info-box">
                <h3>Your Affiliate Benefits</h3>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Competitive commission rates</li>
                    <li>Real-time tracking</li>
                    <li>Monthly payouts</li>
                    <li>Dedicated support</li>
                </ul>
            </div>
        @endif

        <p>If you need any assistance, our support team is here to help you every step of the way.</p>
    @endif

    @if($welcomeMessage && $welcomeMessage->call_to_action_text && $welcomeMessage->call_to_action_url)
        <div class="btn-center">
            <a href="{{ url($welcomeMessage->call_to_action_url) }}" class="btn">
                {{ $welcomeMessage->call_to_action_text }}
            </a>
        </div>
    @else
        <div class="btn-center">
            @if($userType === 'organization')
                <a href="{{ url('/dashboard') }}" class="btn">Get Started</a>
            @elseif($userType === 'affiliate')
                <a href="{{ url('/affiliate/dashboard') }}" class="btn">View Dashboard</a>
            @else
                <a href="{{ url('/dashboard') }}" class="btn">Access Your Account</a>
            @endif
        </div>
    @endif

    <div class="divider"></div>

    <div class="info-box">
        <h3>Need Help?</h3>
        <p>
            If you have any questions or need assistance, don't hesitate to reach out to our support team. 
            We're here to ensure you have the best experience possible.
        </p>
    </div>

    <p class="text-center text-muted small">
        This email was sent to {{ $user->email }} because you created an account with Sederly.com.
    </p>
@endsection
