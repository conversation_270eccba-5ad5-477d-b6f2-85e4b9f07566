@extends('layouts.app')

@section('title', 'Support Analytics')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Support Analytics</h1>
                    <p class="text-muted mb-0">Performance metrics for {{ auth()->user()->organization->name }}</p>
                </div>
                <div>
                    <a href="{{ route('organization.support.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Support
                    </a>
                </div>
            </div>

            <!-- Time Period Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('organization.support.analytics') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="period" class="form-label">Time Period</label>
                                <select name="period" id="period" class="form-control">
                                    <option value="7" {{ request('period', '30') == '7' ? 'selected' : '' }}>Last 7 days</option>
                                    <option value="30" {{ request('period', '30') == '30' ? 'selected' : '' }}>Last 30 days</option>
                                    <option value="90" {{ request('period', '30') == '90' ? 'selected' : '' }}>Last 90 days</option>
                                    <option value="365" {{ request('period', '30') == '365' ? 'selected' : '' }}>Last year</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-chart-bar"></i> Update
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Key Metrics -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $metrics['total_tickets'] }}</h4>
                                    <p class="mb-0">Total Tickets</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-ticket-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $metrics['resolved_tickets'] }}</h4>
                                    <p class="mb-0">Resolved</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $metrics['avg_response_time'] }}h</h4>
                                    <p class="mb-0">Avg Response Time</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $metrics['satisfaction_rate'] }}%</h4>
                                    <p class="mb-0">Satisfaction Rate</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                Tickets Over Time
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="ticketsChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-pie me-2"></i>
                                Tickets by Status
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="statusChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Analytics -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-users me-2"></i>
                                Top Users by Tickets
                            </h5>
                        </div>
                        <div class="card-body">
                            @if(count($metrics['top_users']) > 0)
                                @foreach($metrics['top_users'] as $user)
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                                             style="width: 40px; height: 40px;">
                                            {{ substr($user->name, 0, 1) }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ $user->name }}</div>
                                            <small class="text-muted">{{ $user->email }}</small>
                                        </div>
                                    </div>
                                    <span class="badge bg-primary">{{ $user->tickets_count }} tickets</span>
                                </div>
                                @endforeach
                            @else
                                <p class="text-muted text-center">No ticket data available for this period.</p>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-tags me-2"></i>
                                Tickets by Category
                            </h5>
                        </div>
                        <div class="card-body">
                            @if(count($metrics['categories']) > 0)
                                @foreach($metrics['categories'] as $category)
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <span class="badge bg-secondary me-2">
                                            {{ ucfirst(str_replace('_', ' ', $category->category)) }}
                                        </span>
                                    </div>
                                    <div>
                                        <span class="fw-bold">{{ $category->count }}</span>
                                        <small class="text-muted">tickets</small>
                                    </div>
                                </div>
                                @endforeach
                            @else
                                <p class="text-muted text-center">No category data available for this period.</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Response Time Analysis -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-stopwatch me-2"></i>
                                Response Time Analysis
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 text-center">
                                    <h4 class="text-success">{{ $metrics['response_times']['under_4h'] }}</h4>
                                    <p class="text-muted">Under 4 hours</p>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h4 class="text-warning">{{ $metrics['response_times']['4h_to_24h'] }}</h4>
                                    <p class="text-muted">4-24 hours</p>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h4 class="text-danger">{{ $metrics['response_times']['over_24h'] }}</h4>
                                    <p class="text-muted">Over 24 hours</p>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h4 class="text-secondary">{{ $metrics['response_times']['no_response'] }}</h4>
                                    <p class="text-muted">No response yet</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Tickets Over Time Chart
const ticketsCtx = document.getElementById('ticketsChart').getContext('2d');
new Chart(ticketsCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode($metrics['daily_tickets']['labels']) !!},
        datasets: [{
            label: 'Tickets Created',
            data: {!! json_encode($metrics['daily_tickets']['data']) !!},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Status Chart
const statusCtx = document.getElementById('statusChart').getContext('2d');
new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode($metrics['status_distribution']['labels']) !!},
        datasets: [{
            data: {!! json_encode($metrics['status_distribution']['data']) !!},
            backgroundColor: [
                '#ffc107', // open
                '#17a2b8', // in_progress
                '#6c757d', // waiting_customer
                '#fd7e14', // waiting_admin
                '#28a745', // resolved
                '#6f42c1'  // closed
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
@endsection
