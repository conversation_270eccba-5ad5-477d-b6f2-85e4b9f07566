<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class KnowledgeBaseFeedback extends Model
{
    use HasFactory;

    protected $table = 'knowledge_base_feedback';

    protected $fillable = [
        'knowledge_base_article_id',
        'user_id',
        'is_helpful',
        'comment',
        'ip_address',
    ];

    protected $casts = [
        'is_helpful' => 'boolean',
    ];

    /**
     * Get the article this feedback belongs to
     */
    public function article(): BelongsTo
    {
        return $this->belongsTo(KnowledgeBaseArticle::class, 'knowledge_base_article_id');
    }

    /**
     * Get the user who provided the feedback
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for helpful feedback
     */
    public function scopeHelpful($query)
    {
        return $query->where('is_helpful', true);
    }

    /**
     * Scope for not helpful feedback
     */
    public function scopeNotHelpful($query)
    {
        return $query->where('is_helpful', false);
    }
}
