<?php

namespace App\View\Components;

use Illuminate\View\Component;
use Illuminate\View\View;
use App\Models\Setting;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class Sidebar extends Component
{
    public $menuItems;

    public function __construct(public Setting $settings)
    {
        $this->menuItems = $this->getMenuItems();
    }

    private function getMenuItems(): array
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return [];
            }

            $items = [
                [
                    'label' => 'Dashboard',
                    'route' => 'dashboard',
                    'icon' => 'chart-line',
                    'active' => request()->routeIs('dashboard')
                ],
            ];

            // Orders menu - Staff can create, others can view
            if ($user->hasRole('Staff')) {
                $items[] = [
                    'label' => 'Create Order',
                    'route' => 'orders.create',
                    'icon' => 'plus-circle',
                    'active' => request()->routeIs('orders.create')
                ];
            }

            // Orders viewing menu items - accessible by all roles including Delivery
            $items[] = [
                'label' => 'View Orders',
                'route' => 'orders.index',
                'icon' => 'shopping-cart',
                'active' => request()->routeIs('orders.index', 'orders.show')
            ];

            // Add Pending Orders menu item right after View Orders
            $items[] = [
                'label' => 'Pending Orders',
                'route' => 'orders.pending',
                'icon' => 'clock',
                'active' => request()->routeIs('orders.pending'),
                'badge' => $this->getPendingOrdersCount()
            ];

            // Add Overdue Orders menu item
            $items[] = [
                'label' => 'Overdue Orders',
                'route' => 'orders.overdue',
                'icon' => 'exclamation-circle',
                'active' => request()->routeIs('orders.overdue'),
                'badge' => $this->getOverdueOrdersCount(),
                'badge_color' => 'bg-red-500' // Add red background for overdue count
            ];

            // Add Customer Directory after Orders menu items
            $items[] = [
                'label' => 'Customer Directory',
                'route' => 'customers.index',
                'icon' => 'address-book',
                'active' => request()->routeIs('customers.*')
            ];

            // Add Support Center for all users
            $items[] = [
                'label' => 'Support Center',
                'route' => 'user.support.index',
                'icon' => 'headset',
                'active' => request()->routeIs('user.support.*')
            ];

            // Expenditures - Not visible to Staff, Production, or Delivery roles
            if (!$user->hasRole(['Staff', 'Production', 'Delivery'])) {
                $items[] = [
                    'label' => 'Expenditures',
                    'route' => 'expenditures.index',
                    'icon' => 'money-bill-wave',
                    'active' => request()->routeIs('expenditures.*')
                ];
            }

            // Finance-related items for users with appropriate roles
            if ($user->hasAnyRole(['Organization Owner', 'Manager', 'Account'])) {
                $items[] = [
                    'label' => 'Financial Overview',
                    'route' => 'financial.overview',
                    'icon' => 'chart-bar',
                    'active' => request()->routeIs('financial.*')
                ];
                $items[] = [
                    'label' => 'Account Summary',
                    'route' => 'account.summary',
                    'icon' => 'wallet',
                    'active' => request()->routeIs('account.*')
                ];
            }

            // Billing & Payments for Organization Owner and Manager
            if ($user->hasAnyRole(['Organization Owner', 'Manager'])) {
                $items[] = [
                    'label' => 'Billing & Payments',
                    'route' => 'billing.index',
                    'icon' => 'credit-card',
                    'active' => request()->routeIs('billing.*')
                ];
            }

            // Plan Management - For Organization Owner and Manager
            if ($user->hasAnyRole(['Organization Owner', 'Manager'])) {
                $items[] = [
                    'label' => 'Subscription Plans',
                    'route' => 'plan-change.index',
                    'icon' => 'credit-card',
                    'active' => request()->routeIs('plan-change.*')
                ];
            }

            // Organization Support Dashboard - For Organization Owner and Manager
            if ($user->hasAnyRole(['Organization Owner', 'Manager'])) {
                $items[] = [
                    'label' => 'Organization Support',
                    'route' => 'organization.support.index',
                    'icon' => 'life-ring',
                    'active' => request()->routeIs('organization.support.*')
                ];
            }

            // User Management - Only for Organization Owner
            if ($user->hasRole('Organization Owner')) {
                // Add Branch Management menu item
                $items[] = [
                    'label' => 'Branches',
                    'route' => 'branches.index',
                    'icon' => 'code-branch',
                    'active' => request()->routeIs('branches.*')
                ];

                $items[] = [
                    'label' => 'User Management',
                    'route' => 'users.index',
                    'icon' => 'users',
                    'active' => request()->routeIs('users.*')
                ];

                // Add Settings menu item
                $items[] = [
                    'label' => 'Settings',
                    'route' => 'settings.index',
                    'icon' => 'cog',
                    'active' => request()->routeIs('settings.*')
                ];
            }

            // Verify all routes exist
            foreach ($items as $item) {
                if (!array_key_exists('route', $item) || !route($item['route'])) {
                    throw new \Exception("Invalid route configuration for menu item: {$item['label']}");
                }
            }

            return $items;
        } catch (\Exception $e) {
            Log::error('Error in sidebar menu configuration: ' . $e->getMessage());
            return [
                [
                    'label' => 'Dashboard',
                    'route' => 'dashboard',
                    'icon' => 'home',
                    'active' => true
                ]
            ];
        }
    }

    /**
     * Get the count of pending orders for the current user's organization and branch
     */
    private function getPendingOrdersCount(): int
    {
        try {
            $user = Auth::user();
            $query = \App\Models\Order::pending()->where('organization_id', $user->organization_id);

            // Filter by branch if user is assigned to a branch
            if ($user->branch_id) {
                $query->where('branch_id', $user->branch_id);
            }

            return $query->count();
        } catch (\Exception $e) {
            Log::error('Error getting pending orders count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get the count of overdue orders for the current user's organization and branch
     */
    private function getOverdueOrdersCount(): int
    {
        try {
            $user = Auth::user();
            $query = \App\Models\Order::overdue()->where('organization_id', $user->organization_id);

            // Filter by branch if user is assigned to a branch
            if ($user->branch_id) {
                $query->where('branch_id', $user->branch_id);
            }

            return $query->count();
        } catch (\Exception $e) {
            Log::error('Error getting overdue orders count: ' . $e->getMessage());
            return 0;
        }
    }

    public function render(): View
    {
        return view('components.sidebar');
    }
}
