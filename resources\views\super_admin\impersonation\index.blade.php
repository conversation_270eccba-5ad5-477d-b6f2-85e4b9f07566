@extends('super_admin.layouts.app')

@section('title', 'User Impersonation')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">User Impersonation</h1>
                <div class="text-muted">
                    <i class="fas fa-shield-alt me-1"></i>
                    Secure user switching for support purposes
                </div>
            </div>

            <!-- Search and Filter Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('super_admin.impersonation.index') }}">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search Users</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       value="{{ request('search') }}" placeholder="Name or email...">
                            </div>
                            <div class="col-md-4">
                                <label for="organization_id" class="form-label">Organization</label>
                                <select class="form-select" id="organization_id" name="organization_id">
                                    <option value="">All Organizations</option>
                                    @foreach($organizations as $organization)
                                        <option value="{{ $organization->id }}"
                                                {{ request('organization_id') == $organization->id ? 'selected' : '' }}>
                                            {{ $organization->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <a href="{{ route('super_admin.impersonation.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Warning -->
            <div class="alert alert-warning mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle fa-lg"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="alert-heading">Security Notice</h6>
                        <p class="mb-2">User impersonation is a powerful feature that should be used responsibly:</p>
                        <ul class="mb-0">
                            <li>All activities during impersonation are logged and tracked</li>
                            <li>Impersonation sessions automatically expire after 8 hours</li>
                            <li>Only use this feature for legitimate support purposes</li>
                            <li>Always stop impersonation when your support task is complete</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Users List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        Available Users ({{ $users->total() }})
                    </h5>
                </div>
                <div class="card-body">
                    @if($users->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Organization</th>
                                        <th>Roles</th>
                                        <th>Status</th>
                                        <th>Last Activity</th>
                                        <th width="200">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($users as $user)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                                        <span class="text-white fw-bold">
                                                            {{ strtoupper(substr($user->name, 0, 1)) }}
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">{{ $user->name }}</div>
                                                        <div class="text-muted small">{{ $user->email }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="fw-bold">{{ $user->organization->name }}</div>
                                                <div class="text-muted small">
                                                    Plan: {{ $user->organization->plan->name ?? 'No Plan' }}
                                                </div>
                                            </td>
                                            <td>
                                                @foreach($user->roles as $role)
                                                    <span class="badge bg-secondary me-1">{{ $role->name }}</span>
                                                @endforeach
                                            </td>
                                            <td>
                                                <span class="badge bg-success">{{ ucfirst($user->status) }}</span>
                                            </td>
                                            <td>
                                                <span class="text-muted small">
                                                    {{ $user->updated_at->diffForHumans() }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('super_admin.impersonation.show', $user) }}"
                                                       class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                    <button type="button"
                                                            class="btn btn-sm btn-warning"
                                                            onclick="confirmImpersonation('{{ $user->name }}', '{{ route('super_admin.impersonation.start', $user) }}')">
                                                        <i class="fas fa-user-secret"></i> Impersonate
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            {{ $users->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No users found</h5>
                            <p class="text-muted">Try adjusting your search criteria.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Impersonation Confirmation Modal -->
<div class="modal fade" id="impersonationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-secret me-2"></i>
                    Confirm User Impersonation
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Important:</strong> You are about to impersonate another user.
                </div>
                <p>You are about to start impersonating: <strong id="targetUserName"></strong></p>
                <p>This action will:</p>
                <ul>
                    <li>Log you out as super admin</li>
                    <li>Log you in as the target user</li>
                    <li>Record all activities during impersonation</li>
                    <li>Automatically expire after 8 hours</li>
                </ul>
                <p class="text-muted small">
                    <i class="fas fa-info-circle me-1"></i>
                    You can stop impersonation at any time using the banner that will appear at the top of the page.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="impersonationForm" method="POST" style="display: inline;">
                    @csrf
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-user-secret me-1"></i>
                        Start Impersonation
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}
</style>

<script>
function confirmImpersonation(userName, actionUrl) {
    document.getElementById('targetUserName').textContent = userName;
    document.getElementById('impersonationForm').action = actionUrl;

    const modal = new bootstrap.Modal(document.getElementById('impersonationModal'));
    modal.show();
}
</script>
@endsection
