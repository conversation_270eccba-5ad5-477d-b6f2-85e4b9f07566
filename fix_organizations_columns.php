<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Schema\Blueprint;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "Checking organizations table structure...\n";

    // Check if is_active column exists
    if (!Schema::hasColumn('organizations', 'is_active')) {
        echo "Adding is_active column...\n";
        Schema::table('organizations', function (Blueprint $table) {
            $table->boolean('is_active')->default(true)->after('name');
        });
        echo "✓ is_active column added\n";
    } else {
        echo "✓ is_active column already exists\n";
    }

    // Check if trial_ends_at column exists
    if (!Schema::hasColumn('organizations', 'trial_ends_at')) {
        echo "Adding trial_ends_at column...\n";
        Schema::table('organizations', function (Blueprint $table) {
            $table->dateTime('trial_ends_at')->nullable()->after('is_active');
        });
        echo "✓ trial_ends_at column added\n";
    } else {
        echo "✓ trial_ends_at column already exists\n";
    }

    // Check if plan_id column exists
    if (!Schema::hasColumn('organizations', 'plan_id')) {
        echo "Adding plan_id column...\n";
        Schema::table('organizations', function (Blueprint $table) {
            $table->foreignId('plan_id')->nullable()->after('id')->constrained()->onDelete('set null');
        });
        echo "✓ plan_id column added\n";
    } else {
        echo "✓ plan_id column already exists\n";
    }

    // Update existing organizations to be active
    $updated = DB::table('organizations')
        ->whereNull('is_active')
        ->update(['is_active' => true]);
    
    if ($updated > 0) {
        echo "✓ Updated {$updated} organizations to be active\n";
    }

    echo "\n✅ Organizations table structure fixed successfully!\n";
    echo "You can now access the super admin dashboard.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Please run the SQL script manually or check your database connection.\n";
}
