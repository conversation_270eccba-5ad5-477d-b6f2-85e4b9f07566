<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Organization;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Revenue report
     */
    public function revenue(Request $request)
    {
        $period = $request->get('period', 'monthly'); // daily, weekly, monthly, yearly
        $startDate = $request->get('start_date', now()->subMonths(12)->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        // Revenue by period
        $revenueData = $this->getRevenueByPeriod($period, $startDate, $endDate);

        // Revenue by plan
        $revenueByPlan = Subscription::select('plans.name', DB::raw('SUM(amount_paid) as total_revenue'))
            ->join('plans', 'subscriptions.plan_id', '=', 'plans.id')
            ->whereBetween('subscriptions.created_at', [$startDate, $endDate])
            ->groupBy('plans.id', 'plans.name')
            ->orderBy('total_revenue', 'desc')
            ->get();

        // Key metrics
        $totalRevenue = Subscription::whereBetween('created_at', [$startDate, $endDate])->sum('amount_paid');
        $activeSubscriptions = Subscription::where('status', 'active')->count();
        $averageRevenuePerUser = $activeSubscriptions > 0 ? $totalRevenue / $activeSubscriptions : 0;
        $monthlyRecurringRevenue = Subscription::where('status', 'active')->sum('amount_paid');

        return view('super_admin.reports.revenue', compact(
            'revenueData',
            'revenueByPlan',
            'totalRevenue',
            'activeSubscriptions',
            'averageRevenuePerUser',
            'monthlyRecurringRevenue',
            'period',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Organizations report
     */
    public function organizations(Request $request)
    {
        $period = $request->get('period', 'monthly');
        $startDate = $request->get('start_date', now()->subMonths(12)->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        // Organization growth over time
        $organizationGrowth = $this->getOrganizationGrowth($period, $startDate, $endDate);

        // Organizations by plan
        $organizationsByPlan = Organization::select('plans.name', DB::raw('COUNT(*) as count'))
            ->leftJoin('plans', 'organizations.plan_id', '=', 'plans.id')
            ->groupBy('plans.id', 'plans.name')
            ->orderBy('count', 'desc')
            ->get();

        // Organizations by status
        $organizationsByStatus = Organization::select('is_active', DB::raw('COUNT(*) as count'))
            ->groupBy('is_active')
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->is_active ? 'Active' : 'Inactive' => $item->count];
            });

        // Trial conversion rate
        $totalTrials = Organization::whereNotNull('trial_ends_at')->count();
        $convertedTrials = Organization::whereNotNull('trial_ends_at')
            ->whereHas('subscriptions', function($query) {
                $query->where('status', 'active');
            })->count();
        $conversionRate = $totalTrials > 0 ? ($convertedTrials / $totalTrials) * 100 : 0;

        return view('super_admin.reports.organizations', compact(
            'organizationGrowth',
            'organizationsByPlan',
            'organizationsByStatus',
            'conversionRate',
            'totalTrials',
            'convertedTrials',
            'period',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Subscriptions report
     */
    public function subscriptions(Request $request)
    {
        $period = $request->get('period', 'monthly');
        $startDate = $request->get('start_date', now()->subMonths(12)->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        // Subscription status breakdown
        $subscriptionsByStatus = Subscription::select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status');

        // Churn rate calculation
        $churnData = $this->calculateChurnRate($period, $startDate, $endDate);

        // Subscription lifecycle
        $averageLifetime = Subscription::where('status', 'canceled')
            ->selectRaw('AVG(DATEDIFF(updated_at, created_at)) as avg_days')
            ->value('avg_days') ?? 0;

        // Upcoming renewals
        $upcomingRenewals = Subscription::where('status', 'active')
            ->where('end_date', '>=', now())
            ->where('end_date', '<=', now()->addDays(30))
            ->with(['organization', 'plan'])
            ->orderBy('end_date')
            ->get();

        return view('super_admin.reports.subscriptions', compact(
            'subscriptionsByStatus',
            'churnData',
            'averageLifetime',
            'upcomingRenewals',
            'period',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Export reports
     */
    public function export(Request $request, $type)
    {
        $format = $request->get('format', 'csv'); // csv, excel, pdf

        switch ($type) {
            case 'revenue':
                return $this->exportRevenue($format, $request);
            case 'organizations':
                return $this->exportOrganizations($format, $request);
            case 'subscriptions':
                return $this->exportSubscriptions($format, $request);
            default:
                return back()->with('error', 'Invalid report type.');
        }
    }

    /**
     * Get revenue data by period
     */
    private function getRevenueByPeriod($period, $startDate, $endDate)
    {
        $format = match($period) {
            'daily' => '%Y-%m-%d',
            'weekly' => '%Y-%u',
            'monthly' => '%Y-%m',
            'yearly' => '%Y',
            default => '%Y-%m'
        };

        return Subscription::select(
                DB::raw("DATE_FORMAT(created_at, '{$format}') as period"),
                DB::raw('SUM(amount_paid) as revenue'),
                DB::raw('COUNT(*) as subscriptions')
            )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('period')
            ->orderBy('period')
            ->get();
    }

    /**
     * Get organization growth data
     */
    private function getOrganizationGrowth($period, $startDate, $endDate)
    {
        $format = match($period) {
            'daily' => '%Y-%m-%d',
            'weekly' => '%Y-%u',
            'monthly' => '%Y-%m',
            'yearly' => '%Y',
            default => '%Y-%m'
        };

        return Organization::select(
                DB::raw("DATE_FORMAT(created_at, '{$format}') as period"),
                DB::raw('COUNT(*) as new_organizations')
            )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('period')
            ->orderBy('period')
            ->get();
    }

    /**
     * Calculate churn rate
     */
    private function calculateChurnRate($period, $startDate, $endDate)
    {
        $canceledSubscriptions = Subscription::where('status', 'canceled')
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->count();

        $totalSubscriptions = Subscription::whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $churnRate = $totalSubscriptions > 0 ? ($canceledSubscriptions / $totalSubscriptions) * 100 : 0;

        return [
            'canceled' => $canceledSubscriptions,
            'total' => $totalSubscriptions,
            'rate' => round($churnRate, 2)
        ];
    }

    /**
     * Export revenue data
     */
    private function exportRevenue($format, $request)
    {
        // Implementation for exporting revenue data
        // This would typically use a package like Laravel Excel
        return response()->json(['message' => 'Export functionality coming soon']);
    }

    /**
     * Export organizations data
     */
    private function exportOrganizations($format, $request)
    {
        // Implementation for exporting organizations data
        return response()->json(['message' => 'Export functionality coming soon']);
    }

    /**
     * Export subscriptions data
     */
    private function exportSubscriptions($format, $request)
    {
        // Implementation for exporting subscriptions data
        return response()->json(['message' => 'Export functionality coming soon']);
    }
}
