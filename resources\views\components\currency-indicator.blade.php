@props(['showLocation' => false, 'class' => ''])

@php
    $currency = user_currency();
    $symbol = user_currency_symbol();
    $locationInfo = location_info();
    $isNigerian = is_nigerian_user();
@endphp

<div class="currency-indicator {{ $class }}" title="Your detected currency">
    <span class="currency-symbol {{ currency_class() }}">{{ $symbol }}</span>
    <span class="currency-code">{{ $currency }}</span>
    
    @if($showLocation && !empty($locationInfo['country']))
        <small class="text-muted ms-1">
            ({{ $locationInfo['country'] }})
        </small>
    @endif
    
    @if(config('app.debug'))
        <small class="text-muted ms-1" title="Debug: IP {{ $locationInfo['ip'] ?? 'unknown' }}">
            <i class="fas fa-info-circle"></i>
        </small>
    @endif
</div>

@once
@push('styles')
<style>
.currency-indicator {
    display: inline-flex;
    align-items: center;
    font-size: 0.875rem;
}

.currency-symbol {
    font-weight: 600;
    margin-right: 2px;
}

.currency-ngn {
    color: #198754 !important;
}

.currency-usd {
    color: #0d6efd !important;
}

.currency-default {
    color: #6c757d !important;
}
</style>
@endpush
@endonce
