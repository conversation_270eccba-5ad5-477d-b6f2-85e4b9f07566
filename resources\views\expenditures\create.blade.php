@extends('layouts.app')

@section('title', 'Apply for Expenditure')

@section('content')
@if(auth()->user()->hasRole('Account'))
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 bg-white border-b border-gray-200">
                <h2 class="text-2xl font-bold mb-4">Apply for Expenditure</h2>

                <form action="{{ route('expenditures.store') }}" method="POST" class="space-y-6">
                    @csrf

                    <div>
                        <label for="heading" class="block text-sm font-medium text-gray-700">Expenditure Heading</label>
                        <input type="text" name="heading" id="heading"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            required>
                        @error('heading')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="amount" class="block text-sm font-medium text-gray-700">Amount</label>
                        <div class="mt-1 relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">{{ currency_symbol() }}</span>
                            </div>
                            <input type="number" name="amount" id="amount" step="0.01"
                                class="pl-7 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                required>
                        </div>
                        @error('amount')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Applicant</label>
                        <div class="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 px-3 py-2">
                            {{ auth()->user()->name }}
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit"
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Submit Application
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@else
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-center">
                <p class="text-gray-600">Only Account users can apply for expenditures.</p>
            </div>
        </div>
    </div>
</div>
@endif
@endsection
