<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Expenditure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;

class AccountController extends Controller
{
    public function __construct()
    {
        $this->middleware('role:Organization Owner|Manager|Account');
    }

    public function summary(Request $request)
    {
        // Get date range
        $startDate = $request->get('start_date') ? Carbon::parse($request->start_date)->startOfDay() : Carbon::now()->startOfMonth();
        $endDate = $request->get('end_date') ? Carbon::parse($request->end_date)->endOfDay() : Carbon::now()->endOfDay();

        // Base query for orders with organization filter
        $orderBaseQuery = Order::where('organization_id', Auth::user()->organization_id);

        // Filter by branch if user is assigned to a branch
        if (Auth::user()->branch_id) {
            $orderBaseQuery->where('branch_id', Auth::user()->branch_id);
        }

        // Get order statistics
        $orderStats = (clone $orderBaseQuery)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(
                DB::raw('COUNT(*) as total_orders'),
                DB::raw('SUM(total_amount) as total_revenue'),
                DB::raw('SUM(amount_paid) as total_collected'),
                DB::raw('SUM(pending_payment) as total_pending')
            )->first();

        // Check if organization_id and branch_id columns exist in the expenditures table
        $hasOrganizationId = Schema::hasColumn('expenditures', 'organization_id');
        $hasBranchId = Schema::hasColumn('expenditures', 'branch_id');

        // Base query for expenditures
        $expenditureBaseQuery = Expenditure::query();

        // Apply filters only if the columns exist
        if ($hasOrganizationId) {
            $expenditureBaseQuery->where('organization_id', Auth::user()->organization_id);
        }

        // Filter by branch if user is assigned to a branch and column exists
        if (Auth::user()->branch_id && $hasBranchId) {
            $expenditureBaseQuery->where('branch_id', Auth::user()->branch_id);
        }

        // Get expenditures
        $expenditures = (clone $expenditureBaseQuery)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'Approved')
            ->sum('amount');

        // Calculate net revenue
        $netRevenue = ($orderStats->total_collected ?? 0) - ($expenditures ?? 0);

        // Get payment history for the period
        $recentPayments = (clone $orderBaseQuery)
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->whereColumn('amount_paid', '>', DB::raw('0'))
            ->latest('updated_at')
            ->take(10)
            ->get();

        // Get pending payments
        $pendingPayments = (clone $orderBaseQuery)
            ->where('pending_payment', '>', 0)
            ->latest()
            ->take(10)
            ->get();

        return view('account.summary', compact(
            'orderStats',
            'expenditures',
            'netRevenue',
            'recentPayments',
            'pendingPayments',
            'startDate',
            'endDate'
        ));
    }
}
