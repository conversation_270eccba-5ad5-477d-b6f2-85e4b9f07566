@extends('layouts.app')

@section('title', $user->name . '\'s Profile')

@section('styles')
<style>
    .profile-header {
        @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white py-8 px-6 rounded-lg shadow-md mb-6;
    }
    .profile-avatar {
        @apply h-24 w-24 rounded-full border-4 border-white shadow-lg;
        object-fit: cover;
    }
    .profile-card {
        @apply bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-200;
        transition: all 0.3s ease;
    }
    .profile-card:hover {
        @apply shadow-md;
        transform: translateY(-2px);
    }
    .section-title {
        @apply text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200;
    }
    .tab-button {
        @apply py-3 px-4 border-b-2 font-medium text-sm focus:outline-none transition-colors duration-200;
    }
    .activity-item {
        @apply py-3 border-b border-gray-100 flex items-start;
    }
    .activity-icon {
        @apply mr-3 p-2 rounded-full;
    }
</style>
@endsection

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Back to users list button -->
        <div class="mb-4">
            <a href="{{ route('users.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Users List
            </a>
        </div>

        <!-- Profile Header -->
        <div class="profile-header">
            <div class="flex flex-col sm:flex-row items-center sm:items-start">
                <div class="flex-shrink-0 mb-4 sm:mb-0 sm:mr-6">
                    @if($user->profile_photo_path)
                        <img class="profile-avatar"
                             src="{{ asset('storage/' . $user->profile_photo_path) }}"
                             alt="{{ $user->name }}"
                             onerror="this.onerror=null; this.src='{{ asset('images/default-avatar.png') }}';">
                    @else
                        <div class="profile-avatar bg-gray-300 flex items-center justify-center text-gray-500">
                            <svg class="h-12 w-12" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                        </div>
                    @endif
                </div>
                <div class="text-center sm:text-left">
                    <h1 class="text-2xl font-bold">{{ $user->name }}</h1>
                    <p class="text-gray-900 font-medium">{{ $user->email }}</p>
                    <div class="mt-2">
                        @foreach($user->roles as $role)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1">
                                {{ $role->name }}
                            </span>
                        @endforeach
                    </div>
                    <p class="mt-2 text-sm">Member since {{ $user->created_at->format('F Y') }}</p>
                </div>
            </div>
        </div>

        <div x-data="{ activeTab: window.location.hash ? window.location.hash.substring(1) : 'info' }">
            <!-- Tab Navigation -->
            <div class="mb-6 bg-white rounded-lg shadow-sm">
                <div class="border-b border-gray-200">
                    <nav class="flex -mb-px overflow-x-auto">
                        <button @click="activeTab = 'info'; window.location.hash = 'info'"
                                :class="{ 'border-blue-500 text-blue-600': activeTab === 'info', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'info' }"
                                class="tab-button whitespace-nowrap border-b-2">
                            <svg class="w-5 h-5 inline-block mr-1 -mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            User Information
                        </button>
                        <button @click="activeTab = 'activity'; window.location.hash = 'activity'"
                                :class="{ 'border-blue-500 text-blue-600': activeTab === 'activity', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'activity' }"
                                class="tab-button whitespace-nowrap border-b-2">
                            <svg class="w-5 h-5 inline-block mr-1 -mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Activity
                        </button>
                    </nav>
                </div>
            </div>

            <!-- User Information -->
            <div x-show="activeTab === 'info'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                <div class="profile-card">
                    <h2 class="section-title">User Information</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Full Name</p>
                            <p class="mt-1 text-base">{{ $user->name }}</p>
                        </div>

                        <div>
                            <p class="text-sm font-medium text-gray-500">Email Address</p>
                            <p class="mt-1 text-base">{{ $user->email }}</p>
                        </div>

                        <div>
                            <p class="text-sm font-medium text-gray-500">Phone Number</p>
                            <p class="mt-1 text-base">{{ $user->phone ?? 'Not specified' }}</p>
                        </div>

                        <div>
                            <p class="text-sm font-medium text-gray-500">Position/Job Title</p>
                            <p class="mt-1 text-base">{{ $user->position ?? 'Not specified' }}</p>
                        </div>
                    </div>

                    <div class="mt-4">
                        <p class="text-sm font-medium text-gray-500">Bio</p>
                        <p class="mt-1 text-base">{{ $user->bio ?? 'No bio provided.' }}</p>
                    </div>
                </div>

                <div class="profile-card">
                    <h2 class="section-title">User Roles & Status</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Roles</p>
                            <div class="mt-2 flex flex-wrap gap-2">
                                @foreach($user->roles as $role)
                                    <span class="inline-flex items-center px-2.5 py-1.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $role->name }}
                                    </span>
                                @endforeach
                            </div>
                        </div>

                        <div>
                            <p class="text-sm font-medium text-gray-500">Status</p>
                            <div class="mt-2">
                                @if($user->status === 'active')
                                    <span class="inline-flex items-center px-2.5 py-1.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <svg class="mr-1.5 h-2 w-2 text-green-600" fill="currentColor" viewBox="0 0 8 8">
                                            <circle cx="4" cy="4" r="3" />
                                        </svg>
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-1.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <svg class="mr-1.5 h-2 w-2 text-yellow-600" fill="currentColor" viewBox="0 0 8 8">
                                            <circle cx="4" cy="4" r="3" />
                                        </svg>
                                        Inactive
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div>
                            <p class="text-sm font-medium text-gray-500">Branch</p>
                            <p class="mt-1 text-base">
                                @if($user->branch)
                                    {{ $user->branch->name }}
                                @else
                                    Not assigned to any branch
                                @endif
                            </p>
                        </div>

                        <div>
                            <p class="text-sm font-medium text-gray-500">Account Created</p>
                            <p class="mt-1 text-base">{{ $user->created_at->format('F j, Y') }}</p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end mt-6">
                    <a href="{{ route('users.edit', $user->id) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Edit User
                    </a>
                </div>
            </div>

            <!-- Account Activity -->
            <div x-show="activeTab === 'activity'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                <div class="profile-card">
                    <h2 class="section-title">Recent Activity</h2>

                    <div class="space-y-1">
                        @forelse($recentActivities ?? [] as $activity)
                        <div class="activity-item">
                            <div class="activity-icon
                                @if($activity->type == 'login') bg-blue-100
                                @elseif($activity->type == 'profile_update') bg-green-100
                                @elseif($activity->type == 'password_change') bg-yellow-100
                                @else bg-gray-100
                                @endif">
                                @if($activity->type == 'login')
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                                </svg>
                                @elseif($activity->type == 'profile_update')
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                @elseif($activity->type == 'password_change')
                                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                                </svg>
                                @else
                                <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                @endif
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">
                                    @if($activity->type == 'login')
                                        Logged in
                                    @elseif($activity->type == 'profile_update')
                                        Profile updated
                                    @elseif($activity->type == 'password_change')
                                        Password changed
                                    @else
                                        {{ ucfirst(str_replace('_', ' ', $activity->type)) }}
                                    @endif
                                </p>
                                <p class="text-xs text-gray-500">
                                    @if($activity->type == 'login')
                                        IP: {{ $activity->ip_address }} ·
                                        Browser: {{ Str::before(Str::after($activity->user_agent ?? '', ' '), '/') }} ·
                                    @endif
                                    {{ $activity->created_at->diffForHumans() }}
                                </p>
                            </div>
                        </div>
                        @empty
                        <div class="py-4 text-center text-gray-500">
                            <p>No recent activity recorded.</p>
                        </div>
                        @endforelse
                    </div>
                </div>

                <div class="profile-card">
                    <h2 class="section-title">Account Statistics</h2>

                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div class="p-4 border border-gray-200 rounded-lg text-center">
                            <p class="text-sm text-gray-600">Total Logins</p>
                            <p class="text-3xl font-bold text-gray-900 mt-2">{{ $loginCount ?? 0 }}</p>
                        </div>

                        <div class="p-4 border border-gray-200 rounded-lg text-center">
                            <p class="text-sm text-gray-600">Account Age</p>
                            <p class="text-3xl font-bold text-gray-900 mt-2">{{ now()->diffInDays($user->created_at) }} days</p>
                        </div>

                        <div class="p-4 border border-gray-200 rounded-lg text-center">
                            <p class="text-sm text-gray-600">Last Password Change</p>
                            <p class="text-3xl font-bold text-gray-900 mt-2">
                                @if($daysSincePasswordChange !== null)
                                    {{ $daysSincePasswordChange }} {{ Str::plural('day', $daysSincePasswordChange) }} ago
                                @else
                                    Never
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if there's a hash in the URL and set the tab accordingly
    const hash = window.location.hash.substring(1);
    if (hash && ['info', 'activity'].includes(hash)) {
        Alpine.store('tabState', {
            activeTab: hash
        });
    }
});
</script>
@endpush

@endsection
