<?php

require_once 'vendor/autoload.php';

// Test route generation
echo "Testing route generation:\n\n";

try {
    // Test organization.login route
    $orgLoginRoute = route('organization.login');
    echo "organization.login route: " . $orgLoginRoute . "\n";
    
    // Test other routes
    $superLoginRoute = route('super.login');
    echo "super.login route: " . $superLoginRoute . "\n";
    
    $affiliateLoginRoute = route('affiliate.login');
    echo "affiliate.login route: " . $affiliateLoginRoute . "\n";
    
    $registerRoute = route('register');
    echo "register route: " . $registerRoute . "\n";
    
    echo "\nAll routes generated successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
