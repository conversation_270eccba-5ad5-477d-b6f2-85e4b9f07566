<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscriptionStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string|null  $mode (strict|warning|info)
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, ?string $mode = 'warning'): Response
    {
        $user = Auth::user();

        if (!$user || !$user->organization) {
            return redirect()->route('login')->with('error', 'Please log in to access this feature.');
        }

        $organization = $user->organization;

        // Check if organization is active
        if (!$organization->is_active) {
            return redirect()->route('dashboard')->with('error', 'Your organization account is inactive. Please contact support.');
        }

        // Handle organizations without any plan
        if (!$organization->plan) {
            return $this->handleNoSubscription($request, $organization, $user, $mode);
        }

        // Handle expired subscriptions
        if (!$organization->hasAccess()) {
            return $this->handleExpiredSubscription($request, $organization, $user, $mode);
        }

        // Handle grace period
        if ($organization->isInGracePeriod()) {
            $this->addGracePeriodWarning($organization);
        }

        return $next($request);
    }

    /**
     * Handle organizations without any subscription plan.
     */
    private function handleNoSubscription(Request $request, $organization, $user, string $mode): Response
    {
        $isAdmin = $user->hasRole('organization_admin');

        switch ($mode) {
            case 'strict':
                return $this->redirectWithSubscriptionRequired($isAdmin);

            case 'info':
                $this->addSubscriptionInfo($isAdmin);
                break;

            case 'warning':
            default:
                $this->addSubscriptionWarning($isAdmin);
                break;
        }

        return redirect()->route('dashboard');
    }

    /**
     * Handle organizations with expired subscriptions.
     */
    private function handleExpiredSubscription(Request $request, $organization, $user, string $mode): Response
    {
        $isAdmin = $user->hasRole('organization_admin');

        if ($mode === 'strict') {
            return $this->redirectWithRenewalRequired($isAdmin);
        }

        return redirect()->route('dashboard')->with('error', 'Your subscription has expired. Please renew to continue using all features.');
    }

    /**
     * Add grace period warning to session.
     */
    private function addGracePeriodWarning($organization): void
    {
        $gracePeriodEnd = $organization->grace_period_end;
        $daysLeft = now()->diffInDays($gracePeriodEnd, false);

        session()->flash('warning', "Your subscription has expired. You have {$daysLeft} days remaining in your grace period. Please renew to avoid service interruption.");
    }

    /**
     * Add subscription warning for organizations without plans.
     */
    private function addSubscriptionWarning(bool $isAdmin): void
    {
        if ($isAdmin) {
            session()->flash('subscription_warning', [
                'title' => 'No Active Subscription Plan',
                'message' => 'Your organization doesn\'t have an active subscription plan. Choose a plan to unlock all features and start using the system effectively.',
                'action_text' => 'Contact Support for Plans',
                'action_url' => route('user.support.create'),
                'type' => 'admin'
            ]);
        } else {
            session()->flash('subscription_warning', [
                'title' => 'Subscription Required',
                'message' => 'Your organization needs an active subscription plan to access this feature. Please contact your organization administrator to set up a subscription.',
                'action_text' => 'Contact Admin',
                'action_url' => route('user.support.create'),
                'type' => 'user'
            ]);
        }
    }

    /**
     * Add subscription info for organizations without plans.
     */
    private function addSubscriptionInfo(bool $isAdmin): void
    {
        if ($isAdmin) {
            session()->flash('subscription_info', [
                'title' => 'Welcome! Let\'s Get You Started',
                'message' => 'To begin using all features, please contact support to set up a subscription plan that fits your organization\'s needs.',
                'action_text' => 'Contact Support',
                'action_url' => route('user.support.create'),
                'type' => 'admin'
            ]);
        } else {
            session()->flash('subscription_info', [
                'title' => 'Getting Started',
                'message' => 'Your organization is setting up. Some features may be limited until a subscription plan is activated.',
                'type' => 'user'
            ]);
        }
    }

    /**
     * Redirect with subscription required message.
     */
    private function redirectWithSubscriptionRequired(bool $isAdmin): Response
    {
        if ($isAdmin) {
            return redirect()->route('user.support.create')->with('error', 'A subscription plan is required to access this feature. Please contact support to set up a plan.');
        }

        return redirect()->route('dashboard')->with('error', 'This feature requires an active subscription. Please contact your organization administrator.');
    }

    /**
     * Redirect with renewal required message.
     */
    private function redirectWithRenewalRequired(bool $isAdmin): Response
    {
        if ($isAdmin) {
            return redirect()->route('user.support.create')->with('error', 'Your subscription has expired. Please contact support to renew.');
        }

        return redirect()->route('dashboard')->with('error', 'Your organization\'s subscription has expired. Please contact your administrator to renew.');
    }
}

