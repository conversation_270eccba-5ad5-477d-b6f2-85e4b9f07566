@extends('super_admin.layouts.app')

@section('title', 'Edit Exchange Rate')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Edit Exchange Rate</h1>
                <a href="{{ route('super_admin.currency.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Currency Management
                </a>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                Update Rate: {{ $fromCurrencyObj->code }} to {{ $toCurrencyObj->code }}
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('super_admin.currency.update-rate', [$fromCurrencyObj->code, $toCurrencyObj->code]) }}">
                                @csrf
                                @method('PUT')

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">From Currency</label>
                                        <div class="input-group">
                                            <span class="input-group-text">{{ $fromCurrencyObj->symbol }}</span>
                                            <input type="text" class="form-control" value="{{ $fromCurrencyObj->name }} ({{ $fromCurrencyObj->code }})" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">To Currency</label>
                                        <div class="input-group">
                                            <span class="input-group-text">{{ $toCurrencyObj->symbol }}</span>
                                            <input type="text" class="form-control" value="{{ $toCurrencyObj->name }} ({{ $toCurrencyObj->code }})" readonly>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="rate" class="form-label">Exchange Rate *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">1 {{ $fromCurrencyObj->code }} =</span>
                                        <input type="number" 
                                               class="form-control @error('rate') is-invalid @enderror" 
                                               id="rate" 
                                               name="rate" 
                                               value="{{ old('rate', $currentRate ? number_format($currentRate->rate, 6, '.', '') : '') }}" 
                                               step="0.000001" 
                                               min="0.000001" 
                                               max="999999.999999"
                                               required>
                                        <span class="input-group-text">{{ $toCurrencyObj->code }}</span>
                                    </div>
                                    @error('rate')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        Current rate: {{ $currentRate ? number_format($currentRate->rate, 6) : 'Not set' }}
                                        @if($currentRate)
                                            (Last updated: {{ $currentRate->updated_at->format('M d, Y H:i') }})
                                        @endif
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="reason" class="form-label">Reason for Change</label>
                                    <textarea class="form-control @error('reason') is-invalid @enderror" 
                                              id="reason" 
                                              name="reason" 
                                              rows="3" 
                                              placeholder="Optional: Explain why you're updating this rate">{{ old('reason') }}</textarea>
                                    @error('reason')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('super_admin.currency.index') }}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Update Exchange Rate
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Rate Calculator</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Amount in {{ $fromCurrencyObj->code }}</label>
                                <input type="number" class="form-control" id="fromAmount" value="1" min="0" step="0.01">
                            </div>
                            <div class="text-center mb-3">
                                <i class="fas fa-exchange-alt text-muted"></i>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Equivalent in {{ $toCurrencyObj->code }}</label>
                                <input type="text" class="form-control" id="toAmount" readonly>
                            </div>
                        </div>
                    </div>

                    @if($history->count() > 0)
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Recent Changes</h6>
                            </div>
                            <div class="card-body">
                                <div class="timeline">
                                    @foreach($history->take(5) as $change)
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-{{ $change->action === 'created' ? 'success' : 'warning' }}"></div>
                                            <div class="timeline-content">
                                                <h6 class="mb-1">{{ ucfirst($change->action) }}</h6>
                                                <p class="mb-1 small">
                                                    @if($change->old_rate)
                                                        {{ number_format($change->old_rate, 6) }} → 
                                                    @endif
                                                    {{ number_format($change->new_rate, 6) }}
                                                </p>
                                                <small class="text-muted">
                                                    {{ $change->created_at->format('M d, H:i') }} by {{ $change->changed_by_name }}
                                                </small>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                <a href="{{ route('super_admin.currency.rate-history', [$fromCurrencyObj->code, $toCurrencyObj->code]) }}" 
                                   class="btn btn-sm btn-outline-info w-100 mt-2">
                                    View Full History
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 20px;
}

.timeline-item {
    position: relative;
    margin-bottom: 15px;
}

.timeline-marker {
    position: absolute;
    left: -25px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -21px;
    top: 15px;
    width: 2px;
    height: calc(100% + 10px);
    background-color: #dee2e6;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const rateInput = document.getElementById('rate');
    const fromAmountInput = document.getElementById('fromAmount');
    const toAmountInput = document.getElementById('toAmount');

    function updateCalculation() {
        const rate = parseFloat(rateInput.value) || 0;
        const fromAmount = parseFloat(fromAmountInput.value) || 0;
        const toAmount = rate * fromAmount;
        
        toAmountInput.value = toAmount.toFixed(2);
    }

    rateInput.addEventListener('input', updateCalculation);
    fromAmountInput.addEventListener('input', updateCalculation);

    // Initial calculation
    updateCalculation();
});
</script>
@endpush
