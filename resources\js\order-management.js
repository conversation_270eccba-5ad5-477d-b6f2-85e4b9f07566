// Check user permissions before showing payment update controls
function initializeOrderControls() {
    const userRoles = window.userRoles || [];
    const canUpdateStatus = userRoles.some(role =>
        ['Organization Owner', 'Operator', 'Production'].includes(role)
    );
    const canUpdateDelivery = userRoles.some(role =>
        ['Organization Owner', 'Delivery'].includes(role)
    );
    const canUpdatePayment = userRoles.some(role =>
        ['Organization Owner', 'Account'].includes(role)
    );

    // Hide payment update buttons for unauthorized users
    if (!canUpdatePayment) {
        document.querySelectorAll('.payment-update-btn, .payment-update-section').forEach(btn => {
            btn.style.display = 'none';
        });
    }

    // Hide status update buttons for unauthorized users
    if (!canUpdateStatus) {
        document.querySelectorAll('.status-update-btn').forEach(btn => {
            btn.style.display = 'none';
        });
    }

    // Hide delivery update buttons for unauthorized users
    if (!canUpdateDelivery) {
        document.querySelectorAll('.delivery-update-btn').forEach(btn => {
            btn.style.display = 'none';
        });
    }
}

function showPaymentModal(orderId, pendingPayment) {
    const userRoles = window.userRoles || [];
    const canUpdate = userRoles.some(role =>
        ['Organization Owner', 'Account'].includes(role)
    );

    if (!canUpdate) {
        alert('You do not have permission to update order payments.');
        return;
    }

    // Proceed with payment modal
    const modal = document.getElementById('paymentModal');
    const form = document.getElementById('paymentForm');
    const input = document.getElementById('additional_payment');
    const remainingBalance = document.getElementById('remainingBalance');

    currentPendingPayment = pendingPayment;
    form.action = `/orders/${orderId}/update-payment`;
    input.max = pendingPayment;
    input.value = '';
    remainingBalance.textContent = pendingPayment.toLocaleString('en-NG', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}
