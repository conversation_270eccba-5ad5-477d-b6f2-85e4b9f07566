
public function updateStatus(User $user, Order $order)
{
    return $user->hasAnyRole(['Organization Owner', 'Operator', 'Production'])
        && $user->organization_id === $order->organization_id;
}

public function updateDelivery(User $user, Order $order)
{
    return $user->hasAnyRole(['Organization Owner', 'Delivery'])
        && $user->organization_id === $order->organization_id;
}

public function updatePayment(User $user, Order $order)
{
    return $user->hasAnyRole(['Organization Owner', 'Account'])
        && $user->organization_id === $order->organization_id;
}



