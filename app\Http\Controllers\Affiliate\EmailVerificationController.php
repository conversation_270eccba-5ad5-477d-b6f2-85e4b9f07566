<?php

namespace App\Http\Controllers\Affiliate;

use App\Http\Controllers\Controller;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Affiliate;

class EmailVerificationController extends Controller
{
    /**
     * Display the email verification prompt.
     */
    public function notice()
    {
        $user = Auth::guard('affiliate')->user();
        
        if ($user && $user->hasVerifiedEmail()) {
            return redirect()->route('affiliate.dashboard');
        }

        return view('affiliate.verify-email');
    }

    /**
     * Mark the authenticated user's email address as verified.
     */
    public function verify(Request $request, $id, $hash)
    {
        // Find the user by ID
        $user = User::findOrFail($id);

        // Verify the hash matches
        if (!hash_equals((string) $hash, sha1($user->getEmailForVerification()))) {
            abort(403, 'Invalid verification link.');
        }

        // Check if already verified
        if ($user->hasVerifiedEmail()) {
            // Log the user in if not already logged in
            if (!Auth::guard('affiliate')->check()) {
                Auth::guard('affiliate')->login($user);
            }
            
            return redirect()->route('affiliate.dashboard')
                           ->with('success', 'Your email is already verified!');
        }

        // Mark email as verified
        if ($user->markEmailAsVerified()) {
            event(new Verified($user));
        }

        // Log the user in using affiliate guard
        Auth::guard('affiliate')->login($user);

        // Check affiliate status and redirect accordingly
        $affiliate = Affiliate::where('user_id', $user->id)->first();
        
        if ($affiliate && $affiliate->status === Affiliate::STATUS_PENDING) {
            return redirect()->route('affiliate.pending')
                           ->with('success', 'Email verified successfully! Your affiliate account is pending approval.');
        }

        return redirect()->route('affiliate.dashboard')
                       ->with('success', 'Email verified successfully! Welcome to your affiliate dashboard.');
    }

    /**
     * Send a new email verification notification.
     */
    public function send(Request $request)
    {
        $user = Auth::guard('affiliate')->user();

        if (!$user) {
            return back()->withErrors(['error' => 'You must be logged in to resend verification email.']);
        }

        if ($user->hasVerifiedEmail()) {
            return back()->with('success', 'Your email is already verified!');
        }

        try {
            $user->sendEmailVerificationNotification();

            return back()->with('success', 'Verification email sent successfully! Please check your inbox and spam folder.');
        } catch (\Exception $e) {
            \Log::error('Failed to send verification email', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);

            return back()->withErrors(['error' => 'Failed to send verification email. Please try again or contact support.']);
        }
    }

    /**
     * Handle verification for users who aren't logged in.
     */
    public function verifyGuest(Request $request, $id, $hash)
    {
        // Find the user by ID
        $user = User::findOrFail($id);

        // Verify the hash matches
        if (!hash_equals((string) $hash, sha1($user->getEmailForVerification()))) {
            abort(403, 'Invalid verification link.');
        }

        // Check if already verified
        if ($user->hasVerifiedEmail()) {
            // Log the user in if not already logged in
            if (!Auth::guard('affiliate')->check()) {
                Auth::guard('affiliate')->login($user);
            }

            return redirect()->route('affiliate.dashboard')
                           ->with('success', 'Your email is already verified! Welcome to your affiliate dashboard.');
        }

        // Mark email as verified
        if ($user->markEmailAsVerified()) {
            event(new Verified($user));
        }

        // Log the user in using affiliate guard
        Auth::guard('affiliate')->login($user);

        // Check affiliate status and redirect accordingly
        $affiliate = Affiliate::where('user_id', $user->id)->first();

        if ($affiliate && $affiliate->status === Affiliate::STATUS_PENDING) {
            return redirect()->route('affiliate.pending')
                           ->with('success', 'Email verified successfully! Your affiliate account is pending approval.');
        }

        return redirect()->route('affiliate.dashboard')
                       ->with('success', 'Email verified successfully! Welcome to your affiliate dashboard.');
    }
}
