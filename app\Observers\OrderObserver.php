<?php

namespace App\Observers;

use App\Models\Order;
use App\Models\User;
use App\Models\OrderStatusHistory;
use App\Notifications\OrderStatusChanged;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;

class OrderObserver
{
    public function creating(Order $order)
    {
        // Set default status
        $order->status = 'Pending';

        // Generate unique order number if not set
        if (!$order->order_number) {
            $order->order_number = Order::generateOrderNumber();
        }

        // Ensure calculations are done
        if (!isset($order->total_amount)) {
            $order->total_amount = $order->quantity * $order->unit_cost;
        }

        if (!isset($order->pending_payment)) {
            $order->pending_payment = $order->total_amount - $order->amount_paid;
        }
    }

    public function created(Order $order)
    {
        // Create initial status history
        OrderStatusHistory::create([
            'order_id' => $order->id,
            'old_status' => '',
            'new_status' => $order->status,
            'changed_by' => auth()->user()->name ?? 'System',
            'notes' => 'Order created'
        ]);

        // Notify relevant users
        $adminsAndManagers = User::whereHas('roles', function($query) {
            $query->whereIn('name', ['Organization Owner', 'Manager']);
        })->get();

        if ($adminsAndManagers->isNotEmpty()) {
            Notification::send($adminsAndManagers, new OrderStatusChanged(
                $order,
                '',
                $order->status
            ));
        }
    }

    public function updating(Order $order)
    {
        if ($order->isDirty('status')) {
            $oldStatus = $order->getOriginal('status');
            $newStatus = $order->status;

            // Record status change
            OrderStatusHistory::create([
                'order_id' => $order->id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'changed_by' => auth()->user()->name ?? 'System',
                'notes' => "Status updated from {$oldStatus} to {$newStatus}"
            ]);

            // Notify relevant users
            $adminsAndManagers = User::whereHas('roles', function($query) {
                $query->whereIn('name', ['Organization Owner', 'Manager']);
            })->get();

            if ($adminsAndManagers->isNotEmpty()) {
                Notification::send($adminsAndManagers, new OrderStatusChanged(
                    $order,
                    $oldStatus,
                    $newStatus
                ));
            }
        }
    }
}
