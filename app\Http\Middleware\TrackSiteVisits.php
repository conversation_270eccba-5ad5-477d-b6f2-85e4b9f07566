<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\SiteVisit;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TrackSiteVisits
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip tracking for certain routes
        if ($this->shouldSkipTracking($request)) {
            return $next($request);
        }

        $this->trackVisit($request);

        return $next($request);
    }

    /**
     * Determine if tracking should be skipped for this request
     */
    protected function shouldSkipTracking(Request $request): bool
    {
        $skipRoutes = [
            'api/*',
            'ajax/*',
            '*.js',
            '*.css',
            '*.png',
            '*.jpg',
            '*.jpeg',
            '*.gif',
            '*.svg',
            '*.ico',
            '*.woff',
            '*.woff2',
            '*.ttf',
            '*.eot',
        ];

        $currentPath = $request->path();

        foreach ($skipRoutes as $pattern) {
            if (fnmatch($pattern, $currentPath)) {
                return true;
            }
        }

        // Skip AJAX requests
        if ($request->ajax() || $request->wantsJson()) {
            return true;
        }

        // Skip if user agent suggests it's a bot
        $userAgent = $request->userAgent();
        if ($this->isBot($userAgent)) {
            return true;
        }

        return false;
    }

    /**
     * Check if the user agent suggests it's a bot
     */
    protected function isBot($userAgent): bool
    {
        $botPatterns = [
            'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget',
            'googlebot', 'bingbot', 'slurp', 'duckduckbot',
            'baiduspider', 'yandexbot', 'facebookexternalhit'
        ];

        $userAgent = strtolower($userAgent);

        foreach ($botPatterns as $pattern) {
            if (strpos($userAgent, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Track the visit
     */
    protected function trackVisit(Request $request): void
    {
        try {
            $sessionId = $request->session()->getId();
            $userAgent = $request->userAgent();
            $ipAddress = $request->ip();
            $currentUrl = $request->fullUrl();
            $referrer = $request->header('referer');

            // Get user and organization info
            $userId = Auth::id();
            $organizationId = null;

            if ($userId) {
                $user = Auth::user();
                $organizationId = $user->organization_id;
            }

            // Find existing visit for this session
            $visit = SiteVisit::where('session_id', $sessionId)
                ->where('last_activity_at', '>', Carbon::now()->subHours(4)) // Consider session expired after 4 hours
                ->first();

            $now = Carbon::now();

            if ($visit) {
                // Update existing visit
                $durationSeconds = $now->diffInSeconds($visit->last_activity_at);
                
                $visit->update([
                    'user_id' => $userId, // Update in case user logged in during session
                    'organization_id' => $organizationId,
                    'current_page' => $currentUrl,
                    'page_views' => $visit->page_views + 1,
                    'last_activity_at' => $now,
                    'duration_seconds' => $visit->duration_seconds + $durationSeconds,
                    'is_bounce' => false, // No longer a bounce if they viewed multiple pages
                ]);
            } else {
                // Create new visit record
                SiteVisit::create([
                    'session_id' => $sessionId,
                    'user_id' => $userId,
                    'organization_id' => $organizationId,
                    'ip_address' => $ipAddress,
                    'user_agent' => $userAgent,
                    'device_type' => SiteVisit::getDeviceType($userAgent),
                    'browser' => SiteVisit::getBrowser($userAgent),
                    'platform' => SiteVisit::getPlatform($userAgent),
                    'referrer' => $referrer,
                    'landing_page' => $currentUrl,
                    'current_page' => $currentUrl,
                    'page_views' => 1,
                    'first_visit_at' => $now,
                    'last_activity_at' => $now,
                    'duration_seconds' => 0,
                    'is_bounce' => true, // Will be updated if they visit more pages
                ]);
            }
        } catch (\Exception $e) {
            // Log error but don't break the application
            \Log::error('Error tracking site visit: ' . $e->getMessage());
        }
    }
}
