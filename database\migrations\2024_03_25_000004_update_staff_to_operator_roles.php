<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Role;

return new class extends Migration
{
    public function up()
    {
        $operatorRole = Role::where('name', 'Operator')->first();
        $staffRole = Role::where('name', 'Staff')->first();

        if ($operatorRole && $staffRole) {
            // Get all users with Staff role
            $staffUsers = DB::table('role_user')
                ->where('role_id', $staffRole->id)
                ->get();

            foreach ($staffUsers as $user) {
                // Check if this user should be an operator instead
                // You'll need to manually update this list as needed
                if (in_array($user->user_id, [
                    // Add user IDs here who should be operators
                    // Example: 5, 8, 12
                ])) {
                    // Remove Staff role
                    DB::table('role_user')
                        ->where('user_id', $user->user_id)
                        ->where('role_id', $staffRole->id)
                        ->delete();

                    // Add Operator role
                    DB::table('role_user')->insert([
                        'user_id' => $user->user_id,
                        'role_id' => $operatorRole->id,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }
        }
    }

    public function down()
    {
        // No down migration needed as this is a one-time role adjustment
    }
};