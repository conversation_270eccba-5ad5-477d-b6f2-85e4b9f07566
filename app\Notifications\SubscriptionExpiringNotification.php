<?php

namespace App\Notifications;

use App\Models\Subscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SubscriptionExpiringNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $subscription;
    protected $daysRemaining;

    /**
     * Create a new notification instance.
     */
    public function __construct(Subscription $subscription, int $daysRemaining)
    {
        $this->subscription = $subscription;
        $this->daysRemaining = $daysRemaining;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $subject = $this->getSubject();
        $greeting = $this->getGreeting();
        $message = $this->getMessage();
        $actionText = $this->getActionText();
        $actionUrl = $this->getActionUrl();

        return (new MailMessage)
            ->subject($subject)
            ->greeting($greeting)
            ->line($message)
            ->line($this->getSubscriptionDetails())
            ->action($actionText, $actionUrl)
            ->line('Thank you for using our Sales Management System!')
            ->line('If you have any questions about your subscription, please contact our support team.');
    }

    /**
     * Get the subject line
     */
    private function getSubject(): string
    {
        $planName = $this->subscription->plan->name;
        
        if ($this->daysRemaining === 1) {
            return "Your {$planName} subscription expires tomorrow - Renew now!";
        } else {
            return "Your {$planName} subscription expires in {$this->daysRemaining} days";
        }
    }

    /**
     * Get the greeting
     */
    private function getGreeting(): string
    {
        return "Hello {$this->subscription->organization->name} team,";
    }

    /**
     * Get the main message
     */
    private function getMessage(): string
    {
        $planName = $this->subscription->plan->name;
        
        if ($this->daysRemaining === 1) {
            return "Your {$planName} subscription will expire tomorrow. To avoid any interruption to your business operations, please renew your subscription immediately.";
        } else {
            return "Your {$planName} subscription will expire in {$this->daysRemaining} days. To ensure uninterrupted access to your sales management system, please renew your subscription soon.";
        }
    }

    /**
     * Get subscription details
     */
    private function getSubscriptionDetails(): string
    {
        $planName = $this->subscription->plan->name;
        $price = number_format($this->subscription->plan->price, 2);
        $endDate = $this->subscription->end_date->format('F j, Y');
        
        return "Current Plan: {$planName} (\${$price}/month)\nExpires: {$endDate}";
    }

    /**
     * Get action text
     */
    private function getActionText(): string
    {
        if ($this->subscription->auto_renew) {
            return 'Update Payment Method';
        } else {
            return 'Renew Subscription';
        }
    }

    /**
     * Get action URL
     */
    private function getActionUrl(): string
    {
        // This should point to your subscription management page
        return url('/plan-change');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'subscription_id' => $this->subscription->id,
            'organization_id' => $this->subscription->organization_id,
            'organization_name' => $this->subscription->organization->name,
            'plan_name' => $this->subscription->plan->name,
            'days_remaining' => $this->daysRemaining,
            'end_date' => $this->subscription->end_date,
            'auto_renew' => $this->subscription->auto_renew,
            'type' => 'subscription_expiring'
        ];
    }
}
