<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;
use App\Rules\LastActiveAdmin;

class StoreUserRequest extends FormRequest
{
    public function authorize()
    {
        return $this->user()->hasRole('Organization Owner');
    }

    public function rules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => [
                'required',
                'confirmed',
                Password::min(8)
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised()
            ],
            'roles' => ['required', 'array', 'exists:roles,id'],
            'status' => ['required', 'in:active,inactive', new LastActiveAdmin()],
            'branch_id' => ['nullable', 'exists:branches,id'],
        ];
    }

    public function messages()
    {
        return [
            'password.uncompromised' => 'This password has been exposed in a data breach. Please choose a different password.',
            'roles.required' => 'Please assign at least one role to the user.',
            'status.in' => 'Status must be either active or inactive.',
        ];
    }

    protected function prepareForValidation()
    {
        // Ensure the first user is always created as active
        if (!\App\Models\User::exists()) {
            $this->merge(['status' => 'active']);
        }
    }
}
