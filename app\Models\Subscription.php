<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subscription extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'plan_id',
        'organization_id',
        'status',
        'billing_period_months',
        'start_date',
        'end_date',
        'trial_ends_at',
        'payment_method',
        'payment_reference',
        'payment_status',
        'last_payment_date',
        'next_payment_date',
        'amount_paid',
        'amount_due',
        'scheduled_plan_id',
        'scheduled_change_date',
        'cancellation_reason',
        'canceled_at',
        'auto_renew',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'trial_ends_at' => 'datetime',
        'last_payment_date' => 'datetime',
        'next_payment_date' => 'datetime',
        'scheduled_change_date' => 'datetime',
        'canceled_at' => 'datetime',
        'amount_paid' => 'decimal:2',
        'amount_due' => 'decimal:2',
        'auto_renew' => 'boolean',
    ];

    /**
     * Get the organization that owns the subscription.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the plan that the subscription is for.
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Get the scheduled plan for plan changes.
     */
    public function scheduledPlan(): BelongsTo
    {
        return $this->belongsTo(Plan::class, 'scheduled_plan_id');
    }

    /**
     * Determine if the subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Determine if the subscription is on trial.
     */
    public function onTrial(): bool
    {
        return $this->trial_ends_at && $this->trial_ends_at->isFuture();
    }

    /**
     * Determine if the subscription has expired.
     */
    public function hasExpired(): bool
    {
        return $this->end_date && $this->end_date->isPast();
    }

    /**
     * Get the payments for this subscription.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(SubscriptionPayment::class);
    }

    /**
     * Get the subscription period in months
     */
    public function getPeriodInMonths(): int
    {
        return $this->billing_period_months ?: 1;
    }

    /**
     * Check if subscription is annual billing (12 months)
     */
    public function isAnnual(): bool
    {
        return $this->getPeriodInMonths() === 12;
    }

    /**
     * Check if subscription is monthly billing (1 month)
     */
    public function isMonthly(): bool
    {
        return $this->getPeriodInMonths() === 1;
    }

    /**
     * Get billing period label
     */
    public function getBillingPeriodLabel(): string
    {
        $months = $this->getPeriodInMonths();

        switch ($months) {
            case 1:
                return 'Monthly';
            case 12:
                return 'Annual';
            case 24:
                return 'Bi-Annual';
            case 48:
                return 'Quadrennial';
            default:
                return $months . ' Months';
        }
    }

    /**
     * Get the next billing date based on billing period
     */
    public function calculateNextBillingDate(?\Carbon\Carbon $fromDate = null): \Carbon\Carbon
    {
        $fromDate = $fromDate ?: $this->end_date ?: now();
        $months = $this->getPeriodInMonths();

        return $fromDate->copy()->addMonths($months);
    }

    /**
     * Get the subscription amount for current billing period
     */
    public function getCurrentPeriodAmount(): float
    {
        if (!$this->plan) {
            return 0;
        }

        return $this->plan->getPriceForPeriod($this->getPeriodInMonths());
    }

    /**
     * Get approved payments for this subscription.
     */
    public function approvedPayments()
    {
        return $this->hasMany(SubscriptionPayment::class)->approved();
    }

    /**
     * Get pending payments for this subscription.
     */
    public function pendingPayments()
    {
        return $this->hasMany(SubscriptionPayment::class)->pending();
    }

    /**
     * Calculate total amount paid from approved payments.
     */
    public function calculateTotalPaid(): float
    {
        return $this->approvedPayments()->sum('amount');
    }

    /**
     * Check if subscription has pending payments.
     */
    public function hasPendingPayments(): bool
    {
        return $this->pendingPayments()->exists();
    }

    /**
     * Get the outstanding balance.
     */
    public function getOutstandingBalance(): float
    {
        return max(0, $this->amount_due - $this->calculateTotalPaid());
    }

    /**
     * Check if subscription is fully paid.
     */
    public function isFullyPaid(): bool
    {
        return $this->getOutstandingBalance() <= 0;
    }
}
