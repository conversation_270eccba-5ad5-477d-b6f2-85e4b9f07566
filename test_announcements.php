<?php

require_once 'vendor/autoload.php';

use App\Models\Announcement;
use App\Models\SuperAdmin;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Create a test announcement
try {
    // Get the first super admin
    $superAdmin = SuperAdmin::first();
    
    if (!$superAdmin) {
        echo "No super admin found. Please create one first.\n";
        exit(1);
    }
    
    // Create a test announcement
    $announcement = Announcement::create([
        'title' => 'Test Announcement for Organizations',
        'content' => 'This is a test announcement to check if announcements are working for organization users.',
        'type' => 'info',
        'priority' => 'normal',
        'target_audience' => 'organizations',
        'is_active' => true,
        'is_dismissible' => true,
        'show_on_login' => false,
        'show_on_dashboard' => true,
        'send_email' => false,
        'published_at' => now(),
        'created_by' => $superAdmin->id,
    ]);
    
    echo "Test announcement created with ID: " . $announcement->id . "\n";
    echo "Title: " . $announcement->title . "\n";
    echo "Target Audience: " . $announcement->target_audience . "\n";
    echo "Show on Dashboard: " . ($announcement->show_on_dashboard ? 'Yes' : 'No') . "\n";
    echo "Published: " . ($announcement->published_at ? 'Yes' : 'No') . "\n";
    
    // Test the query that the API uses
    echo "\n--- Testing API Query ---\n";
    
    $announcements = Announcement::active()
        ->published()
        ->current()
        ->forAudience('organizations')
        ->forDashboard()
        ->orderBy('priority', 'desc')
        ->orderBy('created_at', 'desc')
        ->get();
    
    echo "Found " . $announcements->count() . " announcements for organizations\n";
    
    foreach ($announcements as $ann) {
        echo "- " . $ann->title . " (ID: " . $ann->id . ")\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
