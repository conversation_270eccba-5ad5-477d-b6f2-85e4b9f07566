<!DOCTYPE html>
<html>
<head>
    <title>Affiliate System Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Affiliate System Test</h1>

        <div class="row">
            <div class="col-md-6">
                <h3>Test Links</h3>
                <ul class="list-group">
                    <li class="list-group-item">
                        <a href="{{ route('affiliate.program') }}" class="btn btn-primary">Program Info</a>
                    </li>
                    <li class="list-group-item">
                        <a href="{{ route('affiliate.register') }}" class="btn btn-success">Register</a>
                    </li>
                    <li class="list-group-item">
                        <a href="{{ route('affiliate.login') }}" class="btn btn-info">Login</a>
                    </li>
                </ul>
            </div>

            <div class="col-md-6">
                <h3>Quick Login Test</h3>
                <div class="card">
                    <div class="card-body">
                        <form action="{{ route('affiliate.login') }}" method="POST">
                            @csrf
                            <div class="mb-3">
                                <label class="form-label">Test Account</label>
                                <select class="form-select" id="testAccount" onchange="fillCredentials()">
                                    <option value="">Select a test account</option>
                                    <option value="<EMAIL>">Active Affiliate (John)</option>
                                    <option value="<EMAIL>">Pending Affiliate (Jane)</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email" id="email" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Password</label>
                                <input type="password" class="form-control" name="password" id="password" value="password" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Login to Affiliate Dashboard</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function fillCredentials() {
            const select = document.getElementById('testAccount');
            const email = document.getElementById('email');
            const password = document.getElementById('password');

            if (select.value) {
                email.value = select.value;
                password.value = 'password';
            } else {
                email.value = '';
                password.value = '';
            }
        }
    </script>
</body>
</html>
