<?php

namespace App\Services;

use App\Models\Organization;
use App\Models\User;

class SubscriptionStatusService
{
    /**
     * Get subscription status for an organization.
     */
    public static function getStatus(Organization $organization): array
    {
        if (!$organization->is_active) {
            return [
                'status' => 'inactive',
                'level' => 'critical',
                'title' => 'Account Inactive',
                'message' => 'Your organization account is inactive. Please contact support.',
                'action_required' => true,
                'can_access_features' => false,
            ];
        }

        if (!$organization->plan) {
            return [
                'status' => 'no_plan',
                'level' => 'warning',
                'title' => 'No Subscription Plan',
                'message' => 'Your organization doesn\'t have an active subscription plan.',
                'action_required' => true,
                'can_access_features' => false,
            ];
        }

        if (!$organization->hasAccess()) {
            return [
                'status' => 'expired',
                'level' => 'critical',
                'title' => 'Subscription Expired',
                'message' => 'Your subscription has expired and grace period has ended.',
                'action_required' => true,
                'can_access_features' => false,
            ];
        }

        if ($organization->isInGracePeriod()) {
            $daysLeft = now()->diffInDays($organization->grace_period_end, false);
            return [
                'status' => 'grace_period',
                'level' => 'warning',
                'title' => 'Grace Period Active',
                'message' => "Your subscription has expired. {$daysLeft} days remaining in grace period.",
                'action_required' => true,
                'can_access_features' => true,
                'days_left' => $daysLeft,
            ];
        }

        return [
            'status' => 'active',
            'level' => 'success',
            'title' => 'Active Subscription',
            'message' => 'Your subscription is active.',
            'action_required' => false,
            'can_access_features' => true,
        ];
    }

    /**
     * Get user-specific guidance based on subscription status.
     */
    public static function getUserGuidance(Organization $organization, User $user): array
    {
        $status = self::getStatus($organization);
        $isAdmin = $user->hasRole('organization_admin');

        $guidance = [
            'status' => $status,
            'user_role' => $isAdmin ? 'admin' : 'user',
            'actions' => [],
        ];

        switch ($status['status']) {
            case 'inactive':
                $guidance['actions'] = [
                    [
                        'text' => 'Contact Support',
                        'url' => route('user.support.create'),
                        'type' => 'primary',
                        'icon' => 'support'
                    ]
                ];
                break;

            case 'no_plan':
                if ($isAdmin) {
                    $guidance['actions'] = [
                        [
                            'text' => 'Choose a Plan',
                            'url' => route('billing.plans'),
                            'type' => 'primary',
                            'icon' => 'credit-card'
                        ],
                        [
                            'text' => 'Learn More',
                            'url' => '#', // Update this when pricing route exists
                            'type' => 'secondary',
                            'icon' => 'info'
                        ]
                    ];
                } else {
                    $guidance['actions'] = [
                        [
                            'text' => 'Contact Administrator',
                            'url' => route('user.support.create'),
                            'type' => 'primary',
                            'icon' => 'user'
                        ]
                    ];
                }
                break;

            case 'expired':
                if ($isAdmin) {
                    $guidance['actions'] = [
                        [
                            'text' => 'Renew Subscription',
                            'url' => route('billing.index'),
                            'type' => 'primary',
                            'icon' => 'refresh'
                        ]
                    ];
                } else {
                    $guidance['actions'] = [
                        [
                            'text' => 'Contact Administrator',
                            'url' => route('user.support.create'),
                            'type' => 'primary',
                            'icon' => 'user'
                        ]
                    ];
                }
                break;

            case 'grace_period':
                if ($isAdmin) {
                    $guidance['actions'] = [
                        [
                            'text' => 'Renew Now',
                            'url' => route('billing.index'),
                            'type' => 'primary',
                            'icon' => 'refresh'
                        ]
                    ];
                }
                break;
        }

        return $guidance;
    }

    /**
     * Check if specific features should be accessible.
     */
    public static function canAccessFeature(Organization $organization, string $feature): array
    {
        $status = self::getStatus($organization);

        // Define feature accessibility during different states
        $accessibleDuringGracePeriod = [
            'dashboard', 'profile', 'basic_reporting', 'user_management'
        ];

        $accessibleWithoutPlan = [
            'dashboard', 'profile', 'billing', 'support'
        ];

        $result = [
            'can_access' => false,
            'reason' => '',
            'suggested_action' => '',
        ];

        switch ($status['status']) {
            case 'active':
                $result['can_access'] = true;
                break;

            case 'grace_period':
                $result['can_access'] = in_array($feature, $accessibleDuringGracePeriod);
                if (!$result['can_access']) {
                    $result['reason'] = 'This feature is not available during the grace period.';
                    $result['suggested_action'] = 'Please renew your subscription to access all features.';
                }
                break;

            case 'no_plan':
                $result['can_access'] = in_array($feature, $accessibleWithoutPlan);
                if (!$result['can_access']) {
                    $result['reason'] = 'This feature requires an active subscription plan.';
                    $result['suggested_action'] = 'Please choose a subscription plan to access this feature.';
                }
                break;

            case 'expired':
            case 'inactive':
                $result['can_access'] = in_array($feature, ['dashboard', 'billing', 'support']);
                if (!$result['can_access']) {
                    $result['reason'] = 'This feature is not available with your current subscription status.';
                    $result['suggested_action'] = 'Please renew your subscription or contact support.';
                }
                break;
        }

        return $result;
    }
}
