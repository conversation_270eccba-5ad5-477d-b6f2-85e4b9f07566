@extends('super_admin.layouts.app')

@section('title', 'Knowledge Base Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Knowledge Base Management</h1>
                    <p class="text-muted mb-0">Manage help articles and documentation</p>
                </div>
                <div>
                    <a href="{{ route('super_admin.knowledge-base.categories.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-folder"></i> Manage Categories
                    </a>
                    <a href="{{ route('super_admin.knowledge-base.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Article
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['total'] }}</h4>
                                    <p class="mb-0">Total Articles</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-book fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['published'] }}</h4>
                                    <p class="mb-0">Published</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ $stats['draft'] }}</h4>
                                    <p class="mb-0">Drafts</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-edit fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">{{ number_format($stats['total_views']) }}</h4>
                                    <p class="mb-0">Total Views</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-eye fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('super_admin.knowledge-base.index') }}">
                        <div class="row">
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select name="status" id="status" class="form-control">
                                    <option value="">All Statuses</option>
                                    @foreach(\App\Models\KnowledgeBaseArticle::getStatusOptions() as $value => $label)
                                        <option value="{{ $value }}" {{ request('status') === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="category" class="form-label">Category</label>
                                <select name="category" id="category" class="form-control">
                                    <option value="">All Categories</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="visibility" class="form-label">Visibility</label>
                                <select name="visibility" id="visibility" class="form-control">
                                    <option value="">All Visibility</option>
                                    @foreach(\App\Models\KnowledgeBaseArticle::getVisibilityOptions() as $value => $label)
                                        <option value="{{ $value }}" {{ request('visibility') === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" name="search" id="search" class="form-control"
                                       placeholder="Search articles..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Actions -->
            <div class="card mb-4" id="bulkActionsCard" style="display: none;">
                <div class="card-body">
                    <form method="POST" action="{{ route('super_admin.knowledge-base.bulk-action') }}" id="bulkActionForm">
                        @csrf
                        <div class="row align-items-end">
                            <div class="col-md-3">
                                <label for="bulkAction" class="form-label">Bulk Action</label>
                                <select name="action" id="bulkAction" class="form-control" required>
                                    <option value="">Select Action</option>
                                    <option value="publish">Publish</option>
                                    <option value="unpublish">Unpublish</option>
                                    <option value="archive">Archive</option>
                                    <option value="feature">Feature</option>
                                    <option value="unfeature">Unfeature</option>
                                    <option value="delete" class="text-danger">Delete</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-bolt"></i> Apply to <span id="selectedCount">0</span> articles
                                </button>
                            </div>
                            <div class="col-md-6 text-end">
                                <button type="button" class="btn btn-outline-secondary" onclick="clearSelection()">
                                    <i class="fas fa-times"></i> Clear Selection
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Articles Table -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            Knowledge Base Articles
                        </h5>
                        <div>
                            <a href="{{ route('super_admin.knowledge-base.analytics') }}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-chart-bar"></i> Analytics
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if($articles->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="30">
                                            <input type="checkbox" id="selectAll" class="form-check-input">
                                        </th>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Visibility</th>
                                        <th>Views</th>
                                        <th>Helpfulness</th>
                                        <th>Author</th>
                                        <th>Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($articles as $article)
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="articles[]" value="{{ $article->id }}"
                                                   class="form-check-input article-checkbox">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($article->featured)
                                                    <i class="fas fa-star text-warning me-2" title="Featured"></i>
                                                @endif
                                                <div>
                                                    <a href="{{ route('super_admin.knowledge-base.show', $article) }}"
                                                       class="text-decoration-none fw-bold">
                                                        {{ $article->title }}
                                                    </a>
                                                    @if($article->excerpt)
                                                        <div class="text-muted small">{{ Str::limit($article->excerpt, 80) }}</div>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ $article->category->name ?? 'Uncategorized' }}</span>
                                        </td>
                                        <td>{!! $article->status_badge !!}</td>
                                        <td>{!! $article->visibility_badge !!}</td>
                                        <td>
                                            <span class="badge bg-info">{{ number_format($article->view_count) }}</span>
                                        </td>
                                        <td>
                                            @if($article->helpfulness_percentage)
                                                <span class="badge bg-success">{{ $article->helpfulness_percentage }}%</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2"
                                                     style="width: 30px; height: 30px; font-size: 12px;">
                                                    {{ substr($article->author->name ?? 'A', 0, 1) }}
                                                </div>
                                                <small>{{ $article->author->name ?? 'Unknown' }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>{{ $article->updated_at->format('M d, Y') }}</div>
                                            <small class="text-muted">{{ $article->updated_at->diffForHumans() }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('super_admin.knowledge-base.show', $article) }}"
                                                   class="btn btn-outline-primary btn-sm" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('super_admin.knowledge-base.edit', $article) }}"
                                                   class="btn btn-outline-secondary btn-sm" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form method="POST" action="{{ route('super_admin.knowledge-base.destroy', $article) }}"
                                                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this article?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-outline-danger btn-sm" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if($articles->hasPages())
                            <div class="d-flex justify-content-center mt-4">
                                {{ $articles->links() }}
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-book fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Articles Found</h4>
                            <p class="text-muted">
                                @if(request()->hasAny(['status', 'category', 'visibility', 'search']))
                                    No articles match your current filters.
                                @else
                                    Start building your knowledge base by creating your first article.
                                @endif
                            </p>
                            <div class="mt-4">
                                @if(request()->hasAny(['status', 'category', 'visibility', 'search']))
                                    <a href="{{ route('super_admin.knowledge-base.index') }}" class="btn btn-outline-secondary me-2">
                                        <i class="fas fa-times"></i> Clear Filters
                                    </a>
                                @endif
                                <a href="{{ route('super_admin.knowledge-base.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Create First Article
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('selectAll');
    const articleCheckboxes = document.querySelectorAll('.article-checkbox');
    const bulkActionsCard = document.getElementById('bulkActionsCard');
    const selectedCount = document.getElementById('selectedCount');
    const bulkActionForm = document.getElementById('bulkActionForm');

    // Select all functionality
    selectAll.addEventListener('change', function() {
        articleCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    // Individual checkbox functionality
    articleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    function updateBulkActions() {
        const selected = document.querySelectorAll('.article-checkbox:checked');
        const count = selected.length;

        if (count > 0) {
            bulkActionsCard.style.display = 'block';
            selectedCount.textContent = count;

            // Add selected article IDs to form
            const existingInputs = bulkActionForm.querySelectorAll('input[name="articles[]"]');
            existingInputs.forEach(input => input.remove());

            selected.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'articles[]';
                input.value = checkbox.value;
                bulkActionForm.appendChild(input);
            });
        } else {
            bulkActionsCard.style.display = 'none';
        }

        // Update select all checkbox
        selectAll.checked = count === articleCheckboxes.length;
        selectAll.indeterminate = count > 0 && count < articleCheckboxes.length;
    }

    // Bulk action form submission
    bulkActionForm.addEventListener('submit', function(e) {
        const action = document.getElementById('bulkAction').value;
        const count = document.querySelectorAll('.article-checkbox:checked').length;

        if (!action) {
            e.preventDefault();
            alert('Please select an action.');
            return;
        }

        if (action === 'delete') {
            if (!confirm(`Are you sure you want to delete ${count} articles? This action cannot be undone.`)) {
                e.preventDefault();
                return;
            }
        } else {
            if (!confirm(`Are you sure you want to ${action} ${count} articles?`)) {
                e.preventDefault();
                return;
            }
        }
    });
});

function clearSelection() {
    document.querySelectorAll('.article-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('selectAll').checked = false;
    document.getElementById('bulkActionsCard').style.display = 'none';
}
</script>
@endsection
