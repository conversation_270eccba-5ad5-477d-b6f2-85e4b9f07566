<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\KnowledgeBaseCategory;
use App\Models\KnowledgeBaseArticle;
use App\Models\SuperAdmin;

class KnowledgeBaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first super admin as the author
        $superAdmin = SuperAdmin::first();
        
        if (!$superAdmin) {
            $this->command->warn('No super admin found. Please create a super admin first.');
            return;
        }

        // Create categories
        $categories = [
            [
                'name' => 'Getting Started',
                'slug' => 'getting-started',
                'description' => 'Essential guides to help you get started with the system',
                'icon' => 'fas fa-rocket',
                'color' => '#28a745',
                'sort_order' => 1,
            ],
            [
                'name' => 'Account Management',
                'slug' => 'account-management',
                'description' => 'Managing your account, profile, and organization settings',
                'icon' => 'fas fa-user-cog',
                'color' => '#007bff',
                'sort_order' => 2,
            ],
            [
                'name' => 'Billing & Subscriptions',
                'slug' => 'billing-subscriptions',
                'description' => 'Information about billing, payments, and subscription plans',
                'icon' => 'fas fa-credit-card',
                'color' => '#ffc107',
                'sort_order' => 3,
            ],
            [
                'name' => 'Troubleshooting',
                'slug' => 'troubleshooting',
                'description' => 'Common issues and their solutions',
                'icon' => 'fas fa-tools',
                'color' => '#dc3545',
                'sort_order' => 4,
            ],
            [
                'name' => 'FAQ',
                'slug' => 'faq',
                'description' => 'Frequently asked questions and answers',
                'icon' => 'fas fa-question-circle',
                'color' => '#6f42c1',
                'sort_order' => 5,
            ],
        ];

        foreach ($categories as $categoryData) {
            $category = KnowledgeBaseCategory::create($categoryData);

            // Create sample articles for each category
            $this->createArticlesForCategory($category, $superAdmin);
        }

        $this->command->info('Knowledge base seeded successfully!');
    }

    /**
     * Create sample articles for a category
     */
    private function createArticlesForCategory($category, $superAdmin)
    {
        $articles = [];

        switch ($category->slug) {
            case 'getting-started':
                $articles = [
                    [
                        'title' => 'Welcome to Sales Management System',
                        'content' => $this->getWelcomeContent(),
                        'excerpt' => 'Get started with our comprehensive sales management platform',
                        'featured' => true,
                    ],
                    [
                        'title' => 'Setting Up Your Organization',
                        'content' => $this->getOrganizationSetupContent(),
                        'excerpt' => 'Learn how to configure your organization settings and add team members',
                    ],
                    [
                        'title' => 'Creating Your First Order',
                        'content' => $this->getFirstOrderContent(),
                        'excerpt' => 'Step-by-step guide to creating and managing orders',
                    ],
                ];
                break;

            case 'account-management':
                $articles = [
                    [
                        'title' => 'Managing Your Profile',
                        'content' => $this->getProfileManagementContent(),
                        'excerpt' => 'Update your personal information and preferences',
                    ],
                    [
                        'title' => 'User Roles and Permissions',
                        'content' => $this->getUserRolesContent(),
                        'excerpt' => 'Understanding different user roles and their capabilities',
                    ],
                ];
                break;

            case 'billing-subscriptions':
                $articles = [
                    [
                        'title' => 'Understanding Subscription Plans',
                        'content' => $this->getSubscriptionPlansContent(),
                        'excerpt' => 'Compare features and choose the right plan for your organization',
                        'featured' => true,
                    ],
                    [
                        'title' => 'Payment Methods and Billing',
                        'content' => $this->getPaymentMethodsContent(),
                        'excerpt' => 'Managing payment methods and understanding billing cycles',
                    ],
                ];
                break;

            case 'troubleshooting':
                $articles = [
                    [
                        'title' => 'Common Login Issues',
                        'content' => $this->getLoginIssuesContent(),
                        'excerpt' => 'Resolve common problems with logging into your account',
                    ],
                    [
                        'title' => 'Browser Compatibility',
                        'content' => $this->getBrowserCompatibilityContent(),
                        'excerpt' => 'Ensure optimal performance with supported browsers',
                    ],
                ];
                break;

            case 'faq':
                $articles = [
                    [
                        'title' => 'Frequently Asked Questions',
                        'content' => $this->getFAQContent(),
                        'excerpt' => 'Quick answers to the most common questions',
                        'featured' => true,
                    ],
                ];
                break;
        }

        foreach ($articles as $articleData) {
            KnowledgeBaseArticle::create([
                'title' => $articleData['title'],
                'slug' => \Illuminate\Support\Str::slug($articleData['title']),
                'content' => $articleData['content'],
                'excerpt' => $articleData['excerpt'],
                'category_id' => $category->id,
                'author_id' => $superAdmin->id,
                'status' => 'published',
                'visibility' => 'public',
                'featured' => $articleData['featured'] ?? false,
                'tags' => ['guide', 'help', $category->slug],
                'published_at' => now(),
            ]);
        }
    }

    private function getWelcomeContent()
    {
        return "# Welcome to Sales Management System

Thank you for choosing our comprehensive sales management platform! This guide will help you get started quickly and efficiently.

## What You Can Do

Our platform provides everything you need to manage your sales operations:

- **Order Management**: Create, track, and manage customer orders
- **Team Collaboration**: Work together with your team members
- **Analytics & Reporting**: Get insights into your sales performance
- **Customer Management**: Maintain detailed customer records

## Getting Started Steps

1. **Complete Your Profile**: Update your personal information
2. **Set Up Your Organization**: Configure your company settings
3. **Invite Team Members**: Add your colleagues to collaborate
4. **Create Your First Order**: Start managing your sales pipeline

## Need Help?

If you have any questions, our support team is here to help:
- Browse our knowledge base for detailed guides
- Contact support through the help desk
- Join our community forums for tips and best practices

Welcome aboard!";
    }

    private function getOrganizationSetupContent()
    {
        return "# Setting Up Your Organization

Properly configuring your organization is crucial for getting the most out of the Sales Management System.

## Organization Information

1. **Company Details**
   - Update your company name and description
   - Add your business address and contact information
   - Upload your company logo

2. **Business Settings**
   - Configure your timezone
   - Set your preferred currency
   - Define your business hours

## Adding Team Members

1. **Invite Users**
   - Go to Organization Settings > Users
   - Click 'Invite User'
   - Enter their email address and select their role

2. **User Roles**
   - **Organization Owner**: Full administrative access
   - **Manager**: Can manage orders and view reports
   - **User**: Can create and manage their own orders

## Branch Management

If your organization has multiple locations:

1. Create branches for each location
2. Assign users to specific branches
3. Configure branch-specific settings

## Best Practices

- Regularly review user permissions
- Keep organization information up to date
- Use descriptive branch names for easy identification";
    }

    private function getFirstOrderContent()
    {
        return "# Creating Your First Order

Learn how to create and manage orders in the Sales Management System.

## Step-by-Step Guide

### 1. Navigate to Orders
- Click on 'Orders' in the main navigation
- Select 'Create New Order'

### 2. Customer Information
- Search for existing customers or create a new one
- Fill in customer contact details
- Add delivery address if different from billing

### 3. Order Details
- Add products or services to the order
- Specify quantities and pricing
- Apply any discounts or special terms

### 4. Review and Submit
- Double-check all information
- Add any special notes or instructions
- Submit the order for processing

## Order Status Workflow

Orders progress through these stages:
1. **Draft**: Order is being created
2. **Pending**: Awaiting approval or payment
3. **Confirmed**: Order is confirmed and being processed
4. **Shipped**: Order has been dispatched
5. **Delivered**: Order has been completed
6. **Cancelled**: Order was cancelled

## Tips for Success

- Always verify customer information before submitting
- Use clear, descriptive product names
- Include delivery dates when known
- Add internal notes for your team";
    }

    private function getProfileManagementContent()
    {
        return "# Managing Your Profile

Keep your profile information current to ensure smooth communication and system functionality.

## Personal Information

Update your basic details:
- Full name and display name
- Email address (used for notifications)
- Phone number
- Profile photo

## Security Settings

Protect your account:
- Change your password regularly
- Enable two-factor authentication (if available)
- Review login activity

## Notification Preferences

Control what notifications you receive:
- Email notifications for orders
- System alerts and updates
- Team collaboration messages

## Privacy Settings

Manage your privacy:
- Control who can see your profile
- Set availability status
- Manage contact preferences";
    }

    private function getUserRolesContent()
    {
        return "# User Roles and Permissions

Understanding user roles helps you manage team access effectively.

## Available Roles

### Organization Owner
- Full system access
- Can manage all users and settings
- Access to billing and subscription management
- Can delete the organization

### Manager
- Can manage orders and customers
- Access to reports and analytics
- Can invite and manage users (except owners)
- Cannot access billing information

### User
- Can create and manage their own orders
- Limited access to customer information
- Cannot manage other users
- Basic reporting access

## Permission Matrix

| Feature | Owner | Manager | User |
|---------|-------|---------|------|
| Create Orders | ✓ | ✓ | ✓ |
| Manage All Orders | ✓ | ✓ | ✗ |
| User Management | ✓ | ✓ | ✗ |
| Billing Access | ✓ | ✗ | ✗ |
| System Settings | ✓ | Limited | ✗ |

## Best Practices

- Assign the minimum necessary permissions
- Regularly review user roles
- Use Manager role for team leads
- Keep Owner access limited to key personnel";
    }

    private function getSubscriptionPlansContent()
    {
        return "# Understanding Subscription Plans

Choose the right plan for your organization's needs.

## Available Plans

### Starter Plan
- Up to 5 users
- 1 branch location
- Basic order management
- Email support
- 30 days data retention

### Professional Plan
- Up to 25 users
- 5 branch locations
- Advanced reporting
- Priority support
- 90 days data retention
- API access

### Enterprise Plan
- Unlimited users
- Unlimited branches
- Custom integrations
- Dedicated support
- Unlimited data retention
- Advanced security features

## Choosing the Right Plan

Consider these factors:
- Number of team members
- Number of business locations
- Required features and integrations
- Support level needed
- Data retention requirements

## Upgrading Your Plan

You can upgrade at any time:
1. Go to Billing Settings
2. Select your new plan
3. Complete payment
4. Changes take effect immediately

## Billing Cycles

- Monthly billing: Charged every month
- Annual billing: Charged yearly with discount
- Pro-rated charges for mid-cycle upgrades";
    }

    private function getPaymentMethodsContent()
    {
        return "# Payment Methods and Billing

Manage your payment information and understand billing processes.

## Accepted Payment Methods

- Credit cards (Visa, MasterCard, American Express)
- Debit cards
- Bank transfers (for annual plans)
- PayPal (where available)

## Adding Payment Methods

1. Go to Billing Settings
2. Click 'Add Payment Method'
3. Enter your payment information
4. Set as primary if desired

## Billing Process

- Automatic billing on your renewal date
- Email notifications before charges
- Receipts sent after successful payment
- Grace period for failed payments

## Managing Invoices

- View all invoices in Billing History
- Download PDF copies
- Set up automatic invoice forwarding
- Request custom billing arrangements

## Failed Payments

If a payment fails:
1. You'll receive an email notification
2. Service continues for a grace period
3. Update your payment method
4. Contact support if issues persist";
    }

    private function getLoginIssuesContent()
    {
        return "# Common Login Issues

Resolve login problems quickly with these troubleshooting steps.

## Forgot Password

1. Click 'Forgot Password' on the login page
2. Enter your email address
3. Check your email for reset instructions
4. Follow the link to create a new password

## Account Locked

If your account is locked:
- Wait 15 minutes and try again
- Contact support if still locked
- Ensure you're using the correct credentials

## Browser Issues

Clear browser data:
1. Clear cookies and cache
2. Disable browser extensions
3. Try incognito/private mode
4. Update your browser

## Two-Factor Authentication Problems

- Ensure your device time is correct
- Try generating a new code
- Use backup codes if available
- Contact support to reset 2FA

## Still Having Issues?

Contact our support team:
- Email: <EMAIL>
- Live chat during business hours
- Submit a support ticket";
    }

    private function getBrowserCompatibilityContent()
    {
        return "# Browser Compatibility

Ensure optimal performance with supported browsers and settings.

## Supported Browsers

### Fully Supported
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Limited Support
- Internet Explorer 11 (basic functionality only)
- Older browser versions

## Recommended Settings

- Enable JavaScript
- Allow cookies
- Disable ad blockers for our domain
- Enable local storage

## Performance Tips

- Keep your browser updated
- Close unnecessary tabs
- Clear cache regularly
- Disable unused extensions

## Mobile Browsers

Our system works on mobile devices:
- iOS Safari 14+
- Chrome Mobile 90+
- Samsung Internet 14+

## Troubleshooting

If you experience issues:
1. Update your browser
2. Clear cache and cookies
3. Disable extensions temporarily
4. Try a different browser";
    }

    private function getFAQContent()
    {
        return "# Frequently Asked Questions

Quick answers to common questions about the Sales Management System.

## General Questions

**Q: How do I get started?**
A: Begin by completing your profile, setting up your organization, and creating your first order. Check our Getting Started guide for detailed steps.

**Q: Can I invite team members?**
A: Yes! Organization Owners and Managers can invite team members. Go to Organization Settings > Users to send invitations.

**Q: Is my data secure?**
A: Absolutely. We use industry-standard encryption and security measures to protect your data.

## Billing Questions

**Q: Can I change my plan anytime?**
A: Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.

**Q: Do you offer refunds?**
A: We offer refunds within 30 days of purchase. Contact support for assistance.

**Q: What payment methods do you accept?**
A: We accept major credit cards, debit cards, and PayPal.

## Technical Questions

**Q: Which browsers are supported?**
A: We support Chrome, Firefox, Safari, and Edge. See our Browser Compatibility guide for details.

**Q: Can I export my data?**
A: Yes, you can export your data in various formats. Contact support for assistance with large exports.

**Q: Is there an API available?**
A: API access is available with Professional and Enterprise plans.

## Support Questions

**Q: How can I contact support?**
A: You can reach us via email, live chat, or by submitting a support ticket through the help desk.

**Q: What are your support hours?**
A: Standard support is available 24/7. Priority support (Professional+ plans) includes faster response times.

**Q: Do you offer training?**
A: Yes, we provide training resources and can arrange custom training sessions for Enterprise customers.";
    }
}
