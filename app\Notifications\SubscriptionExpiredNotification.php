<?php

namespace App\Notifications;

use App\Models\Subscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SubscriptionExpiredNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $subscription;

    /**
     * Create a new notification instance.
     */
    public function __construct(Subscription $subscription)
    {
        $this->subscription = $subscription;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $planName = $this->subscription->plan->name;
        $organizationName = $this->subscription->organization->name;
        $expiredDate = $this->subscription->end_date->format('F j, Y');

        return (new MailMessage)
            ->subject("Your {$planName} subscription has expired - Immediate action required")
            ->greeting("Hello {$organizationName} team,")
            ->line("Your {$planName} subscription expired on {$expiredDate}.")
            ->line('Your account is now in a limited state. To restore full access to your sales management system, please renew your subscription immediately.')
            ->line($this->getGracePeriodInfo())
            ->action('Renew Subscription Now', $this->getActionUrl())
            ->line('**What happens during the grace period:**')
            ->line('• Limited access to core features')
            ->line('• No new data entry allowed')
            ->line('• Read-only access to existing data')
            ->line('• No customer support')
            ->line('')
            ->line('**After the grace period:**')
            ->line('• Complete loss of access')
            ->line('• Data may be archived or deleted')
            ->line('• Account suspension')
            ->line('')
            ->line('Don\'t lose your valuable business data - renew now!')
            ->line('If you have any questions, please contact our support team immediately.');
    }

    /**
     * Get grace period information
     */
    private function getGracePeriodInfo(): string
    {
        $gracePeriodEnd = $this->subscription->end_date->addDays(7); // 7-day grace period
        $daysLeft = now()->diffInDays($gracePeriodEnd, false);
        
        if ($daysLeft > 0) {
            return "You have {$daysLeft} days remaining in your grace period (until {$gracePeriodEnd->format('F j, Y')}).";
        } else {
            return "Your grace period has ended. Your account may be suspended at any time.";
        }
    }

    /**
     * Get action URL
     */
    private function getActionUrl(): string
    {
        return url('/plan-change');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'subscription_id' => $this->subscription->id,
            'organization_id' => $this->subscription->organization_id,
            'organization_name' => $this->subscription->organization->name,
            'plan_name' => $this->subscription->plan->name,
            'expired_date' => $this->subscription->end_date,
            'grace_period_end' => $this->subscription->end_date->addDays(7),
            'type' => 'subscription_expired'
        ];
    }
}
