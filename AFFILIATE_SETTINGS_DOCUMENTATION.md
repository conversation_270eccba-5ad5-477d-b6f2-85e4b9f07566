# Affiliate Settings System Documentation

## Overview
The Affiliate Settings System provides Super Admins with comprehensive control over the affiliate program, including system-wide commission rate management and all program configuration options.

## Access Information
- **URL**: `http://localhost/SalesManagementSystem/super-admin/affiliate-settings`
- **Access Level**: Super Admin only
- **Navigation**: Super Admin Dashboard → Affiliate Settings (in sidebar)

## Key Features

### 1. System-Wide Commission Rate Management
- **Global Rate Control**: Adjust the default commission rate for all affiliates
- **Selective Application**: Choose to apply changes to existing affiliates or only new ones
- **Impact Analysis**: See how many affiliates will be affected before making changes
- **Safety Confirmation**: Modal confirmation for commission rate changes
- **Individual Protection**: Manually set affiliate rates are preserved

### 2. Comprehensive Program Settings

#### Commission Settings
- Default commission rate (0-100%)
- Option to apply rate changes to existing affiliates
- Recurring commissions toggle
- Commission tier validation

#### Withdrawal Settings
- Minimum withdrawal amount
- Withdrawal fee percentage (0-100%)
- Fixed withdrawal fee amount
- Payment method configuration

#### Program Controls
- Enable/disable affiliate program
- Auto-approve new affiliates
- Auto-approve earnings
- Cookie duration (1-365 days)
- Maximum referrals per affiliate (optional)

#### Communication Settings
- Welcome message for new affiliates
- Terms and conditions text
- Custom program messaging

### 3. Real-Time Statistics Dashboard
- Total affiliates count
- Active affiliates count
- Total referrals and conversions
- Total and pending earnings
- Total and pending withdrawals
- Affiliates using current default rate

## Technical Implementation

### Files Modified/Created
```
routes/web.php                                          # Added affiliate settings routes
app/Http/Controllers/SuperAdmin/AffiliateController.php # Added settings methods
app/Models/AffiliateSetting.php                        # Enhanced with helper methods
resources/views/super_admin/affiliate_settings/         # New settings interface
resources/views/super_admin/layouts/app.blade.php      # Added navigation link
```

### Database Schema
Uses existing `affiliate_settings` table with fields:
- `default_commission_rate` - System default rate
- `minimum_withdrawal` - Minimum withdrawal amount
- `payment_methods` - Available payment methods (JSON)
- `auto_approve_earnings` - Auto-approve earnings flag
- `auto_approve_affiliates` - Auto-approve affiliates flag
- `cookie_duration_days` - Referral tracking duration
- `withdrawal_fee_percentage` - Percentage fee
- `withdrawal_fee_fixed` - Fixed fee amount
- `program_active` - Program status
- `welcome_message` - Welcome text
- `terms_and_conditions` - Legal terms
- `recurring_commissions` - Recurring commission flag
- `max_referrals_per_affiliate` - Optional limit

### Key Methods

#### AffiliateSetting Model
- `getInstance()` - Get singleton settings instance
- `updateAffiliateCommissionRates($oldRate, $newRate)` - Bulk rate update
- `getAffectedAffiliatesCount($rate)` - Count affected affiliates
- `getSystemStats()` - Comprehensive statistics
- `validateCommissionTiers($tiers)` - Validate tier structure

#### AffiliateController
- `settings()` - Display settings page
- `updateSettings(Request $request)` - Process settings updates

## Usage Instructions

### Changing Commission Rates
1. Navigate to Super Admin → Affiliate Settings
2. Modify the "Default Commission Rate" field
3. Check "Apply new rate to existing affiliates" if desired
4. Click "Update Settings"
5. Confirm changes in the modal dialog
6. Review the success message showing impact

### Configuring Payment Methods
1. Select desired payment methods (Bank Transfer, PayPal, Stripe, Manual)
2. Set minimum withdrawal amount
3. Configure withdrawal fees (percentage and/or fixed)
4. Save changes

### Program Management
1. Toggle "Affiliate Program Active" to enable/disable
2. Configure auto-approval settings
3. Set cookie duration for referral tracking
4. Add welcome messages and terms
5. Save configuration

## Security Features
- Super Admin authentication required
- CSRF protection on all forms
- Transaction rollback on errors
- Input validation and sanitization
- Confirmation modals for critical changes

## Error Handling
- Comprehensive validation messages
- Database transaction safety
- Graceful error recovery
- User-friendly error display
- Detailed logging for debugging

## Testing
Run the test file to verify functionality:
```bash
php test_affiliate_settings.php
```

## Support
For issues or questions regarding the affiliate settings system:
1. Check the system logs for errors
2. Verify database connectivity
3. Ensure proper Super Admin permissions
4. Review the test file for troubleshooting

## Future Enhancements
- Commission tier management interface
- Advanced analytics and reporting
- Email notification templates
- API endpoints for external integrations
- Bulk affiliate management tools
