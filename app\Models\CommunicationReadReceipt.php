<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CommunicationReadReceipt extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_communication_id',
        'user_id',
        'read_at',
    ];

    protected $casts = [
        'read_at' => 'datetime',
    ];

    /**
     * Get the communication this receipt belongs to
     */
    public function communication(): BelongsTo
    {
        return $this->belongsTo(OrganizationCommunication::class, 'organization_communication_id');
    }

    /**
     * Get the user who read the communication
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
