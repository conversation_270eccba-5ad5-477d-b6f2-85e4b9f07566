<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'account_name',
        'bank_name',
        'account_number',
        'account_type',
        'routing_number',
        'swift_code',
        'additional_instructions',
        'is_active',
        'is_primary',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_primary' => 'boolean',
    ];

    /**
     * Scope for active accounts.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for primary account.
     */
    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    /**
     * Set this account as primary and unset others.
     */
    public function setPrimary(): void
    {
        // First, unset all other primary accounts
        static::where('is_primary', true)->update(['is_primary' => false]);
        
        // Then set this one as primary
        $this->update(['is_primary' => true]);
    }

    /**
     * Get formatted account details for display.
     */
    public function getFormattedDetailsAttribute(): string
    {
        $details = "{$this->bank_name}\n";
        $details .= "Account Name: {$this->account_name}\n";
        $details .= "Account Number: {$this->account_number}\n";
        $details .= "Account Type: " . ucfirst($this->account_type);
        
        if ($this->routing_number) {
            $details .= "\nRouting Number: {$this->routing_number}";
        }
        
        if ($this->swift_code) {
            $details .= "\nSWIFT Code: {$this->swift_code}";
        }
        
        if ($this->additional_instructions) {
            $details .= "\n\nInstructions:\n{$this->additional_instructions}";
        }
        
        return $details;
    }

    /**
     * Get masked account number for security.
     */
    public function getMaskedAccountNumberAttribute(): string
    {
        $accountNumber = $this->account_number;
        $length = strlen($accountNumber);
        
        if ($length <= 4) {
            return str_repeat('*', $length);
        }
        
        return str_repeat('*', $length - 4) . substr($accountNumber, -4);
    }
}
