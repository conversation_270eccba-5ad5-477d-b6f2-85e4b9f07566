<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Organization;
use App\Models\Subscription;
use App\Notifications\TrialExpiringNotification;
use App\Notifications\SubscriptionExpiringNotification;
use App\Notifications\SubscriptionExpiredNotification;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class SendExpirationNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:send-expiration {--dry-run : Show what notifications would be sent without actually sending them}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send expiration notifications for trials and subscriptions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('🔍 DRY RUN MODE - No notifications will be sent');
        } else {
            $this->info('📧 SENDING EXPIRATION NOTIFICATIONS');
        }

        $notificationsSent = 0;

        // 1. Trial expiration notifications
        $notificationsSent += $this->sendTrialExpirationNotifications($dryRun);

        // 2. Subscription expiration notifications
        $notificationsSent += $this->sendSubscriptionExpirationNotifications($dryRun);

        // 3. Already expired notifications
        $notificationsSent += $this->sendExpiredNotifications($dryRun);

        if ($dryRun) {
            $this->info("✅ DRY RUN COMPLETE - Would send {$notificationsSent} notifications");
        } else {
            $this->info("✅ NOTIFICATIONS SENT - {$notificationsSent} notifications delivered");
        }

        return 0;
    }

    /**
     * Send trial expiration notifications
     */
    private function sendTrialExpirationNotifications(bool $dryRun): int
    {
        $sent = 0;
        $this->info('📅 Processing trial expiration notifications...');

        // Organizations with trials expiring in 7 days
        $organizations7Days = Organization::whereNotNull('trial_ends_at')
            ->whereBetween('trial_ends_at', [
                Carbon::now()->addDays(7)->startOfDay(),
                Carbon::now()->addDays(7)->endOfDay()
            ])
            ->where('is_active', true)
            ->get();

        foreach ($organizations7Days as $org) {
            if ($dryRun) {
                $this->line("  📧 Would send 7-day trial warning to: {$org->name} ({$org->email})");
            } else {
                $this->sendTrialNotification($org, 7);
                $this->line("  ✅ Sent 7-day trial warning to: {$org->name}");
            }
            $sent++;
        }

        // Organizations with trials expiring in 3 days
        $organizations3Days = Organization::whereNotNull('trial_ends_at')
            ->whereBetween('trial_ends_at', [
                Carbon::now()->addDays(3)->startOfDay(),
                Carbon::now()->addDays(3)->endOfDay()
            ])
            ->where('is_active', true)
            ->get();

        foreach ($organizations3Days as $org) {
            if ($dryRun) {
                $this->line("  📧 Would send 3-day trial warning to: {$org->name} ({$org->email})");
            } else {
                $this->sendTrialNotification($org, 3);
                $this->line("  ✅ Sent 3-day trial warning to: {$org->name}");
            }
            $sent++;
        }

        // Organizations with trials expiring tomorrow
        $organizationsTomorrow = Organization::whereNotNull('trial_ends_at')
            ->whereBetween('trial_ends_at', [
                Carbon::now()->addDay()->startOfDay(),
                Carbon::now()->addDay()->endOfDay()
            ])
            ->where('is_active', true)
            ->get();

        foreach ($organizationsTomorrow as $org) {
            if ($dryRun) {
                $this->line("  📧 Would send 1-day trial warning to: {$org->name} ({$org->email})");
            } else {
                $this->sendTrialNotification($org, 1);
                $this->line("  ✅ Sent 1-day trial warning to: {$org->name}");
            }
            $sent++;
        }

        return $sent;
    }

    /**
     * Send subscription expiration notifications
     */
    private function sendSubscriptionExpirationNotifications(bool $dryRun): int
    {
        $sent = 0;
        $this->info('💳 Processing subscription expiration notifications...');

        // Subscriptions expiring in 7 days
        $subscriptions7Days = Subscription::where('status', 'active')
            ->whereBetween('end_date', [
                Carbon::now()->addDays(7)->startOfDay(),
                Carbon::now()->addDays(7)->endOfDay()
            ])
            ->with(['organization', 'plan'])
            ->get();

        foreach ($subscriptions7Days as $subscription) {
            if ($dryRun) {
                $this->line("  📧 Would send 7-day subscription warning to: {$subscription->organization->name}");
            } else {
                $this->sendSubscriptionNotification($subscription, 7);
                $this->line("  ✅ Sent 7-day subscription warning to: {$subscription->organization->name}");
            }
            $sent++;
        }

        // Subscriptions expiring in 3 days
        $subscriptions3Days = Subscription::where('status', 'active')
            ->whereBetween('end_date', [
                Carbon::now()->addDays(3)->startOfDay(),
                Carbon::now()->addDays(3)->endOfDay()
            ])
            ->with(['organization', 'plan'])
            ->get();

        foreach ($subscriptions3Days as $subscription) {
            if ($dryRun) {
                $this->line("  📧 Would send 3-day subscription warning to: {$subscription->organization->name}");
            } else {
                $this->sendSubscriptionNotification($subscription, 3);
                $this->line("  ✅ Sent 3-day subscription warning to: {$subscription->organization->name}");
            }
            $sent++;
        }

        // Subscriptions expiring tomorrow
        $subscriptionsTomorrow = Subscription::where('status', 'active')
            ->whereBetween('end_date', [
                Carbon::now()->addDay()->startOfDay(),
                Carbon::now()->addDay()->endOfDay()
            ])
            ->with(['organization', 'plan'])
            ->get();

        foreach ($subscriptionsTomorrow as $subscription) {
            if ($dryRun) {
                $this->line("  📧 Would send 1-day subscription warning to: {$subscription->organization->name}");
            } else {
                $this->sendSubscriptionNotification($subscription, 1);
                $this->line("  ✅ Sent 1-day subscription warning to: {$subscription->organization->name}");
            }
            $sent++;
        }

        return $sent;
    }

    /**
     * Send notifications for already expired items
     */
    private function sendExpiredNotifications(bool $dryRun): int
    {
        $sent = 0;
        $this->info('⚠️  Processing expired notifications...');

        // Trials that expired yesterday (grace period)
        $expiredTrials = Organization::whereNotNull('trial_ends_at')
            ->whereBetween('trial_ends_at', [
                Carbon::now()->subDay()->startOfDay(),
                Carbon::now()->subDay()->endOfDay()
            ])
            ->where('is_active', true)
            ->get();

        foreach ($expiredTrials as $org) {
            if ($dryRun) {
                $this->line("  📧 Would send trial expired notice to: {$org->name} ({$org->email})");
            } else {
                $this->sendTrialExpiredNotification($org);
                $this->line("  ✅ Sent trial expired notice to: {$org->name}");
            }
            $sent++;
        }

        // Subscriptions that expired yesterday
        $expiredSubscriptions = Subscription::where('status', 'active')
            ->whereBetween('end_date', [
                Carbon::now()->subDay()->startOfDay(),
                Carbon::now()->subDay()->endOfDay()
            ])
            ->with(['organization', 'plan'])
            ->get();

        foreach ($expiredSubscriptions as $subscription) {
            if ($dryRun) {
                $this->line("  📧 Would send subscription expired notice to: {$subscription->organization->name}");
            } else {
                $this->sendSubscriptionExpiredNotification($subscription);
                $this->line("  ✅ Sent subscription expired notice to: {$subscription->organization->name}");
            }
            $sent++;
        }

        return $sent;
    }

    /**
     * Send trial notification
     */
    private function sendTrialNotification(Organization $organization, int $daysRemaining): void
    {
        try {
            Notification::route('mail', $organization->email)
                ->notify(new TrialExpiringNotification($organization, $daysRemaining));

            Log::info('Trial expiration notification sent', [
                'organization_id' => $organization->id,
                'days_remaining' => $daysRemaining,
                'trial_ends_at' => $organization->trial_ends_at
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send trial notification', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send subscription notification
     */
    private function sendSubscriptionNotification(Subscription $subscription, int $daysRemaining): void
    {
        try {
            Notification::route('mail', $subscription->organization->email)
                ->notify(new SubscriptionExpiringNotification($subscription, $daysRemaining));

            Log::info('Subscription expiration notification sent', [
                'subscription_id' => $subscription->id,
                'organization_id' => $subscription->organization_id,
                'days_remaining' => $daysRemaining,
                'end_date' => $subscription->end_date
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send subscription notification', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send trial expired notification
     */
    private function sendTrialExpiredNotification(Organization $organization): void
    {
        try {
            Notification::route('mail', $organization->email)
                ->notify(new TrialExpiringNotification($organization, 0)); // 0 days = expired

            Log::info('Trial expired notification sent', [
                'organization_id' => $organization->id,
                'trial_ends_at' => $organization->trial_ends_at
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send trial expired notification', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send subscription expired notification
     */
    private function sendSubscriptionExpiredNotification(Subscription $subscription): void
    {
        try {
            Notification::route('mail', $subscription->organization->email)
                ->notify(new SubscriptionExpiredNotification($subscription));

            Log::info('Subscription expired notification sent', [
                'subscription_id' => $subscription->id,
                'organization_id' => $subscription->organization_id,
                'end_date' => $subscription->end_date
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send subscription expired notification', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
