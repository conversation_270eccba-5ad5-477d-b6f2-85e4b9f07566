<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            // Remove old annual billing fields
            if (Schema::hasColumn('plans', 'annual_price')) {
                $table->dropColumn('annual_price');
            }
            if (Schema::hasColumn('plans', 'annual_discount_percentage')) {
                $table->dropColumn('annual_discount_percentage');
            }

            // Add new flexible billing period discounts (at the end to avoid column position issues)
            $table->json('billing_period_discounts')->nullable()
                  ->comment('JSON object with discount percentages for different billing periods');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            $table->dropColumn('billing_period_discounts');

            // Restore old fields (at the end to avoid column position issues)
            $table->decimal('annual_price', 10, 2)->nullable();
            $table->integer('annual_discount_percentage')->default(0);
        });
    }
};
