<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Affiliate;
use App\Models\AffiliateClick;
use Illuminate\Support\Facades\Log;

class AffiliateClickController extends Controller
{
    /**
     * Track affiliate link click and redirect to registration
     */
    public function trackClick(Request $request, $affiliateCode)
    {
        try {
            // Find the affiliate
            $affiliate = Affiliate::where('affiliate_code', $affiliateCode)
                ->where('status', Affiliate::STATUS_ACTIVE)
                ->first();

            if (!$affiliate) {
                Log::warning('Invalid affiliate code in click tracking', ['code' => $affiliateCode]);
                return redirect()->route('register');
            }

            // Get user information
            $userAgent = $request->userAgent();
            $ipAddress = $request->ip();

            // Check if this is a unique click
            $isUnique = AffiliateClick::isUniqueClick($affiliate->id, $ipAddress);

            // Create click record
            $clickData = [
                'affiliate_id' => $affiliate->id,
                'affiliate_code' => $affiliateCode,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'referrer' => $request->header('referer'),
                'utm_source' => $request->get('utm_source'),
                'utm_medium' => $request->get('utm_medium'),
                'utm_campaign' => $request->get('utm_campaign'),
                'utm_content' => $request->get('utm_content'),
                'utm_term' => $request->get('utm_term'),
                'device_type' => AffiliateClick::getDeviceType($userAgent),
                'browser' => AffiliateClick::getBrowser($userAgent),
                'platform' => AffiliateClick::getPlatform($userAgent),
                'is_unique' => $isUnique,
                'clicked_at' => now(),
            ];

            // Try to get location data (optional - you can integrate with a GeoIP service)
            // $clickData['country'] = $this->getCountryFromIP($ipAddress);
            // $clickData['city'] = $this->getCityFromIP($ipAddress);

            AffiliateClick::create($clickData);

            Log::info('Affiliate click tracked', [
                'affiliate_id' => $affiliate->id,
                'code' => $affiliateCode,
                'ip' => $ipAddress,
                'is_unique' => $isUnique
            ]);

            // Build registration URL with all parameters
            $registrationParams = [
                'ref' => $affiliateCode
            ];

            // Preserve UTM parameters
            $utmParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term'];
            foreach ($utmParams as $param) {
                if ($request->has($param)) {
                    $registrationParams[$param] = $request->get($param);
                }
            }

            $registrationUrl = route('register') . '?' . http_build_query($registrationParams);

            return redirect($registrationUrl);

        } catch (\Exception $e) {
            Log::error('Error tracking affiliate click', [
                'code' => $affiliateCode,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Fallback to registration page
            return redirect()->route('register');
        }
    }

    /**
     * Get click analytics for affiliate (API endpoint)
     */
    public function getClickAnalytics(Request $request, $affiliateId)
    {
        $affiliate = Affiliate::findOrFail($affiliateId);
        
        // Check authorization (affiliate can only see their own data)
        if (auth()->guard('affiliate')->check()) {
            $currentAffiliate = auth()->guard('affiliate')->user()->affiliate;
            if ($currentAffiliate->id !== $affiliate->id) {
                abort(403, 'Unauthorized');
            }
        } elseif (!auth()->guard('super_admin')->check()) {
            abort(403, 'Unauthorized');
        }

        $period = $request->get('period', 'month'); // today, week, month, all

        $stats = AffiliateClick::getClickStats($affiliate->id, $period);

        // Get daily breakdown for charts
        $dailyStats = $this->getDailyClickStats($affiliate->id, $period);

        // Get top sources
        $topSources = $this->getTopSources($affiliate->id, $period);

        // Get device breakdown
        $deviceStats = $this->getDeviceStats($affiliate->id, $period);

        return response()->json([
            'stats' => $stats,
            'daily_breakdown' => $dailyStats,
            'top_sources' => $topSources,
            'device_breakdown' => $deviceStats
        ]);
    }

    /**
     * Get daily click statistics
     */
    protected function getDailyClickStats($affiliateId, $period)
    {
        $query = AffiliateClick::where('affiliate_id', $affiliateId);

        switch ($period) {
            case 'today':
                $query->today();
                break;
            case 'week':
                $query->thisWeek();
                break;
            case 'month':
                $query->thisMonth();
                break;
        }

        return $query->selectRaw('DATE(clicked_at) as date, COUNT(*) as total_clicks, COUNT(CASE WHEN is_unique = 1 THEN 1 END) as unique_clicks')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    /**
     * Get top traffic sources
     */
    protected function getTopSources($affiliateId, $period)
    {
        $query = AffiliateClick::where('affiliate_id', $affiliateId);

        switch ($period) {
            case 'today':
                $query->today();
                break;
            case 'week':
                $query->thisWeek();
                break;
            case 'month':
                $query->thisMonth();
                break;
        }

        return $query->selectRaw('COALESCE(utm_source, "Direct") as source, COUNT(*) as clicks, COUNT(CASE WHEN is_unique = 1 THEN 1 END) as unique_clicks')
            ->groupBy('source')
            ->orderByDesc('clicks')
            ->limit(10)
            ->get();
    }

    /**
     * Get device type statistics
     */
    protected function getDeviceStats($affiliateId, $period)
    {
        $query = AffiliateClick::where('affiliate_id', $affiliateId);

        switch ($period) {
            case 'today':
                $query->today();
                break;
            case 'week':
                $query->thisWeek();
                break;
            case 'month':
                $query->thisMonth();
                break;
        }

        return $query->selectRaw('device_type, COUNT(*) as clicks, COUNT(CASE WHEN is_unique = 1 THEN 1 END) as unique_clicks')
            ->groupBy('device_type')
            ->orderByDesc('clicks')
            ->get();
    }

    /**
     * Optional: Get country from IP address
     * You can integrate with services like MaxMind GeoIP, ipapi.co, etc.
     */
    protected function getCountryFromIP($ipAddress)
    {
        // Placeholder - integrate with your preferred GeoIP service
        return null;
    }

    /**
     * Optional: Get city from IP address
     */
    protected function getCityFromIP($ipAddress)
    {
        // Placeholder - integrate with your preferred GeoIP service
        return null;
    }
}
