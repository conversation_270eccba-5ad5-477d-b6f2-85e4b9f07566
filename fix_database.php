<?php

echo "=== Database Fix Script ===\n";

// Database configuration from .env
$host = '127.0.0.1';
$port = '4306';
$database = 'ofp_pro';
$username = 'root';
$password = '';

try {
    // Connect to database
    $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "✓ Connected to database: {$database}\n";
    
    // Check if sessions table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'sessions'");
    if ($stmt->rowCount() == 0) {
        echo "Creating sessions table...\n";
        
        $createSessionsTable = "
        CREATE TABLE `sessions` (
            `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
            `user_id` bigint unsigned DEFAULT NULL,
            `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `user_agent` text COLLATE utf8mb4_unicode_ci,
            `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
            `last_activity` int NOT NULL,
            PRIMARY KEY (`id`),
            KEY `sessions_user_id_index` (`user_id`),
            KEY `sessions_last_activity_index` (`last_activity`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($createSessionsTable);
        echo "✓ Sessions table created\n";
    } else {
        echo "✓ Sessions table already exists\n";
    }
    
    // Check if announcements table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'announcements'");
    if ($stmt->rowCount() == 0) {
        echo "Creating announcements table...\n";
        
        $createAnnouncementsTable = "
        CREATE TABLE `announcements` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
            `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
            `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
            `type` enum('info','warning','success','danger','maintenance') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'info',
            `priority` enum('low','normal','high','urgent') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal',
            `target_audience` enum('all','customers','organizations','admins') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'all',
            `is_active` tinyint(1) NOT NULL DEFAULT '1',
            `is_dismissible` tinyint(1) NOT NULL DEFAULT '1',
            `show_on_login` tinyint(1) NOT NULL DEFAULT '0',
            `show_on_dashboard` tinyint(1) NOT NULL DEFAULT '1',
            `send_email` tinyint(1) NOT NULL DEFAULT '0',
            `affected_features` json DEFAULT NULL,
            `starts_at` timestamp NULL DEFAULT NULL,
            `ends_at` timestamp NULL DEFAULT NULL,
            `published_at` timestamp NULL DEFAULT NULL,
            `created_by` bigint unsigned NOT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `announcements_is_active_published_at_index` (`is_active`,`published_at`),
            KEY `announcements_type_target_audience_index` (`type`,`target_audience`),
            KEY `announcements_starts_at_ends_at_index` (`starts_at`,`ends_at`),
            KEY `announcements_created_by_foreign` (`created_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($createAnnouncementsTable);
        echo "✓ Announcements table created\n";
    } else {
        echo "✓ Announcements table already exists\n";
    }
    
    // Check if user_announcement_interactions table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_announcement_interactions'");
    if ($stmt->rowCount() == 0) {
        echo "Creating user_announcement_interactions table...\n";
        
        $createInteractionsTable = "
        CREATE TABLE `user_announcement_interactions` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
            `user_id` bigint unsigned NOT NULL,
            `announcement_id` bigint unsigned NOT NULL,
            `is_read` tinyint(1) NOT NULL DEFAULT '0',
            `is_dismissed` tinyint(1) NOT NULL DEFAULT '0',
            `read_at` timestamp NULL DEFAULT NULL,
            `dismissed_at` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `user_announcement_interactions_user_id_announcement_id_unique` (`user_id`,`announcement_id`),
            KEY `user_announcement_interactions_announcement_id_foreign` (`announcement_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($createInteractionsTable);
        echo "✓ User announcement interactions table created\n";
    } else {
        echo "✓ User announcement interactions table already exists\n";
    }
    
    // Check if super_admins table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'super_admins'");
    if ($stmt->rowCount() == 0) {
        echo "Creating super_admins table...\n";
        
        $createSuperAdminsTable = "
        CREATE TABLE `super_admins` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
            `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
            `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
            `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `role` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'super_admin',
            `is_active` tinyint(1) NOT NULL DEFAULT '1',
            `last_login_at` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `super_admins_email_unique` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($createSuperAdminsTable);
        echo "✓ Super admins table created\n";
    } else {
        echo "✓ Super admins table already exists\n";
    }
    
    // Check if there are any super admins
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM super_admins");
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        echo "Creating default super admin...\n";
        
        $stmt = $pdo->prepare("
            INSERT INTO super_admins (name, email, password, role, is_active, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
        $stmt->execute([
            'Super Admin',
            '<EMAIL>',
            $hashedPassword,
            'super_admin',
            1
        ]);
        
        echo "✓ Default super admin created (email: <EMAIL>, password: password)\n";
    } else {
        echo "✓ Super admin(s) already exist\n";
    }
    
    // Create a test announcement
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM announcements");
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        echo "Creating test announcements...\n";
        
        // Get super admin ID
        $stmt = $pdo->query("SELECT id FROM super_admins LIMIT 1");
        $superAdmin = $stmt->fetch();
        $superAdminId = $superAdmin['id'];
        
        $stmt = $pdo->prepare("
            INSERT INTO announcements 
            (title, content, type, priority, target_audience, is_active, is_dismissible, show_on_login, show_on_dashboard, send_email, published_at, created_by, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, NOW(), NOW())
        ");
        
        // Test announcement for organizations
        $stmt->execute([
            'Welcome to the System!',
            'This is a test announcement for organization users. The announcement system is now working correctly.',
            'info',
            'normal',
            'organizations',
            1, // is_active
            1, // is_dismissible
            0, // show_on_login
            1, // show_on_dashboard
            0, // send_email
            $superAdminId
        ]);
        
        // Test announcement for all users
        $stmt->execute([
            'System Maintenance Notice',
            'Please be aware that we may perform system maintenance during off-peak hours. You will be notified in advance of any scheduled downtime.',
            'warning',
            'high',
            'all',
            1, // is_active
            1, // is_dismissible
            0, // show_on_login
            1, // show_on_dashboard
            0, // send_email
            $superAdminId
        ]);
        
        echo "✓ Test announcements created\n";
    } else {
        echo "✓ Announcements already exist\n";
    }
    
    echo "\n=== Database Fix Complete ===\n";
    echo "✓ All required tables are now present\n";
    echo "✓ Test data has been created\n";
    echo "✓ You can now test the announcement system\n";
    echo "\nNext steps:\n";
    echo "1. Login to the application as an organization user\n";
    echo "2. Check the dashboard for announcements\n";
    echo "3. Login to super admin at /super-admin/login (<EMAIL> / password)\n";
    echo "4. Manage announcements at /super-admin/announcements\n";
    
} catch (PDOException $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
    echo "Please check your database connection settings in .env file\n";
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}
