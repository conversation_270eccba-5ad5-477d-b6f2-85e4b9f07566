<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Profile Dropdown</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* Same styles as in the main layout */
        .dropdown-menu {
            z-index: 1050 !important;
            min-width: 200px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(0, 0, 0, 0.15);
            margin-top: 0.5rem;
            border-radius: 0.5rem;
            padding: 0.5rem 0;
        }
        
        .dropdown {
            position: relative;
        }

        .dropdown .btn {
            border-color: #6c757d;
            color: #495057;
            font-weight: 500;
            white-space: nowrap;
            min-width: 100px;
        }

        .dropdown .btn:hover {
            background-color: #e9ecef;
            border-color: #6c757d;
        }

        .dropdown .btn:focus {
            box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
        }

        .dropdown-header {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6c757d;
            padding: 0.5rem 1rem;
            margin-bottom: 0;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            transition: all 0.15s ease-in-out;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item.text-danger:hover {
            background-color: #f8d7da;
            color: #721c24 !important;
        }

        @media (max-width: 576px) {
            .dropdown .btn {
                padding: 0.375rem 0.75rem;
                font-size: 0.875rem;
            }
            
            .dropdown-menu {
                min-width: 180px;
                right: 0 !important;
                left: auto !important;
            }
        }

        body {
            padding: 2rem;
            background-color: #f8f9fa;
        }

        .test-container {
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .header-simulation {
            display: flex;
            justify-content: between;
            align-items: center;
            padding: 1rem;
            background: #fff;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h1>Profile & Logout Dropdown Test</h1>
            <p>This page tests the profile and logout dropdown functionality that should appear in the organization user area.</p>
            
            <!-- Simulate the header with dropdown -->
            <div class="header-simulation">
                <h2>Dashboard</h2>
                
                <div class="d-flex align-items-center flex-wrap gap-2">
                    <!-- Currency Display -->
                    <div class="text-center d-none d-sm-block me-3">
                        <div class="small text-muted">Currency</div>
                        <span class="badge bg-success">₦ NGN</span>
                    </div>

                    <!-- User Dropdown -->
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle d-flex align-items-center"
                                type="button" id="userDropdown" data-bs-toggle="dropdown"
                                aria-expanded="false" title="User Menu" style="min-width: 120px;">
                            <i class="fas fa-user me-1"></i>
                            <span class="d-none d-md-inline">John Doe</span>
                            <span class="d-md-none">John D.</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="userDropdown"
                            style="min-width: 220px; z-index: 1060;">
                            <li>
                                <h6 class="dropdown-header d-flex align-items-center">
                                    <i class="fas fa-user-circle me-2 text-primary"></i>
                                    <div>
                                        <div class="fw-bold">John Doe</div>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                </h6>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="#" onclick="alert('Profile clicked!')">
                                    <i class="fas fa-user-cog me-3 text-info"></i>
                                    <div>
                                        <div>Profile Settings</div>
                                        <small class="text-muted">Manage your account</small>
                                    </div>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <button type="button" class="dropdown-item text-danger d-flex align-items-center"
                                        onclick="alert('Logout clicked!')">
                                    <i class="fas fa-sign-out-alt me-3"></i>
                                    <div>
                                        <div>Logout</div>
                                        <small class="text-muted">Sign out of your account</small>
                                    </div>
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>Test Instructions:</h5>
                <ol>
                    <li>Click the "John Doe" button (or "Menu" on mobile) in the top-right corner</li>
                    <li>You should see a dropdown menu with "Profile Settings" and "Logout" options</li>
                    <li>Click on either option to test the functionality</li>
                    <li>If the dropdown doesn't work, check the browser console for errors</li>
                </ol>
            </div>
            
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>Expected Behavior:</h5>
                <ul>
                    <li>Dropdown should open when clicked</li>
                    <li>Profile Settings should show an alert saying "Profile clicked!"</li>
                    <li>Logout should show an alert saying "Logout clicked!"</li>
                    <li>Dropdown should be responsive and work on mobile devices</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Initialize dropdowns -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing dropdowns...');

            // Check if Bootstrap is loaded
            if (typeof bootstrap === 'undefined') {
                console.error('Bootstrap is not loaded!');
                alert('Bootstrap is not loaded! The dropdown will not work.');
                return;
            }

            // Initialize all dropdowns
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            console.log('Found dropdown elements:', dropdownElementList.length);

            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });

            console.log('Dropdowns initialized:', dropdownList.length);

            // Enhanced user dropdown handling
            const userDropdown = document.getElementById('userDropdown');
            if (userDropdown) {
                console.log('User dropdown found');

                // Add click event listener
                userDropdown.addEventListener('click', function(e) {
                    console.log('User dropdown clicked');
                });

                // Add event listeners for dropdown state changes
                userDropdown.addEventListener('shown.bs.dropdown', function () {
                    console.log('Dropdown shown');
                });

                userDropdown.addEventListener('hidden.bs.dropdown', function () {
                    console.log('Dropdown hidden');
                });

                userDropdown.addEventListener('show.bs.dropdown', function () {
                    console.log('Dropdown showing...');
                });
            } else {
                console.error('User dropdown not found!');
            }

            // Test button to manually trigger dropdown
            const testBtn = document.createElement('button');
            testBtn.textContent = 'Test Dropdown';
            testBtn.className = 'btn btn-warning btn-sm mt-2';
            testBtn.onclick = function() {
                const dropdown = bootstrap.Dropdown.getInstance(userDropdown);
                if (dropdown) {
                    dropdown.toggle();
                    console.log('Manually toggled dropdown');
                } else {
                    console.log('No dropdown instance found');
                }
            };
            document.querySelector('.test-container').appendChild(testBtn);
        });
    </script>
</body>
</html>
