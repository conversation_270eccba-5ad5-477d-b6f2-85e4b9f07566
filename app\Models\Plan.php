<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Plan extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'billing_period_discounts',
        'branch_limit',
        'user_limit',
        'data_retention_days',
        'order_limit',
        'thermal_printing',
        'advanced_reporting',
        'api_access',
        'white_label',
        'custom_branding',
        'additional_features',
        'is_active',
        'is_featured',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'billing_period_discounts' => 'json',
        'thermal_printing' => 'boolean',
        'advanced_reporting' => 'boolean',
        'api_access' => 'boolean',
        'white_label' => 'boolean',
        'custom_branding' => 'boolean',
        'additional_features' => 'json',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
    ];

    /**
     * Get the organizations that belong to this plan.
     */
    public function organizations()
    {
        return $this->hasMany(Organization::class);
    }

    /**
     * Get the subscriptions for this plan.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Check if the plan has the given feature.
     *
     * @param string $feature
     * @return bool
     */
    public function hasFeature(string $feature): bool
    {
        if (in_array($feature, ['thermal_printing', 'advanced_reporting', 'api_access', 'white_label', 'custom_branding'])) {
            return $this->$feature;
        }

        if ($this->additional_features && isset($this->additional_features[$feature])) {
            return $this->additional_features[$feature];
        }

        return false;
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        return format_price($this->price);
    }

    /**
     * Check if this is a free plan
     */
    public function isFree(): bool
    {
        return $this->price == 0;
    }

    /**
     * Get available billing periods with pricing and discounts
     *
     * @return array
     */
    public function getAvailableBillingPeriods(): array
    {
        $periods = [
            1 => [
                'label' => '1 Month',
                'months' => 1,
                'price' => $this->getPriceForPeriod(1),
                'monthly_equivalent' => $this->price,
                'discount_percentage' => $this->getDiscountForPeriod(1),
                'savings' => 0,
                'period_label' => 'per month'
            ],
            12 => [
                'label' => '12 Months',
                'months' => 12,
                'price' => $this->getPriceForPeriod(12),
                'monthly_equivalent' => $this->getPriceForPeriod(12) / 12,
                'discount_percentage' => $this->getDiscountForPeriod(12),
                'savings' => ($this->price * 12) - $this->getPriceForPeriod(12),
                'period_label' => 'per year'
            ],
            24 => [
                'label' => '24 Months',
                'months' => 24,
                'price' => $this->getPriceForPeriod(24),
                'monthly_equivalent' => $this->getPriceForPeriod(24) / 24,
                'discount_percentage' => $this->getDiscountForPeriod(24),
                'savings' => ($this->price * 24) - $this->getPriceForPeriod(24),
                'period_label' => 'per 2 years'
            ],
            48 => [
                'label' => '48 Months',
                'months' => 48,
                'price' => $this->getPriceForPeriod(48),
                'monthly_equivalent' => $this->getPriceForPeriod(48) / 48,
                'discount_percentage' => $this->getDiscountForPeriod(48),
                'savings' => ($this->price * 48) - $this->getPriceForPeriod(48),
                'period_label' => 'per 4 years'
            ]
        ];

        return $periods;
    }

    /**
     * Set discount for a specific billing period
     *
     * @param int $periodMonths
     * @param float $discountPercentage
     * @return void
     */
    public function setDiscountForPeriod(int $periodMonths, float $discountPercentage): void
    {
        $discounts = $this->billing_period_discounts ?: [];
        $discounts[$periodMonths] = $discountPercentage;
        $this->billing_period_discounts = $discounts;
    }

    /**
     * Get the price for a specific billing period in months
     *
     * @param int $periodMonths Number of months (1, 12, 24, 48)
     * @return float
     */
    public function getPriceForPeriod(int $periodMonths = 1): float
    {
        // Base monthly price
        $basePrice = $this->price * $periodMonths;

        // Get discount for this period
        $discountPercentage = $this->getDiscountForPeriod($periodMonths);

        if ($discountPercentage > 0) {
            $discount = $basePrice * ($discountPercentage / 100);
            return $basePrice - $discount;
        }

        return $basePrice;
    }

    /**
     * Get discount percentage for a specific billing period
     *
     * @param int $periodMonths Number of months
     * @return float
     */
    public function getDiscountForPeriod(int $periodMonths): float
    {
        if (!$this->billing_period_discounts) {
            return 0;
        }

        return $this->billing_period_discounts[$periodMonths] ?? 0;
    }

    /**
     * Get formatted price for a specific billing period
     *
     * @param int $periodMonths
     * @return string
     */
    public function getFormattedPriceForPeriod(int $periodMonths): string
    {
        return format_price($this->getPriceForPeriod($periodMonths));
    }

    /**
     * Get savings amount for a specific billing period
     *
     * @param int $periodMonths
     * @return float
     */
    public function getSavingsForPeriod(int $periodMonths): float
    {
        $regularPrice = $this->price * $periodMonths;
        $discountedPrice = $this->getPriceForPeriod($periodMonths);
        return $regularPrice - $discountedPrice;
    }
}
