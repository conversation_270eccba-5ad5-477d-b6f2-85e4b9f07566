<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\SettingsSeeder;

class SetupDefaultSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:setup-default-settings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up default application settings with Order Flow Pro branding';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up default application settings...');

        try {
            $seeder = new SettingsSeeder();
            $seeder->setContainer($this->laravel);
            $seeder->setCommand($this);
            $seeder->run();

            $this->info('Default settings have been successfully set up!');
            $this->info('App Name: Order Flow Pro');
            $this->info('App Slogan: Manage Orders & Reports');
            $this->info('Theme: Light');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Failed to set up default settings: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
