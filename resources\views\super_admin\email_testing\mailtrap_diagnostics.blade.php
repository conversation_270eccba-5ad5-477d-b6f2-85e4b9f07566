@extends('super_admin.layouts.app')

@section('title', 'Mailtrap Diagnostics')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">🔧 Mailtrap Configuration Diagnostics</h1>
                <div class="btn-group" role="group">
                    <a href="{{ route('super_admin.email-testing.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Back to Email Testing
                    </a>
                    <a href="{{ route('super_admin.email-testing.system-check') }}" class="btn btn-outline-warning">
                        <i class="fas fa-heartbeat"></i> System Check
                    </a>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle"></i> 
                    @foreach($errors->all() as $error)
                        {{ $error }}<br>
                    @endforeach
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('diagnostic_info'))
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <h5><i class="fas fa-info-circle"></i> Diagnostic Email Sent</h5>
                    <p>Diagnostic email sent with the following information:</p>
                    <pre class="small">{{ json_encode(session('diagnostic_info'), JSON_PRETTY_PRINT) }}</pre>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Issue Identification -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i> Identified Issue: Email Delivery Problem
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6><strong>Problem:</strong> Emails showing as "Soft Rejected" in Mailtrap</h6>
                        <p class="mb-2">Your emails are being sent to Mailtrap but marked as "Soft Rejected" and not delivered to the actual email address.</p>
                        
                        <h6><strong>Root Cause:</strong></h6>
                        <ul class="mb-2">
                            <li>Using Mailtrap Email Sending environment but domain may not be verified</li>
                            <li>From address <code><EMAIL></code> needs domain verification</li>
                            <li>Mailtrap requires domain verification for email delivery</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Current Configuration -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog"></i> Current Mailtrap Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Mail Driver:</strong></td>
                                    <td>{{ $config['mailer'] }}</td>
                                </tr>
                                <tr>
                                    <td><strong>SMTP Host:</strong></td>
                                    <td>{{ $config['host'] }}</td>
                                </tr>
                                <tr>
                                    <td><strong>SMTP Port:</strong></td>
                                    <td>{{ $config['port'] }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Username:</strong></td>
                                    <td>{{ $config['username'] }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Encryption:</strong></td>
                                    <td>{{ $config['encryption'] }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>From Address:</strong></td>
                                    <td>{{ $config['from_address'] }}</td>
                                </tr>
                                <tr>
                                    <td><strong>From Name:</strong></td>
                                    <td>{{ $config['from_name'] }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Environment:</strong></td>
                                    <td>
                                        @if($config['host'] === 'live.smtp.mailtrap.io')
                                            <span class="badge bg-success">Email Sending</span>
                                        @elseif($config['host'] === 'sandbox.smtp.mailtrap.io')
                                            <span class="badge bg-info">Email Testing</span>
                                        @else
                                            <span class="badge bg-warning">Unknown</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Domain Status:</strong></td>
                                    <td><span class="badge bg-warning">Needs Verification</span></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Solutions -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools"></i> Solutions to Fix Email Delivery
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-success">Option 1: Verify Domain in Mailtrap (Recommended)</h6>
                            <ol class="small">
                                <li>Login to your Mailtrap account</li>
                                <li>Go to Email Sending → Domains</li>
                                <li>Add and verify <code>sms.macivergroup.com</code></li>
                                <li>Add required DNS records (SPF, DKIM, DMARC)</li>
                                <li>Wait for verification (can take up to 24 hours)</li>
                                <li>Test email delivery again</li>
                            </ol>
                            <div class="alert alert-info small">
                                <strong>Result:</strong> Emails will be delivered to real email addresses
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">Option 2: Switch to Email Testing Environment</h6>
                            <ol class="small">
                                <li>Change SMTP host to <code>sandbox.smtp.mailtrap.io</code></li>
                                <li>Use testing inbox credentials</li>
                                <li>Emails will appear in Mailtrap inbox only</li>
                                <li>Good for development and testing</li>
                                <li>No real email delivery</li>
                            </ol>
                            <div class="alert alert-warning small">
                                <strong>Result:</strong> Emails visible in Mailtrap inbox, not delivered to real addresses
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Diagnostic Email Testing -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-stethoscope"></i> Send Diagnostic Email
                    </h5>
                </div>
                <div class="card-body">
                    <p>Send a diagnostic email with detailed headers to help identify the issue:</p>
                    <form method="POST" action="{{ route('super_admin.email-testing.send-diagnostic-email') }}">
                        @csrf
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="test_email" class="form-label">Test Email Address</label>
                                    <input type="email" class="form-control" id="test_email" name="test_email" 
                                           value="<EMAIL>" required>
                                    <div class="form-text">Enter the email address where diagnostic email should be sent</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i> Send Diagnostic Email
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Domain Verification Guide -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt"></i> Domain Verification Guide
                    </h5>
                </div>
                <div class="card-body">
                    <h6>Steps to verify <code>sms.macivergroup.com</code> in Mailtrap:</h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">1. Add Domain in Mailtrap</h6>
                            <ul class="small">
                                <li>Login to Mailtrap dashboard</li>
                                <li>Navigate to Email Sending → Domains</li>
                                <li>Click "Add Domain"</li>
                                <li>Enter: <code>sms.macivergroup.com</code></li>
                                <li>Click "Add Domain"</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">2. Add DNS Records</h6>
                            <p class="small">Mailtrap will provide DNS records to add:</p>
                            <ul class="small">
                                <li><strong>SPF Record:</strong> TXT record for sender verification</li>
                                <li><strong>DKIM Record:</strong> TXT record for email authentication</li>
                                <li><strong>DMARC Record:</strong> TXT record for email policy</li>
                                <li><strong>MX Record:</strong> Mail exchange record (if needed)</li>
                            </ul>
                        </div>
                    </div>

                    <div class="alert alert-info mt-3">
                        <h6><i class="fas fa-info-circle"></i> Important Notes:</h6>
                        <ul class="mb-0 small">
                            <li>DNS propagation can take up to 24 hours</li>
                            <li>All DNS records must be added correctly</li>
                            <li>Verification status will show in Mailtrap dashboard</li>
                            <li>Once verified, emails will be delivered to real addresses</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt"></i> Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="https://mailtrap.io/signin" class="btn btn-outline-primary" target="_blank">
                                    <i class="fas fa-external-link-alt"></i><br>
                                    <small>Mailtrap Dashboard</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ route('super_admin.email-testing.system-check') }}" class="btn btn-outline-warning">
                                    <i class="fas fa-heartbeat"></i><br>
                                    <small>System Check</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ route('super_admin.email-testing.index') }}" class="btn btn-outline-success">
                                    <i class="fas fa-envelope"></i><br>
                                    <small>Email Testing Dashboard</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ route('super_admin.email-testing.account-creation') }}" class="btn btn-outline-info">
                                    <i class="fas fa-user-plus"></i><br>
                                    <small>Account Creation Testing</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
