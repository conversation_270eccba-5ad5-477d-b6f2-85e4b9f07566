@extends('super_admin.layouts.app')

@section('title', $organization->name . ' - Organization Details')
@section('page-title', 'Organization Details')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ $organization->name }}</h1>
            <p class="text-muted">Organization details and statistics</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('super_admin.organizations.edit', $organization) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit Organization
            </a>
            <a href="{{ route('super_admin.organizations.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Organizations
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Organization Details -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Organization Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 text-center">
                            @if($organization->logo)
                                <img src="{{ asset('storage/logos/' . $organization->logo) }}"
                                     alt="{{ $organization->name }}"
                                     class="rounded-circle" width="80" height="80" style="object-fit: cover;">
                            @else
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 80px; height: 80px;">
                                    <span class="text-white font-weight-bold fs-3">
                                        {{ substr($organization->name, 0, 1) }}
                                    </span>
                                </div>
                            @endif
                        </div>
                        <div class="col-md-10">
                            <h4>{{ $organization->name }}</h4>
                            @if($organization->description)
                                <p class="text-muted">{{ $organization->description }}</p>
                            @endif
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Email:</strong> {{ $organization->email }}</p>
                                    @if($organization->phone)
                                        <p class="mb-1"><strong>Phone:</strong> {{ $organization->phone }}</p>
                                    @endif
                                    @if($organization->website)
                                        <p class="mb-1"><strong>Website:</strong>
                                            <a href="{{ $organization->website }}" target="_blank">{{ $organization->website }}</a>
                                        </p>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    @if($organization->address)
                                        <p class="mb-1"><strong>Address:</strong></p>
                                        <p class="text-muted">{{ $organization->address }}</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="h3 text-primary">{{ $stats['total_users'] }}</div>
                            <small class="text-muted">Total Users</small>
                            <div class="mt-1">
                                <small class="text-success">{{ $stats['active_users'] }} Active</small>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h3 text-success">{{ $stats['total_branches'] }}</div>
                            <small class="text-muted">Branches</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h3 text-info">{{ $stats['total_orders'] }}</div>
                            <small class="text-muted">Total Orders</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h3 text-warning">${{ number_format($stats['monthly_revenue'], 2) }}</div>
                            <small class="text-muted">Monthly Revenue</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Plan -->
            @if($organization->plan)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Plan</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>{{ $organization->plan->name }}</h5>
                            @if($organization->plan->description)
                                <p class="text-muted">{{ $organization->plan->description }}</p>
                            @endif
                            <div class="h4 text-primary">${{ number_format($organization->plan->price, 2) }}</div>
                            <small class="text-muted">per month</small>
                        </div>
                        <div class="col-md-6">
                            <h6>Plan Limits:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-building text-info me-2"></i>
                                    {{ $organization->plan->branch_limit == 999 ? 'Unlimited' : $organization->plan->branch_limit }} Branches
                                </li>
                                <li><i class="fas fa-users text-success me-2"></i>
                                    {{ $organization->plan->user_limit == 999 ? 'Unlimited' : $organization->plan->user_limit }} Users
                                </li>
                                <li><i class="fas fa-database text-warning me-2"></i>
                                    {{ $organization->plan->data_retention_days == 999 ? 'Forever' : $organization->plan->data_retention_days . ' Days' }} Data Retention
                                </li>
                            </ul>

                            <h6>Features:</h6>
                            <div class="d-flex flex-wrap gap-1">
                                @if($organization->plan->thermal_printing)
                                    <span class="badge bg-success">Thermal Printing</span>
                                @endif
                                @if($organization->plan->advanced_reporting)
                                    <span class="badge bg-info">Advanced Reports</span>
                                @endif
                                @if($organization->plan->api_access)
                                    <span class="badge bg-primary">API Access</span>
                                @endif
                                @if($organization->plan->white_label)
                                    <span class="badge bg-secondary">White Label</span>
                                @endif
                                @if($organization->plan->custom_branding)
                                    <span class="badge bg-warning">Custom Branding</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Users -->
            @if($organization->users->count() > 0)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Users ({{ $organization->users->count() }})</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Branch</th>
                                    <th>Status</th>
                                    <th>Joined</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($organization->users->take(10) as $user)
                                    <tr>
                                        <td>{{ $user->name }}</td>
                                        <td>{{ $user->email }}</td>
                                        <td>
                                            @if($user->roles->count() > 0)
                                                <span class="badge bg-secondary">{{ $user->roles->first()->name }}</span>
                                            @else
                                                <span class="text-muted">No role</span>
                                            @endif
                                        </td>
                                        <td>{{ $user->branch ? $user->branch->name : 'No branch' }}</td>
                                        <td>
                                            @if($user->status === 'active')
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-secondary">{{ ucfirst($user->status) }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $user->created_at->format('M j, Y') }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                        @if($organization->users->count() > 10)
                            <p class="text-muted text-center">Showing 10 of {{ $organization->users->count() }} users</p>
                        @endif
                    </div>
                </div>
            </div>
            @endif

            <!-- Branches -->
            @if($organization->branches->count() > 0)
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Branches ({{ $organization->branches->count() }})</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($organization->branches as $branch)
                            <div class="col-md-6 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">{{ $branch->name }}</h6>
                                        @if($branch->address)
                                            <p class="card-text text-muted small">{{ $branch->address }}</p>
                                        @endif
                                        <small class="text-muted">
                                            {{ $branch->users->count() }} users
                                        </small>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Status Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Status</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Status:</span>
                        @if($organization->is_active)
                            <span class="badge bg-success">Active</span>
                        @else
                            <span class="badge bg-danger">Inactive</span>
                        @endif
                    </div>

                    @if($organization->trial_ends_at)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>Trial:</span>
                            @if($organization->trial_ends_at->isFuture())
                                <span class="badge bg-info">{{ $organization->trial_ends_at->diffForHumans() }}</span>
                            @else
                                <span class="badge bg-warning">Expired</span>
                            @endif
                        </div>
                    @endif

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Created:</span>
                        <span>{{ $organization->created_at->format('M j, Y') }}</span>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <span>Updated:</span>
                        <span>{{ $organization->updated_at->format('M j, Y') }}</span>
                    </div>

                    <hr>

                    <div class="d-grid gap-2">
                        @if($organization->is_active)
                            <form method="POST" action="{{ route('super_admin.organizations.deactivate', $organization) }}">
                                @csrf
                                <button type="submit" class="btn btn-warning w-100"
                                        onclick="return confirm('Are you sure you want to deactivate this organization?')">
                                    <i class="fas fa-pause me-2"></i>Deactivate
                                </button>
                            </form>
                        @else
                            <form method="POST" action="{{ route('super_admin.organizations.activate', $organization) }}">
                                @csrf
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-play me-2"></i>Activate
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Subscriptions -->
            @if($organization->subscriptions->count() > 0)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Subscriptions</h6>
                </div>
                <div class="card-body">
                    @foreach($organization->subscriptions->take(5) as $subscription)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <div class="font-weight-bold">{{ $subscription->plan->name }}</div>
                                <small class="text-muted">{{ $subscription->start_date->format('M j') }} - {{ $subscription->end_date->format('M j, Y') }}</small>
                            </div>
                            <div>
                                @switch($subscription->status)
                                    @case('active')
                                        <span class="badge bg-success">Active</span>
                                        @break
                                    @case('trial')
                                        <span class="badge bg-info">Trial</span>
                                        @break
                                    @case('canceled')
                                        <span class="badge bg-warning">Canceled</span>
                                        @break
                                    @case('expired')
                                        <span class="badge bg-danger">Expired</span>
                                        @break
                                    @default
                                        <span class="badge bg-secondary">{{ ucfirst($subscription->status) }}</span>
                                @endswitch
                            </div>
                        </div>
                    @endforeach

                    @if($organization->subscriptions->count() > 5)
                        <small class="text-muted">Showing 5 of {{ $organization->subscriptions->count() }} subscriptions</small>
                    @endif
                </div>
            </div>
            @endif

            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('super_admin.subscriptions.create') }}?organization_id={{ $organization->id }}"
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-plus me-2"></i>Add Subscription
                        </a>
                        <a href="{{ route('super_admin.organizations.edit', $organization) }}"
                           class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-edit me-2"></i>Edit Details
                        </a>
                        <button type="button" class="btn btn-outline-info btn-sm"
                                onclick="alert('Feature coming soon!')">
                            <i class="fas fa-chart-bar me-2"></i>View Reports
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
