<aside class="bg-gray-800 text-white w-64 min-h-screen flex-shrink-0 flex flex-col justify-between">
    <nav class="p-4">
        <div class="space-y-2 flex flex-col">
            @foreach($menuItems as $item)
                <a href="{{ route($item['route']) }}"
                    class="flex items-center p-2 rounded hover:bg-gray-700 space-x-3 no-underline">
                    <i class="fas fa-{{ $item['icon'] }} sidebar-icon"></i>
                    <span class="flex-1">{{ $item['label'] }}</span>
                </a>
            @endforeach
        </div>
    </nav>

    <!-- Settings Link -->
    @hasrole('Organization Owner|Manager')
    <div class="space-y-1 mt-4">
        <x-sidebar-link
            href="{{ route('settings.index') }}"
            icon="cog"
            :active="request()->routeIs('settings.index')">
            App Settings
        </x-sidebar-link>

        <x-sidebar-link
            href="{{ route('settings.business') }}"
            icon="building-office"
            :active="request()->routeIs('settings.business')">
            Business Settings
        </x-sidebar-link>

        <x-sidebar-link
            href="{{ route('settings.receipt') }}"
            icon="receipt-tax"
            :active="request()->routeIs('settings.receipt')">
            Receipt Settings
        </x-sidebar-link>

        <x-sidebar-link
            href="{{ route('settings.notifications') }}"
            icon="bell"
            :active="request()->routeIs('settings.notifications')">
            Notification Settings
        </x-sidebar-link>

        <x-sidebar-link
            href="{{ route('settings.printer') }}"
            icon="printer"
            :active="request()->routeIs('settings.printer')">
            Printer Settings
        </x-sidebar-link>
    </div>
    @endhasrole
</aside>
