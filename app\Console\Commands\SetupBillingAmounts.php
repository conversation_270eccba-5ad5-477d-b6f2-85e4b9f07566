<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Subscription;

class SetupBillingAmounts extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'billing:setup-amounts';

    /**
     * The console command description.
     */
    protected $description = 'Setup amount_due for existing subscriptions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up billing amounts for existing subscriptions...');
        
        $subscriptions = Subscription::whereNull('amount_due')
                                   ->orWhere('amount_due', 0)
                                   ->with('plan')
                                   ->get();
        
        if ($subscriptions->count() === 0) {
            $this->info('No subscriptions need amount_due setup.');
            return 0;
        }
        
        $updated = 0;
        foreach ($subscriptions as $subscription) {
            $subscription->update(['amount_due' => $subscription->plan->price]);
            $updated++;
            $this->line("Updated subscription {$subscription->id} - Amount due: \${$subscription->plan->price}");
        }
        
        $this->info("✅ Successfully updated {$updated} subscriptions with amount_due values.");
        return 0;
    }
}
