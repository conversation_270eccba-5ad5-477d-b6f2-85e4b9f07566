<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Announcement;
use App\Services\LogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AnnouncementController extends Controller
{
    /**
     * Display announcements management
     */
    public function index(Request $request)
    {
        $query = Announcement::with(['creator', 'userInteractions']);

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('target_audience')) {
            $query->where('target_audience', $request->target_audience);
        }

        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->active()->published()->current();
                    break;
                case 'scheduled':
                    $query->active()->where('starts_at', '>', now());
                    break;
                case 'expired':
                    $query->where('ends_at', '<', now());
                    break;
                case 'draft':
                    $query->where(function($q) {
                        $q->whereNull('published_at')
                          ->orWhere('published_at', '>', now());
                    });
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        $announcements = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get statistics
        $stats = [
            'total' => Announcement::count(),
            'active' => Announcement::active()->published()->current()->count(),
            'scheduled' => Announcement::active()->where('starts_at', '>', now())->count(),
            'expired' => Announcement::where('ends_at', '<', now())->count(),
        ];

        return view('super_admin.announcements.index', compact('announcements', 'stats'));
    }

    /**
     * Show form to create new announcement
     */
    public function create()
    {
        return view('super_admin.announcements.create');
    }

    /**
     * Store new announcement
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|in:' . implode(',', array_keys(Announcement::getTypeOptions())),
            'priority' => 'required|in:' . implode(',', array_keys(Announcement::getPriorityOptions())),
            'target_audience' => 'required|in:' . implode(',', array_keys(Announcement::getTargetAudienceOptions())),
            'is_dismissible' => 'boolean',
            'show_on_login' => 'boolean',
            'show_on_dashboard' => 'boolean',
            'send_email' => 'boolean',
            'affected_features' => 'nullable|string',
            'starts_at' => 'nullable|date|after_or_equal:now',
            'ends_at' => 'nullable|date|after:starts_at',
            'publish_now' => 'boolean',
        ]);

        $admin = Auth::guard('super_admin')->user();

        $announcement = Announcement::create([
            'title' => $request->title,
            'content' => $request->content,
            'type' => $request->type,
            'priority' => $request->priority,
            'target_audience' => $request->target_audience,
            'is_active' => true,
            'is_dismissible' => $request->boolean('is_dismissible', true),
            'show_on_login' => $request->boolean('show_on_login'),
            'show_on_dashboard' => $request->boolean('show_on_dashboard', true),
            'send_email' => $request->boolean('send_email'),
            'affected_features' => $request->affected_features ? explode(',', $request->affected_features) : null,
            'starts_at' => $request->starts_at,
            'ends_at' => $request->ends_at,
            'published_at' => $request->boolean('publish_now') ? now() : null,
            'created_by' => $admin->id,
        ]);

        LogService::info('Announcement created', [
            'announcement_id' => $announcement->id,
            'title' => $announcement->title,
            'type' => $announcement->type,
            'target_audience' => $announcement->target_audience,
        ]);

        // Send email notifications if requested
        if ($request->boolean('send_email') && $request->boolean('publish_now')) {
            $this->sendEmailNotifications($announcement);
        }

        return redirect()->route('super.announcements.show', $announcement)
            ->with('success', 'Announcement created successfully.');
    }

    /**
     * Display announcement
     */
    public function show(Announcement $announcement)
    {
        $announcement->load(['creator', 'userInteractions.user']);
        
        // Get interaction statistics
        $stats = [
            'total_views' => $announcement->userInteractions()->where('is_read', true)->count(),
            'total_dismissals' => $announcement->userInteractions()->where('is_dismissed', true)->count(),
            'engagement_rate' => 0,
        ];

        if ($stats['total_views'] > 0) {
            $stats['engagement_rate'] = round(($stats['total_dismissals'] / $stats['total_views']) * 100, 1);
        }

        return view('super_admin.announcements.show', compact('announcement', 'stats'));
    }

    /**
     * Show form to edit announcement
     */
    public function edit(Announcement $announcement)
    {
        return view('super_admin.announcements.edit', compact('announcement'));
    }

    /**
     * Update announcement
     */
    public function update(Request $request, Announcement $announcement)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|in:' . implode(',', array_keys(Announcement::getTypeOptions())),
            'priority' => 'required|in:' . implode(',', array_keys(Announcement::getPriorityOptions())),
            'target_audience' => 'required|in:' . implode(',', array_keys(Announcement::getTargetAudienceOptions())),
            'is_dismissible' => 'boolean',
            'show_on_login' => 'boolean',
            'show_on_dashboard' => 'boolean',
            'send_email' => 'boolean',
            'affected_features' => 'nullable|string',
            'starts_at' => 'nullable|date',
            'ends_at' => 'nullable|date|after:starts_at',
        ]);

        $wasPublished = $announcement->published_at !== null;

        $announcement->update([
            'title' => $request->title,
            'content' => $request->content,
            'type' => $request->type,
            'priority' => $request->priority,
            'target_audience' => $request->target_audience,
            'is_dismissible' => $request->boolean('is_dismissible', true),
            'show_on_login' => $request->boolean('show_on_login'),
            'show_on_dashboard' => $request->boolean('show_on_dashboard', true),
            'send_email' => $request->boolean('send_email'),
            'affected_features' => $request->affected_features ? explode(',', $request->affected_features) : null,
            'starts_at' => $request->starts_at,
            'ends_at' => $request->ends_at,
        ]);

        LogService::info('Announcement updated', [
            'announcement_id' => $announcement->id,
            'title' => $announcement->title,
        ]);

        return redirect()->route('super.announcements.show', $announcement)
            ->with('success', 'Announcement updated successfully.');
    }

    /**
     * Publish announcement
     */
    public function publish(Announcement $announcement)
    {
        $announcement->update([
            'published_at' => now(),
            'is_active' => true,
        ]);

        LogService::info('Announcement published', [
            'announcement_id' => $announcement->id,
            'title' => $announcement->title,
        ]);

        // Send email notifications if enabled
        if ($announcement->send_email) {
            $this->sendEmailNotifications($announcement);
        }

        return back()->with('success', 'Announcement published successfully.');
    }

    /**
     * Unpublish announcement
     */
    public function unpublish(Announcement $announcement)
    {
        $announcement->update(['is_active' => false]);

        LogService::info('Announcement unpublished', [
            'announcement_id' => $announcement->id,
            'title' => $announcement->title,
        ]);

        return back()->with('success', 'Announcement unpublished successfully.');
    }

    /**
     * Delete announcement
     */
    public function destroy(Announcement $announcement)
    {
        $title = $announcement->title;
        
        $announcement->delete();

        LogService::info('Announcement deleted', [
            'announcement_id' => $announcement->id,
            'title' => $title,
        ]);

        return redirect()->route('super.announcements.index')
            ->with('success', 'Announcement deleted successfully.');
    }

    /**
     * Send email notifications for announcement
     */
    private function sendEmailNotifications(Announcement $announcement)
    {
        // This would integrate with your email service
        // For now, we'll just log it
        LogService::info('Email notifications sent for announcement', [
            'announcement_id' => $announcement->id,
            'target_audience' => $announcement->target_audience,
        ]);
    }
}
