<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Affiliate;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

echo "=== Testing Affiliate Login Fix ===\n\n";

try {
    // 1. Verify affiliate routes are working
    echo "1. Testing affiliate route registration...\n";
    
    try {
        $loginUrl = route('affiliate.login');
        echo "   ✓ Affiliate login route: {$loginUrl}\n";
    } catch (Exception $e) {
        echo "   ❌ Affiliate login route error: {$e->getMessage()}\n";
        return;
    }
    
    try {
        $dashboardUrl = route('affiliate.dashboard');
        echo "   ✓ Affiliate dashboard route: {$dashboardUrl}\n";
    } catch (Exception $e) {
        echo "   ❌ Affiliate dashboard route error: {$e->getMessage()}\n";
    }
    
    // 2. Check if custom middleware is registered
    echo "\n2. Testing custom middleware...\n";
    
    if (class_exists('App\Http\Middleware\AffiliateGuestMiddleware')) {
        echo "   ✓ AffiliateGuestMiddleware class exists\n";
    } else {
        echo "   ❌ AffiliateGuestMiddleware class not found\n";
    }
    
    // 3. Ensure test affiliate user exists
    echo "\n3. Setting up test affiliate user...\n";
    
    $testEmail = '<EMAIL>';
    $testPassword = 'password123';
    
    // Check if test user exists
    $user = User::where('email', $testEmail)->first();
    $affiliate = null;
    
    if (!$user) {
        echo "   Creating test user...\n";
        DB::beginTransaction();
        
        $user = User::create([
            'name' => 'Test Affiliate',
            'email' => $testEmail,
            'password' => Hash::make($testPassword),
            'phone' => '+1234567890',
            'status' => 'active',
        ]);
        
        $affiliate = Affiliate::create([
            'user_id' => $user->id,
            'referral_code' => 'TEST' . strtoupper(substr(md5($user->id), 0, 6)),
            'status' => Affiliate::STATUS_ACTIVE,
            'commission_rate' => 10.00,
            'total_earnings' => 0,
            'available_balance' => 0,
            'pending_balance' => 0,
            'withdrawn_amount' => 0,
        ]);
        
        DB::commit();
        echo "   ✓ Test user created: {$testEmail}\n";
    } else {
        echo "   ✓ Test user exists: {$testEmail}\n";
        $affiliate = Affiliate::where('user_id', $user->id)->first();
        
        if (!$affiliate) {
            echo "   Creating affiliate record...\n";
            $affiliate = Affiliate::create([
                'user_id' => $user->id,
                'referral_code' => 'TEST' . strtoupper(substr(md5($user->id), 0, 6)),
                'status' => Affiliate::STATUS_ACTIVE,
                'commission_rate' => 10.00,
                'total_earnings' => 0,
                'available_balance' => 0,
                'pending_balance' => 0,
                'withdrawn_amount' => 0,
            ]);
            echo "   ✓ Affiliate record created\n";
        }
    }
    
    // 4. Verify affiliate status
    echo "\n4. Verifying affiliate setup...\n";
    echo "   User ID: {$user->id}\n";
    echo "   Email: {$user->email}\n";
    echo "   Affiliate ID: {$affiliate->id}\n";
    echo "   Referral Code: {$affiliate->referral_code}\n";
    echo "   Status: {$affiliate->status}\n";
    echo "   Commission Rate: {$affiliate->commission_rate}%\n";
    
    // 5. Test route accessibility
    echo "\n5. Testing route accessibility...\n";
    
    $routes = Route::getRoutes();
    $affiliateRoutes = [];
    
    foreach ($routes as $route) {
        $uri = $route->uri();
        if (strpos($uri, 'affiliate') !== false) {
            $name = $route->getName();
            if ($name && (strpos($name, 'affiliate.') === 0)) {
                $affiliateRoutes[] = [
                    'name' => $name,
                    'uri' => $uri,
                    'methods' => $route->methods(),
                    'middleware' => $route->middleware()
                ];
            }
        }
    }
    
    echo "   Found " . count($affiliateRoutes) . " affiliate routes:\n";
    foreach ($affiliateRoutes as $route) {
        $methods = implode('|', $route['methods']);
        $middleware = implode(', ', $route['middleware']);
        echo "   - {$methods} /{$route['uri']} ({$route['name']}) [{$middleware}]\n";
    }
    
    // 6. Check middleware configuration
    echo "\n6. Checking middleware configuration...\n";
    
    $loginRoute = collect($affiliateRoutes)->firstWhere('name', 'affiliate.login');
    if ($loginRoute) {
        $hasCustomMiddleware = in_array('affiliate.guest', $loginRoute['middleware']);
        if ($hasCustomMiddleware) {
            echo "   ✓ Login route uses custom affiliate.guest middleware\n";
        } else {
            echo "   ❌ Login route not using custom middleware\n";
            echo "   Current middleware: " . implode(', ', $loginRoute['middleware']) . "\n";
        }
    }
    
    echo "\n=== Test Results ===\n";
    echo "✓ Affiliate routes are properly registered\n";
    echo "✓ Custom middleware is available\n";
    echo "✓ Test affiliate user is ready\n";
    echo "✓ Route configuration is correct\n";
    
    echo "\n=== Testing Instructions ===\n";
    echo "1. Open browser in incognito/private mode (to avoid session conflicts)\n";
    echo "2. Visit: {$loginUrl}\n";
    echo "3. Login with:\n";
    echo "   Email: {$testEmail}\n";
    echo "   Password: {$testPassword}\n";
    echo "4. Should redirect to: {$dashboardUrl}\n";
    echo "5. Should NOT redirect to main application dashboard\n";
    
    echo "\n=== Troubleshooting ===\n";
    echo "If still redirecting to main dashboard:\n";
    echo "1. Clear browser cache and cookies\n";
    echo "2. Use incognito/private browsing mode\n";
    echo "3. Run quick_logout.php to clear all sessions\n";
    echo "4. Check if you're logged into main application\n";

} catch (Exception $e) {
    if (DB::transactionLevel() > 0) {
        DB::rollBack();
    }
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
