<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Organization;
use App\Models\Plan;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class OrganizationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Organization::with(['plan', 'users', 'branches'])
            ->withCount(['users', 'branches', 'subscriptions']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by plan
        if ($request->has('plan') && $request->plan) {
            $query->where('plan_id', $request->plan);
        }

        $organizations = $query->latest()->paginate(15);
        $plans = Plan::where('is_active', true)->get();

        // Get statistics for the index page
        $stats = [
            'total_organizations' => Organization::count(),
            'active_organizations' => Organization::where('is_active', true)->count(),
            'trial_organizations' => Organization::whereNotNull('trial_ends_at')
                ->where('trial_ends_at', '>', now())->count(),
            'monthly_revenue' => DB::table('subscriptions')
                ->where('status', 'active')
                ->whereMonth('created_at', now()->month)
                ->sum('amount_paid') ?? 0,
        ];

        return view('super_admin.organizations.index', compact('organizations', 'plans', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $plans = Plan::where('is_active', true)->get();
        return view('super_admin.organizations.create', compact('plans'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:organizations,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'website' => 'nullable|url|max:255',
            'description' => 'nullable|string|max:1000',
            'plan_id' => 'required|exists:plans,id',
            'trial_ends_at' => 'nullable|date|after:today',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        DB::beginTransaction();
        try {
            // Handle logo upload
            if ($request->hasFile('logo')) {
                $logoName = time() . '.' . $request->logo->extension();
                $request->logo->storeAs('public/logos', $logoName);
                $validated['logo'] = $logoName;
            }

            $validated['is_active'] = true;
            $organization = Organization::create($validated);

            DB::commit();
            return redirect()->route('super.organizations.index')
                ->with('success', 'Organization created successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to create organization: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Organization $organization)
    {
        $organization->load(['plan', 'users.roles', 'branches', 'subscriptions.plan']);

        // Get organization statistics
        $stats = [
            'total_users' => $organization->users()->count(),
            'active_users' => $organization->users()->where('status', 'active')->count(),
            'total_branches' => $organization->branches()->count(),
            'total_orders' => 0, // Placeholder - will be implemented when Order model is ready
            'monthly_revenue' => $organization->subscriptions()
                ->where('status', 'active')
                ->whereMonth('created_at', now()->month)
                ->sum('amount_paid'),
        ];

        return view('super_admin.organizations.show', compact('organization', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Organization $organization)
    {
        $plans = Plan::where('is_active', true)->get();
        return view('super_admin.organizations.edit', compact('organization', 'plans'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Organization $organization)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('organizations')->ignore($organization->id)],
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'website' => 'nullable|url|max:255',
            'description' => 'nullable|string|max:1000',
            'plan_id' => 'required|exists:plans,id',
            'trial_ends_at' => 'nullable|date',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        DB::beginTransaction();
        try {
            // Handle logo upload
            if ($request->hasFile('logo')) {
                // Delete old logo
                if ($organization->logo && Storage::exists('public/logos/' . $organization->logo)) {
                    Storage::delete('public/logos/' . $organization->logo);
                }

                $logoName = time() . '.' . $request->logo->extension();
                $request->logo->storeAs('public/logos', $logoName);
                $validated['logo'] = $logoName;
            }

            $organization->update($validated);

            DB::commit();
            return redirect()->route('super.organizations.show', $organization)
                ->with('success', 'Organization updated successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to update organization: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Organization $organization)
    {
        DB::beginTransaction();
        try {
            // Delete logo if exists
            if ($organization->logo && Storage::exists('public/logos/' . $organization->logo)) {
                Storage::delete('public/logos/' . $organization->logo);
            }

            $organization->delete();

            DB::commit();
            return redirect()->route('super.organizations.index')
                ->with('success', 'Organization deleted successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to delete organization: ' . $e->getMessage()]);
        }
    }

    /**
     * Activate an organization
     */
    public function activate(Organization $organization)
    {
        $organization->update(['is_active' => true]);
        return back()->with('success', 'Organization activated successfully.');
    }

    /**
     * Deactivate an organization
     */
    public function deactivate(Organization $organization)
    {
        $organization->update(['is_active' => false]);
        return back()->with('success', 'Organization deactivated successfully.');
    }
}
