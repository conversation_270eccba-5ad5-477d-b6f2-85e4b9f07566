<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    protected $fillable = [
        // Organization relationship
        'organization_id',
        
        // App appearance
        'app_name',
        'app_slogan',
        'theme_mode',
        'primary_color',
        'sidebar_color',
        'site_title',
        'site_logo_path',
        'favicon_path',

        // Company information
        'company_address',
        'company_phone',
        'company_email',
        'organization_name',
        'business_registration_number',
        'tax_identification_number',

        // Business settings
        'default_departments',
        'require_payment_upfront',
        'default_payment_due_days',

        // Notification settings
        'order_confirmation_emails',
        'order_status_update_emails',
        'payment_reminder_emails',

        // Printer settings
        'thermal_printer_name',
        'thermal_paper_width',

        // Currency and numbering
        'currency_symbol',
        'currency_code',
        'timezone',
        'order_number_prefix',
        'include_year_in_order_number',
        'order_number_format',
        'order_number_digits'
    ];

    protected $casts = [
        'theme_mode' => 'string',
        'primary_color' => 'string',
        'sidebar_color' => 'string',
        'require_payment_upfront' => 'boolean',
        'default_payment_due_days' => 'integer',
        'order_confirmation_emails' => 'boolean',
        'order_status_update_emails' => 'boolean',
        'payment_reminder_emails' => 'boolean',
        'thermal_paper_width' => 'integer',
        'include_year_in_order_number' => 'boolean',
        'order_number_format' => 'string',
        'order_number_digits' => 'integer',
    ];

    public function getLogoUrlAttribute()
    {
        return $this->site_logo_path ?
            asset('storage/' . $this->site_logo_path) :
            asset('branding/logo.png');
    }

    public function getFaviconUrlAttribute()
    {
        return $this->favicon_path ?
            asset('storage/' . $this->favicon_path) :
            asset('branding/favicon.png');
    }

    public function getDefaultDepartmentsArrayAttribute()
    {
        if (empty($this->default_departments)) {
            return ['Design', 'Production', 'Digital', 'Large Format'];
        }

        return array_map('trim', explode(',', $this->default_departments));
    }

    /**
     * Get the organization that owns the settings.
     */
    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

}
