<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Profile Dropdown</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body { padding: 2rem; background-color: #f8f9fa; }
        .debug-section { background: white; padding: 2rem; margin-bottom: 2rem; border-radius: 0.5rem; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        .dropdown-menu { z-index: 1060 !important; min-width: 220px; box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; }
        .dropdown { position: relative !important; }
        .dropdown-menu.show { display: block !important; opacity: 1 !important; visibility: visible !important; }
        .dropdown-header { background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; padding: 0.75rem 1rem; }
        #debug-log { height: 200px; overflow-y: auto; background: #f8f9fa; padding: 1rem; border-radius: 0.25rem; font-family: monospace; font-size: 0.875rem; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Profile Dropdown Debug Page</h1>
        <p class="text-muted">This page helps debug the profile dropdown functionality.</p>
        
        <!-- Test Dropdown -->
        <div class="debug-section">
            <h3>Test Dropdown</h3>
            <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded">
                <h4>Dashboard</h4>
                
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle d-flex align-items-center"
                            type="button" id="userDropdown" data-bs-toggle="dropdown"
                            aria-expanded="false" title="User Menu" style="min-width: 120px;">
                        <i class="fas fa-user me-1"></i>
                        <span class="d-none d-md-inline">Test User</span>
                        <span class="d-md-none">Test U.</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="userDropdown" 
                        style="min-width: 220px; z-index: 1060;">
                        <li>
                            <h6 class="dropdown-header d-flex align-items-center">
                                <i class="fas fa-user-circle me-2 text-primary"></i>
                                <div>
                                    <div class="fw-bold">Test User</div>
                                    <small class="text-muted"><EMAIL></small>
                                </div>
                            </h6>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item d-flex align-items-center" href="#" onclick="logAction('Profile clicked')">
                                <i class="fas fa-user-cog me-3 text-info"></i>
                                <div>
                                    <div>Profile Settings</div>
                                    <small class="text-muted">Manage your account</small>
                                </div>
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <button type="button" class="dropdown-item text-danger d-flex align-items-center" 
                                    onclick="logAction('Logout clicked')">
                                <i class="fas fa-sign-out-alt me-3"></i>
                                <div>
                                    <div>Logout</div>
                                    <small class="text-muted">Sign out of your account</small>
                                </div>
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Debug Controls -->
        <div class="debug-section">
            <h3>Debug Controls</h3>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-primary" onclick="testDropdown()">Test Dropdown</button>
                <button type="button" class="btn btn-success" onclick="showDropdown()">Force Show</button>
                <button type="button" class="btn btn-warning" onclick="hideDropdown()">Force Hide</button>
                <button type="button" class="btn btn-info" onclick="checkBootstrap()">Check Bootstrap</button>
                <button type="button" class="btn btn-secondary" onclick="clearLog()">Clear Log</button>
            </div>
        </div>
        
        <!-- Debug Log -->
        <div class="debug-section">
            <h3>Debug Log</h3>
            <div id="debug-log"></div>
        </div>
        
        <!-- Instructions -->
        <div class="debug-section">
            <h3>Instructions</h3>
            <ol>
                <li>Click the "Test User" button above to test the dropdown</li>
                <li>Check the debug log for any errors or messages</li>
                <li>Use the debug controls to force show/hide the dropdown</li>
                <li>Open browser console (F12) for additional debugging info</li>
            </ol>
            
            <div class="alert alert-info mt-3">
                <strong>Expected Behavior:</strong>
                <ul class="mb-0">
                    <li>Dropdown should open when clicked</li>
                    <li>Profile and Logout options should be visible</li>
                    <li>Clicking options should log actions</li>
                    <li>Dropdown should close when clicking outside</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let logElement = document.getElementById('debug-log');
        
        function logAction(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            logElement.innerHTML = '';
        }
        
        function testDropdown() {
            const userDropdown = document.getElementById('userDropdown');
            const dropdown = bootstrap.Dropdown.getInstance(userDropdown);
            if (dropdown) {
                dropdown.toggle();
                logAction('Dropdown toggled via instance');
            } else {
                logAction('No dropdown instance found, creating new one');
                const newDropdown = new bootstrap.Dropdown(userDropdown);
                newDropdown.toggle();
            }
        }
        
        function showDropdown() {
            const userDropdown = document.getElementById('userDropdown');
            const dropdown = bootstrap.Dropdown.getInstance(userDropdown) || new bootstrap.Dropdown(userDropdown);
            dropdown.show();
            logAction('Dropdown forced to show');
        }
        
        function hideDropdown() {
            const userDropdown = document.getElementById('userDropdown');
            const dropdown = bootstrap.Dropdown.getInstance(userDropdown);
            if (dropdown) {
                dropdown.hide();
                logAction('Dropdown forced to hide');
            } else {
                logAction('No dropdown instance to hide');
            }
        }
        
        function checkBootstrap() {
            if (typeof bootstrap !== 'undefined') {
                logAction('✅ Bootstrap is loaded: ' + bootstrap.Dropdown);
            } else {
                logAction('❌ Bootstrap is NOT loaded');
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            logAction('Page loaded, initializing...');
            
            // Check Bootstrap
            checkBootstrap();
            
            // Initialize dropdown
            const userDropdown = document.getElementById('userDropdown');
            if (userDropdown) {
                logAction('User dropdown element found');
                
                // Add event listeners
                userDropdown.addEventListener('click', function(e) {
                    logAction('Dropdown button clicked');
                });
                
                userDropdown.addEventListener('shown.bs.dropdown', function () {
                    logAction('✅ Dropdown shown successfully');
                });
                
                userDropdown.addEventListener('hidden.bs.dropdown', function () {
                    logAction('Dropdown hidden');
                });
                
                userDropdown.addEventListener('show.bs.dropdown', function () {
                    logAction('Dropdown showing...');
                });
                
                // Initialize dropdown instance
                try {
                    const dropdown = new bootstrap.Dropdown(userDropdown);
                    logAction('✅ Dropdown instance created successfully');
                } catch (error) {
                    logAction('❌ Error creating dropdown: ' + error.message);
                }
            } else {
                logAction('❌ User dropdown element not found');
            }
        });
    </script>
</body>
</html>
