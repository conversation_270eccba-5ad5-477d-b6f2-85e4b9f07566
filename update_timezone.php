<?php
require_once 'vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;

// Update the timezone setting
$updated = DB::table('settings')
    ->where('id', 1)
    ->update(['timezone' => 'Africa/Lagos']);

if ($updated) {
    echo "Timezone successfully updated to Africa/Lagos (West African Time)\n";
} else {
    echo "Failed to update timezone\n";
}

// Verify the change
$settings = DB::table('settings')->select('timezone')->first();
echo "Current timezone setting: " . $settings->timezone . "\n";
