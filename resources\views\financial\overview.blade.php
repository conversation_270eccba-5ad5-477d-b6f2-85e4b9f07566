@extends('layouts.app')

@section('title', 'Financial Overview')

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold">Financial Overview</h2>

                <div class="flex space-x-4">
                    <!-- Period Selector -->
                    <select id="period" class="rounded-md border-gray-300" onchange="updatePeriod(this.value)">
                        <option value="daily" {{ $period === 'daily' ? 'selected' : '' }}>Daily</option>
                        <option value="monthly" {{ $period === 'monthly' ? 'selected' : '' }}>Monthly</option>
                        <option value="yearly" {{ $period === 'yearly' ? 'selected' : '' }}>Yearly</option>
                    </select>

                    <!-- Date Picker -->
                    <input type="date" id="date" value="{{ $date }}"
                        class="rounded-md border-gray-300"
                        onchange="updateDate(this.value)">
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6 border border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Total Revenue</h3>
                    <p class="text-3xl font-bold text-green-600">{{ format_money($paymentStats->total_revenue ?? 0) }}</p>
                </div>

                <div class="bg-white rounded-lg shadow p-6 border border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Amount Received</h3>
                    <p class="text-3xl font-bold text-blue-600">{{ format_money($paymentStats->total_paid ?? 0) }}</p>
                </div>

                <div class="bg-white rounded-lg shadow p-6 border border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Pending Payments</h3>
                    <p class="text-3xl font-bold text-red-600">{{ format_money($paymentStats->total_pending ?? 0) }}</p>
                </div>

                <div class="bg-white rounded-lg shadow p-6 border border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Total Orders</h3>
                    <p class="text-3xl font-bold text-gray-600">{{ number_format($paymentStats->total_orders ?? 0) }}</p>
                </div>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 gap-6">
                <div class="bg-white rounded-lg shadow p-6 border border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue Trend</h3>
                    <canvas id="revenueChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
function updatePeriod(period) {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('period', period);
    window.location.href = currentUrl.toString();
}

function updateDate(date) {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('date', date);
    window.location.href = currentUrl.toString();
}

document.addEventListener('DOMContentLoaded', function() {
    const summaryData = @json($summaryData);

    // Prepare data for charts
    const labels = summaryData.map(item =>
        @if($period === 'yearly')
            `Month ${item.month}`
        @elseif($period === 'monthly')
            item.date
        @else
            `Hour ${item.hour}`
        @endif
    );

    const revenueData = summaryData.map(item => item.revenue);
    const ordersData = summaryData.map(item => item.orders_count);

    // Revenue Chart
    new Chart(document.getElementById('revenueChart'), {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Revenue',
                    data: revenueData,
                    borderColor: 'rgb(34, 197, 94)',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    borderWidth: 2,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: value => '{{ currency_symbol() }}' + value.toLocaleString()
                    }
                }
            }
        }
    });
});
</script>
@endpush
@endsection
