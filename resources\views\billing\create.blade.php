@extends('layouts.app')

@section('title', 'Submit Payment')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Submit Payment</h1>
                <a href="{{ route('billing.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Billing
                </a>
            </div>

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Payment Information</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('billing.store') }}">
                                @csrf

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Outstanding Balance:</strong> {{ format_price($outstandingBalance) }}
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_reference" class="form-label">Payment Reference <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('payment_reference') is-invalid @enderror"
                                                   id="payment_reference" name="payment_reference" value="{{ old('payment_reference') }}" required
                                                   placeholder="Bank transfer reference, receipt number, etc.">
                                            <div class="form-text">Enter the reference number from your bank transfer or payment receipt</div>
                                            @error('payment_reference')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="amount" class="form-label">Amount <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text">{{ user_currency_symbol() }}</span>
                                                <input type="number" class="form-control @error('amount') is-invalid @enderror"
                                                       id="amount" name="amount" value="{{ old('amount', app(\App\Services\CurrencyService::class)->convert($outstandingBalance, 'USD', user_currency())) }}"
                                                       step="0.01" min="0.01" max="{{ app(\App\Services\CurrencyService::class)->convert($outstandingBalance, 'USD', user_currency()) }}" required>
                                            </div>
                                            <div class="form-text">Maximum: {{ format_price($outstandingBalance) }}</div>
                                            @error('amount')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_method" class="form-label">Payment Method <span class="text-danger">*</span></label>
                                            <select class="form-select @error('payment_method') is-invalid @enderror"
                                                    id="payment_method" name="payment_method" required>
                                                <option value="">Select Payment Method</option>
                                                <option value="bank_transfer" {{ old('payment_method') == 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                                                <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>Cash Deposit</option>
                                                <option value="check" {{ old('payment_method') == 'check' ? 'selected' : '' }}>Check</option>
                                                <option value="mobile_money" {{ old('payment_method') == 'mobile_money' ? 'selected' : '' }}>Mobile Money</option>
                                            </select>
                                            @error('payment_method')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="payment_date" class="form-label">Payment Date <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control @error('payment_date') is-invalid @enderror"
                                                   id="payment_date" name="payment_date" value="{{ old('payment_date', date('Y-m-d')) }}"
                                                   max="{{ date('Y-m-d') }}" required>
                                            @error('payment_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Additional Notes</label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror"
                                              id="notes" name="notes" rows="3" placeholder="Any additional information about this payment">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('billing.index') }}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i> Submit Payment
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-university me-2"></i>
                                Payment Accounts
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Important:</strong> Make your payment first, then submit this form with the payment details.
                            </div>

                            @if($paymentAccounts->count() > 0)
                                @foreach($paymentAccounts as $account)
                                    <div class="card mb-3 {{ $account->is_primary ? 'border-primary' : 'border-light' }}">
                                        <div class="card-header {{ $account->is_primary ? 'bg-primary text-white' : 'bg-light' }} p-2">
                                            @if($account->is_primary)
                                                <span class="badge bg-light text-primary me-2">Primary</span>
                                            @endif
                                            <strong>{{ $account->bank_name }}</strong>
                                        </div>
                                        <div class="card-body p-3">
                                            <div class="mb-2">
                                                <strong>Account Name:</strong><br>
                                                <span class="text-muted">{{ $account->account_name }}</span>
                                            </div>

                                            <div class="mb-2">
                                                <strong>Account Number:</strong><br>
                                                <code class="bg-light p-1 rounded">{{ $account->account_number }}</code>
                                                <button class="btn btn-sm btn-outline-secondary ms-1"
                                                        onclick="copyToClipboard('{{ $account->account_number }}')"
                                                        title="Copy account number">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>

                                            <div class="mb-2">
                                                <strong>Type:</strong>
                                                <span class="badge bg-secondary">{{ ucfirst($account->account_type) }}</span>
                                            </div>

                                            @if($account->routing_number)
                                                <div class="mb-2">
                                                    <strong>Routing:</strong>
                                                    <code class="bg-light p-1 rounded">{{ $account->routing_number }}</code>
                                                    <button class="btn btn-sm btn-outline-secondary ms-1"
                                                            onclick="copyToClipboard('{{ $account->routing_number }}')"
                                                            title="Copy routing number">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                            @endif

                                            @if($account->swift_code)
                                                <div class="mb-2">
                                                    <strong>SWIFT:</strong>
                                                    <code class="bg-light p-1 rounded">{{ $account->swift_code }}</code>
                                                    <button class="btn btn-sm btn-outline-secondary ms-1"
                                                            onclick="copyToClipboard('{{ $account->swift_code }}')"
                                                            title="Copy SWIFT code">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                            @endif

                                            @if($account->additional_instructions)
                                                <div class="alert alert-info p-2 mt-2">
                                                    <small>
                                                        <strong>Instructions:</strong><br>
                                                        {{ $account->additional_instructions }}
                                                    </small>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    No payment accounts available. Please contact support.
                                </div>
                            @endif

                            <div class="mt-3">
                                <h6><i class="fas fa-list-ol text-info"></i> Next Steps:</h6>
                                <ol class="small">
                                    <li>Make payment to one of the accounts above</li>
                                    <li>Keep your payment receipt/reference</li>
                                    <li>Fill out this form with payment details</li>
                                    <li>Wait for admin approval</li>
                                    <li>Download your invoice once approved</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast for copy notifications -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="copyToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i class="fas fa-check-circle text-success me-2"></i>
            <strong class="me-auto">Copied!</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            Text copied to clipboard successfully.
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show toast notification
        const toast = new bootstrap.Toast(document.getElementById('copyToast'));
        toast.show();
    }).catch(function(err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        // Show toast notification
        const toast = new bootstrap.Toast(document.getElementById('copyToast'));
        toast.show();
    });
}
</script>
@endsection
