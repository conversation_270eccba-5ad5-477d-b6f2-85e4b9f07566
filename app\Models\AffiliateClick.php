<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class AffiliateClick extends Model
{
    use HasFactory;

    protected $fillable = [
        'affiliate_id',
        'affiliate_code',
        'ip_address',
        'user_agent',
        'referrer',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_content',
        'utm_term',
        'country',
        'city',
        'device_type',
        'browser',
        'platform',
        'is_unique',
        'converted',
        'clicked_at',
    ];

    protected $casts = [
        'is_unique' => 'boolean',
        'converted' => 'boolean',
        'clicked_at' => 'datetime',
    ];

    /**
     * Get the affiliate that owns this click
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(Affiliate::class);
    }

    /**
     * Scope for unique clicks only
     */
    public function scopeUnique($query)
    {
        return $query->where('is_unique', true);
    }

    /**
     * Scope for converted clicks only
     */
    public function scopeConverted($query)
    {
        return $query->where('converted', true);
    }

    /**
     * Scope for clicks within date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('clicked_at', [$startDate, $endDate]);
    }

    /**
     * Scope for today's clicks
     */
    public function scopeToday($query)
    {
        return $query->whereDate('clicked_at', Carbon::today());
    }

    /**
     * Scope for this week's clicks
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('clicked_at', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ]);
    }

    /**
     * Scope for this month's clicks
     */
    public function scopeThisMonth($query)
    {
        return $query->whereBetween('clicked_at', [
            Carbon::now()->startOfMonth(),
            Carbon::now()->endOfMonth()
        ]);
    }

    /**
     * Get device type from user agent
     */
    public static function getDeviceType($userAgent): string
    {
        if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
            if (preg_match('/iPad/', $userAgent)) {
                return 'tablet';
            }
            return 'mobile';
        }
        return 'desktop';
    }

    /**
     * Get browser from user agent
     */
    public static function getBrowser($userAgent): string
    {
        if (preg_match('/Chrome/', $userAgent)) {
            return 'Chrome';
        } elseif (preg_match('/Firefox/', $userAgent)) {
            return 'Firefox';
        } elseif (preg_match('/Safari/', $userAgent)) {
            return 'Safari';
        } elseif (preg_match('/Edge/', $userAgent)) {
            return 'Edge';
        } elseif (preg_match('/Opera/', $userAgent)) {
            return 'Opera';
        }
        return 'Other';
    }

    /**
     * Get platform from user agent
     */
    public static function getPlatform($userAgent): string
    {
        if (preg_match('/Windows/', $userAgent)) {
            return 'Windows';
        } elseif (preg_match('/Mac/', $userAgent)) {
            return 'macOS';
        } elseif (preg_match('/Linux/', $userAgent)) {
            return 'Linux';
        } elseif (preg_match('/Android/', $userAgent)) {
            return 'Android';
        } elseif (preg_match('/iOS|iPhone|iPad/', $userAgent)) {
            return 'iOS';
        }
        return 'Other';
    }

    /**
     * Check if this is a unique click (first from this IP in 24 hours)
     */
    public static function isUniqueClick($affiliateId, $ipAddress): bool
    {
        $yesterday = Carbon::now()->subDay();
        
        return !static::where('affiliate_id', $affiliateId)
            ->where('ip_address', $ipAddress)
            ->where('clicked_at', '>', $yesterday)
            ->exists();
    }

    /**
     * Get click statistics for an affiliate
     */
    public static function getClickStats($affiliateId, $period = 'all'): array
    {
        $query = static::where('affiliate_id', $affiliateId);

        switch ($period) {
            case 'today':
                $query->today();
                break;
            case 'week':
                $query->thisWeek();
                break;
            case 'month':
                $query->thisMonth();
                break;
        }

        $totalClicks = $query->count();
        $uniqueClicks = $query->unique()->count();
        $convertedClicks = $query->converted()->count();

        return [
            'total_clicks' => $totalClicks,
            'unique_clicks' => $uniqueClicks,
            'converted_clicks' => $convertedClicks,
            'conversion_rate' => $uniqueClicks > 0 ? round(($convertedClicks / $uniqueClicks) * 100, 2) : 0,
        ];
    }
}
