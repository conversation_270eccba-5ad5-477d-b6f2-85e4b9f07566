<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Currency extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'symbol',
        'is_base_currency',
        'is_active',
    ];

    protected $casts = [
        'is_base_currency' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the base currency (USD)
     */
    public static function getBaseCurrency()
    {
        return static::where('is_base_currency', true)->first();
    }

    /**
     * Get active currencies
     */
    public static function getActiveCurrencies()
    {
        return static::where('is_active', true)->get();
    }

    /**
     * Get currency by code
     */
    public static function getByCode($code)
    {
        return static::where('code', $code)->where('is_active', true)->first();
    }

    /**
     * Check if this is the base currency
     */
    public function isBaseCurrency()
    {
        return $this->is_base_currency;
    }

    /**
     * Get exchange rates from this currency
     */
    public function ratesFrom()
    {
        return $this->hasMany(CurrencyRate::class, 'from_currency', 'code');
    }

    /**
     * Get exchange rates to this currency
     */
    public function ratesTo()
    {
        return $this->hasMany(CurrencyRate::class, 'to_currency', 'code');
    }
}
