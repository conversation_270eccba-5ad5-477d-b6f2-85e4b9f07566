<?php

namespace App\Jobs;

use App\Models\SubscriptionPayment;
use App\Services\CommissionCalculationService;
use App\Services\ReferralTrackingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessCommissionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $subscriptionPayment;

    /**
     * Create a new job instance.
     */
    public function __construct(SubscriptionPayment $subscriptionPayment)
    {
        $this->subscriptionPayment = $subscriptionPayment;
    }

    /**
     * Execute the job.
     */
    public function handle(
        CommissionCalculationService $commissionCalculationService,
        ReferralTrackingService $referralTrackingService
    ): void {
        try {
            Log::info('Processing commission job started', [
                'payment_id' => $this->subscriptionPayment->id,
                'organization_id' => $this->subscriptionPayment->organization_id,
                'amount' => $this->subscriptionPayment->amount
            ]);

            // Convert referral if this is the first payment
            $referralTrackingService->convertReferral($this->subscriptionPayment->organization);

            // Calculate and create commission
            $earning = $commissionCalculationService->calculateCommission($this->subscriptionPayment);

            if ($earning) {
                Log::info('Commission processed successfully', [
                    'earning_id' => $earning->id,
                    'affiliate_id' => $earning->affiliate_id,
                    'amount' => $earning->amount,
                    'payment_id' => $this->subscriptionPayment->id
                ]);

                // Check for milestone bonuses
                $affiliate = $earning->affiliate;
                $bonuses = $commissionCalculationService->checkMilestoneBonuses($affiliate);
                
                if (!empty($bonuses)) {
                    Log::info('Milestone bonuses processed', [
                        'affiliate_id' => $affiliate->id,
                        'bonus_count' => count($bonuses),
                        'total_bonus_amount' => array_sum(array_column($bonuses, 'amount'))
                    ]);
                }

                // Check for tier upgrade bonus
                $tierBonus = $commissionCalculationService->checkTierUpgrade($affiliate);
                
                if ($tierBonus) {
                    Log::info('Tier upgrade bonus processed', [
                        'affiliate_id' => $affiliate->id,
                        'bonus_amount' => $tierBonus->amount
                    ]);
                }

                // TODO: Send notification emails to affiliate about new earnings
                
            } else {
                Log::info('No commission processed - no referral found or conditions not met', [
                    'payment_id' => $this->subscriptionPayment->id,
                    'organization_id' => $this->subscriptionPayment->organization_id
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Commission processing job failed', [
                'payment_id' => $this->subscriptionPayment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Re-throw the exception to mark the job as failed
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Commission processing job permanently failed', [
            'payment_id' => $this->subscriptionPayment->id,
            'error' => $exception->getMessage()
        ]);

        // TODO: Send notification to admins about failed commission processing
    }
}
