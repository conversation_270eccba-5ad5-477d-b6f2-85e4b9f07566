<?php

namespace App\Services;

use App\Models\Affiliate;
use App\Models\AffiliateEarning;
use App\Models\AffiliateReferral;
use App\Models\AffiliateSetting;
use App\Models\Organization;
use App\Models\SubscriptionPayment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CommissionCalculationService
{
    /**
     * Calculate and create commission for a subscription payment
     */
    public function calculateCommission(SubscriptionPayment $payment): ?AffiliateEarning
    {
        try {
            Log::info('Starting commission calculation', [
                'payment_id' => $payment->id,
                'organization_id' => $payment->organization_id,
                'amount' => $payment->amount
            ]);

            // Find referral for this organization (check both pending and converted)
            $referral = AffiliateReferral::where('organization_id', $payment->organization_id)
                ->whereIn('status', [AffiliateReferral::STATUS_PENDING, AffiliateReferral::STATUS_CONVERTED])
                ->first();

            if (!$referral) {
                Log::info('No referral found for organization', [
                    'organization_id' => $payment->organization_id
                ]);
                return null;
            }

            Log::info('Found referral', [
                'referral_id' => $referral->id,
                'status' => $referral->status,
                'affiliate_id' => $referral->affiliate_id
            ]);

            // Convert referral if it's still pending
            if ($referral->status === AffiliateReferral::STATUS_PENDING) {
                $referral->markAsConverted();
                Log::info('Referral converted to converted status', [
                    'referral_id' => $referral->id
                ]);
            }

            $affiliate = $referral->affiliate;
            $settings = AffiliateSetting::getInstance();

            Log::info('Checking affiliate program settings', [
                'program_active' => $settings->isProgramActive(),
                'recurring_commissions' => $settings->hasRecurringCommissions(),
                'auto_approve_earnings' => $settings->shouldAutoApproveEarnings()
            ]);

            // Check if program is active
            if (!$settings->isProgramActive()) {
                Log::info('Affiliate program is not active');
                return null;
            }

            // Check if this is a recurring commission scenario
            if (!$settings->hasRecurringCommissions()) {
                // Only pay commission on first payment
                $existingEarning = AffiliateEarning::where('affiliate_id', $affiliate->id)
                    ->where('organization_id', $payment->organization_id)
                    ->where('type', AffiliateEarning::TYPE_COMMISSION)
                    ->first();

                if ($existingEarning) {
                    Log::info('Commission already paid for this organization', [
                        'affiliate_id' => $affiliate->id,
                        'organization_id' => $payment->organization_id,
                        'existing_earning_id' => $existingEarning->id
                    ]);
                    return null;
                }
            }

            // Calculate commission amount
            $commissionRate = $settings->getCommissionRateForAffiliate($affiliate);
            $commissionAmount = ($payment->amount * $commissionRate) / 100;

            Log::info('Calculating commission', [
                'commission_rate' => $commissionRate,
                'payment_amount' => $payment->amount,
                'commission_amount' => $commissionAmount
            ]);

            // Create earning record
            $earning = $this->createEarning([
                'affiliate_id' => $affiliate->id,
                'referral_id' => $referral->id,
                'organization_id' => $payment->organization_id,
                'subscription_payment_id' => $payment->id,
                'amount' => $commissionAmount,
                'type' => AffiliateEarning::TYPE_COMMISSION,
                'description' => "Commission for subscription payment #{$payment->id}",
                'status' => $settings->shouldAutoApproveEarnings() ?
                    AffiliateEarning::STATUS_APPROVED :
                    AffiliateEarning::STATUS_PENDING,
            ]);

            Log::info('Commission earning created', [
                'earning_id' => $earning->id,
                'amount' => $earning->amount,
                'status' => $earning->status
            ]);

            // Update referral commission tracking
            $referral->commission_earned += $commissionAmount;
            $referral->save();

            Log::info('Commission calculated successfully', [
                'earning_id' => $earning->id,
                'affiliate_id' => $affiliate->id,
                'amount' => $commissionAmount,
                'payment_id' => $payment->id
            ]);

            return $earning;
        } catch (\Exception $e) {
            Log::error('Failed to calculate commission', [
                'error' => $e->getMessage(),
                'payment_id' => $payment->id
            ]);
            return null;
        }
    }

    /**
     * Create an earning record
     */
    public function createEarning(array $data): AffiliateEarning
    {
        return DB::transaction(function () use ($data) {
            $earning = AffiliateEarning::create(array_merge($data, [
                'earned_at' => now(),
            ]));

            // Update affiliate balance
            $affiliate = $earning->affiliate;
            $affiliate->addEarning($earning->amount, $earning->status);

            return $earning;
        });
    }

    /**
     * Create bonus earning
     */
    public function createBonus(Affiliate $affiliate, float $amount, string $description, bool $autoApprove = false): AffiliateEarning
    {
        $settings = AffiliateSetting::getInstance();

        return $this->createEarning([
            'affiliate_id' => $affiliate->id,
            'organization_id' => null,
            'amount' => $amount,
            'type' => AffiliateEarning::TYPE_BONUS,
            'description' => $description,
            'status' => ($autoApprove || $settings->shouldAutoApproveEarnings()) ?
                AffiliateEarning::STATUS_APPROVED :
                AffiliateEarning::STATUS_PENDING,
        ]);
    }

    /**
     * Create adjustment earning (can be positive or negative)
     */
    public function createAdjustment(Affiliate $affiliate, float $amount, string $description, ?int $approvedBy = null): AffiliateEarning
    {
        $type = $amount >= 0 ? AffiliateEarning::TYPE_ADJUSTMENT : AffiliateEarning::TYPE_PENALTY;

        return $this->createEarning([
            'affiliate_id' => $affiliate->id,
            'organization_id' => null,
            'amount' => abs($amount), // Store as positive, type indicates if it's penalty
            'type' => $type,
            'description' => $description,
            'status' => AffiliateEarning::STATUS_APPROVED, // Adjustments are pre-approved
            'approved_by' => $approvedBy,
            'approved_at' => now(),
        ]);
    }

    /**
     * Calculate milestone bonuses
     */
    public function checkMilestoneBonuses(Affiliate $affiliate): array
    {
        $bonuses = [];
        $referralCount = $affiliate->converted_referrals_count;

        // Define milestone bonuses
        $milestones = [
            5 => 50.00,   // $50 for 5 referrals
            10 => 100.00, // $100 for 10 referrals
            25 => 250.00, // $250 for 25 referrals
            50 => 500.00, // $500 for 50 referrals
            100 => 1000.00, // $1000 for 100 referrals
        ];

        foreach ($milestones as $milestone => $bonusAmount) {
            if ($referralCount >= $milestone) {
                // Check if bonus already paid
                $existingBonus = AffiliateEarning::where('affiliate_id', $affiliate->id)
                    ->where('type', AffiliateEarning::TYPE_BONUS)
                    ->where('description', 'like', "%{$milestone} referrals%")
                    ->first();

                if (!$existingBonus) {
                    $bonus = $this->createBonus(
                        $affiliate,
                        $bonusAmount,
                        "Milestone bonus for reaching {$milestone} referrals",
                        true // Auto-approve milestone bonuses
                    );
                    $bonuses[] = $bonus;
                }
            }
        }

        return $bonuses;
    }

    /**
     * Calculate tier upgrade bonuses
     */
    public function checkTierUpgrade(Affiliate $affiliate): ?AffiliateEarning
    {
        $settings = AffiliateSetting::getInstance();
        $currentRate = $settings->getCommissionRateForAffiliate($affiliate);

        // Check if affiliate just reached a new tier
        $tiers = collect($settings->commission_tiers ?? [])->sortBy('min_referrals');
        $referralCount = $affiliate->converted_referrals_count;

        foreach ($tiers as $tier) {
            if ($referralCount >= $tier['min_referrals'] && $currentRate == $tier['commission_rate']) {
                // Check if upgrade bonus already paid for this tier
                $existingBonus = AffiliateEarning::where('affiliate_id', $affiliate->id)
                    ->where('type', AffiliateEarning::TYPE_BONUS)
                    ->where('description', 'like', "%{$tier['name']} tier%")
                    ->first();

                if (!$existingBonus) {
                    $bonusAmount = $tier['commission_rate'] * 5; // Bonus = 5x the commission rate
                    return $this->createBonus(
                        $affiliate,
                        $bonusAmount,
                        "Tier upgrade bonus for reaching {$tier['name']} tier",
                        true
                    );
                }
                break;
            }
        }

        return null;
    }

    /**
     * Process all pending earnings for approval
     */
    public function processPendingEarnings(array $earningIds, int $approvedBy, bool $approve = true): array
    {
        $results = [];

        foreach ($earningIds as $earningId) {
            try {
                $earning = AffiliateEarning::find($earningId);

                if (!$earning || !$earning->isPending()) {
                    $results[$earningId] = ['success' => false, 'message' => 'Earning not found or not pending'];
                    continue;
                }

                if ($approve) {
                    $earning->approve($approvedBy);
                    $results[$earningId] = ['success' => true, 'message' => 'Approved'];
                } else {
                    $earning->reject($approvedBy, 'Rejected by admin');
                    $results[$earningId] = ['success' => true, 'message' => 'Rejected'];
                }
            } catch (\Exception $e) {
                $results[$earningId] = ['success' => false, 'message' => $e->getMessage()];
            }
        }

        return $results;
    }

    /**
     * Calculate total commissions for a period
     */
    public function calculatePeriodCommissions(\DateTime $startDate, \DateTime $endDate): array
    {
        $earnings = AffiliateEarning::where('type', AffiliateEarning::TYPE_COMMISSION)
            ->whereBetween('earned_at', [$startDate, $endDate])
            ->with(['affiliate', 'organization'])
            ->get();

        return [
            'total_amount' => $earnings->sum('amount'),
            'total_count' => $earnings->count(),
            'pending_amount' => $earnings->where('status', AffiliateEarning::STATUS_PENDING)->sum('amount'),
            'approved_amount' => $earnings->where('status', AffiliateEarning::STATUS_APPROVED)->sum('amount'),
            'paid_amount' => $earnings->where('status', AffiliateEarning::STATUS_PAID)->sum('amount'),
            'by_affiliate' => $earnings->groupBy('affiliate_id')->map(function ($group) {
                return [
                    'affiliate' => $group->first()->affiliate,
                    'total_amount' => $group->sum('amount'),
                    'count' => $group->count(),
                ];
            })->values(),
        ];
    }

    /**
     * Get commission summary for affiliate
     */
    public function getAffiliateSummary(Affiliate $affiliate): array
    {
        $earnings = $affiliate->earnings();

        return [
            'total_earnings' => $affiliate->total_earnings,
            'available_balance' => $affiliate->available_balance,
            'pending_balance' => $affiliate->pending_balance,
            'pending_earnings' => $affiliate->pending_balance, // Alias for consistency
            'withdrawn_amount' => $affiliate->withdrawn_amount,
            'total_withdrawn' => $affiliate->withdrawn_amount, // Alias for view compatibility
            'commission_count' => $earnings->where('type', AffiliateEarning::TYPE_COMMISSION)->count(),
            'bonus_count' => $earnings->where('type', AffiliateEarning::TYPE_BONUS)->count(),
            'last_earning_date' => $earnings->latest('earned_at')->first()?->earned_at,
            'conversion_rate' => $affiliate->conversion_rate,
            'current_tier' => $this->getCurrentTier($affiliate),
        ];
    }

    /**
     * Get current tier for affiliate
     */
    protected function getCurrentTier(Affiliate $affiliate): array
    {
        $settings = AffiliateSetting::getInstance();
        $referralCount = $affiliate->converted_referrals_count;
        $commissionRate = $settings->getCommissionRateForAffiliate($affiliate);

        $tiers = collect($settings->commission_tiers ?? [])->sortByDesc('min_referrals');

        foreach ($tiers as $tier) {
            if ($referralCount >= $tier['min_referrals']) {
                return $tier;
            }
        }

        return [
            'name' => 'Bronze',
            'min_referrals' => 0,
            'commission_rate' => $settings->default_commission_rate
        ];
    }
}
