<?php

namespace App\Console\Commands;

use App\Models\SubscriptionPayment;
use Illuminate\Console\Command;

class GenerateMissingInvoiceNumbers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'invoices:generate-missing';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate invoice numbers for approved payments that are missing them';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking for approved payments without invoice numbers...');

        // Find approved payments without invoice numbers
        $paymentsWithoutInvoices = SubscriptionPayment::where('status', 'approved')
            ->whereNull('invoice_number')
            ->get();

        if ($paymentsWithoutInvoices->isEmpty()) {
            $this->info('No approved payments found without invoice numbers.');
            return 0;
        }

        $this->info("Found {$paymentsWithoutInvoices->count()} approved payments without invoice numbers.");

        $bar = $this->output->createProgressBar($paymentsWithoutInvoices->count());
        $bar->start();

        $generated = 0;
        foreach ($paymentsWithoutInvoices as $payment) {
            try {
                $payment->update([
                    'invoice_number' => $payment->generateInvoiceNumber(),
                    'invoice_generated_at' => now(),
                ]);
                $generated++;
            } catch (\Exception $e) {
                $this->error("\nFailed to generate invoice for payment ID {$payment->id}: " . $e->getMessage());
            }
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("Successfully generated {$generated} invoice numbers.");

        return 0;
    }
}
