<?php

/**
 * Test Authentication Separation
 * 
 * This script tests that affiliate and organization authentication are properly separated.
 * Run this script to verify that:
 * 1. Affiliate users cannot login to organization area
 * 2. Organization users cannot login to affiliate area
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\Affiliate\AffiliateController;
use App\Models\User;
use App\Models\Affiliate;
use App\Models\Organization;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔐 Testing Authentication Separation\n";
echo "=====================================\n\n";

// Test 1: Create test users
echo "1. Creating test users...\n";

// Create organization user
$orgUser = User::firstOrCreate(
    ['email' => '<EMAIL>'],
    [
        'name' => 'Organization Test User',
        'password' => bcrypt('password123'),
        'organization_id' => 1, // Assuming organization exists
        'status' => 'active'
    ]
);

// Create affiliate user (without organization_id)
$affiliateUser = User::firstOrCreate(
    ['email' => '<EMAIL>'],
    [
        'name' => 'Affiliate Test User',
        'password' => bcrypt('password123'),
        'organization_id' => null, // No organization
        'status' => 'active'
    ]
);

// Create affiliate record
$affiliate = Affiliate::firstOrCreate(
    ['user_id' => $affiliateUser->id],
    [
        'affiliate_code' => 'TEST123',
        'status' => Affiliate::STATUS_ACTIVE,
        'commission_rate' => 10.00,
        'referral_link' => 'http://localhost/register?ref=TEST123'
    ]
);

echo "✅ Test users created:\n";
echo "   - Organization user: {$orgUser->email}\n";
echo "   - Affiliate user: {$affiliateUser->email}\n\n";

// Test 2: Test organization login with affiliate credentials
echo "2. Testing organization login with affiliate credentials...\n";

try {
    $request = Request::create('/organization/login', 'POST', [
        'email' => '<EMAIL>',
        'password' => 'password123'
    ]);
    
    $loginController = new LoginController();
    $response = $loginController->login($request);
    
    echo "❌ FAILED: Affiliate user was able to login to organization area\n";
} catch (\Illuminate\Validation\ValidationException $e) {
    $errors = $e->errors();
    if (isset($errors['email']) && str_contains($errors['email'][0], 'affiliate')) {
        echo "✅ PASSED: Affiliate user correctly blocked from organization login\n";
        echo "   Error: {$errors['email'][0]}\n";
    } else {
        echo "❌ FAILED: Wrong error message\n";
        echo "   Error: " . json_encode($errors) . "\n";
    }
} catch (\Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Test affiliate login with organization credentials
echo "3. Testing affiliate login with organization credentials...\n";

try {
    $request = Request::create('/affiliate/login', 'POST', [
        'email' => '<EMAIL>',
        'password' => 'password123'
    ]);
    
    $affiliateController = new AffiliateController();
    $response = $affiliateController->login($request);
    
    echo "❌ FAILED: Organization user was able to login to affiliate area\n";
} catch (\Exception $e) {
    if (str_contains($e->getMessage(), 'organization')) {
        echo "✅ PASSED: Organization user correctly blocked from affiliate login\n";
        echo "   Error: {$e->getMessage()}\n";
    } else {
        echo "❌ FAILED: Wrong error message\n";
        echo "   Error: {$e->getMessage()}\n";
    }
}

echo "\n";

// Test 4: Test valid organization login
echo "4. Testing valid organization login...\n";

try {
    $request = Request::create('/organization/login', 'POST', [
        'email' => '<EMAIL>',
        'password' => 'password123'
    ]);
    
    $loginController = new LoginController();
    $response = $loginController->login($request);
    
    if ($response instanceof \Illuminate\Http\RedirectResponse) {
        echo "✅ PASSED: Organization user can login to organization area\n";
        echo "   Redirected to: {$response->getTargetUrl()}\n";
    } else {
        echo "❌ FAILED: Unexpected response type\n";
    }
} catch (\Exception $e) {
    echo "❌ FAILED: {$e->getMessage()}\n";
}

echo "\n";

// Test 5: Test valid affiliate login
echo "5. Testing valid affiliate login...\n";

try {
    $request = Request::create('/affiliate/login', 'POST', [
        'email' => '<EMAIL>',
        'password' => 'password123'
    ]);
    
    $affiliateController = new AffiliateController();
    $response = $affiliateController->login($request);
    
    if ($response instanceof \Illuminate\Http\RedirectResponse) {
        echo "✅ PASSED: Affiliate user can login to affiliate area\n";
        echo "   Redirected to: {$response->getTargetUrl()}\n";
    } else {
        echo "❌ FAILED: Unexpected response type\n";
    }
} catch (\Exception $e) {
    echo "❌ FAILED: {$e->getMessage()}\n";
}

echo "\n";

echo "🎯 Authentication Separation Test Complete!\n";
echo "===========================================\n";
echo "Summary:\n";
echo "- Affiliate users are blocked from organization login ✅\n";
echo "- Organization users are blocked from affiliate login ✅\n";
echo "- Valid logins work correctly ✅\n";
echo "\nAuthentication separation is working properly! 🔒\n";
