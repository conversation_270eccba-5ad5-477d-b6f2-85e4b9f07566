<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use App\Models\Plan;
use App\Models\Subscription;

echo "=== Fixing Annual Billing Database Issues ===\n\n";

try {
    // 1. Update existing subscriptions to have monthly billing period
    echo "1. Updating existing subscriptions...\n";
    $subscriptionsUpdated = DB::table('subscriptions')
        ->whereNull('billing_period')
        ->orWhere('billing_period', '')
        ->update(['billing_period' => 'monthly']);
    
    echo "   Updated {$subscriptionsUpdated} subscriptions to monthly billing\n\n";
    
    // 2. Update existing plans to support annual billing
    echo "2. Updating existing plans...\n";
    $plans = Plan::all();
    $plansUpdated = 0;
    
    foreach ($plans as $plan) {
        $updated = false;
        $updates = [];
        
        // Set billing period if not set
        if (!$plan->billing_period) {
            $updates['billing_period'] = 'both';
            $updated = true;
        }
        
        // Calculate annual price if not set
        if (!$plan->annual_price && $plan->price > 0) {
            $annualPrice = ($plan->price * 12) * 0.85; // 15% discount
            $updates['annual_price'] = $annualPrice;
            $updated = true;
        }
        
        // Set discount percentage if not set
        if (!$plan->annual_discount_percentage) {
            $updates['annual_discount_percentage'] = 15;
            $updated = true;
        }
        
        if ($updated) {
            $plan->update($updates);
            $plansUpdated++;
            echo "   Updated {$plan->name}: ";
            if (isset($updates['annual_price'])) {
                echo "Annual price: \${$updates['annual_price']} ";
            }
            if (isset($updates['billing_period'])) {
                echo "Billing: {$updates['billing_period']} ";
            }
            echo "\n";
        }
    }
    
    echo "   Updated {$plansUpdated} plans with annual billing support\n\n";
    
    // 3. Test the fixes
    echo "3. Testing the fixes...\n";
    
    // Test subscription methods
    $subscription = Subscription::first();
    if ($subscription) {
        echo "   Subscription billing period: " . ($subscription->billing_period ?: 'null') . "\n";
        echo "   Is monthly: " . ($subscription->isMonthly() ? 'Yes' : 'No') . "\n";
        echo "   Is annual: " . ($subscription->isAnnual() ? 'Yes' : 'No') . "\n";
        
        if ($subscription->plan) {
            echo "   Current period amount: \${$subscription->getCurrentPeriodAmount()}\n";
        }
    }
    
    // Test plan methods
    $plan = Plan::first();
    if ($plan) {
        echo "   Plan billing period: " . ($plan->billing_period ?: 'null') . "\n";
        echo "   Monthly price: \${$plan->getPriceForPeriod('monthly')}\n";
        echo "   Annual price: \${$plan->getPriceForPeriod('annual')}\n";
        echo "   Supports annual: " . ($plan->supportsAnnualBilling() ? 'Yes' : 'No') . "\n";
        echo "   Annual savings: \${$plan->getAnnualSavings()}\n";
    }
    
    echo "\n=== Database Fix Completed Successfully! ===\n";
    echo "\nChanges made:\n";
    echo "✓ All existing subscriptions set to monthly billing\n";
    echo "✓ All plans now support both monthly and annual billing\n";
    echo "✓ Annual prices calculated with 15% discount\n";
    echo "✓ Null value handling added to prevent errors\n";
    echo "\nThe annual billing system should now work without errors!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
