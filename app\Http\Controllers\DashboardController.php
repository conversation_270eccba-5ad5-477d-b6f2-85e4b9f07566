<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Expenditure;
use App\Models\Branch;
use App\Models\Announcement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        // Get organization and branch data
        $organization = Auth::user()->organization;
        $currentBranch = Auth::user()->branch;
        $branches = Branch::where('organization_id', Auth::user()->organization_id)->get();

        // Initialize query builder - filter by organization
        $ordersQuery = Order::query();

        // If user has a branch, filter by branch
        if ($currentBranch) {
            $ordersQuery->where('branch_id', $currentBranch->id);
        } else {
            // If no branch selected, show data from all org branches
            $branchIds = $branches->pluck('id')->toArray();
            if (!empty($branchIds)) {
                $ordersQuery->whereIn('branch_id', $branchIds);
            }
        }

        $periodLabel = 'Today';
        $selectedDate = $request->get('date', now()->format('Y-m-d'));

        // Get date range for filtering
        if ($request->has('start_date') && $request->has('end_date')) {
            $start = Carbon::parse($request->start_date)->startOfDay();
            $end = Carbon::parse($request->end_date)->endOfDay();
            $ordersQuery->whereBetween('created_at', [$start, $end]);
            $periodLabel = $start->format('M d, Y') . ' - ' . $end->format('M d, Y');
        } else {
            $selectedDate = Carbon::parse($selectedDate);
            $ordersQuery->whereDate('created_at', $selectedDate);
            $periodLabel = $selectedDate->format('F d, Y');
        }

        // Get order counts
        $totalOrders = $ordersQuery->count();
        $pendingOrders = (clone $ordersQuery)->where('status', 'Pending')->count();
        $processingOrders = (clone $ordersQuery)->where('status', 'Processing')->count();
        $completedOrders = (clone $ordersQuery)->where('status', 'Completed')->count();
        $deliveredOrders = (clone $ordersQuery)->where('status', 'Delivered')->count();

        // Get financial metrics
        $financialMetrics = (clone $ordersQuery)->select(
            DB::raw('SUM(total_amount) as total_revenue'),
            DB::raw('SUM(amount_paid) as total_amount_paid'),
            DB::raw('SUM(pending_payment) as total_pending_payment'),
            DB::raw('SUM(CASE WHEN status IN ("Processing", "Completed", "Delivered") THEN total_amount ELSE 0 END) as total_production')
        )->first();

        // Get recent orders
        $recentOrders = (clone $ordersQuery)->latest()->take(5)->get();

        // Get user order statistics for the selected date range
        $userOrderStats = (clone $ordersQuery)
            ->select('user_id', DB::raw('COUNT(*) as order_count'))
            ->with('user:id,name')
            ->groupBy('user_id')
            ->get()
            ->map(function ($stat) {
                return [
                    'user_name' => $stat->user ? $stat->user->name : 'Unknown',
                    'order_count' => $stat->order_count
                ];
            });

        // Get announcements for the current user
        $user = Auth::user();
        $audience = $user->organization_id ? 'organizations' : 'customers';

        $announcements = Announcement::active()
            ->published()
            ->current()
            ->forAudience($audience)
            ->forDashboard()
            ->orderBy('priority', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('dashboard', compact(
            'totalOrders',
            'pendingOrders',
            'processingOrders',
            'completedOrders',
            'deliveredOrders',
            'financialMetrics',
            'recentOrders',
            'periodLabel',
            'userOrderStats',
            'selectedDate',
            'organization',
            'currentBranch',
            'branches',
            'announcements'
        ));
    }
}
