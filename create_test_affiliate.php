<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Affiliate;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

echo "=== Creating Test Affiliate User ===\n\n";

try {
    $testEmail = '<EMAIL>';
    $testPassword = 'password123';
    
    // Check if user already exists
    $existingUser = User::where('email', $testEmail)->first();
    
    if ($existingUser) {
        echo "User already exists: {$testEmail}\n";
        
        // Check if affiliate record exists
        $affiliate = Affiliate::where('user_id', $existingUser->id)->first();
        
        if (!$affiliate) {
            echo "Creating affiliate record for existing user...\n";
            $affiliate = Affiliate::create([
                'user_id' => $existingUser->id,
                'referral_code' => 'TEST' . strtoupper(substr(md5($existingUser->id), 0, 6)),
                'status' => Affiliate::STATUS_ACTIVE,
                'commission_rate' => 10.00,
                'total_earnings' => 0,
                'available_balance' => 0,
                'pending_balance' => 0,
                'withdrawn_amount' => 0,
            ]);
            echo "✓ Affiliate record created\n";
        } else {
            echo "✓ Affiliate record already exists\n";
        }
        
        $user = $existingUser;
    } else {
        echo "Creating new test user and affiliate...\n";
        
        DB::beginTransaction();
        
        // Create user
        $user = User::create([
            'name' => 'Test Affiliate',
            'email' => $testEmail,
            'password' => Hash::make($testPassword),
            'phone' => '+1234567890',
            'status' => 'active',
        ]);
        
        // Create affiliate
        $affiliate = Affiliate::create([
            'user_id' => $user->id,
            'referral_code' => 'TEST' . strtoupper(substr(md5($user->id), 0, 6)),
            'status' => Affiliate::STATUS_ACTIVE,
            'commission_rate' => 10.00,
            'total_earnings' => 0,
            'available_balance' => 0,
            'pending_balance' => 0,
            'withdrawn_amount' => 0,
        ]);
        
        DB::commit();
        echo "✓ New user and affiliate created\n";
    }
    
    echo "\n=== Test Affiliate Details ===\n";
    echo "User ID: {$user->id}\n";
    echo "Name: {$user->name}\n";
    echo "Email: {$user->email}\n";
    echo "Password: {$testPassword}\n";
    echo "Phone: {$user->phone}\n";
    echo "Status: {$user->status}\n";
    echo "\nAffiliate ID: {$affiliate->id}\n";
    echo "Referral Code: {$affiliate->referral_code}\n";
    echo "Status: {$affiliate->status}\n";
    echo "Commission Rate: {$affiliate->commission_rate}%\n";
    
    echo "\n=== Testing Instructions ===\n";
    echo "1. Visit: http://localhost/SalesManagementSystem/affiliate/login\n";
    echo "2. Login with:\n";
    echo "   Email: {$testEmail}\n";
    echo "   Password: {$testPassword}\n";
    echo "3. Should redirect to: http://localhost/SalesManagementSystem/affiliate/dashboard\n";
    echo "4. Should see affiliate dashboard with referral code: {$affiliate->referral_code}\n";
    
    echo "\n=== Success! ===\n";
    echo "Test affiliate user is ready for testing.\n";

} catch (Exception $e) {
    if (DB::transactionLevel() > 0) {
        DB::rollBack();
    }
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
