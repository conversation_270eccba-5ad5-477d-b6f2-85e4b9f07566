<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('plan_id')->constrained()->onDelete('restrict');
            $table->foreignId('organization_id')->constrained()->onDelete('cascade');
            $table->string('status'); // active, canceled, past_due, trial, expired
            $table->dateTime('start_date');
            $table->dateTime('end_date');
            $table->dateTime('trial_ends_at')->nullable();
            $table->string('payment_method')->nullable();
            $table->string('payment_id')->nullable();
            $table->string('customer_id')->nullable(); // Payment provider customer ID
            $table->string('subscription_id')->nullable(); // Payment provider subscription ID
            $table->decimal('amount_paid', 10, 2)->default(0);
            $table->boolean('auto_renew')->default(true);
            $table->text('cancellation_reason')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
