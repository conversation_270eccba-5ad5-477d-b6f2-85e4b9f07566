<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\WelcomeMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class WelcomeMessageController extends Controller
{
    /**
     * Display a listing of welcome messages.
     */
    public function index()
    {
        $messages = WelcomeMessage::with(['creator', 'updater'])
            ->orderBy('user_type')
            ->get();

        return view('super_admin.welcome_messages.index', compact('messages'));
    }

    /**
     * Show the form for creating a new welcome message.
     */
    public function create()
    {
        $userTypes = WelcomeMessage::getUserTypes();
        $existingTypes = WelcomeMessage::pluck('user_type')->toArray();

        return view('super_admin.welcome_messages.create', compact('userTypes', 'existingTypes'));
    }

    /**
     * Store a newly created welcome message.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'user_type' => 'required|string|in:organization,affiliate,super_admin',
            'subject' => 'required|string|max:255',
            'greeting' => 'required|string|max:255',
            'content' => 'required|string|max:5000',
            'call_to_action_text' => 'nullable|string|max:100',
            'call_to_action_url' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        DB::beginTransaction();
        try {
            // If setting as active, deactivate other messages for this user type
            if ($validated['is_active'] ?? false) {
                WelcomeMessage::where('user_type', $validated['user_type'])
                    ->update(['is_active' => false]);
            }

            $validated['created_by'] = Auth::id();
            $message = WelcomeMessage::create($validated);

            DB::commit();
            return redirect()->route('super_admin.welcome-messages.index')
                           ->with('success', 'Welcome message created successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to create welcome message: ' . $e->getMessage()])
                        ->withInput();
        }
    }

    /**
     * Display the specified welcome message.
     */
    public function show(WelcomeMessage $welcomeMessage)
    {
        $welcomeMessage->load(['creator', 'updater']);
        return view('super_admin.welcome_messages.show', compact('welcomeMessage'));
    }

    /**
     * Show the form for editing the specified welcome message.
     */
    public function edit(WelcomeMessage $welcomeMessage)
    {
        $userTypes = WelcomeMessage::getUserTypes();
        return view('super_admin.welcome_messages.edit', compact('welcomeMessage', 'userTypes'));
    }

    /**
     * Update the specified welcome message.
     */
    public function update(Request $request, WelcomeMessage $welcomeMessage)
    {
        $validated = $request->validate([
            'user_type' => 'required|string|in:organization,affiliate,super_admin',
            'subject' => 'required|string|max:255',
            'greeting' => 'required|string|max:255',
            'content' => 'required|string|max:5000',
            'call_to_action_text' => 'nullable|string|max:100',
            'call_to_action_url' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        DB::beginTransaction();
        try {
            // If setting as active, deactivate other messages for this user type
            if ($validated['is_active'] ?? false) {
                WelcomeMessage::where('user_type', $validated['user_type'])
                    ->where('id', '!=', $welcomeMessage->id)
                    ->update(['is_active' => false]);
            }

            $validated['updated_by'] = Auth::id();
            $welcomeMessage->update($validated);

            DB::commit();
            return redirect()->route('super_admin.welcome-messages.index')
                           ->with('success', 'Welcome message updated successfully.');
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to update welcome message: ' . $e->getMessage()])
                        ->withInput();
        }
    }

    /**
     * Remove the specified welcome message.
     */
    public function destroy(WelcomeMessage $welcomeMessage)
    {
        try {
            $welcomeMessage->delete();
            return redirect()->route('super_admin.welcome-messages.index')
                           ->with('success', 'Welcome message deleted successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to delete welcome message: ' . $e->getMessage()]);
        }
    }

    /**
     * Toggle the active status of a welcome message.
     */
    public function toggleStatus(WelcomeMessage $welcomeMessage)
    {
        DB::beginTransaction();
        try {
            if (!$welcomeMessage->is_active) {
                // If activating, deactivate other messages for this user type
                WelcomeMessage::where('user_type', $welcomeMessage->user_type)
                    ->where('id', '!=', $welcomeMessage->id)
                    ->update(['is_active' => false]);
            }

            $welcomeMessage->update([
                'is_active' => !$welcomeMessage->is_active,
                'updated_by' => Auth::id(),
            ]);

            DB::commit();

            $status = $welcomeMessage->is_active ? 'activated' : 'deactivated';
            return back()->with('success', "Welcome message {$status} successfully.");
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Failed to toggle status: ' . $e->getMessage()]);
        }
    }

    /**
     * Preview the welcome message.
     */
    public function preview(WelcomeMessage $welcomeMessage)
    {
        // Create a dummy user model instance for preview
        $dummyUser = new \App\Models\User([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ]);

        return view('super_admin.welcome_messages.preview', compact('welcomeMessage', 'dummyUser'));
    }

    /**
     * Send a test email.
     */
    public function sendTest(Request $request, WelcomeMessage $welcomeMessage)
    {
        $request->validate([
            'test_email' => 'required|email',
        ]);

        try {
            // Create a dummy user model instance for testing
            $testUser = new \App\Models\User([
                'name' => 'Test User',
                'email' => $request->test_email,
            ]);

            Mail::to($request->test_email)->send(new \App\Mail\WelcomeEmail($testUser, $welcomeMessage->user_type));

            return back()->with('success', 'Test email sent successfully to ' . $request->test_email);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to send test email: ' . $e->getMessage()]);
        }
    }

    /**
     * Initialize default welcome messages.
     */
    public function initializeDefaults()
    {
        try {
            WelcomeMessage::createDefaults(Auth::id());
            return back()->with('success', 'Default welcome messages created successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create defaults: ' . $e->getMessage()]);
        }
    }
}
