<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currency_rates', function (Blueprint $table) {
            $table->id();
            $table->string('from_currency', 3); // USD
            $table->string('to_currency', 3); // NGN
            $table->decimal('rate', 15, 6); // Exchange rate (e.g., 1 USD = 1500.000000 NGN)
            $table->boolean('is_active')->default(true);
            $table->timestamp('effective_from')->nullable();
            $table->timestamp('effective_to')->nullable();
            $table->unsignedBigInteger('created_by')->nullable(); // User or Super Admin who set the rate
            $table->timestamps();

            // Note: created_by can reference either users or super_admins table
            // We'll handle this in the model relationships instead of foreign key constraints
            $table->index(['from_currency', 'to_currency', 'is_active']);
            $table->index('effective_from');
        });

        // Insert default exchange rate
        DB::table('currency_rates')->insert([
            'from_currency' => 'USD',
            'to_currency' => 'NGN',
            'rate' => 1500.000000, // Default rate: 1 USD = 1500 NGN
            'is_active' => true,
            'effective_from' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currency_rates');
    }
};
