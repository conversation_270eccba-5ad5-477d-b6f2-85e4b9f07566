<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PlanChangeRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'organization_id',
        'current_plan_id',
        'requested_plan_id',
        'requested_by',
        'change_type',
        'status',
        'amount_due',
        'proration_details',
        'request_reason',
        'admin_notes',
        'reviewed_by',
        'reviewed_at',
        'scheduled_date',
        'completed_at',
    ];

    protected $casts = [
        'amount_due' => 'decimal:2',
        'proration_details' => 'json',
        'reviewed_at' => 'datetime',
        'scheduled_date' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the organization that owns the plan change request.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the current plan.
     */
    public function currentPlan(): BelongsTo
    {
        return $this->belongsTo(Plan::class, 'current_plan_id');
    }

    /**
     * Get the requested plan.
     */
    public function requestedPlan(): BelongsTo
    {
        return $this->belongsTo(Plan::class, 'requested_plan_id');
    }

    /**
     * Get the user who requested the change.
     */
    public function requestedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    /**
     * Get the super admin who reviewed the request.
     */
    public function reviewedBy(): BelongsTo
    {
        return $this->belongsTo(SuperAdmin::class, 'reviewed_by');
    }

    /**
     * Check if the request is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the request is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if the request is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Check if the request is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if this is an upgrade.
     */
    public function isUpgrade(): bool
    {
        if (!$this->currentPlan) {
            return true; // New subscription is considered an upgrade
        }
        return $this->requestedPlan->price > $this->currentPlan->price;
    }

    /**
     * Check if this is a downgrade.
     */
    public function isDowngrade(): bool
    {
        if (!$this->currentPlan) {
            return false; // New subscription is not a downgrade
        }
        return $this->requestedPlan->price < $this->currentPlan->price;
    }

    /**
     * Approve the plan change request.
     */
    public function approve($adminUser, string $notes = null): void
    {
        $this->update([
            'status' => 'approved',
            'reviewed_by' => $adminUser->id,
            'reviewed_at' => now(),
            'admin_notes' => $notes,
            'scheduled_date' => $this->change_type === 'immediate' ? now() : $this->calculateEndOfCycleDate(),
        ]);
    }

    /**
     * Reject the plan change request.
     */
    public function reject($adminUser, string $reason): void
    {
        $this->update([
            'status' => 'rejected',
            'reviewed_by' => $adminUser->id,
            'reviewed_at' => now(),
            'admin_notes' => $reason,
        ]);
    }

    /**
     * Mark the request as completed.
     */
    public function markCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);
    }

    /**
     * Calculate the end of cycle date for scheduled changes.
     */
    private function calculateEndOfCycleDate()
    {
        $subscription = $this->organization->activeSubscription;
        return $subscription ? $subscription->end_date : now()->addMonth();
    }

    /**
     * Scope for pending requests.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved requests.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for requests ready to be processed.
     */
    public function scopeReadyToProcess($query)
    {
        return $query->where('status', 'approved')
                    ->where('scheduled_date', '<=', now());
    }
}
