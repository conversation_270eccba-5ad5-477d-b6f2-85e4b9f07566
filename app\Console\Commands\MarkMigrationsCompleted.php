<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MarkMigrationsCompleted extends Command
{
    protected $signature = 'migrations:mark-completed {migrations*}';
    protected $description = 'Mark specific migrations as completed';

    public function handle()
    {
        $migrations = $this->argument('migrations');
        $batch = DB::table('migrations')->max('batch') + 1;

        foreach ($migrations as $migration) {
            DB::table('migrations')->insertOrIgnore([
                'migration' => $migration,
                'batch' => $batch
            ]);
            $this->info("Marked {$migration} as completed.");
        }
    }
}