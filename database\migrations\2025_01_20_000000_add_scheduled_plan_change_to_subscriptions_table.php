<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('subscriptions')) {
            Schema::table('subscriptions', function (Blueprint $table) {
                $table->unsignedBigInteger('scheduled_plan_id')->nullable(); // Remove foreign key constraint
                $table->timestamp('scheduled_change_date')->nullable();
                $table->text('cancellation_reason')->nullable();
                $table->timestamp('canceled_at')->nullable();
                $table->boolean('auto_renew')->default(true);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropForeign(['scheduled_plan_id']);
            $table->dropColumn([
                'scheduled_plan_id',
                'scheduled_change_date',
                'cancellation_reason',
                'canceled_at',
                'auto_renew'
            ]);
        });
    }
};
