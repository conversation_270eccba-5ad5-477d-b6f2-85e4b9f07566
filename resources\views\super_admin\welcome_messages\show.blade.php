@extends('super_admin.layouts.app')

@section('title', 'Welcome Message Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Welcome Message Details</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('super_admin.welcome-messages.index') }}">Welcome Messages</a>
                            </li>
                            <li class="breadcrumb-item active">{{ $welcomeMessage->formatted_user_type }}</li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-group" role="group">
                    <a href="{{ route('super_admin.welcome-messages.preview', $welcomeMessage) }}" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> Preview
                    </a>
                    <a href="{{ route('super_admin.welcome-messages.edit', $welcomeMessage) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="{{ route('super_admin.welcome-messages.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Messages
                    </a>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error') || $errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') ?? $errors->first() }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <!-- Message Details -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-envelope"></i> Message Content
                            </h5>
                            <div>
                                <span class="badge bg-info">{{ $welcomeMessage->formatted_user_type }}</span>
                                @if($welcomeMessage->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-secondary">Inactive</span>
                                @endif
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label text-muted">Email Subject</label>
                                <div class="fw-bold">{{ $welcomeMessage->subject }}</div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Greeting</label>
                                <div class="fw-bold">{{ $welcomeMessage->greeting }}</div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Message Content</label>
                                <div class="border rounded p-3 bg-light">
                                    @foreach(explode("\n", $welcomeMessage->content) as $paragraph)
                                        @if(trim($paragraph))
                                            <p class="mb-2">{{ trim($paragraph) }}</p>
                                        @endif
                                    @endforeach
                                </div>
                            </div>

                            @if($welcomeMessage->call_to_action_text || $welcomeMessage->call_to_action_url)
                                <div class="mb-3">
                                    <label class="form-label text-muted">Call to Action</label>
                                    <div>
                                        @if($welcomeMessage->call_to_action_text)
                                            <span class="badge bg-primary">{{ $welcomeMessage->call_to_action_text }}</span>
                                        @endif
                                        @if($welcomeMessage->call_to_action_url)
                                            <br><small class="text-muted">URL: {{ $welcomeMessage->call_to_action_url }}</small>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Actions & Info -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-cogs"></i> Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('super_admin.welcome-messages.preview', $welcomeMessage) }}" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Preview Email
                                </a>
                                
                                <a href="{{ route('super_admin.welcome-messages.edit', $welcomeMessage) }}" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> Edit Message
                                </a>

                                <form method="POST" action="{{ route('super_admin.welcome-messages.toggle-status', $welcomeMessage) }}">
                                    @csrf
                                    <button type="submit" class="btn btn-{{ $welcomeMessage->is_active ? 'danger' : 'success' }} w-100"
                                            onclick="return confirm('{{ $welcomeMessage->is_active ? 'Deactivate' : 'Activate' }} this message?')">
                                        <i class="fas fa-{{ $welcomeMessage->is_active ? 'ban' : 'check' }}"></i> 
                                        {{ $welcomeMessage->is_active ? 'Deactivate' : 'Activate' }}
                                    </button>
                                </form>

                                <form method="POST" action="{{ route('super_admin.welcome-messages.destroy', $welcomeMessage) }}">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger w-100"
                                            onclick="return confirm('Are you sure you want to delete this welcome message? This action cannot be undone.')">
                                        <i class="fas fa-trash"></i> Delete Message
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Test Email -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-paper-plane"></i> Send Test Email
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('super_admin.welcome-messages.send-test', $welcomeMessage) }}">
                                @csrf
                                <div class="mb-3">
                                    <input type="email" class="form-control" name="test_email" 
                                           placeholder="Enter email address" required>
                                </div>
                                <button type="submit" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-paper-plane"></i> Send Test
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Message Info -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle"></i> Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label text-muted">User Type</label>
                                <div>{{ $welcomeMessage->formatted_user_type }}</div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    @if($welcomeMessage->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Created</label>
                                <div>{{ $welcomeMessage->created_at->format('M d, Y \a\t g:i A') }}</div>
                                @if($welcomeMessage->creator)
                                    <small class="text-muted">by {{ $welcomeMessage->creator->name }}</small>
                                @endif
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Last Updated</label>
                                <div>{{ $welcomeMessage->updated_at->format('M d, Y \a\t g:i A') }}</div>
                                @if($welcomeMessage->updater)
                                    <small class="text-muted">by {{ $welcomeMessage->updater->name }}</small>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
