<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Ensure roles table exists
        if (!Schema::hasTable('roles')) {
            Schema::create('roles', function (Blueprint $table) {
                $table->id();
                $table->string('name')->unique();
                $table->timestamps();
            });
        }

        // Create default roles
        DB::transaction(function () {
            $roles = [
                'Organization Owner',
                'Manager',
                'Account',
                'Staff'
            ];

            foreach ($roles as $roleName) {
                Role::firstOrCreate(['name' => $roleName]);
            }

            // Admin user creation has been removed
            // Users will now be created only through registration
        });
    }

    public function down()
    {
        DB::transaction(function () {
            // Remove roles
            Role::whereIn('name', ['Organization Owner', 'Manager', 'Account', 'Staff'])->delete();
        });
    }
};
