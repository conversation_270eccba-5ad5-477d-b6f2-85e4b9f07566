<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Subscription;
use App\Models\Plan;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ProcessScheduledPlanChanges extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:process-scheduled-changes {--dry-run : Show what changes would be processed without actually processing them}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process scheduled plan changes for subscriptions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('🔍 DRY RUN MODE - No changes will be processed');
        } else {
            $this->info('⚙️  PROCESSING SCHEDULED PLAN CHANGES');
        }

        $today = Carbon::now()->startOfDay();
        
        // Find subscriptions with scheduled plan changes due today or earlier
        $subscriptionsToProcess = Subscription::whereNotNull('scheduled_plan_id')
            ->whereNotNull('scheduled_change_date')
            ->where('scheduled_change_date', '<=', $today)
            ->where('status', 'active')
            ->with(['organization', 'plan', 'scheduledPlan'])
            ->get();

        if ($subscriptionsToProcess->isEmpty()) {
            $this->info('✅ No scheduled plan changes to process');
            return 0;
        }

        $this->info("📋 Found {$subscriptionsToProcess->count()} scheduled plan changes to process");

        $processed = 0;
        $failed = 0;

        foreach ($subscriptionsToProcess as $subscription) {
            try {
                if ($dryRun) {
                    $this->line("  📅 Would process plan change for {$subscription->organization->name}:");
                    $this->line("    From: {$subscription->plan->name} (\${$subscription->plan->price})");
                    $this->line("    To: {$subscription->scheduledPlan->name} (\${$subscription->scheduledPlan->price})");
                    $this->line("    Scheduled for: {$subscription->scheduled_change_date->format('Y-m-d')}");
                } else {
                    $this->processScheduledChange($subscription);
                    $this->line("  ✅ Processed plan change for {$subscription->organization->name}");
                }
                $processed++;
            } catch (\Exception $e) {
                $this->error("  ❌ Failed to process plan change for {$subscription->organization->name}: {$e->getMessage()}");
                
                Log::error('Scheduled plan change failed', [
                    'subscription_id' => $subscription->id,
                    'organization_id' => $subscription->organization_id,
                    'old_plan_id' => $subscription->plan_id,
                    'new_plan_id' => $subscription->scheduled_plan_id,
                    'error' => $e->getMessage()
                ]);
                
                $failed++;
            }
        }

        if ($dryRun) {
            $this->info("✅ DRY RUN COMPLETE - Would process {$processed} plan changes");
        } else {
            $this->info("✅ PROCESSING COMPLETE - {$processed} plan changes processed, {$failed} failed");
        }

        return 0;
    }

    /**
     * Process a scheduled plan change
     */
    private function processScheduledChange(Subscription $subscription): void
    {
        $oldPlan = $subscription->plan;
        $newPlan = $subscription->scheduledPlan;
        $organization = $subscription->organization;

        DB::beginTransaction();
        try {
            // Update the subscription with the new plan
            $subscription->update([
                'plan_id' => $newPlan->id,
                'scheduled_plan_id' => null,
                'scheduled_change_date' => null,
                'amount_paid' => $newPlan->price, // Set new plan price for next billing cycle
            ]);

            // Update the organization's plan
            $organization->update([
                'plan_id' => $newPlan->id
            ]);

            // Log the successful plan change
            Log::info('Scheduled plan change processed', [
                'subscription_id' => $subscription->id,
                'organization_id' => $organization->id,
                'old_plan_id' => $oldPlan->id,
                'old_plan_name' => $oldPlan->name,
                'new_plan_id' => $newPlan->id,
                'new_plan_name' => $newPlan->name,
                'processed_at' => now()
            ]);

            DB::commit();

            // Send notification to organization about the plan change
            $this->sendPlanChangeNotification($organization, $oldPlan, $newPlan);

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Send notification about the plan change
     */
    private function sendPlanChangeNotification($organization, $oldPlan, $newPlan): void
    {
        try {
            // You can implement a notification class for this
            // For now, just log it
            Log::info('Plan change notification should be sent', [
                'organization_id' => $organization->id,
                'organization_email' => $organization->email,
                'old_plan' => $oldPlan->name,
                'new_plan' => $newPlan->name
            ]);

            // TODO: Implement actual email notification
            // Notification::route('mail', $organization->email)
            //     ->notify(new PlanChangedNotification($organization, $oldPlan, $newPlan));

        } catch (\Exception $e) {
            Log::error('Failed to send plan change notification', [
                'organization_id' => $organization->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
