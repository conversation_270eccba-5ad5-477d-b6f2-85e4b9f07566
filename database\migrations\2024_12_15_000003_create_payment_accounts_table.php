<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_accounts', function (Blueprint $table) {
            $table->id();
            $table->string('account_name'); // Account holder name
            $table->string('bank_name'); // Bank name
            $table->string('account_number'); // Account number
            $table->string('account_type')->default('savings'); // savings, current, etc.
            $table->string('routing_number')->nullable(); // For international transfers
            $table->string('swift_code')->nullable(); // For international transfers
            $table->text('additional_instructions')->nullable(); // Additional payment instructions
            $table->boolean('is_active')->default(true);
            $table->boolean('is_primary')->default(false); // Primary account to show first
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_accounts');
    }
};
