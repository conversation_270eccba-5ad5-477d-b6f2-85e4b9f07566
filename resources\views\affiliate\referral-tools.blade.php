@extends('affiliate.layouts.app')

@section('title', 'Referral Tools')
@section('page-title', 'Referral Tools')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>How to Use Your Referral Links</h6>
                <p class="mb-0">Share these links with potential customers. When they register and subscribe using your link, you'll earn a commission on their payments!</p>
            </div>
        </div>
    </div>

    <!-- Affiliate Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Your Affiliate Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Affiliate Code:</strong></td>
                            <td>
                                <code id="affiliate-code">{{ $affiliate->affiliate_code }}</code>
                                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('affiliate-code')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Commission Rate:</strong></td>
                            <td><span class="badge bg-success">{{ number_format($affiliate->commission_rate, 1) }}%</span></td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                @if($affiliate->status === 'active')
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-warning">{{ ucfirst($affiliate->status) }}</span>
                                @endif
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Stats</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border-end">
                                <h4 class="text-primary">{{ $affiliate->referrals()->count() }}</h4>
                                <small class="text-muted">Total Referrals</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-end">
                                <h4 class="text-success">{{ $affiliate->referrals()->where('status', 'converted')->count() }}</h4>
                                <small class="text-muted">Converted</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <h4 class="text-info">{{ format_price($affiliate->total_earnings ?? 0) }}</h4>
                            <small class="text-muted">Total Earned</small>
                        </div>
                    </div>

                    <!-- Click Statistics -->
                    <hr class="my-3">
                    <div class="row text-center">
                        <div class="col-3">
                            <h5 class="text-primary">{{ $affiliate->clicks()->count() ?? 0 }}</h5>
                            <small class="text-muted">Total Clicks</small>
                        </div>
                        <div class="col-3">
                            <h5 class="text-success">{{ $affiliate->clicks()->where('is_unique', true)->count() ?? 0 }}</h5>
                            <small class="text-muted">Unique Clicks</small>
                        </div>
                        <div class="col-3">
                            <h5 class="text-warning">{{ $affiliate->clicks()->whereDate('clicked_at', today())->count() ?? 0 }}</h5>
                            <small class="text-muted">Today's Clicks</small>
                        </div>
                        <div class="col-3">
                            @php
                                $uniqueClicks = $affiliate->clicks()->where('is_unique', true)->count() ?? 0;
                                $totalReferrals = $affiliate->referrals()->count() ?? 0;
                                $clickConversion = $uniqueClicks > 0 ? round(($totalReferrals / $uniqueClicks) * 100, 1) : 0;
                            @endphp
                            <h5 class="text-info">{{ $clickConversion }}%</h5>
                            <small class="text-muted">Click Conversion</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Trackable Referral Links (NEW) -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow border-success">
                <div class="card-header py-3 bg-success text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-mouse-pointer me-2"></i>
                        Trackable Referral Links (With Click Analytics)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-success border-0 mb-4">
                        <h6><i class="fas fa-star me-2"></i>NEW: Enhanced Click Tracking!</h6>
                        <p class="mb-0">These links provide detailed click analytics including device type, browser, location, and conversion tracking. Perfect for measuring your marketing performance!</p>
                    </div>

                    @php
                        $trackableLinks = [
                            'basic' => [
                                'title' => 'Basic Trackable Link',
                                'description' => 'General purpose link with full click tracking.',
                                'icon' => 'fas fa-chart-line',
                                'url' => url('/go/' . $affiliate->affiliate_code),
                                'color' => 'primary'
                            ],
                            'social' => [
                                'title' => 'Social Media Trackable Link',
                                'description' => 'Optimized for social media with UTM tracking.',
                                'icon' => 'fas fa-share-alt',
                                'url' => url('/go/' . $affiliate->affiliate_code . '?utm_source=social&utm_medium=social_media&utm_campaign=affiliate_share'),
                                'color' => 'info'
                            ],
                            'email' => [
                                'title' => 'Email Campaign Trackable Link',
                                'description' => 'Perfect for email marketing with campaign tracking.',
                                'icon' => 'fas fa-envelope',
                                'url' => url('/go/' . $affiliate->affiliate_code . '?utm_source=email&utm_medium=email&utm_campaign=affiliate_email'),
                                'color' => 'warning'
                            ],
                            'website' => [
                                'title' => 'Website/Blog Trackable Link',
                                'description' => 'Ideal for embedding in websites and blogs.',
                                'icon' => 'fas fa-globe',
                                'url' => url('/go/' . $affiliate->affiliate_code . '?utm_source=website&utm_medium=referral&utm_campaign=affiliate_website'),
                                'color' => 'secondary'
                            ]
                        ];
                    @endphp

                    @foreach($trackableLinks as $linkType => $linkInfo)
                        <div class="mb-4 p-3 border rounded">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h6 class="text-{{ $linkInfo['color'] }} mb-2">
                                        <i class="{{ $linkInfo['icon'] }} me-2"></i>
                                        {{ $linkInfo['title'] }}
                                    </h6>
                                    <p class="text-muted mb-2">{{ $linkInfo['description'] }}</p>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="trackable-link-{{ $linkType }}" value="{{ $linkInfo['url'] }}" readonly>
                                        <button class="btn btn-outline-{{ $linkInfo['color'] }}" type="button" onclick="copyToClipboard('trackable-link-{{ $linkType }}')">
                                            <i class="fas fa-copy me-1"></i>Copy
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="bg-light rounded p-2">
                                        <small class="text-muted d-block">Click Analytics</small>
                                        <i class="fas fa-chart-bar text-{{ $linkInfo['color'] }} fa-2x"></i>
                                        <small class="text-muted d-block mt-1">Full Tracking</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach

                    <div class="alert alert-info border-0 mt-4">
                        <h6><i class="fas fa-info-circle me-2"></i>What's Tracked:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Total clicks & unique visitors</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Device type (mobile, desktop, tablet)</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Browser and operating system</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Traffic source and campaign data</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Click-to-registration conversion</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Daily and monthly performance</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Standard Referral Links -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Standard Referral Links</h6>
                </div>
                <div class="card-body">
                    @php
                        $linkTypes = [
                            'basic' => [
                                'title' => 'Basic Referral Link',
                                'description' => 'General purpose referral link for any marketing channel.',
                                'icon' => 'fas fa-link'
                            ],
                            'social_media' => [
                                'title' => 'Social Media Link',
                                'description' => 'Optimized for sharing on social media platforms.',
                                'icon' => 'fas fa-share-alt'
                            ],
                            'email' => [
                                'title' => 'Email Marketing Link',
                                'description' => 'Perfect for email campaigns and newsletters.',
                                'icon' => 'fas fa-envelope'
                            ]
                        ];
                    @endphp

                    @foreach($referralLinks as $linkType => $linkUrl)
                        @php
                            $linkInfo = $linkTypes[$linkType] ?? [
                                'title' => ucfirst(str_replace('_', ' ', $linkType)) . ' Link',
                                'description' => 'Referral link for ' . str_replace('_', ' ', $linkType) . ' marketing.',
                                'icon' => 'fas fa-link'
                            ];
                        @endphp
                        <div class="mb-4">
                            <h6 class="text-gray-800">
                                <i class="{{ $linkInfo['icon'] }} me-2"></i>
                                {{ $linkInfo['title'] }}
                            </h6>
                            <p class="text-muted small">{{ $linkInfo['description'] }}</p>

                            <div class="input-group mb-2">
                                <input type="text" class="form-control" id="link-{{ $linkType }}"
                                       value="{{ $linkUrl }}" readonly>
                                <button class="btn btn-outline-secondary" type="button"
                                        onclick="copyToClipboard('link-{{ $linkType }}')">
                                    <i class="fas fa-copy me-1"></i>Copy
                                </button>
                                <a href="{{ $linkUrl }}" target="_blank" class="btn btn-outline-primary">
                                    <i class="fas fa-external-link-alt me-1"></i>Test
                                </a>
                            </div>

                            <!-- Social Media Sharing -->
                            <div class="btn-group" role="group">
                                <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode($linkUrl) }}"
                                   target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fab fa-facebook-f me-1"></i>Facebook
                                </a>
                                <a href="https://twitter.com/intent/tweet?url={{ urlencode($linkUrl) }}&text={{ urlencode('Check out this amazing sales management system!') }}"
                                   target="_blank" class="btn btn-sm btn-outline-info">
                                    <i class="fab fa-twitter me-1"></i>Twitter
                                </a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode($linkUrl) }}"
                                   target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fab fa-linkedin-in me-1"></i>LinkedIn
                                </a>
                                <a href="mailto:?subject=Sales Management System&body=Check out this amazing sales management system: {{ $linkUrl }}"
                                   class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-envelope me-1"></i>Email
                                </a>
                            </div>
                        </div>
                        @if(!$loop->last)
                            <hr>
                        @endif
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Marketing Materials -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Marketing Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Share your links on social media platforms
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Include links in your email signature
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Write blog posts about sales management
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Recommend to business contacts
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Join relevant online communities
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Commission Structure</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-percentage me-2"></i>{{ number_format($affiliate->commission_rate, 1) }}% Commission Rate</h6>
                        <p class="mb-0">You earn {{ number_format($affiliate->commission_rate, 1) }}% commission on all subscription payments made by your referred customers.</p>
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Commissions are calculated monthly and paid after admin approval.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');

        // Show success feedback
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check text-success"></i>';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary', 'btn-outline-primary');

        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);

    } catch (err) {
        console.error('Failed to copy: ', err);
        alert('Failed to copy to clipboard');
    }
}
</script>
@endpush
@endsection
