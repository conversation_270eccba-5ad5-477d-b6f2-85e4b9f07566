@extends('layouts.app')

@section('content')
<div class="container-fluid px-4">
    <h1 class="mt-4">Branches</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item active">Branches</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-code-branch me-1"></i>
                All Branches
            </div>
            <a href="{{ route('branches.create') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Add New Branch
            </a>
        </div>
        <div class="card-body">
            @if (session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
            @endif

            @if (session('error'))
            <div class="alert alert-danger">
                {{ session('error') }}
            </div>
            @endif

            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="branches-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Address</th>
                            <th>Current Branch</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($branches as $branch)
                        <tr class="{{ auth()->user()->branch_id == $branch->id ? 'table-primary' : '' }}">
                            <td>{{ $branch->name }}</td>
                            <td>{{ $branch->email ?? 'N/A' }}</td>
                            <td>{{ $branch->phone ?? 'N/A' }}</td>
                            <td>{{ $branch->address ?? 'N/A' }}</td>
                            <td>
                                @if(auth()->user()->branch_id == $branch->id)
                                <span class="badge bg-success">Current</span>
                                @else
                                <span class="badge bg-secondary">No</span>
                                @endif
                            </td>
                            <td>
                                <div class="d-flex flex-wrap gap-1">
                                    <a href="{{ route('branches.show', $branch) }}" class="btn btn-info btn-sm" title="View">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="{{ route('branches.edit', $branch) }}" class="btn btn-primary btn-sm" title="Edit">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>

                                    @if(count($branches) > 1)
                                    <form action="{{ route('branches.destroy', $branch) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger btn-sm" title="Delete"
                                            onclick="return confirm('Are you sure you want to delete this branch?')">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                    @endif

                                    <form action="{{ route('branches.switch') }}" method="POST" class="d-inline">
                                        @csrf
                                        <input type="hidden" name="branch_id" value="{{ $branch->id }}">
                                        <button type="submit" class="btn btn-success btn-sm" title="Switch"
                                            {{ auth()->user()->branch_id == $branch->id ? 'disabled' : '' }}>
                                            <i class="fas fa-exchange-alt"></i> Switch
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center">No branches found.</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-3">
                {{ $branches->links() }}
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        $('#branches-table').DataTable({
            responsive: true,
            paging: false,
            searching: false,
            info: false
        });
    });
</script>
@endpush
