@extends('layouts.app')

@section('title', 'Search Results - Knowledge Base')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ route('user.support.index') }}">Support Center</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ route('user.support.knowledge-base') }}">Knowledge Base</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Search Results</li>
                </ol>
            </nav>

            <!-- Search Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h1 class="h3 mb-3">
                                <i class="fas fa-search me-2"></i>
                                Search Results
                            </h1>

                            <!-- Search Form -->
                            <form method="GET" action="{{ route('user.support.search') }}">
                                <div class="input-group input-group-lg">
                                    <input type="text" name="q" class="form-control"
                                           placeholder="Search for help articles..."
                                           value="{{ $query }}">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i> Search
                                    </button>
                                </div>
                            </form>

                            @if($query)
                                <div class="mt-3">
                                    <p class="mb-0">
                                        <strong>{{ $articles->total() }}</strong>
                                        {{ Str::plural('result', $articles->total()) }} found for
                                        <strong>"{{ $query }}"</strong>
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Results -->
            @if($articles->count() > 0)
                <div class="row">
                    @foreach($articles as $article)
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body d-flex flex-column">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <span class="badge bg-secondary">{{ $article->category->name ?? 'General' }}</span>
                                    {!! $article->visibility_badge !!}
                                </div>

                                <h5 class="card-title">
                                    <a href="{{ route('user.support.article', $article->id) }}" class="text-decoration-none">
                                        {{ $article->title }}
                                    </a>
                                </h5>

                                <p class="card-text text-muted flex-grow-1">
                                    {{ $article->truncated_excerpt }}
                                </p>

                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ $article->reading_time }}m read
                                        </small>
                                        <div>
                                            <span class="badge bg-primary">{{ $article->view_count }} views</span>
                                            @if($article->helpfulness_percentage)
                                                <span class="badge bg-success">{{ $article->helpfulness_percentage }}% helpful</span>
                                            @endif
                                        </div>
                                    </div>

                                    <div class="d-flex gap-2">
                                        <a href="{{ route('user.support.article', $article->id) }}"
                                           class="btn btn-outline-primary btn-sm flex-grow-1">
                                            <i class="fas fa-book-open me-1"></i>Read Article
                                        </a>
                                        @if($article->category)
                                            <a href="{{ route('user.support.category', $article->category->id) }}"
                                               class="btn btn-outline-secondary btn-sm">
                                                <i class="fas fa-folder me-1"></i>Category
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                @if($articles->hasPages())
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-center">
                            {{ $articles->links() }}
                        </div>
                    </div>
                </div>
                @endif
            @else
                <!-- No Results Found -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center py-5">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h4 class="text-muted">No results found</h4>
                                @if($query)
                                    <p class="text-muted">
                                        We couldn't find any articles matching "<strong>{{ $query }}</strong>".
                                    </p>
                                @else
                                    <p class="text-muted">Please enter a search term to find relevant articles.</p>
                                @endif

                                <div class="mt-4">
                                    <h6>Try these suggestions:</h6>
                                    <ul class="list-unstyled">
                                        <li>• Check your spelling</li>
                                        <li>• Use different keywords</li>
                                        <li>• Try more general terms</li>
                                        <li>• Browse categories instead</li>
                                    </ul>
                                </div>

                                <div class="mt-4">
                                    <a href="{{ route('user.support.knowledge-base') }}" class="btn btn-primary me-2">
                                        <i class="fas fa-book me-2"></i>Browse Categories
                                    </a>
                                    <a href="{{ route('user.support.create') }}" class="btn btn-outline-primary">
                                        <i class="fas fa-envelope me-2"></i>Contact Support
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Popular Searches -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-fire text-danger me-2"></i>
                                Popular Searches
                            </h5>
                            <div class="d-flex flex-wrap gap-2">
                                <a href="{{ route('user.support.search') }}?q=getting+started"
                                   class="btn btn-outline-primary btn-sm">Getting Started</a>
                                <a href="{{ route('user.support.search') }}?q=login"
                                   class="btn btn-outline-primary btn-sm">Login Issues</a>
                                <a href="{{ route('user.support.search') }}?q=password"
                                   class="btn btn-outline-primary btn-sm">Password Reset</a>
                                <a href="{{ route('user.support.search') }}?q=billing"
                                   class="btn btn-outline-primary btn-sm">Billing</a>
                                <a href="{{ route('user.support.search') }}?q=orders"
                                   class="btn btn-outline-primary btn-sm">Orders</a>
                                <a href="{{ route('user.support.search') }}?q=users"
                                   class="btn btn-outline-primary btn-sm">User Management</a>
                                <a href="{{ route('user.support.search') }}?q=settings"
                                   class="btn btn-outline-primary btn-sm">Settings</a>
                                <a href="{{ route('user.support.search') }}?q=troubleshooting"
                                   class="btn btn-outline-primary btn-sm">Troubleshooting</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center">
                            <h5 class="card-title">
                                <i class="fas fa-question-circle me-2"></i>
                                Still can't find what you're looking for?
                            </h5>
                            <p class="card-text">Our support team is here to help you with any questions or issues.</p>
                            <div class="d-flex justify-content-center gap-3 flex-wrap">
                                <a href="{{ route('user.support.create') }}" class="btn btn-primary">
                                    <i class="fas fa-envelope me-2"></i>
                                    Contact Support
                                </a>
                                <a href="{{ route('user.support.knowledge-base') }}" class="btn btn-outline-info">
                                    <i class="fas fa-book me-2"></i>
                                    Browse Knowledge Base
                                </a>
                                <a href="{{ route('user.support.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-home me-2"></i>
                                    Support Center
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.card-title a {
    color: inherit;
}

.card-title a:hover {
    color: #007bff;
}

.badge {
    font-size: 0.75rem;
}

.btn-sm {
    font-size: 0.8rem;
}
</style>
@endsection
