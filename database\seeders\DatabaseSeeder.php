<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        Schema::disableForeignKeyConstraints();

        // Clear existing roles
        DB::table('role_user')->truncate();
        DB::table('roles')->truncate();

        Schema::enableForeignKeyConstraints();

        // Only seed roles - no default users since we now use registration
        $this->call([
            RoleSeeder::class,
            SettingsSeeder::class,
            PlanSeeder::class,
            SuperAdminSeeder::class,
        ]);
    }
}
