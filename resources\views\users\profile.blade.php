@extends('layouts.app')

@section('title', 'My Profile')

@section('styles')
<style>
    .profile-header {
        @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white py-8 px-6 rounded-lg shadow-md mb-6;
    }
    .profile-avatar {
        @apply h-24 w-24 rounded-full border-4 border-white shadow-lg;
        object-fit: cover;
    }
    .profile-card {
        @apply bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-200;
        transition: all 0.3s ease;
    }
    .profile-card:hover {
        @apply shadow-md;
        transform: translateY(-2px);
    }
    .section-title {
        @apply text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200;
    }
    .form-group {
        @apply mb-4;
    }
    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-1;
    }
    .form-input {
        @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50;
    }
    .tab-button {
        @apply py-3 px-4 border-b-2 font-medium text-sm focus:outline-none transition-colors duration-200;
    }
    .activity-item {
        @apply py-3 border-b border-gray-100 flex items-start;
    }
    .activity-icon {
        @apply mr-3 p-2 rounded-full;
    }
</style>
@endsection

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Success message -->
        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4 alert-dismissible" role="alert">
                <span class="block sm:inline">{{ session('success') }}</span>
                <button type="button" class="absolute top-0 right-0 px-4 py-3" onclick="this.parentElement.remove()">
                    <span class="sr-only">Close</span>
                    <svg class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"/>
                    </svg>
                </button>
            </div>
        @endif

        <!-- Profile Header -->
        <div class="profile-header">
            <div class="flex flex-col sm:flex-row items-center sm:items-start">
                <div class="flex-shrink-0 mb-4 sm:mb-0 sm:mr-6">
                    @if(Auth::user()->profile_photo_path)
                        <!-- Debug info - will be removed after fixing -->
                        <div class="text-xs text-white mb-2">Debug: {{ Auth::user()->profile_photo_path }}</div>

                        <img class="profile-avatar"
                             src="{{ asset('storage/' . Auth::user()->profile_photo_path) }}"
                             alt="{{ Auth::user()->name }}"
                             onerror="
                                if (!this.dataset.tried) {
                                    this.dataset.tried = true;
                                    this.src = '{{ url('/') }}/storage/{{ Auth::user()->profile_photo_path }}';
                                } else {
                                    this.onerror=null;
                                    this.src='{{ asset('images/default-avatar.png') }}';
                                    console.log('Error loading image from all paths');
                                }
                             ">
                    @else
                        <div class="profile-avatar bg-gray-300 flex items-center justify-center text-gray-500">
                            <svg class="h-12 w-12" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                        </div>
                    @endif
                </div>
                <div class="text-center sm:text-left">
                    <h1 class="text-2xl font-bold">{{ Auth::user()->name }}</h1>
                    <p class="text-gray-900 font-medium">{{ Auth::user()->email }}</p>
                    <div class="mt-2">
                        @foreach(Auth::user()->roles as $role)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1">
                                {{ $role->name }}
                            </span>
                        @endforeach
                    </div>
                    <p class="mt-2 text-sm">Member since {{ Auth::user()->created_at->format('F Y') }}</p>
                </div>
            </div>
        </div>

        <div x-data="{ activeTab: window.location.hash ? window.location.hash.substring(1) : 'profile' }">
            <!-- Tab Navigation -->
            <div class="mb-6 bg-white rounded-lg shadow-sm">
                <div class="border-b border-gray-200">
                    <nav class="flex -mb-px overflow-x-auto">
                        <button @click="activeTab = 'profile'; window.location.hash = 'profile'"
                                :class="{ 'border-blue-500 text-blue-600': activeTab === 'profile', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'profile' }"
                                class="tab-button whitespace-nowrap border-b-2">
                            <svg class="w-5 h-5 inline-block mr-1 -mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            Personal Information
                        </button>
                        <button @click="activeTab = 'security'; window.location.hash = 'security'"
                                :class="{ 'border-blue-500 text-blue-600': activeTab === 'security', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'security' }"
                                class="tab-button whitespace-nowrap border-b-2">
                            <svg class="w-5 h-5 inline-block mr-1 -mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0H10m5-4a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            Security
                        </button>
                        <button @click="activeTab = 'activity'; window.location.hash = 'activity'"
                                :class="{ 'border-blue-500 text-blue-600': activeTab === 'activity', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'activity' }"
                                class="tab-button whitespace-nowrap border-b-2">
                            <svg class="w-5 h-5 inline-block mr-1 -mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Activity
                        </button>
                    </nav>
                </div>
            </div>

            <!-- Personal Information -->
            <div x-show="activeTab === 'profile'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                <div class="profile-card">
                    <h2 class="section-title">Personal Information</h2>
                    <form action="{{ route('profile.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text" name="name" id="name" class="form-input" value="{{ Auth::user()->name }}" required>
                                @error('name')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" name="email" id="email" class="form-input" value="{{ Auth::user()->email }}" required>
                                @error('email')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" name="phone" id="phone" class="form-input" value="{{ Auth::user()->phone ?? '' }}">
                                @error('phone')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label for="position" class="form-label">Position/Job Title</label>
                                <input type="text" name="position" id="position" class="form-input" value="{{ Auth::user()->position ?? '' }}">
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <label for="bio" class="form-label">Bio</label>
                            <textarea name="bio" id="bio" rows="3" class="form-input">{{ Auth::user()->bio ?? '' }}</textarea>
                        </div>

                        <div class="mt-6">
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                                Update Profile
                            </button>
                        </div>
                    </form>
                </div>

                <div class="profile-card">
                    <h2 class="section-title">Profile Photo</h2>
                    <form action="{{ route('profile.photo.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="flex items-center">
                            <div class="flex-shrink-0 mr-4">
                                @if(Auth::user()->profile_photo_path)
                                    <img class="h-16 w-16 rounded-full" src="{{ asset('storage/' . Auth::user()->profile_photo_path) }}" alt="{{ Auth::user()->name }}">
                                @else
                                    <div class="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center">
                                        <svg class="h-8 w-8 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                                        </svg>
                                    </div>
                                @endif
                            </div>

                            <div class="flex-1">
                                <input type="file" name="photo" id="photo" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                <p class="mt-1 text-xs text-gray-500">JPG, PNG or GIF up to 2MB</p>
                                @error('photo')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="mt-4 flex space-x-3">
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                                Update Photo
                            </button>
                            @if(Auth::user()->profile_photo_path)
                                <button type="button" onclick="document.getElementById('remove-photo-form').submit()" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200">
                                    Remove Photo
                                </button>
                                <form id="remove-photo-form" action="{{ route('profile.photo.remove') }}" method="POST" class="hidden">
                                    @csrf
                                    @method('DELETE')
                                </form>
                            @endif
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Settings -->
            <div x-show="activeTab === 'security'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                <div class="profile-card">
                    <h2 class="section-title">Change Password</h2>
                    <form action="{{ route('profile.password.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="form-group">
                            <label for="current_password" class="form-label">Current Password</label>
                            <input type="password" name="current_password" id="current_password" class="form-input" required>
                            @error('current_password')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="password" class="form-label">New Password</label>
                            <input type="password" name="password" id="password" class="form-input" required>
                            @error('password')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="password_confirmation" class="form-label">Confirm New Password</label>
                            <input type="password" name="password_confirmation" id="password_confirmation" class="form-input" required>
                        </div>

                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mt-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-yellow-700">
                                        Your password must be at least 8 characters and contain a mix of letters, numbers, and symbols.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                                Change Password
                            </button>
                        </div>
                    </form>
                </div>

                <div class="profile-card">
                    <h2 class="section-title">Two-Factor Authentication</h2>
                    <div class="mb-4">
                        @if(Auth::user()->two_factor_enabled)
                            <p class="text-green-600 flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                Two-factor authentication is enabled
                            </p>
                            <button type="button" class="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200">
                                Disable Two-Factor Authentication
                            </button>
                        @else
                            <p class="text-gray-600 flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                                Two-factor authentication is not enabled
                            </p>
                            <p class="mt-2 text-sm text-gray-600">Add an additional layer of security to your account by enabling two-factor authentication.</p>
                            <button type="button" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                                Enable Two-Factor Authentication
                            </button>
                        @endif
                    </div>
                </div>

                <div class="profile-card">
                    <h2 class="section-title flex justify-between items-center">
                        <span>Login Sessions</span>
                        <button type="button" class="text-sm text-red-600 hover:text-red-800">
                            Log out of all other browser sessions
                        </button>
                    </h2>

                    <p class="text-sm text-gray-600 mb-4">If necessary, you may log out of all of your other browser sessions across all of your devices. Some of your recent sessions are listed below.</p>

                    <div class="space-y-3">
                        <div class="py-2 border-b border-gray-100 flex items-center justify-between">
                            <div class="flex items-center">
                                <svg class="w-8 h-8 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Windows - Chrome</div>
                                    <div class="text-xs text-gray-500">
                                        <span class="font-semibold text-green-600">This device</span> ·
                                        <span>IP: ***********</span> ·
                                        <span>Last active: Now</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="py-2 border-b border-gray-100 flex items-center justify-between">
                            <div class="flex items-center">
                                <svg class="w-8 h-8 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                </svg>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">iPhone - Safari</div>
                                    <div class="text-xs text-gray-500">
                                        <span>IP: ***********</span> ·
                                        <span>Last active: 2 hours ago</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Activity -->
            <div x-show="activeTab === 'activity'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                <div class="profile-card">
                    <h2 class="section-title">Recent Activity</h2>

                    <div class="space-y-1">
                        @forelse($recentActivities ?? [] as $activity)
                        <div class="activity-item">
                            <div class="activity-icon
                                @if($activity->type == 'login') bg-blue-100
                                @elseif($activity->type == 'profile_update') bg-green-100
                                @elseif($activity->type == 'password_change') bg-yellow-100
                                @else bg-gray-100
                                @endif">
                                @if($activity->type == 'login')
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                                </svg>
                                @elseif($activity->type == 'profile_update')
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                @elseif($activity->type == 'password_change')
                                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                                </svg>
                                @else
                                <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                @endif
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">
                                    @if($activity->type == 'login')
                                        Logged in
                                    @elseif($activity->type == 'profile_update')
                                        Profile updated
                                    @elseif($activity->type == 'password_change')
                                        Password changed
                                    @else
                                        {{ ucfirst(str_replace('_', ' ', $activity->type)) }}
                                    @endif
                                </p>
                                <p class="text-xs text-gray-500">
                                    @if($activity->type == 'login')
                                        IP: {{ $activity->ip_address }} ·
                                        Browser: {{ Str::before(Str::after($activity->user_agent ?? '', ' '), '/') }} ·
                                    @endif
                                    {{ $activity->created_at->diffForHumans() }}
                                </p>
                            </div>
                        </div>
                        @empty
                        <div class="py-4 text-center text-gray-500">
                            <p>No recent activity recorded.</p>
                        </div>
                        @endforelse
                    </div>

                    @if(count($recentActivities ?? []) > 0)
                    <div class="mt-6 text-center">
                        <button type="button" class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                            View all activity
                        </button>
                    </div>
                    @endif
                </div>

                <div class="profile-card">
                    <h2 class="section-title">Account Statistics</h2>

                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="p-4 border border-gray-200 rounded-lg text-center">
                            <p class="text-sm text-gray-600">Total Logins</p>
                            <p class="text-3xl font-bold text-gray-900 mt-2">{{ $loginCount ?? 0 }}</p>
                        </div>

                        <div class="p-4 border border-gray-200 rounded-lg text-center">
                            <p class="text-sm text-gray-600">Account Age</p>
                            <p class="text-3xl font-bold text-gray-900 mt-2">{{ now()->diffInDays(Auth::user()->created_at) }} days</p>
                        </div>

                        <div class="p-4 border border-gray-200 rounded-lg text-center">
                            <p class="text-sm text-gray-600">Last Password Change</p>
                            <p class="text-3xl font-bold text-gray-900 mt-2">
                                @if($daysSincePasswordChange !== null)
                                    {{ $daysSincePasswordChange }} {{ Str::plural('day', $daysSincePasswordChange) }} ago
                                @else
                                    Never
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if there's a hash in the URL and set the tab accordingly
    const hash = window.location.hash.substring(1);
    if (hash && ['profile', 'security', 'activity'].includes(hash)) {
        Alpine.store('tabState', {
            activeTab: hash
        });
    }

    // Initialize file input custom styling
    const photoInput = document.getElementById('photo');
    if (photoInput) {
        photoInput.addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name;
            if (fileName) {
                // You could display the filename if needed
            }
        });
    }

    // Auto-hide alerts after 5 seconds
    setTimeout(() => {
        document.querySelectorAll('.alert-dismissible').forEach(alert => {
            alert.classList.add('opacity-0');
            setTimeout(() => alert.remove(), 300);
        });
    }, 5000);
});
</script>
@endpush

@endsection

