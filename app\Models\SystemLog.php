<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class SystemLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'level',
        'message',
        'context',
        'channel',
        'datetime',
        'user_id',
        'organization_id',
        'ip_address',
        'user_agent',
        'url',
        'method',
        'status_code',
        'response_time',
        'memory_usage',
        'tags',
        'environment',
        'session_id',
        'request_id',
    ];

    protected $casts = [
        'context' => 'array',
        'tags' => 'array',
        'datetime' => 'datetime',
        'response_time' => 'float',
        'memory_usage' => 'integer',
    ];

    // Log levels
    const LEVEL_EMERGENCY = 'emergency';
    const LEVEL_ALERT = 'alert';
    const LEVEL_CRITICAL = 'critical';
    const LEVEL_ERROR = 'error';
    const LEVEL_WARNING = 'warning';
    const LEVEL_NOTICE = 'notice';
    const LEVEL_INFO = 'info';
    const LEVEL_DEBUG = 'debug';

    // Log channels
    const CHANNEL_APPLICATION = 'application';
    const CHANNEL_AUTHENTICATION = 'authentication';
    const CHANNEL_DATABASE = 'database';
    const CHANNEL_SECURITY = 'security';
    const CHANNEL_PERFORMANCE = 'performance';
    const CHANNEL_API = 'api';
    const CHANNEL_IMPERSONATION = 'impersonation';

    /**
     * Get the user associated with the log entry
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the organization associated with the log entry
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Scope for filtering by level
     */
    public function scopeLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Scope for filtering by channel
     */
    public function scopeChannel($query, $channel)
    {
        return $query->where('channel', $channel);
    }

    /**
     * Scope for filtering by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('datetime', [$startDate, $endDate]);
    }

    /**
     * Scope for filtering by organization
     */
    public function scopeForOrganization($query, $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Scope for filtering by user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for searching in message and context
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function($q) use ($search) {
            $q->where('message', 'like', "%{$search}%")
              ->orWhere('context', 'like', "%{$search}%")
              ->orWhere('url', 'like', "%{$search}%");
        });
    }

    /**
     * Scope for recent logs
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('datetime', '>=', now()->subHours($hours));
    }

    /**
     * Scope for critical logs (emergency, alert, critical, error)
     */
    public function scopeCritical($query)
    {
        return $query->whereIn('level', [
            self::LEVEL_EMERGENCY,
            self::LEVEL_ALERT,
            self::LEVEL_CRITICAL,
            self::LEVEL_ERROR
        ]);
    }

    /**
     * Get formatted level with color
     */
    public function getLevelBadgeAttribute()
    {
        $colors = [
            self::LEVEL_EMERGENCY => 'danger',
            self::LEVEL_ALERT => 'danger',
            self::LEVEL_CRITICAL => 'danger',
            self::LEVEL_ERROR => 'danger',
            self::LEVEL_WARNING => 'warning',
            self::LEVEL_NOTICE => 'info',
            self::LEVEL_INFO => 'primary',
            self::LEVEL_DEBUG => 'secondary',
        ];

        $color = $colors[$this->level] ?? 'secondary';
        return "<span class='badge bg-{$color}'>" . ucfirst($this->level) . "</span>";
    }

    /**
     * Get formatted context for display
     */
    public function getFormattedContextAttribute()
    {
        if (empty($this->context)) {
            return null;
        }

        return json_encode($this->context, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    }

    /**
     * Get response time in human readable format
     */
    public function getFormattedResponseTimeAttribute()
    {
        if (!$this->response_time) {
            return null;
        }

        if ($this->response_time < 1) {
            return round($this->response_time * 1000) . 'ms';
        }

        return round($this->response_time, 2) . 's';
    }

    /**
     * Get memory usage in human readable format
     */
    public function getFormattedMemoryUsageAttribute()
    {
        if (!$this->memory_usage) {
            return null;
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = $this->memory_usage;
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if log is critical
     */
    public function isCritical(): bool
    {
        return in_array($this->level, [
            self::LEVEL_EMERGENCY,
            self::LEVEL_ALERT,
            self::LEVEL_CRITICAL,
            self::LEVEL_ERROR
        ]);
    }

    /**
     * Get all available log levels
     */
    public static function getLogLevels(): array
    {
        return [
            self::LEVEL_EMERGENCY => 'Emergency',
            self::LEVEL_ALERT => 'Alert',
            self::LEVEL_CRITICAL => 'Critical',
            self::LEVEL_ERROR => 'Error',
            self::LEVEL_WARNING => 'Warning',
            self::LEVEL_NOTICE => 'Notice',
            self::LEVEL_INFO => 'Info',
            self::LEVEL_DEBUG => 'Debug',
        ];
    }

    /**
     * Get all available log channels
     */
    public static function getLogChannels(): array
    {
        return [
            self::CHANNEL_APPLICATION => 'Application',
            self::CHANNEL_AUTHENTICATION => 'Authentication',
            self::CHANNEL_DATABASE => 'Database',
            self::CHANNEL_SECURITY => 'Security',
            self::CHANNEL_PERFORMANCE => 'Performance',
            self::CHANNEL_API => 'API',
            self::CHANNEL_IMPERSONATION => 'Impersonation',
        ];
    }

    /**
     * Create a system log entry
     */
    public static function createLog(
        string $level,
        string $message,
        array $context = [],
        string $channel = self::CHANNEL_APPLICATION
    ): self {
        return self::create([
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'channel' => $channel,
            'datetime' => now(),
            'user_id' => auth()->id(),
            'organization_id' => auth()->user()->organization_id ?? null,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'method' => request()->method(),
            'session_id' => session()->getId(),
            'request_id' => request()->header('X-Request-ID', uniqid()),
            'environment' => app()->environment(),
        ]);
    }
}
