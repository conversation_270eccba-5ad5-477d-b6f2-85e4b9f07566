<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currency_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique(); // Setting key
            $table->text('value'); // Setting value (JSON or string)
            $table->string('type', 20)->default('string'); // string, json, boolean, integer
            $table->text('description')->nullable();
            $table->timestamps();
        });

        // Insert default settings
        DB::table('currency_settings')->insert([
            [
                'key' => 'default_currency',
                'value' => 'USD',
                'type' => 'string',
                'description' => 'Default currency for the application',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'nigerian_ip_ranges',
                'value' => json_encode([
                    '********/8',
                    '*********/8',
                    '***********/16',
                    '*********/8',
                    '*********/8',
                    '*********/8'
                ]),
                'type' => 'json',
                'description' => 'IP ranges for Nigerian users',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'auto_currency_detection',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Enable automatic currency detection based on IP',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'currency_decimal_places',
                'value' => '2',
                'type' => 'integer',
                'description' => 'Number of decimal places for currency display',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currency_settings');
    }
};
