@extends('layouts.app')

@section('content')
<div class="container-fluid px-4">
    <h1 class="mt-4">Organization Details</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item active">Organization</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-building me-1"></i>
                Organization Information
            </div>
            <a href="{{ route('organization.edit') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-edit"></i> Edit
            </a>
        </div>
        <div class="card-body">
            <div class="row">
                @if($organization->logo)
                <div class="col-md-3 text-center mb-4">
                    <img src="{{ asset('storage/logos/' . $organization->logo) }}"
                         alt="{{ $organization->name }} Logo"
                         class="img-fluid rounded" style="max-height: 150px;">
                </div>
                @endif

                <div class="col-md-{{ $organization->logo ? '9' : '12' }}">
                    <table class="table table-striped">
                        <tr>
                            <th style="width: 200px;">Organization Name</th>
                            <td>{{ $organization->name }}</td>
                        </tr>
                        <tr>
                            <th>Email</th>
                            <td>{{ $organization->email ?? 'Not specified' }}</td>
                        </tr>
                        <tr>
                            <th>Phone</th>
                            <td>{{ $organization->phone ?? 'Not specified' }}</td>
                        </tr>
                        <tr>
                            <th>Address</th>
                            <td>{{ $organization->address ?? 'Not specified' }}</td>
                        </tr>
                        <tr>
                            <th>Website</th>
                            <td>
                                @if($organization->website)
                                <a href="{{ $organization->website }}" target="_blank">
                                    {{ $organization->website }}
                                </a>
                                @else
                                Not specified
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Description</th>
                            <td>{{ $organization->description ?? 'No description available' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-code-branch me-1"></i>
            Branches
        </div>
        <div class="card-body">
            <div class="d-flex justify-content-end mb-3">
                <a href="{{ route('branches.create') }}" class="btn btn-success">
                    <i class="fas fa-plus"></i> Add New Branch
                </a>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="branches-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Address</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($organization->branches as $branch)
                        <tr>
                            <td>{{ $branch->name }}</td>
                            <td>{{ $branch->email ?? 'N/A' }}</td>
                            <td>{{ $branch->phone ?? 'N/A' }}</td>
                            <td>{{ $branch->address ?? 'N/A' }}</td>
                            <td>
                                <div class="d-flex flex-wrap gap-1">
                                    <a href="{{ route('branches.show', $branch) }}" class="btn btn-info btn-sm" title="View">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="{{ route('branches.edit', $branch) }}" class="btn btn-primary btn-sm" title="Edit">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>

                                    @if(count($organization->branches) > 1)
                                    <form action="{{ route('branches.destroy', $branch) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger btn-sm" title="Delete"
                                            onclick="return confirm('Are you sure you want to delete this branch?')">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </form>
                                    @endif

                                    <form action="{{ route('branches.switch') }}" method="POST" class="d-inline">
                                        @csrf
                                        <input type="hidden" name="branch_id" value="{{ $branch->id }}">
                                        <button type="submit" class="btn btn-success btn-sm" title="Switch"
                                            {{ auth()->user()->branch_id == $branch->id ? 'disabled' : '' }}>
                                            <i class="fas fa-exchange-alt"></i> Switch
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="5" class="text-center">No branches found.</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        $('#branches-table').DataTable({
            responsive: true
        });
    });
</script>
@endpush
