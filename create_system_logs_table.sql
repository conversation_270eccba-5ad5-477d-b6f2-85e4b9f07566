CREATE TABLE `system_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `level` varchar(20) NOT NULL,
  `message` text NOT NULL,
  `context` json DEFAULT NULL,
  `channel` varchar(50) NOT NULL,
  `datetime` timestamp NOT NULL,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `organization_id` bigint(20) unsigned DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `url` text DEFAULT NULL,
  `method` varchar(10) DEFAULT NULL,
  `status_code` int(11) DEFAULT NULL,
  `response_time` decimal(8,3) DEFAULT NULL,
  `memory_usage` bigint(20) DEFAULT NULL,
  `tags` json DEFAULT NULL,
  `environment` varchar(20) DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `request_id` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `system_logs_level_index` (`level`),
  KEY `system_logs_channel_index` (`channel`),
  KEY `system_logs_datetime_index` (`datetime`),
  KEY `system_logs_user_id_index` (`user_id`),
  KEY `system_logs_organization_id_index` (`organization_id`),
  KEY `system_logs_session_id_index` (`session_id`),
  KEY `system_logs_request_id_index` (`request_id`),
  KEY `system_logs_level_datetime_index` (`level`,`datetime`),
  KEY `system_logs_channel_datetime_index` (`channel`,`datetime`),
  KEY `system_logs_organization_id_datetime_index` (`organization_id`,`datetime`),
  KEY `system_logs_user_id_datetime_index` (`user_id`,`datetime`),
  KEY `system_logs_datetime_level_channel_index` (`datetime`,`level`,`channel`),
  CONSTRAINT `system_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `system_logs_organization_id_foreign` FOREIGN KEY (`organization_id`) REFERENCES `organizations` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
