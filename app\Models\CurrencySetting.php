<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CurrencySetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
    ];

    /**
     * Get a setting value by key
     */
    public static function get($key, $default = null)
    {
        $setting = static::where('key', $key)->first();
        
        if (!$setting) {
            return $default;
        }

        return static::castValue($setting->value, $setting->type);
    }

    /**
     * Set a setting value
     */
    public static function set($key, $value, $type = 'string', $description = null)
    {
        $setting = static::updateOrCreate(
            ['key' => $key],
            [
                'value' => is_array($value) || is_object($value) ? json_encode($value) : $value,
                'type' => $type,
                'description' => $description,
            ]
        );

        return $setting;
    }

    /**
     * Cast value to appropriate type
     */
    protected static function castValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
            case 'decimal':
                return (float) $value;
            case 'json':
            case 'array':
                return json_decode($value, true);
            case 'string':
            default:
                return $value;
        }
    }

    /**
     * Get all settings as key-value pairs
     */
    public static function getAll()
    {
        $settings = static::all();
        $result = [];

        foreach ($settings as $setting) {
            $result[$setting->key] = static::castValue($setting->value, $setting->type);
        }

        return $result;
    }

    /**
     * Check if auto currency detection is enabled
     */
    public static function isAutoCurrencyDetectionEnabled()
    {
        return static::get('auto_currency_detection', true);
    }

    /**
     * Get default currency
     */
    public static function getDefaultCurrency()
    {
        return static::get('default_currency', 'USD');
    }

    /**
     * Get Nigerian IP ranges
     */
    public static function getNigerianIpRanges()
    {
        return static::get('nigerian_ip_ranges', []);
    }

    /**
     * Get currency decimal places
     */
    public static function getCurrencyDecimalPlaces()
    {
        return static::get('currency_decimal_places', 2);
    }
}
