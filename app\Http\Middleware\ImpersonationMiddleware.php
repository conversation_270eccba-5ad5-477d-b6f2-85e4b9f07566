<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\ImpersonationService;
use Illuminate\Support\Facades\Auth;

class ImpersonationMiddleware
{
    protected $impersonationService;

    public function __construct(ImpersonationService $impersonationService)
    {
        $this->impersonationService = $impersonationService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if currently impersonating
        if ($this->impersonationService->isImpersonating()) {

            // Check if impersonation has expired
            if ($this->impersonationService->hasExpired()) {
                $this->impersonationService->forceStopImpersonation('timeout');

                return redirect()->route('super_admin.login')
                    ->with('warning', 'Impersonation session has expired. Please log in again.');
            }

            // Add impersonation data to view
            $impersonationData = $this->impersonationService->getImpersonationData();
            $remainingTime = $this->impersonationService->getRemainingTime();

            view()->share('impersonationData', $impersonationData);
            view()->share('impersonationRemainingTime', $remainingTime);
            view()->share('isImpersonating', true);
        } else {
            view()->share('isImpersonating', false);
        }

        return $next($request);
    }
}
