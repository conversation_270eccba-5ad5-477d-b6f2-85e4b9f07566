<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Schema;

class SubscriptionPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'subscription_id',
        'organization_id',
        'payment_reference',
        'amount',
        'payment_method',
        'status',
        'notes',
        'payment_date',
        'approved_at',
        'approved_by',
        'invoice_number',
        'invoice_generated_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_date' => 'datetime',
        'approved_at' => 'datetime',
        'invoice_generated_at' => 'datetime',
    ];

    /**
     * Get the subscription that owns the payment.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Get the organization that owns the payment.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the user who approved the payment.
     * Note: This might return null if approved by super admin
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the name of who approved the payment.
     */
    public function getApproverNameAttribute(): string
    {
        if ($this->approvedBy) {
            return $this->approvedBy->name;
        }

        // Try to get super admin name
        try {
            $superAdmin = \App\Models\SuperAdmin::find($this->approved_by);
            return $superAdmin ? $superAdmin->name : 'System';
        } catch (\Exception $e) {
            return 'System';
        }
    }

    /**
     * Check if payment is pending approval.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if payment is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if payment is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Approve the payment.
     */
    public function approve($approvedBy): void
    {
        $this->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => $approvedBy ? $approvedBy->id : null,
        ]);
    }

    /**
     * Reject the payment.
     */
    public function reject($rejectedBy, string $reason = null): void
    {
        $notes = $this->notes;
        if ($reason) {
            $notes = $notes ? $notes . "\n\nRejection reason: " . $reason : "Rejection reason: " . $reason;
        }

        $this->update([
            'status' => 'rejected',
            'notes' => $notes,
        ]);
    }

    /**
     * Generate invoice number.
     */
    public function generateInvoiceNumber(): string
    {
        $prefix = 'INV';
        $year = now()->year;
        $month = now()->format('m');
        $sequence = str_pad($this->id, 4, '0', STR_PAD_LEFT);

        return "{$prefix}-{$year}{$month}-{$sequence}";
    }

    /**
     * Scope for pending payments.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved payments.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for rejected payments.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }
}
