<?php

namespace App\Http\Traits;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

trait RespondsWithJson
{
    /**
     * Format the response based on request type
     */
    protected function respondWithSuccess(
        string $message, 
        array $data = [], 
        string $redirectRoute = null
    ): JsonResponse|RedirectResponse {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if (!empty($data)) {
            $response['data'] = $data;
        }

        if ($redirectRoute) {
            $response['redirect'] = route($redirectRoute);
        }

        if (request()->wantsJson()) {
            return response()->json($response);
        }

        return redirect()->route($redirectRoute ?? 'users.index')
            ->with('success', $message);
    }

    /**
     * Format error response based on request type
     */
    protected function respondWithError(
        string $message, 
        int $statusCode = 422, 
        array $errors = []
    ): JsonResponse|RedirectResponse {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        if (request()->wantsJson()) {
            return response()->json($response, $statusCode);
        }

        return back()->withInput()
            ->withErrors(['error' => $message]);
    }
}