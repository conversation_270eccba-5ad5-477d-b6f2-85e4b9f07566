@extends('layouts.app')

@section('styles')
<link href="{{ asset('css/sweetalert2.min.css') }}" rel="stylesheet">
@endsection

@section('scripts')
<script src="{{ asset('js/sweetalert2.min.js') }}"></script>
<script src="{{ asset('js/user-management.js') }}"></script>
@endsection

@section('content')
<x-slot name="header">
    <h2 class="font-semibold text-xl text-gray-800 leading-tight">
        {{ __('Create New User') }}
    </h2>
</x-slot>

<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Plan Limit Warning -->
        @if(isset($planUsage))
            <div class="mb-6">
                @if(!$canAddUser)
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-red-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="flex-1">
                                <h3 class="text-sm font-medium text-red-800">User Limit Reached</h3>
                                <p class="mt-1 text-sm text-red-700">
                                    You have reached your {{ $planUsage['plan_name'] }} plan limit of {{ $planUsage['users']['limit'] }} users.
                                    You cannot add more users until you upgrade your plan or remove existing users.
                                </p>
                                <div class="mt-3 flex space-x-3">
                                    <a href="{{ route('billing.index') }}" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                        Upgrade Plan
                                    </a>
                                    <a href="{{ route('users.index') }}" class="inline-flex items-center px-3 py-2 border border-red-300 text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                        Manage Users
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @elseif($remainingSlots <= 2 && $remainingSlots !== PHP_INT_MAX)
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="flex-1">
                                <h3 class="text-sm font-medium text-yellow-800">Approaching User Limit</h3>
                                <p class="mt-1 text-sm text-yellow-700">
                                    You have {{ $remainingSlots }} user slots remaining in your {{ $planUsage['plan_name'] }} plan.
                                    Consider upgrading to avoid hitting the limit.
                                </p>
                                <div class="mt-3">
                                    <a href="{{ route('billing.index') }}" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-yellow-800 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                                        View Upgrade Options
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        @endif

        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
                <form method="POST" action="{{ route('users.store') }}" class="space-y-6" id="createUserForm" novalidate>
                    @csrf

                    @if($errors->has('limit_exceeded'))
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-red-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                <div class="flex-1">
                                    <h3 class="text-sm font-medium text-red-800">User Limit Reached</h3>
                                    <p class="mt-1 text-sm text-red-700">{{ $errors->first('limit_exceeded') }}</p>
                                    @if(session('upgrade_required'))
                                        <div class="mt-3 flex space-x-3">
                                            <a href="{{ route('billing.index') }}" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                                                </svg>
                                                Upgrade Plan Now
                                            </a>
                                            <a href="{{ route('users.index') }}" class="inline-flex items-center px-3 py-2 border border-red-300 text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                                </svg>
                                                Manage Existing Users
                                            </a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @elseif($errors->any())
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
                            <ul class="list-disc list-inside">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <!-- Name -->
                    <div>
                        <x-input-label for="name" :value="__('Name')" />
                        <x-text-input id="name"
                                     class="block mt-1 w-full"
                                     type="text"
                                     name="name"
                                     :value="old('name')"
                                     required
                                     autofocus
                                     minlength="2"
                                     maxlength="255"
                                     data-validation-message="Name must be between 2 and 255 characters" />
                        <p class="mt-1 text-sm text-red-600 hidden" id="name-error"></p>
                    </div>

                    <!-- Email -->
                    <div>
                        <x-input-label for="email" :value="__('Email')" />
                        <x-text-input id="email"
                                     class="block mt-1 w-full"
                                     type="email"
                                     name="email"
                                     :value="old('email')"
                                     required
                                     pattern="[^@]+@[^@]+\.[a-zA-Z]{2,}"
                                     data-validation-message="Please enter a valid email address" />
                        <p class="mt-1 text-sm text-red-600 hidden" id="email-error"></p>
                    </div>

                    <!-- Password -->
                    <div>
                        <x-input-label for="password" :value="__('Password')" />
                        <div class="relative">
                            <x-text-input id="password"
                                         class="block mt-1 w-full pr-10"
                                         type="password"
                                         name="password"
                                         required
                                         autocomplete="new-password"
                                         minlength="8"
                                         data-validation-message="Password must be at least 8 characters" />
                            <button type="button"
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5 text-gray-500 hover:text-gray-700 focus:outline-none"
                                    onclick="togglePasswordVisibility('password')">
                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                            </button>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Password must contain at least 8 characters, including uppercase, lowercase, numbers, and symbols.</p>
                        <p class="mt-1 text-sm text-red-600 hidden" id="password-error"></p>
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <x-input-label for="password_confirmation" :value="__('Confirm Password')" />
                        <div class="relative">
                            <x-text-input id="password_confirmation"
                                         class="block mt-1 w-full pr-10"
                                         type="password"
                                         name="password_confirmation"
                                         required
                                         data-validation-message="Passwords must match" />
                            <button type="button"
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5 text-gray-500 hover:text-gray-700 focus:outline-none"
                                    onclick="togglePasswordVisibility('password_confirmation')">
                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                            </button>
                        </div>
                        <p class="mt-1 text-sm text-red-600 hidden" id="password-confirmation-error"></p>
                    </div>

                    <!-- Status -->
                    <div>
                        <x-input-label for="status" :value="__('Status')" />
                        <select id="status"
                                name="status"
                                class="block mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                                required>
                            <option value="">Select Status</option>
                            <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                        <p class="mt-1 text-sm text-red-600 hidden" id="status-error"></p>
                    </div>

                    <!-- Branch -->
                    <div>
                        <x-input-label for="branch_id" :value="__('Branch')" />
                        <select id="branch_id"
                                name="branch_id"
                                class="block mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                            <option value="">None (No Branch Assignment)</option>
                            @foreach($branches as $branch)
                                <option value="{{ $branch->id }}" {{ old('branch_id') == $branch->id ? 'selected' : '' }}>
                                    {{ $branch->name }}
                                </option>
                            @endforeach
                        </select>
                        <p class="mt-1 text-sm text-gray-500">Assign this user to a specific branch</p>
                        <p class="mt-1 text-sm text-red-600 hidden" id="branch_id-error"></p>
                    </div>

                    <!-- Roles -->
                    <div>
                        <x-input-label :value="__('Roles')" />
                        <div class="mt-2 space-y-2 max-h-48 overflow-y-auto border rounded-md p-3">
                            @foreach($roles as $role)
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           id="role_{{ $role->id }}"
                                           name="roles[]"
                                           value="{{ $role->id }}"
                                           class="rounded border-gray-300 text-primary shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                                           {{ (is_array(old('roles')) && in_array($role->id, old('roles'))) ? 'checked' : '' }}>
                                    <label for="role_{{ $role->id }}" class="ml-2 text-sm text-gray-600">
                                        {{ $role->name }}
                                    </label>
                                </div>
                            @endforeach
                        </div>
                        <p class="mt-1 text-sm text-red-600 hidden" id="roles-error"></p>
                    </div>

                    <div class="flex items-center justify-end mt-4">
                        <x-secondary-button type="button" onclick="confirmCancel()" class="mr-3">
                            {{ __('Cancel') }}
                        </x-secondary-button>
                        @if(isset($canAddUser) && !$canAddUser)
                            <button type="button" disabled class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-500 uppercase tracking-widest cursor-not-allowed">
                                {{ __('User Limit Reached') }}
                            </button>
                        @else
                            <x-primary-button>
                                {{ __('Create User') }}
                            </x-primary-button>
                        @endif
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function togglePasswordVisibility(fieldId) {
    const field = document.getElementById(fieldId);
    const type = field.type === 'password' ? 'text' : 'password';
    field.type = type;
}

function confirmCancel() {
    Swal.fire({
        title: 'Are you sure?',
        text: "You will lose all entered data!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, cancel',
        cancelButtonText: 'No, keep editing'
    }).then((result) => {
        if (result.isConfirmed) {
            window.history.back();
        }
    });
}

document.getElementById('createUserForm').addEventListener('submit', function(e) {
    let isValid = true;
    const password = document.getElementById('password');
    const confirmation = document.getElementById('password_confirmation');

    // Reset previous error messages
    document.querySelectorAll('.text-red-600').forEach(el => el.classList.add('hidden'));

    // Validate required fields
    this.querySelectorAll('[required]').forEach(field => {
        if (!field.value) {
            isValid = false;
            const errorEl = document.getElementById(`${field.id}-error`);
            if (errorEl) {
                errorEl.textContent = `${field.previousElementSibling.textContent.trim()} is required`;
                errorEl.classList.remove('hidden');
            }
        }
    });

    // Validate password match
    if (password.value !== confirmation.value) {
        isValid = false;
        document.getElementById('password-confirmation-error').textContent = 'Passwords do not match';
        document.getElementById('password-confirmation-error').classList.remove('hidden');
    }

    // Validate role selection
    const roleCheckboxes = document.querySelectorAll('input[name="roles[]"]:checked');
    if (roleCheckboxes.length === 0) {
        isValid = false;
        document.getElementById('roles-error').textContent = 'Please select at least one role';
        document.getElementById('roles-error').classList.remove('hidden');
    }

    if (!isValid) {
        e.preventDefault();
        Swal.fire({
            title: 'Validation Error',
            text: 'Please check the form for errors',
            icon: 'error',
            confirmButtonText: 'OK'
        });
    }
});
</script>
@endpush

@endsection
