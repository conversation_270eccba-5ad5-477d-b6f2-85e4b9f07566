@extends('layouts.app')

@section('title', 'Users')

@section('styles')
<link href="{{ asset('css/sweetalert2.min.css') }}" rel="stylesheet">
@endsection

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">

        <!-- User Statistics Cards -->
        <div class="mb-8">
            <div class="row g-4">
                <!-- Active Users Card -->
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100" style="border-left: 4px solid #28a745 !important;">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-3">
                                <div class="rounded-circle bg-success bg-opacity-10 p-3">
                                    <svg class="w-8 h-8 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24" width="32" height="32">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <h2 class="display-4 fw-bold text-success mb-2">{{ $userStats['active'] }}</h2>
                            <h5 class="card-title text-muted mb-1">Active Users</h5>
                            <small class="text-muted">{{ $userStats['percentage_active'] }}% of total users</small>
                        </div>
                    </div>
                </div>

                <!-- Inactive Users Card -->
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100" style="border-left: 4px solid #dc3545 !important;">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-3">
                                <div class="rounded-circle bg-danger bg-opacity-10 p-3">
                                    <svg class="w-8 h-8 text-danger" fill="none" stroke="currentColor" viewBox="0 0 24 24" width="32" height="32">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                    </svg>
                                </div>
                            </div>
                            <h2 class="display-4 fw-bold text-danger mb-2">{{ $userStats['inactive'] }}</h2>
                            <h5 class="card-title text-muted mb-1">Inactive Users</h5>
                            <small class="text-muted">Including archived users</small>
                        </div>
                    </div>
                </div>

                <!-- Total Users Card -->
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100" style="border-left: 4px solid #007bff !important;">
                        <div class="card-body text-center">
                            <div class="d-flex align-items-center justify-content-center mb-3">
                                <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                                    <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" width="32" height="32">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <h2 class="display-4 fw-bold text-primary mb-2">{{ $userStats['total'] }}</h2>
                            <h5 class="card-title text-muted mb-1">Total Users</h5>
                            <small class="text-muted">All users in organization</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Header -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 text-gray-900">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">User Management</h1>
                    <a href="{{ route('users.create') }}" class="btn btn-primary">
                        <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" width="16" height="16">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add New User
                    </a>
                </div>
            </div>
        </div>

        <!-- Role Permissions Guide -->
        <div class="bg-white shadow-lg rounded-lg mb-8 border border-gray-200">
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-4 rounded-t-lg">
                <h2 class="text-xl font-semibold flex items-center">
                    <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 2.676-1.148 5.16-2.904 7.323-5.114.559-.571 1.077-1.175 1.559-1.808A11.955 11.955 0 0021 9a12.02 12.02 0 00-.382-3.016z" />
                    </svg>
                    Role Permissions Guide
                </h2>
                <p class="text-blue-100 mt-1">Understanding what each role can access and do in your organization</p>
            </div>

            <div class="p-6">
                <!-- Quick Reference Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
                    <!-- Organization Owner Card -->
                    <div class="border-2 border-red-200 rounded-lg p-4 bg-red-50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center mb-3">
                            <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                            <h3 class="font-bold text-red-800">Organization Owner</h3>
                        </div>
                        <p class="text-sm text-red-700 mb-2">Complete system control</p>
                        <ul class="text-xs text-red-600 space-y-1">
                            <li>• Full administrative access</li>
                            <li>• User & role management</li>
                            <li>• Organization settings</li>
                            <li>• Financial oversight</li>
                        </ul>
                    </div>

                    <!-- Manager Card -->
                    <div class="border-2 border-orange-200 rounded-lg p-4 bg-orange-50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center mb-3">
                            <div class="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                            <h3 class="font-bold text-orange-800">Manager</h3>
                        </div>
                        <p class="text-sm text-orange-700 mb-2">Operations management</p>
                        <ul class="text-xs text-orange-600 space-y-1">
                            <li>• Order management</li>
                            <li>• Financial reports</li>
                            <li>• Expenditure approval</li>
                            <li>• Team oversight</li>
                        </ul>
                    </div>

                    <!-- Account Card -->
                    <div class="border-2 border-green-200 rounded-lg p-4 bg-green-50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center mb-3">
                            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                            <h3 class="font-bold text-green-800">Account</h3>
                        </div>
                        <p class="text-sm text-green-700 mb-2">Financial management</p>
                        <ul class="text-xs text-green-600 space-y-1">
                            <li>• Create expenditures</li>
                            <li>• Financial reports</li>
                            <li>• Account summaries</li>
                            <li>• View orders</li>
                        </ul>
                    </div>

                    <!-- Staff Card -->
                    <div class="border-2 border-blue-200 rounded-lg p-4 bg-blue-50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center mb-3">
                            <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                            <h3 class="font-bold text-blue-800">Staff</h3>
                        </div>
                        <p class="text-sm text-blue-700 mb-2">Order processing</p>
                        <ul class="text-xs text-blue-600 space-y-1">
                            <li>• Create & manage orders</li>
                            <li>• View orders</li>
                            <li>• View reports</li>
                            <li>• Limited access</li>
                        </ul>
                    </div>

                    <!-- Production Card -->
                    <div class="border-2 border-purple-200 rounded-lg p-4 bg-purple-50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center mb-3">
                            <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                            <h3 class="font-bold text-purple-800">Production</h3>
                        </div>
                        <p class="text-sm text-purple-700 mb-2">Production workflow</p>
                        <ul class="text-xs text-purple-600 space-y-1">
                            <li>• View orders</li>
                            <li>• Update order status</li>
                            <li>• Production tracking</li>
                            <li>• Limited access</li>
                        </ul>
                    </div>

                    <!-- Delivery Card -->
                    <div class="border-2 border-indigo-200 rounded-lg p-4 bg-indigo-50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center mb-3">
                            <div class="w-3 h-3 bg-indigo-500 rounded-full mr-2"></div>
                            <h3 class="font-bold text-indigo-800">Delivery</h3>
                        </div>
                        <p class="text-sm text-indigo-700 mb-2">Delivery operations</p>
                        <ul class="text-xs text-indigo-600 space-y-1">
                            <li>• View orders</li>
                            <li>• Update delivery status</li>
                            <li>• Delivery tracking</li>
                            <li>• Limited access</li>
                        </ul>
                    </div>
                </div>

                <!-- Detailed Permissions Table -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2h2a2 2 0 002-2z" />
                        </svg>
                        Detailed Permissions Matrix
                    </h3>

                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Feature/Action</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Owner</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Manager</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Account</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Staff</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Production</th>
                                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Delivery</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <!-- User Management -->
                                <tr class="bg-red-25">
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900 bg-red-50">User Management</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-700">Organization Settings</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                </tr>

                                <!-- Order Management -->
                                <tr class="bg-blue-25">
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900 bg-blue-50">Order Creation</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-700">View All Orders</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-700">Update Order Status</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-700">Update Delivery Status</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                </tr>

                                <!-- Financial Management -->
                                <tr class="bg-green-25">
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900 bg-green-50">Create Expenditures</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-700">Approve/Reject Expenditures</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-700">Financial Reports</td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-green-600 font-bold">✓</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                    <td class="px-3 py-3 text-center"><span class="text-red-600 font-bold">✗</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Role Assignment Recommendations -->
                <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-blue-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                        Role Assignment Recommendations
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-blue-800 mb-2">👑 Organization Owner</h4>
                            <p class="text-sm text-blue-700 mb-2">Assign to: Business owners, CEOs, or primary administrators</p>
                            <p class="text-xs text-blue-600">⚠️ Limit to 1-2 trusted individuals with full business authority</p>
                        </div>

                        <div>
                            <h4 class="font-semibold text-orange-800 mb-2">👨‍💼 Manager</h4>
                            <p class="text-sm text-orange-700 mb-2">Assign to: Department heads, operations managers</p>
                            <p class="text-xs text-orange-600">✓ Perfect for overseeing daily operations and approving expenditures</p>
                        </div>

                        <div>
                            <h4 class="font-semibold text-green-800 mb-2">💰 Account</h4>
                            <p class="text-sm text-green-700 mb-2">Assign to: Accountants, financial staff</p>
                            <p class="text-xs text-green-600">✓ Ideal for managing expenses and financial tracking</p>
                        </div>

                        <div>
                            <h4 class="font-semibold text-blue-800 mb-2">👥 Staff</h4>
                            <p class="text-sm text-blue-700 mb-2">Assign to: Sales staff, customer service representatives</p>
                            <p class="text-xs text-blue-600">✓ Best for front-line employees who handle customer orders</p>
                        </div>

                        <div>
                            <h4 class="font-semibold text-purple-800 mb-2">🏭 Production</h4>
                            <p class="text-sm text-purple-700 mb-2">Assign to: Production supervisors, manufacturing staff</p>
                            <p class="text-xs text-purple-600">✓ Focused on production workflow and order status updates</p>
                        </div>

                        <div>
                            <h4 class="font-semibold text-indigo-800 mb-2">🚚 Delivery</h4>
                            <p class="text-sm text-indigo-700 mb-2">Assign to: Delivery drivers, logistics coordinators</p>
                            <p class="text-xs text-indigo-600">✓ Specialized for delivery operations and status tracking</p>
                        </div>
                    </div>
                </div>

                <!-- Security Best Practices -->
                <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h4 class="font-semibold text-yellow-800 mb-2 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        Security Best Practices
                    </h4>
                    <ul class="text-sm text-yellow-700 space-y-1">
                        <li>• <strong>Principle of Least Privilege:</strong> Assign the minimum role necessary for job functions</li>
                        <li>• <strong>Regular Review:</strong> Periodically review and update user roles as responsibilities change</li>
                        <li>• <strong>Owner Access:</strong> Limit Organization Owner role to key decision-makers only</li>
                        <li>• <strong>Role Separation:</strong> Consider separating financial and operational roles for better control</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- User Management Table -->
        <div class="bg-white shadow-lg rounded-lg border border-gray-200">
            <!-- Tab Navigation -->
            <div class="border-b border-gray-200" x-data="{ activeTab: 'active' }">
                <nav class="-mb-px flex space-x-8 px-6 pt-4" aria-label="Tabs">
                    <button @click="activeTab = 'active'"
                            :class="activeTab === 'active' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Active Users ({{ $activeUsers->count() }})
                    </button>
                    <button @click="activeTab = 'inactive'"
                            :class="activeTab === 'inactive' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Inactive Users ({{ $inactiveUsers->count() }})
                    </button>
                    <button @click="activeTab = 'archived'"
                            :class="activeTab === 'archived' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                            class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Archived Users ({{ $archivedUsers->count() }})
                    </button>
                </nav>

                <!-- Search and Filters -->
                <div class="p-6 border-b border-gray-200">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text"
                                   id="userSearch"
                                   placeholder="Search users..."
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 search-input">
                        </div>
                        <div class="flex flex-col sm:flex-row gap-4 sm:items-center">
                            <select id="roleFilter"
                                    class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 filter-select">
                                <option value="">All Roles</option>
                                @foreach($roles as $role)
                                    <option value="{{ $role->id }}">{{ $role->name }}</option>
                                @endforeach
                            </select>
                            <select id="statusFilter"
                                    class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 filter-select">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="archived">Archived</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <!-- Active Users Table -->
                    <div x-show="activeTab === 'active'">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roles</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($activeUsers as $user)
                                    <tr data-user-id="{{ $user->id }}" class="bg-white hover:bg-gray-50">
                                        @include('users.partials.user-row', ['user' => $user])
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                        @if($activeUsers->count() === 0)
                            <div class="text-center py-8">
                                <p class="text-gray-500">No active users found.</p>
                            </div>
                        @endif
                    </div>

                    <!-- Inactive Users Table -->
                    <div x-show="activeTab === 'inactive'" x-cloak>
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roles</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($inactiveUsers as $user)
                                    <tr data-user-id="{{ $user->id }}" class="bg-white hover:bg-gray-50">
                                        @include('users.partials.user-row', ['user' => $user])
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                        @if($inactiveUsers->count() === 0)
                            <div class="text-center py-8">
                                <p class="text-gray-500">No inactive users found.</p>
                            </div>
                        @endif
                    </div>

                    <!-- Archived Users Table -->
                    <div x-show="activeTab === 'archived'" x-cloak>
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roles</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($archivedUsers as $user)
                                    <tr data-user-id="{{ $user->id }}" class="bg-white hover:bg-gray-50">
                                        @include('users.partials.user-row', ['user' => $user])
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                        @if($archivedUsers->count() === 0)
                            <div class="text-center py-8">
                                <p class="text-gray-500">No archived users found.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="{{ asset('js/sweetalert2.min.js') }}"></script>
<script>
// Immediate UserManagement definition as backup
if (typeof window.UserManagement === 'undefined') {
    console.log('Creating immediate UserManagement backup...');
    window.UserManagement = {
        async toggleStatus(userId) {
            try {
                console.log('Backup: Toggling status for user:', userId);

                const response = await fetch(`{{ url('/') }}/users/${userId}/toggle-status`, {
                    method: 'PATCH',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    credentials: 'same-origin'
                });

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    console.error('Non-JSON response received:', text.substring(0, 500));
                    throw new Error('Server returned HTML instead of JSON. Check server logs for errors.');
                }

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.message || `HTTP ${response.status}: Failed to update user status`);
                }

                Swal.fire({
                    title: 'Success!',
                    text: data.message,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });

                setTimeout(() => {
                    window.location.reload();
                }, 1500);

            } catch (error) {
                console.error('Toggle status error:', error);
                Swal.fire({
                    title: 'Error!',
                    text: error.message || 'Failed to update user status',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        },

        init() {
            console.log('Backup UserManagement initialized');
        }
    };
    console.log('Backup UserManagement created');
}
</script>
<script>
// Additional initialization for users index page
document.addEventListener('DOMContentLoaded', () => {
    console.log('Users index page loaded');

    // Debug: Check if UserManagement is available
    if (typeof UserManagement !== 'undefined') {
        console.log('UserManagement is available from external script');
        UserManagement.init();
    } else {
        console.error('UserManagement not found - external script may not have loaded');
        console.log('Creating fallback UserManagement object...');

        // Create fallback UserManagement object
        window.UserManagement = {
            async toggleStatus(userId) {
                try {
                    console.log('Fallback: Toggling status for user:', userId);

                    const response = await fetch(`/users/${userId}/toggle-status`, {
                        method: 'PATCH',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'Accept': 'application/json',
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        credentials: 'same-origin'
                    });

                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        const text = await response.text();
                        console.error('Non-JSON response received:', text.substring(0, 500));
                        throw new Error('Server returned HTML instead of JSON. Check server logs for errors.');
                    }

                    const data = await response.json();

                    if (!response.ok) {
                        throw new Error(data.message || `HTTP ${response.status}: Failed to update user status`);
                    }

                    Swal.fire({
                        title: 'Success!',
                        text: data.message,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });

                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);

                } catch (error) {
                    console.error('Toggle status error:', error);
                    Swal.fire({
                        title: 'Error!',
                        text: error.message || 'Failed to update user status',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            },

            init() {
                console.log('Fallback UserManagement initialized');
            }
        };

        UserManagement.init();
    }
});


</script>
@endpush

@endsection


