@extends('layouts.app')

@section('title', 'Knowledge Base')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Knowledge Base</h1>
                <div>
                    <a href="{{ route('user.support.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Support
                    </a>
                    <a href="{{ route('user.support.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Ticket
                    </a>
                </div>
            </div>

            <!-- Search Bar -->
            <div class="row mb-4">
                <div class="col-lg-8 mx-auto">
                    <form method="GET" action="{{ route('user.support.search') }}">
                        <div class="input-group input-group-lg">
                            <input type="text" name="q" class="form-control"
                                   placeholder="Search for help articles..."
                                   value="{{ request('q') }}">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i> Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Featured Articles -->
            @if($featuredArticles->count() > 0)
            <div class="row mb-5">
                <div class="col-12">
                    <h4 class="mb-3">
                        <i class="fas fa-star text-warning me-2"></i>
                        Featured Articles
                    </h4>
                    <div class="row">
                        @foreach($featuredArticles as $article)
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <a href="{{ route('user.support.article', $article->id) }}" class="text-decoration-none">
                                            {{ $article->title }}
                                        </a>
                                    </h5>
                                    <p class="card-text text-muted">{{ $article->truncated_excerpt }}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-folder me-1"></i>
                                            {{ $article->category->name ?? 'General' }}
                                        </small>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ $article->reading_time }}m read
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Categories -->
            <div class="row mb-5">
                <div class="col-12">
                    <h4 class="mb-3">
                        <i class="fas fa-folder-open me-2"></i>
                        Browse by Category
                    </h4>
                    <div class="row">
                        @foreach($categories as $category)
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    @if($category->icon)
                                        <i class="{{ $category->icon }} fa-3x mb-3"
                                           style="color: {{ $category->color ?? '#007bff' }}"></i>
                                    @else
                                        <i class="fas fa-folder fa-3x mb-3 text-primary"></i>
                                    @endif
                                    <h5 class="card-title">{{ $category->name }}</h5>
                                    <p class="card-text text-muted">{{ $category->description }}</p>
                                    <div class="mt-auto">
                                        <a href="{{ route('user.support.category', $category->id) }}"
                                           class="btn btn-outline-primary">
                                            View Articles
                                            <span class="badge bg-primary ms-2">{{ $category->published_articles_count ?? 0 }}</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Popular Articles -->
            @if($popularArticles->count() > 0)
            <div class="row">
                <div class="col-12">
                    <h4 class="mb-3">
                        <i class="fas fa-fire text-danger me-2"></i>
                        Popular Articles
                    </h4>
                    <div class="row">
                        @foreach($popularArticles as $article)
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <a href="{{ route('user.support.article', $article->id) }}" class="text-decoration-none">
                                            {{ $article->title }}
                                        </a>
                                    </h5>
                                    <p class="card-text text-muted">{{ $article->truncated_excerpt }}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-folder me-1"></i>
                                            {{ $article->category->name ?? 'General' }}
                                        </small>
                                        <div>
                                            <span class="badge bg-primary">{{ $article->view_count }} views</span>
                                            @if($article->helpfulness_percentage)
                                                <span class="badge bg-success">{{ $article->helpfulness_percentage }}% helpful</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Quick Help Section -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5 class="card-title">
                                <i class="fas fa-question-circle me-2"></i>
                                Can't find what you're looking for?
                            </h5>
                            <p class="card-text">Our support team is here to help you with any questions or issues.</p>
                            <div class="d-flex justify-content-center gap-3">
                                <a href="{{ route('user.support.create') }}" class="btn btn-primary">
                                    <i class="fas fa-envelope me-2"></i>
                                    Contact Support
                                </a>
                                <a href="{{ route('user.support.search') }}?q=getting+started" class="btn btn-outline-info">
                                    <i class="fas fa-play me-2"></i>
                                    Getting Started Guide
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.card-title a {
    color: inherit;
}

.card-title a:hover {
    color: #007bff;
}

.input-group-lg .form-control {
    border-radius: 0.5rem 0 0 0.5rem;
}

.input-group-lg .btn {
    border-radius: 0 0.5rem 0.5rem 0;
}
</style>
@endsection
