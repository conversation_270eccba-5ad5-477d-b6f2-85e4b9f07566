@extends('layouts.auth')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-5">
        <div class="card shadow-lg">
            <div class="card-header text-white">
                <div class="shape-1"></div>
                <div class="shape-2"></div>
                <h3 class="text-center font-weight-light my-2">Reset Password</h3>
            </div>
            <div class="card-body">
                @if(session('status'))
                <div class="alert alert-success mb-4">
                    <i class="fas fa-check-circle me-2"></i>{{ session('status') }}
                </div>
                @endif

                <p class="mb-4 text-muted">Forgot your password? No problem. Enter your email address and we'll send you a password reset link.</p>

                <form method="POST" action="{{ route('password.email') }}">
                    @csrf
                    <div class="form-floating mb-4">
                        <input class="form-control @error('email') is-invalid @enderror"
                               id="email" type="email" name="email"
                               value="{{ old('email') }}" placeholder="<EMAIL>" required autofocus />
                        <label for="email"><i class="fas fa-envelope me-2"></i>Email address</label>
                        @error('email')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                        @enderror
                    </div>

                    <div class="d-flex align-items-center justify-content-between mt-4 mb-2">
                        <a class="small text-info" href="{{ route('login') }}">
                            <i class="fas fa-arrow-left me-1"></i>Back to login
                        </a>
                        <button type="submit" class="btn btn-primary px-4">
                            <i class="fas fa-paper-plane me-2"></i>Send Link
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
