@extends('layouts.app')

@section('title', 'Create Support Ticket')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Create Support Ticket</h1>
                <a href="{{ route('user.support.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Support
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-plus me-2"></i>
                                New Support Request
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('user.support.store') }}">
                                @csrf
                                
                                <div class="mb-3">
                                    <label for="title" class="form-label">Subject <span class="text-danger">*</span></label>
                                    <input type="text" name="title" id="title" class="form-control @error('title') is-invalid @enderror" 
                                           value="{{ old('title') }}" placeholder="Brief description of your issue" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Please provide a clear, concise subject line</div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                                        <select name="category" id="category" class="form-select @error('category') is-invalid @enderror" required>
                                            <option value="">Select Category</option>
                                            <option value="technical" {{ old('category') == 'technical' ? 'selected' : '' }}>Technical Support</option>
                                            <option value="billing" {{ old('category') == 'billing' ? 'selected' : '' }}>Billing & Payments</option>
                                            <option value="account" {{ old('category') == 'account' ? 'selected' : '' }}>Account Management</option>
                                            <option value="feature_request" {{ old('category') == 'feature_request' ? 'selected' : '' }}>Feature Request</option>
                                            <option value="bug_report" {{ old('category') == 'bug_report' ? 'selected' : '' }}>Bug Report</option>
                                            <option value="general" {{ old('category') == 'general' ? 'selected' : '' }}>General Inquiry</option>
                                        </select>
                                        @error('category')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6">
                                        <label for="priority" class="form-label">Priority</label>
                                        <select name="priority" id="priority" class="form-select @error('priority') is-invalid @enderror">
                                            <option value="normal" {{ old('priority') == 'normal' ? 'selected' : 'selected' }}>Normal</option>
                                            <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>High</option>
                                            <option value="urgent" {{ old('priority') == 'urgent' ? 'selected' : '' }}>Urgent</option>
                                        </select>
                                        @error('priority')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">Select "Urgent" only for critical issues</div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                    <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror" 
                                              rows="8" placeholder="Please provide detailed information about your issue..." required>{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        Include steps to reproduce the issue, error messages, and any relevant details
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Before submitting:</h6>
                                    <ul class="mb-0">
                                        <li>Check our <a href="{{ route('user.support.knowledge-base') }}" target="_blank">Knowledge Base</a> for existing solutions</li>
                                        <li>Provide as much detail as possible to help us resolve your issue quickly</li>
                                        <li>You'll receive email notifications when we respond to your ticket</li>
                                    </ul>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('user.support.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i> Submit Ticket
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Help Card -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-question-circle me-2"></i>
                                Need Help?
                            </h6>
                        </div>
                        <div class="card-body">
                            <h6>Category Guidelines:</h6>
                            <ul class="list-unstyled">
                                <li><strong>Technical:</strong> System errors, login issues, performance problems</li>
                                <li><strong>Billing:</strong> Payment questions, subscription issues, invoices</li>
                                <li><strong>Account:</strong> User management, permissions, settings</li>
                                <li><strong>Feature Request:</strong> Suggestions for new features</li>
                                <li><strong>Bug Report:</strong> Software defects or unexpected behavior</li>
                                <li><strong>General:</strong> Other questions or inquiries</li>
                            </ul>

                            <h6 class="mt-3">Priority Levels:</h6>
                            <ul class="list-unstyled">
                                <li><span class="badge bg-primary">Normal</span> - Standard support requests</li>
                                <li><span class="badge bg-warning">High</span> - Important issues affecting your work</li>
                                <li><span class="badge bg-danger">Urgent</span> - Critical issues requiring immediate attention</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Response Times -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Expected Response Times
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="border-end">
                                        <div class="h5 mb-0 text-primary">4h</div>
                                        <small class="text-muted">Urgent</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border-end">
                                        <div class="h5 mb-0 text-warning">12h</div>
                                        <small class="text-muted">High</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="h5 mb-0 text-success">24h</div>
                                    <small class="text-muted">Normal</small>
                                </div>
                            </div>
                            <hr>
                            <p class="text-muted small mb-0">
                                Response times are during business hours (9 AM - 6 PM, Monday - Friday)
                            </p>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-external-link-alt me-2"></i>
                                Quick Links
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('user.support.knowledge-base') }}" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-book me-2"></i>Knowledge Base
                                </a>
                                <a href="{{ route('user.support.search') }}?q=getting+started" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-play me-2"></i>Getting Started
                                </a>
                                <a href="{{ route('user.support.search') }}?q=faq" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-question me-2"></i>FAQ
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textarea
    const textarea = document.getElementById('description');
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Category change suggestions
    const categorySelect = document.getElementById('category');
    const descriptionTextarea = document.getElementById('description');
    
    categorySelect.addEventListener('change', function() {
        const category = this.value;
        let placeholder = 'Please provide detailed information about your issue...';
        
        switch(category) {
            case 'technical':
                placeholder = 'Please describe the technical issue you\'re experiencing. Include any error messages, steps to reproduce, and what you expected to happen...';
                break;
            case 'billing':
                placeholder = 'Please describe your billing question or concern. Include relevant dates, amounts, and invoice numbers if applicable...';
                break;
            case 'bug_report':
                placeholder = 'Please describe the bug you encountered. Include steps to reproduce, expected behavior, actual behavior, and your browser/device information...';
                break;
            case 'feature_request':
                placeholder = 'Please describe the feature you would like to see added. Explain how it would benefit your workflow and any specific requirements...';
                break;
        }
        
        descriptionTextarea.placeholder = placeholder;
    });
});
</script>
@endsection
