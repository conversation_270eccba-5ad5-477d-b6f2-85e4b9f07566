<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Affiliate extends Model
{
    use HasFactory;

    const STATUS_PENDING = 'pending';
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_SUSPENDED = 'suspended';

    protected $fillable = [
        'user_id',
        'affiliate_code',
        'status',
        'commission_rate',
        'total_earnings',
        'available_balance',
        'pending_balance',
        'withdrawn_amount',
        'referral_link',
        'joined_at',
        'approved_at',
        'approved_by',
        'payment_details',
        'bio',
        'website',
        'social_media',
    ];

    protected $casts = [
        'commission_rate' => 'decimal:2',
        'total_earnings' => 'decimal:2',
        'available_balance' => 'decimal:2',
        'pending_balance' => 'decimal:2',
        'withdrawn_amount' => 'decimal:2',
        'joined_at' => 'datetime',
        'approved_at' => 'datetime',
        'payment_details' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($affiliate) {
            if (empty($affiliate->affiliate_code)) {
                $affiliate->affiliate_code = static::generateUniqueCode();
            }
            if (empty($affiliate->referral_link)) {
                $affiliate->referral_link = static::generateReferralLink($affiliate->affiliate_code);
            }
            if (empty($affiliate->joined_at)) {
                $affiliate->joined_at = now();
            }
        });
    }

    /**
     * Generate a unique affiliate code
     */
    public static function generateUniqueCode(): string
    {
        do {
            $code = strtoupper(Str::random(8));
        } while (static::where('affiliate_code', $code)->exists());

        return $code;
    }

    /**
     * Generate referral link
     */
    public static function generateReferralLink(string $code): string
    {
        return url('/register?ref=' . $code);
    }

    /**
     * Get the user that owns the affiliate account
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the super admin who approved this affiliate
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(SuperAdmin::class, 'approved_by');
    }

    /**
     * Get all referrals made by this affiliate
     */
    public function referrals(): HasMany
    {
        return $this->hasMany(AffiliateReferral::class);
    }

    /**
     * Get all earnings for this affiliate
     */
    public function earnings(): HasMany
    {
        return $this->hasMany(AffiliateEarning::class);
    }

    /**
     * Get all withdrawal requests for this affiliate
     */
    public function withdrawals(): HasMany
    {
        return $this->hasMany(AffiliateWithdrawal::class);
    }

    /**
     * Get all clicks for this affiliate
     */
    public function clicks(): HasMany
    {
        return $this->hasMany(AffiliateClick::class);
    }

    /**
     * Scope for active affiliates
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope for pending affiliates
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Check if affiliate is active
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Check if affiliate is pending approval
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Approve the affiliate
     */
    public function approve($approvedBy = null): bool
    {
        $this->status = self::STATUS_ACTIVE;
        $this->approved_at = now();
        $this->approved_by = $approvedBy;

        return $this->save();
    }

    /**
     * Get total converted referrals
     */
    public function getConvertedReferralsCountAttribute(): int
    {
        return $this->referrals()->where('status', 'converted')->count();
    }

    /**
     * Get total pending referrals
     */
    public function getPendingReferralsCountAttribute(): int
    {
        return $this->referrals()->where('status', 'pending')->count();
    }

    /**
     * Calculate conversion rate
     */
    public function getConversionRateAttribute(): float
    {
        $totalReferrals = $this->referrals()->count();
        if ($totalReferrals === 0) {
            return 0;
        }

        return ($this->converted_referrals_count / $totalReferrals) * 100;
    }

    /**
     * Update balances after earning
     */
    public function addEarning(float $amount, string $status = 'pending'): void
    {
        if ($status === 'approved') {
            $this->available_balance += $amount;
        } else {
            $this->pending_balance += $amount;
        }

        $this->total_earnings += $amount;
        $this->save();
    }

    /**
     * Process withdrawal request (deduct from available balance)
     */
    public function processWithdrawal(float $amount): bool
    {
        if ($this->available_balance >= $amount) {
            $this->available_balance -= $amount;
            // Note: withdrawn_amount is only updated when withdrawal is actually paid
            $this->save();
            return true;
        }

        return false;
    }

    /**
     * Move pending earnings to available
     */
    public function approvePendingEarnings(float $amount): void
    {
        $this->pending_balance -= $amount;
        $this->available_balance += $amount;
        $this->save();
    }
}
