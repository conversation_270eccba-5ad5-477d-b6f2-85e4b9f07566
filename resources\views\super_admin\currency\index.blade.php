@extends('super_admin.layouts.app')

@section('title', 'Currency Management')

@push('styles')
<style>
.cursor-pointer {
    cursor: pointer;
}
.alert-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}
.badge.cursor-pointer:hover {
    opacity: 0.8;
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Currency Management</h1>
                <div>
                    <a href="{{ route('super_admin.currency.settings') }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                    <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#testGeolocationModal">
                        <i class="fas fa-globe"></i> Test Geolocation
                    </button>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Active Currencies</h6>
                                    <h3 class="mb-0">{{ $stats['total_currencies'] }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-coins fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Exchange Rates</h6>
                                    <h3 class="mb-0">{{ $stats['total_rates'] }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exchange-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Changes Today</h6>
                                    <h3 class="mb-0">{{ $stats['rate_changes_today'] }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Last Update</h6>
                                    <p class="mb-0 small">
                                        {{ $stats['last_rate_update'] ? $stats['last_rate_update']->diffForHumans() : 'Never' }}
                                    </p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Currency Override -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-globe-americas"></i> System Currency Override
                        @if($overrideInfo['enabled'])
                            <span class="badge bg-warning ms-2">ACTIVE</span>
                        @endif
                    </h5>
                </div>
                <div class="card-body">
                    @if($overrideInfo['enabled'])
                        <!-- Override Active -->
                        <div class="alert alert-warning d-flex align-items-center" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <div>
                                <strong>System Override Active!</strong><br>
                                All users are currently seeing prices in <strong>{{ $overrideInfo['currency'] }}</strong>
                                @if($overrideInfo['method'] === 'ip_simulation' && $overrideInfo['ip_address'])
                                    (simulating IP: {{ $overrideInfo['ip_address'] }})
                                @else
                                    (direct override)
                                @endif
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <form action="{{ route('super_admin.currency.disable-override') }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to disable the currency override? Users will return to IP-based currency detection.')">
                                    <i class="fas fa-times"></i> Disable Override
                                </button>
                            </form>
                        </div>
                    @else
                        <!-- Override Controls -->
                        <p class="text-muted mb-3">
                            Set a system-wide currency override to force all users to see prices in a specific currency,
                            regardless of their IP location. This is useful for testing or setting a global currency preference.
                        </p>

                        <form action="{{ route('super_admin.currency.set-override') }}" method="POST" id="currencyOverrideForm">
                            @csrf
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Override Method</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="method" id="method_direct" value="direct" checked>
                                            <label class="form-check-label" for="method_direct">
                                                Direct Currency Selection
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="method" id="method_ip" value="ip_simulation">
                                            <label class="form-check-label" for="method_ip">
                                                IP Address Simulation
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Direct Currency Selection -->
                                    <div id="direct_currency_section" class="mb-3">
                                        <label for="currency" class="form-label">Select Currency</label>
                                        <select name="currency" id="currency" class="form-select">
                                            <option value="USD">USD - US Dollar ($)</option>
                                            <option value="NGN">NGN - Nigerian Naira (₦)</option>
                                        </select>
                                    </div>

                                    <!-- IP Simulation Section -->
                                    <div id="ip_simulation_section" class="mb-3" style="display: none;">
                                        <label for="ip_address" class="form-label">IP Address</label>
                                        <div class="input-group">
                                            <input type="text" name="ip_address" id="ip_address" class="form-control"
                                                   placeholder="e.g., ******** (Nigerian IP)" pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$">
                                            <button type="button" class="btn btn-outline-secondary" id="testIpBtn">
                                                <i class="fas fa-search"></i> Test
                                            </button>
                                        </div>
                                        <div class="form-text">
                                            Enter an IP address to simulate currency detection for that location.
                                            <br><strong>Examples:</strong>
                                            <span class="badge bg-secondary cursor-pointer" onclick="setTestIp('********')">******** (Nigeria)</span>
                                            <span class="badge bg-secondary cursor-pointer" onclick="setTestIp('*******')">******* (USA)</span>
                                        </div>
                                        <div id="ip_test_result" class="mt-2"></div>
                                    </div>

                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-globe"></i> Enable System Override
                                    </button>
                                </div>

                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-info-circle"></i> How Override Works
                                            </h6>
                                            <ul class="small mb-0">
                                                <li><strong>Direct Selection:</strong> All users will see prices in the selected currency</li>
                                                <li><strong>IP Simulation:</strong> System will detect currency for the specified IP and apply it to all users</li>
                                                <li><strong>Global Effect:</strong> Affects Super Admin, Affiliate, and Organization users</li>
                                                <li><strong>Conversion:</strong> Prices are automatically converted using current exchange rates</li>
                                                <li><strong>Persistence:</strong> Override remains active until manually disabled</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    @endif
                </div>
            </div>

            <!-- Current Exchange Rates -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Current Exchange Rates</h5>
                </div>
                <div class="card-body">
                    @if($currentRates->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>From</th>
                                        <th>To</th>
                                        <th>Rate</th>
                                        <th>Effective From</th>
                                        <th>Updated By</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($currentRates as $rate)
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary">{{ $rate->from_currency }}</span>
                                                {{ $rate->fromCurrency->name ?? $rate->from_currency }}
                                            </td>
                                            <td>
                                                <span class="badge bg-success">{{ $rate->to_currency }}</span>
                                                {{ $rate->toCurrency->name ?? $rate->to_currency }}
                                            </td>
                                            <td>
                                                <strong>{{ number_format($rate->rate, 6) }}</strong>
                                                <small class="text-muted d-block">
                                                    1 {{ $rate->from_currency }} = {{ number_format($rate->rate, 2) }} {{ $rate->to_currency }}
                                                </small>
                                            </td>
                                            <td>{{ $rate->effective_from ? $rate->effective_from->format('M d, Y H:i') : 'N/A' }}</td>
                                            <td>{{ $rate->creator_name }}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ route('super_admin.currency.edit-rate', [$rate->from_currency, $rate->to_currency]) }}" 
                                                       class="btn btn-outline-primary">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </a>
                                                    <a href="{{ route('super_admin.currency.rate-history', [$rate->from_currency, $rate->to_currency]) }}" 
                                                       class="btn btn-outline-info">
                                                        <i class="fas fa-history"></i> History
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No exchange rates configured yet.</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Recent Changes -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Recent Rate Changes</h5>
                </div>
                <div class="card-body">
                    @if($recentChanges->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Currency Pair</th>
                                        <th>Action</th>
                                        <th>Old Rate</th>
                                        <th>New Rate</th>
                                        <th>Changed By</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentChanges as $change)
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary">{{ $change->from_currency }}/{{ $change->to_currency }}</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $change->action === 'created' ? 'success' : 'warning' }}">
                                                    {{ ucfirst($change->action) }}
                                                </span>
                                            </td>
                                            <td>{{ $change->old_rate ? number_format($change->old_rate, 6) : 'N/A' }}</td>
                                            <td>{{ number_format($change->new_rate, 6) }}</td>
                                            <td>{{ $change->changed_by_name }}</td>
                                            <td>{{ $change->created_at->format('M d, H:i') }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No recent changes.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Geolocation Modal -->
<div class="modal fade" id="testGeolocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test IP Geolocation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="testGeolocationForm">
                    <div class="mb-3">
                        <label for="ip_address" class="form-label">IP Address</label>
                        <input type="text" class="form-control" id="ip_address" name="ip_address" 
                               placeholder="Leave empty to use your current IP">
                    </div>
                    <button type="submit" class="btn btn-primary">Test</button>
                </form>
                <div id="geolocationResult" class="mt-3" style="display: none;">
                    <h6>Result:</h6>
                    <pre id="geolocationData"></pre>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Currency Override functionality
document.addEventListener('DOMContentLoaded', function() {
    const methodRadios = document.querySelectorAll('input[name="method"]');
    const directSection = document.getElementById('direct_currency_section');
    const ipSection = document.getElementById('ip_simulation_section');
    const testIpBtn = document.getElementById('testIpBtn');
    const ipInput = document.getElementById('ip_address');

    // Toggle sections based on method selection
    if (methodRadios.length > 0) {
        methodRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'direct') {
                    if (directSection) directSection.style.display = 'block';
                    if (ipSection) ipSection.style.display = 'none';
                    if (ipInput) ipInput.removeAttribute('required');
                } else {
                    if (directSection) directSection.style.display = 'none';
                    if (ipSection) ipSection.style.display = 'block';
                    if (ipInput) ipInput.setAttribute('required', 'required');
                }
            });
        });
    }

    // Test IP functionality
    if (testIpBtn) {
        testIpBtn.addEventListener('click', async function() {
            const ip = ipInput.value.trim();
            if (!ip) {
                alert('Please enter an IP address');
                return;
            }

            const resultDiv = document.getElementById('ip_test_result');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2" role="status"></div>Testing IP...';

            try {
                const response = await fetch('{{ route("super_admin.currency.test-ip-currency") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ ip_address: ip })
                });

                const data = await response.json();

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success alert-sm">
                            <strong>Currency:</strong> ${data.currency}<br>
                            <strong>Country:</strong> ${data.location_info.country || 'Unknown'}<br>
                            <strong>City:</strong> ${data.location_info.city || 'Unknown'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger alert-sm">${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="alert alert-danger alert-sm">Error: ${error.message}</div>`;
            }
        });
    }
});

// Helper function to set test IP
function setTestIp(ip) {
    const ipInput = document.getElementById('ip_address');
    const methodIp = document.getElementById('method_ip');
    if (ipInput) ipInput.value = ip;
    if (methodIp) {
        methodIp.checked = true;
        methodIp.dispatchEvent(new Event('change'));
    }
}

// Test Geolocation functionality
document.getElementById('testGeolocationForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const resultDiv = document.getElementById('geolocationResult');
    const dataDiv = document.getElementById('geolocationData');
    
    try {
        const response = await fetch('{{ route("super_admin.currency.test-geolocation") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Accept': 'application/json',
            },
            body: formData
        });
        
        const data = await response.json();
        dataDiv.textContent = JSON.stringify(data, null, 2);
        resultDiv.style.display = 'block';
        
    } catch (error) {
        dataDiv.textContent = 'Error: ' + error.message;
        resultDiv.style.display = 'block';
    }
});
</script>
@endpush
