<?php

namespace App\View\Composers;

use App\Services\SubscriptionStatusService;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class SubscriptionStatusComposer
{
    /**
     * Bind data to the view.
     */
    public function compose(View $view): void
    {
        $user = Auth::user();
        
        if ($user && $user->organization) {
            $guidance = SubscriptionStatusService::getUserGuidance($user->organization, $user);
            
            // Only add guidance if action is required
            if ($guidance['status']['action_required'] ?? false) {
                $view->with('subscription_guidance', $guidance);
            }
        }
    }
}