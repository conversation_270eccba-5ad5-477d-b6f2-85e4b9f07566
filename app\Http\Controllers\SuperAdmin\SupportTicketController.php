<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\SupportTicket;
use App\Models\SupportTicketReply;
use App\Models\Organization;
use App\Models\SuperAdmin;
use App\Services\LogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SupportTicketController extends Controller
{
    public function __construct()
    {
        $this->middleware('super_admin');
    }

    /**
     * Display a listing of support tickets
     */
    public function index(Request $request)
    {
        $query = SupportTicket::with(['user', 'organization', 'assignedAdmin', 'latestReply'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        $this->applyFilters($query, $request);

        $tickets = $query->paginate(20)->appends($request->query());

        // Get filter options
        $filterOptions = [
            'statuses' => SupportTicket::getStatusOptions(),
            'priorities' => SupportTicket::getPriorityOptions(),
            'categories' => SupportTicket::getCategoryOptions(),
            'organizations' => Organization::where('is_active', true)
                ->orderBy('name')
                ->pluck('name', 'id'),
            'admins' => SuperAdmin::orderBy('name')->pluck('name', 'id'),
        ];

        // Get statistics
        $stats = [
            'total' => SupportTicket::count(),
            'open' => SupportTicket::open()->count(),
            'urgent' => SupportTicket::urgent()->open()->count(),
            'unassigned' => SupportTicket::unassigned()->open()->count(),
        ];

        return view('super_admin.support.tickets.index', compact(
            'tickets',
            'filterOptions',
            'stats'
        ));
    }

    /**
     * Show the form for creating a new ticket
     */
    public function create()
    {
        $organizations = Organization::where('is_active', true)->orderBy('name')->get();
        $admins = SuperAdmin::orderBy('name')->get();

        return view('super_admin.support.tickets.create', compact('organizations', 'admins'));
    }

    /**
     * Store a newly created ticket
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'priority' => 'required|in:' . implode(',', array_keys(SupportTicket::getPriorityOptions())),
            'category' => 'required|in:' . implode(',', array_keys(SupportTicket::getCategoryOptions())),
            'organization_id' => 'required|exists:organizations,id',
            'user_id' => 'nullable|exists:users,id',
            'assigned_to' => 'nullable|exists:super_admins,id',
        ]);

        $ticket = SupportTicket::create([
            'ticket_number' => SupportTicket::generateTicketNumber(),
            'title' => $request->title,
            'description' => $request->description,
            'priority' => $request->priority,
            'category' => $request->category,
            'status' => $request->assigned_to ? SupportTicket::STATUS_IN_PROGRESS : SupportTicket::STATUS_OPEN,
            'organization_id' => $request->organization_id,
            'user_id' => $request->user_id,
            'assigned_to' => $request->assigned_to,
        ]);

        // Log the creation
        LogService::info('Support ticket created by admin', [
            'ticket_id' => $ticket->id,
            'ticket_number' => $ticket->ticket_number,
            'created_by_admin' => Auth::guard('super_admin')->id(),
            'organization_id' => $ticket->organization_id,
        ]);

        return redirect()->route('super.support.tickets.show', $ticket)
            ->with('success', 'Support ticket created successfully.');
    }

    /**
     * Display the specified ticket
     */
    public function show(SupportTicket $ticket)
    {
        $ticket->load(['user', 'organization', 'assignedAdmin', 'replies.replier']);
        
        $admins = SuperAdmin::orderBy('name')->get();

        return view('super_admin.support.tickets.show', compact('ticket', 'admins'));
    }

    /**
     * Show the form for editing the ticket
     */
    public function edit(SupportTicket $ticket)
    {
        $organizations = Organization::where('is_active', true)->orderBy('name')->get();
        $admins = SuperAdmin::orderBy('name')->get();

        return view('super_admin.support.tickets.edit', compact('ticket', 'organizations', 'admins'));
    }

    /**
     * Update the specified ticket
     */
    public function update(Request $request, SupportTicket $ticket)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'priority' => 'required|in:' . implode(',', array_keys(SupportTicket::getPriorityOptions())),
            'category' => 'required|in:' . implode(',', array_keys(SupportTicket::getCategoryOptions())),
            'status' => 'required|in:' . implode(',', array_keys(SupportTicket::getStatusOptions())),
            'assigned_to' => 'nullable|exists:super_admins,id',
        ]);

        $oldStatus = $ticket->status;
        $oldAssignee = $ticket->assigned_to;

        $ticket->update($request->only([
            'title', 'description', 'priority', 'category', 'status', 'assigned_to'
        ]));

        // Log status change
        if ($oldStatus !== $ticket->status) {
            LogService::info('Support ticket status changed', [
                'ticket_id' => $ticket->id,
                'old_status' => $oldStatus,
                'new_status' => $ticket->status,
                'changed_by_admin' => Auth::guard('super_admin')->id(),
            ]);
        }

        // Log assignment change
        if ($oldAssignee !== $ticket->assigned_to) {
            LogService::info('Support ticket assignment changed', [
                'ticket_id' => $ticket->id,
                'old_assignee' => $oldAssignee,
                'new_assignee' => $ticket->assigned_to,
                'changed_by_admin' => Auth::guard('super_admin')->id(),
            ]);
        }

        return redirect()->route('super.support.tickets.show', $ticket)
            ->with('success', 'Ticket updated successfully.');
    }

    /**
     * Add a reply to the ticket
     */
    public function reply(Request $request, SupportTicket $ticket)
    {
        $request->validate([
            'message' => 'required|string',
            'is_internal' => 'boolean',
        ]);

        $admin = Auth::guard('super_admin')->user();
        
        $reply = SupportTicketReply::createFromAdmin(
            $ticket,
            $admin,
            $request->message,
            $request->boolean('is_internal')
        );

        LogService::info('Support ticket reply added', [
            'ticket_id' => $ticket->id,
            'reply_id' => $reply->id,
            'is_internal' => $reply->is_internal,
            'admin_id' => $admin->id,
        ]);

        return back()->with('success', 'Reply added successfully.');
    }

    /**
     * Assign ticket to admin
     */
    public function assign(Request $request, SupportTicket $ticket)
    {
        $request->validate([
            'assigned_to' => 'required|exists:super_admins,id',
        ]);

        $oldAssignee = $ticket->assigned_to;
        $ticket->assignTo($request->assigned_to);

        LogService::info('Support ticket assigned', [
            'ticket_id' => $ticket->id,
            'old_assignee' => $oldAssignee,
            'new_assignee' => $request->assigned_to,
            'assigned_by_admin' => Auth::guard('super_admin')->id(),
        ]);

        return back()->with('success', 'Ticket assigned successfully.');
    }

    /**
     * Mark ticket as resolved
     */
    public function resolve(SupportTicket $ticket)
    {
        $admin = Auth::guard('super_admin')->user();
        $ticket->markAsResolved($admin->id);

        LogService::info('Support ticket resolved', [
            'ticket_id' => $ticket->id,
            'resolved_by_admin' => $admin->id,
            'resolution_time_hours' => $ticket->resolution_time,
        ]);

        return back()->with('success', 'Ticket marked as resolved.');
    }

    /**
     * Close ticket
     */
    public function close(SupportTicket $ticket)
    {
        $ticket->update(['status' => SupportTicket::STATUS_CLOSED]);

        LogService::info('Support ticket closed', [
            'ticket_id' => $ticket->id,
            'closed_by_admin' => Auth::guard('super_admin')->id(),
        ]);

        return back()->with('success', 'Ticket closed successfully.');
    }

    /**
     * Reopen ticket
     */
    public function reopen(SupportTicket $ticket)
    {
        $ticket->update([
            'status' => SupportTicket::STATUS_OPEN,
            'resolved_at' => null,
        ]);

        LogService::info('Support ticket reopened', [
            'ticket_id' => $ticket->id,
            'reopened_by_admin' => Auth::guard('super_admin')->id(),
        ]);

        return back()->with('success', 'Ticket reopened successfully.');
    }

    /**
     * Apply filters to the query
     */
    private function applyFilters($query, Request $request)
    {
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('organization_id')) {
            $query->where('organization_id', $request->organization_id);
        }

        if ($request->filled('assigned_to')) {
            if ($request->assigned_to === 'unassigned') {
                $query->whereNull('assigned_to');
            } else {
                $query->where('assigned_to', $request->assigned_to);
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('ticket_number', 'like', "%{$search}%");
            });
        }

        if ($request->boolean('urgent_only')) {
            $query->urgent();
        }

        if ($request->boolean('overdue_only')) {
            // This would need to be implemented with a custom scope
            // For now, we'll filter in the view
        }
    }
}
