<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CurrencyRateHistory extends Model
{
    use HasFactory;

    protected $table = 'currency_rate_history';

    protected $fillable = [
        'from_currency',
        'to_currency',
        'old_rate',
        'new_rate',
        'action',
        'reason',
        'changed_by',
        'metadata',
    ];

    protected $casts = [
        'old_rate' => 'decimal:6',
        'new_rate' => 'decimal:6',
        'metadata' => 'array',
    ];

    /**
     * Get the user who made the change
     */
    public function changedBy()
    {
        return $this->belongsTo(User::class, 'changed_by');
    }

    /**
     * Get the super admin who made the change
     */
    public function changedBySuperAdmin()
    {
        return $this->belongsTo(\App\Models\SuperAdmin::class, 'changed_by');
    }

    /**
     * Get the name of who made the change (works for both users and super admins)
     */
    public function getChangedByNameAttribute()
    {
        // Try to get regular user first
        $user = $this->changedBy;
        if ($user) {
            return $user->name;
        }

        // Try to get super admin
        try {
            $superAdmin = \App\Models\SuperAdmin::find($this->changed_by);
            if ($superAdmin) {
                return $superAdmin->name . ' (Super Admin)';
            }
        } catch (\Exception $e) {
            // Super admin model might not exist or have issues
        }

        return 'System';
    }

    /**
     * Get the from currency
     */
    public function fromCurrency()
    {
        return $this->belongsTo(Currency::class, 'from_currency', 'code');
    }

    /**
     * Get the to currency
     */
    public function toCurrency()
    {
        return $this->belongsTo(Currency::class, 'to_currency', 'code');
    }

    /**
     * Log a currency rate change
     */
    public static function logChange($fromCurrency, $toCurrency, $oldRate, $newRate, $action, $changedBy, $reason = null, $metadata = [])
    {
        return static::create([
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency,
            'old_rate' => $oldRate,
            'new_rate' => $newRate,
            'action' => $action,
            'reason' => $reason,
            'changed_by' => $changedBy,
            'metadata' => array_merge($metadata, [
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'timestamp' => now()->toISOString(),
            ]),
        ]);
    }

    /**
     * Get history for a specific currency pair
     */
    public static function getHistoryForPair($fromCurrency, $toCurrency, $limit = 50)
    {
        return static::where('from_currency', $fromCurrency)
            ->where('to_currency', $toCurrency)
            ->with('changedBy')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent changes by a specific user
     */
    public static function getRecentChangesByUser($userId, $limit = 20)
    {
        return static::where('changed_by', $userId)
            ->with(['fromCurrency', 'toCurrency'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
}
