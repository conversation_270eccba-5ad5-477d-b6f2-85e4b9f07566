// Download and cache external resources
async function cacheExternalResources() {
    if ('caches' in window) {
        const cache = await caches.open('printworks-cache-v1');
        const resources = [
            'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css',
            'https://cdn.jsdelivr.net/npm/sweetalert2@11'
        ];

        for (const resource of resources) {
            try {
                const response = await fetch(resource);
                if (response.ok) {
                    await cache.put(resource, response);
                }
            } catch (error) {
                console.warn('Failed to cache:', resource);
            }
        }
    }
}

// Run when online
if (navigator.onLine) {
    cacheExternalResources();
} 