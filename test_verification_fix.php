<?php

/**
 * Test script to verify the Organization Email Verification Success Message Fix
 */

echo "=== Organization Email Verification Success Message Fix ===\n\n";

// Test 1: Check VerifyEmailController changes
echo "1. Testing VerifyEmailController Changes...\n";

try {
    $controller = file_get_contents('app/Http/Controllers/Auth/VerifyEmailController.php');
    
    if (strpos($controller, 'function __invoke(Request $request, $id, $hash)') !== false) {
        echo "   ✅ VerifyEmailController - Method signature updated to handle guest verification\n";
    } else {
        echo "   ❌ VerifyEmailController - Method signature not updated\n";
    }
    
    if (strpos($controller, 'User::findOrFail($id)') !== false) {
        echo "   ✅ VerifyEmailController - Finds user by ID from URL\n";
    } else {
        echo "   ❌ VerifyEmailController - Missing user lookup by ID\n";
    }
    
    if (strpos($controller, 'hash_equals((string) $hash, sha1($user->getEmailForVerification()))') !== false) {
        echo "   ✅ VerifyEmailController - Verifies hash security\n";
    } else {
        echo "   ❌ VerifyEmailController - Missing hash verification\n";
    }
    
    if (strpos($controller, 'Auth::login($user)') !== false) {
        echo "   ✅ VerifyEmailController - Logs user in after verification\n";
    } else {
        echo "   ❌ VerifyEmailController - Missing user login\n";
    }
    
    if (strpos($controller, 'Email verified successfully! Please select a subscription plan to get started.') !== false) {
        echo "   ✅ VerifyEmailController - Success message included\n";
    } else {
        echo "   ❌ VerifyEmailController - Missing success message\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Error checking VerifyEmailController: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Check route changes
echo "2. Testing Route Configuration...\n";

try {
    $routes = file_get_contents('routes/web.php');
    
    // Check if verification route is outside auth middleware
    $lines = explode("\n", $routes);
    $verificationRouteFound = false;
    $insideAuthGroup = false;
    
    foreach ($lines as $lineNum => $line) {
        if (strpos($line, "Route::middleware('auth')->group") !== false) {
            $insideAuthGroup = true;
        }
        
        if (strpos($line, "Route::get('/email/verify/{id}/{hash}'") !== false) {
            $verificationRouteFound = true;
            if (!$insideAuthGroup) {
                echo "   ✅ Routes - Verification route is outside auth middleware (accessible to guests)\n";
            } else {
                echo "   ❌ Routes - Verification route is still inside auth middleware\n";
            }
            break;
        }
        
        if (strpos($line, '});') !== false && $insideAuthGroup) {
            $insideAuthGroup = false;
        }
    }
    
    if (!$verificationRouteFound) {
        echo "   ❌ Routes - Verification route not found\n";
    }
    
    if (strpos($routes, "['signed', 'throttle:6,1']") !== false) {
        echo "   ✅ Routes - Verification route has signed and throttle middleware\n";
    } else {
        echo "   ❌ Routes - Missing signed/throttle middleware\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Error checking routes: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Compare with Affiliate implementation
echo "3. Comparing with Affiliate Implementation...\n";

try {
    $affiliateController = file_get_contents('app/Http/Controllers/Affiliate/EmailVerificationController.php');
    $orgController = file_get_contents('app/Http/Controllers/Auth/VerifyEmailController.php');
    
    // Check for similar patterns
    $patterns = [
        'User::findOrFail($id)' => 'User lookup by ID',
        'hash_equals' => 'Hash verification',
        'Auth::login($user)' => 'User login after verification',
        'with(\'success\'' => 'Success message'
    ];
    
    foreach ($patterns as $pattern => $description) {
        $inAffiliate = strpos($affiliateController, $pattern) !== false;
        $inOrg = strpos($orgController, $pattern) !== false;
        
        if ($inAffiliate && $inOrg) {
            echo "   ✅ $description - Present in both implementations\n";
        } elseif ($inAffiliate && !$inOrg) {
            echo "   ❌ $description - Missing in organization implementation\n";
        } elseif (!$inAffiliate && $inOrg) {
            echo "   ✅ $description - Added to organization implementation\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ Error comparing implementations: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Summary
echo "4. Fix Summary...\n";

echo "   🔧 Changes Made:\n";
echo "      • VerifyEmailController now handles guest verification (like Affiliate implementation)\n";
echo "      • Verification route moved outside auth middleware group\n";
echo "      • User is automatically logged in after successful verification\n";
echo "      • Success message is properly passed to the plan selection page\n";

echo "\n   🎯 Expected Behavior:\n";
echo "      1. User clicks verification link in email (while not logged in)\n";
echo "      2. Link is processed by VerifyEmailController without requiring authentication\n";
echo "      3. Email is marked as verified and user is logged in automatically\n";
echo "      4. User is redirected to plan selection with success message\n";
echo "      5. Success message appears on the plan selection page\n";

echo "\n   ✅ The success message should now appear correctly!\n";

echo "\n=== Fix Complete ===\n";
echo "Please test the email verification flow again to confirm the success message appears.\n";

?>
