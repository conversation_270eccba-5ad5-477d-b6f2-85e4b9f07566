<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_name',
        'phone_number',
        'order_title',
        'job_description',
        'department',
        'sales_team',
        'media',
        'pages',
        'size',
        'quantity',
        'unit_cost',
        'amount_paid',
        'expected_delivery_date',
        'expected_delivery_time',
        'status',
        'date_delivered',
        'receiver_name',
        'receiver_phone',
        'total_amount',
        'pending_payment',
        'order_number',
        'user_id',
        'branch_id',
        'organization_id'
    ];

    protected $casts = [
        'date_delivered' => 'datetime',
        'expected_delivery_date' => 'date',
        'expected_delivery_time' => 'string',
        'unit_cost' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'pending_payment' => 'decimal:2'
    ];

    /**
     * Get the branch that the order belongs to.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the organization that the order belongs to.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    protected static function boot()
    {
        parent::boot();

        static::updating(function ($order) {
            $changes = $order->getDirty();
            if (isset($changes['status'])) {
                $oldStatus = $order->getOriginal('status');
                $newStatus = $changes['status'];
                Log::info('Order status changed', [
                    'order_id' => $order->id,
                    'old_status' => $oldStatus,
                    'new_status' => $newStatus,
                    'changed_by' => auth()->user()->name ?? 'System',
                    'customer_name' => $order->customer_name
                ]);
            }
        });
    }

    public function calculateTotalAmount()
    {
        return floatval($this->quantity) * floatval($this->unit_cost);
    }

    public function calculatePendingPayment()
    {
        return floatval($this->total_amount) - floatval($this->amount_paid);
    }

    public function getBalanceAfterRunningCost()
    {
        return floatval($this->total_amount) - $this->calculateRunningCost();
    }

    public static function generateOrderNumber()
    {
        // Get settings
        $settings = \App\Models\Setting::first();

        // Set defaults if settings don't exist
        $prefix = $settings->order_number_prefix ?? 'ORD-';
        $includeYear = $settings->include_year_in_order_number ?? false;
        $format = $settings->order_number_format ?? 'sequential';
        $digits = $settings->order_number_digits ?? 6;

        // Build the order number
        $orderNumber = $prefix;

        // Add year if needed
        if ($includeYear) {
            $orderNumber .= date('Y') . '-';
        }

        // Generate the number part based on format
        switch ($format) {
            case 'alphanumeric':
                $orderNumber .= strtoupper(substr(uniqid(), -$digits));
                break;

            case 'numeric':
                $orderNumber .= substr(str_pad(rand(0, pow(10, $digits) - 1), $digits, '0', STR_PAD_LEFT), -$digits);
                break;

            case 'sequential':
            default:
                // Get the latest order to determine the next sequential number
                $latestOrder = self::latest()->first();
                $latestNumber = 0;

                if ($latestOrder) {
                    // Extract the numeric part from the latest order number
                    $matches = [];
                    if (preg_match('/(\d+)$/', $latestOrder->order_number, $matches)) {
                        $latestNumber = intval($matches[1]);
                    }
                }

                $nextNumber = $latestNumber + 1;
                $orderNumber .= str_pad($nextNumber, $digits, '0', STR_PAD_LEFT);
                break;
        }

        return $orderNumber;
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_name', 'name');
    }

    public function job()
    {
        return $this->hasOne(Job::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getSalesTeamAttribute($value)
    {
        return $value ?? 'None';
    }

    public function isOverdue()
    {
        if (in_array($this->status, ['Delivered', 'Completed'])) {
            return false;
        }

        $deliveryDateTime = Carbon::parse($this->expected_delivery_date)
            ->setTimeFromTimeString($this->expected_delivery_time);

        $diffInMinutes = now()->diffInMinutes($deliveryDateTime, false);
        return $diffInMinutes <= -1;
    }

    public function getOverdueDuration()
    {
        if (!$this->isOverdue()) {
            return null;
        }

        $deliveryDateTime = Carbon::parse($this->expected_delivery_date)
            ->setTimeFromTimeString($this->expected_delivery_time);

        $diffInMinutes = now()->diffInMinutes($deliveryDateTime);

        $days = floor($diffInMinutes / 1440);
        $hours = floor(($diffInMinutes % 1440) / 60);
        $minutes = $diffInMinutes % 60;

        $duration = '';
        if ($days > 0) {
            $duration .= $days . ' day' . ($days > 1 ? 's' : '') . ' ';
        }
        if ($hours > 0) {
            $duration .= $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ';
        }
        $duration .= $minutes . ' minute' . ($minutes !== 1 ? 's' : '');

        return $duration;
    }

    public function getOverdueHoursAttribute()
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        $deliveryDateTime = Carbon::parse($this->expected_delivery_date)
            ->setTimeFromTimeString($this->expected_delivery_time);

        return now()->diffInHours($deliveryDateTime);
    }

    public function isStatusChangeable()
    {
        return !in_array($this->status, ['Delivered']);
    }

    public function getStatusColorAttribute()
    {
        return [
            'Pending' => 'yellow',
            'Processing' => 'blue',
            'Completed' => 'green',
            'Delivered' => 'gray'
        ][$this->status] ?? 'gray';
    }

    public function getStatusBadgeColorAttribute()
    {
        return [
            'Pending' => 'bg-yellow-100 text-yellow-800',
            'Processing' => 'bg-blue-100 text-blue-800',
            'Completed' => 'bg-green-100 text-green-800',
            'Delivered' => 'bg-gray-100 text-gray-800'
        ][$this->status] ?? 'bg-gray-100 text-gray-800';
    }

    public function scopePending($query)
    {
        return $query->where('status', 'Pending');
    }

    public function scopeSearch($query, $term)
    {
        return $query->where(function ($query) use ($term) {
            $query->where('order_number', 'like', "%{$term}%")
                  ->orWhere('customer_name', 'like', "%{$term}%")
                  ->orWhere('job_description', 'like', "%{$term}%");
        });
    }

    // Add count scope for Sidebar display
    public function scopePendingCount($query)
    {
        return $query->where('status', 'Pending')->count();
    }

    public function statusHistory()
    {
        return $this->hasMany(OrderStatusHistory::class);
    }

    public function getStatusTimelineAttribute()
    {
        $timeline = $this->statusHistory()
            ->select('new_status', 'created_at', 'changed_by')
            ->orderBy('created_at', 'asc')
            ->get()
            ->map(function ($history) {
                return [
                    'status' => $history->new_status,
                    'date' => $history->created_at,
                    'by' => $history->changed_by
                ];
            });

        return $timeline;
    }

    public function getStatusDurationAttribute()
    {
        return $this->created_at->diffForHumans($this->updated_at, true);
    }

    public function scopeOverdue($query)
    {
        return $query->whereNotIn('status', ['Delivered', 'Completed'])
            ->where(function ($query) {
                $query->whereRaw('CONCAT(expected_delivery_date, " ", expected_delivery_time) < ?', [now()]);
            });
    }

    public function scopeOverdueByDuration($query, $range)
    {
        $now = now();
        $query->overdue();

        switch ($range) {
            case '1-3':
                $query->whereBetween(DB::raw('DATEDIFF(NOW(), CONCAT(expected_delivery_date, " ", expected_delivery_time))'), [1, 3]);
                break;
            case '4-7':
                $query->whereBetween(DB::raw('DATEDIFF(NOW(), CONCAT(expected_delivery_date, " ", expected_delivery_time))'), [4, 7]);
                break;
            case '7+':
                $query->where(DB::raw('DATEDIFF(NOW(), CONCAT(expected_delivery_date, " ", expected_delivery_time))'), '>', 7);
                break;
        }

        return $query;
    }

    public function scopeOverdueCount($query)
    {
        return $query->overdue()->count();
    }

    public function getOverdueDaysAttribute()
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        $expectedDateTime = Carbon::parse($this->expected_delivery_date)
            ->setTimeFromTimeString($this->expected_delivery_time);

        return $expectedDateTime->diffInDays(now(), false);
    }

    public function getPriorityLevelAttribute()
    {
        $days = $this->overdue_days;

        if ($days > 7) return ['level' => 'Critical', 'color' => 'red-600'];
        if ($days > 3) return ['level' => 'High', 'color' => 'orange-500'];
        return ['level' => 'Medium', 'color' => 'yellow-500'];
    }
}
