<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\SiteVisit;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Http\Request;
use Carbon\Carbon;

class SiteAnalyticsController extends Controller
{
    /**
     * Display site analytics dashboard
     */
    public function index(Request $request)
    {
        $period = $request->get('period', 'today');

        // Get visit statistics
        $visitStats = SiteVisit::getVisitStats($period);

        // Get online statistics
        $onlineStats = SiteVisit::getOnlineStats();

        // Get device breakdown
        $deviceBreakdown = SiteVisit::getDeviceBreakdown($period);

        // Get browser breakdown
        $browserBreakdown = SiteVisit::getBrowserBreakdown($period);

        // Get daily visits for chart (last 30 days)
        $dailyVisits = $this->getDailyVisits();

        // Get top organizations by activity
        $topOrganizations = $this->getTopOrganizations($period);

        // Get online users details
        $onlineUsers = $this->getOnlineUsers();

        // Get system totals
        $systemTotals = $this->getSystemTotals();

        return view('super_admin.site_analytics.index', compact(
            'visitStats',
            'onlineStats',
            'deviceBreakdown',
            'browserBreakdown',
            'dailyVisits',
            'topOrganizations',
            'onlineUsers',
            'systemTotals',
            'period'
        ));
    }

    /**
     * Get daily visits for the last 30 days
     */
    protected function getDailyVisits(): array
    {
        $startDate = Carbon::now()->subDays(29)->startOfDay();
        $endDate = Carbon::now()->endOfDay();

        $visits = SiteVisit::selectRaw('DATE(first_visit_at) as date, COUNT(*) as visits, COUNT(DISTINCT ip_address) as unique_visitors')
            ->whereBetween('first_visit_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Fill in missing dates with zero values
        $result = [];
        $currentDate = $startDate->copy();

        while ($currentDate <= $endDate) {
            $dateStr = $currentDate->format('Y-m-d');
            $visit = $visits->firstWhere('date', $dateStr);

            $result[] = [
                'date' => $dateStr,
                'formatted_date' => $currentDate->format('M j'),
                'visits' => $visit ? $visit->visits : 0,
                'unique_visitors' => $visit ? $visit->unique_visitors : 0,
            ];

            $currentDate->addDay();
        }

        return $result;
    }

    /**
     * Get top organizations by activity
     */
    protected function getTopOrganizations($period): array
    {
        $query = SiteVisit::whereNotNull('organization_id');

        switch ($period) {
            case 'today':
                $query->today();
                break;
            case 'week':
                $query->thisWeek();
                break;
            case 'month':
                $query->thisMonth();
                break;
            case 'year':
                $query->thisYear();
                break;
        }

        return $query->selectRaw('organization_id, COUNT(*) as visit_count, COUNT(DISTINCT user_id) as unique_users, SUM(page_views) as total_page_views')
            ->with('organization:id,name')
            ->groupBy('organization_id')
            ->orderByDesc('visit_count')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get currently online users with details
     */
    protected function getOnlineUsers(): array
    {
        $onlineVisits = SiteVisit::online()
            ->with(['user:id,name,email', 'organization:id,name'])
            ->orderByDesc('last_activity_at')
            ->get();

        $result = [];
        foreach ($onlineVisits as $visit) {
            $result[] = [
                'user_name' => $visit->user ? $visit->user->name : 'Guest',
                'user_email' => $visit->user ? $visit->user->email : null,
                'organization_name' => $visit->organization ? $visit->organization->name : 'N/A',
                'device_type' => $visit->device_type,
                'browser' => $visit->browser,
                'platform' => $visit->platform,
                'current_page' => $visit->current_page,
                'last_activity' => $visit->last_activity_at,
                'duration' => $this->formatDuration($visit->duration_seconds),
                'page_views' => $visit->page_views,
            ];
        }

        return $result;
    }

    /**
     * Get system totals
     */
    protected function getSystemTotals(): array
    {
        return [
            'total_organizations' => Organization::count(),
            'active_organizations' => Organization::where('is_active', true)->count(),
            'total_users' => User::count(),
            'active_users' => User::where('status', 'active')->count(),
            'total_visits_all_time' => SiteVisit::count(),
            'unique_visitors_all_time' => SiteVisit::distinct('ip_address')->count(),
        ];
    }

    /**
     * Format duration in seconds to human readable format
     */
    protected function formatDuration($seconds): string
    {
        if ($seconds < 60) {
            return $seconds . 's';
        } elseif ($seconds < 3600) {
            return floor($seconds / 60) . 'm ' . ($seconds % 60) . 's';
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            return $hours . 'h ' . $minutes . 'm';
        }
    }

    /**
     * Get analytics data via API
     */
    public function getAnalyticsData(Request $request)
    {
        $period = $request->get('period', 'today');
        $type = $request->get('type', 'visits');

        switch ($type) {
            case 'visits':
                return response()->json(SiteVisit::getVisitStats($period));

            case 'online':
                return response()->json(SiteVisit::getOnlineStats());

            case 'devices':
                return response()->json(SiteVisit::getDeviceBreakdown($period));

            case 'browsers':
                return response()->json(SiteVisit::getBrowserBreakdown($period));

            case 'daily':
                return response()->json($this->getDailyVisits());

            case 'organizations':
                return response()->json($this->getTopOrganizations($period));

            case 'online_users':
                return response()->json($this->getOnlineUsers());

            default:
                return response()->json(['error' => 'Invalid type'], 400);
        }
    }
}
