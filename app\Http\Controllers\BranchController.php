<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\User;
use App\Models\Role;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class BranchController extends Controller
{
    /**
     * Display a listing of the branches.
     */
    public function index()
    {
        $branches = Branch::where('organization_id', Auth::user()->organization_id)
            ->latest()
            ->paginate(10);

        return view('branches.index', compact('branches'));
    }

    /**
     * Show the form for creating a new branch.
     */
    public function create()
    {
        $organization = Auth::user()->organization;

        // Check if organization can add more branches
        $canAddBranch = $organization->canAddBranch();
        $remainingSlots = $organization->getRemainingBranchSlots();
        $planUsage = $organization->getPlanUsage();

        // If at limit, show warning but still allow access to form
        if (!$canAddBranch) {
            session()->flash('warning',
                "You have reached your plan limit of {$organization->plan->branch_limit} branches. " .
                "Please upgrade your plan to add more branches."
            );
        } elseif ($remainingSlots <= 1 && $remainingSlots !== PHP_INT_MAX) {
            session()->flash('info',
                "You have {$remainingSlots} branch slots remaining in your {$organization->plan->name} plan."
            );
        }

        return view('branches.create', compact('canAddBranch', 'remainingSlots', 'planUsage'));
    }

    /**
     * Store a newly created branch in storage.
     */
    public function store(Request $request)
    {
        $organization = Auth::user()->organization;

        // Double-check plan limits before creating branch
        if (!$organization->canAddBranch()) {
            return back()
                ->withErrors(['limit_exceeded' => "You have reached your plan limit of {$organization->plan->branch_limit} branches. Please upgrade your plan to add more branches."])
                ->withInput()
                ->with('upgrade_required', true);
        }

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['nullable', 'string', 'email', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
        ]);

        try {
            $branch = Branch::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'address' => $request->address,
                'description' => $request->description,
                'organization_id' => Auth::user()->organization_id,
            ]);

            if ($request->filled('switch_to_branch') && $request->switch_to_branch) {
                Auth::user()->update(['branch_id' => $branch->id]);
            }

            if ($branch->id === 1 || $request->has('redirect_to_dashboard')) {
                return redirect()->route('dashboard')
                    ->with('success', 'Branch created successfully.');
            }

            return redirect()->route('branches.index')
                ->with('success', 'Branch created successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create branch: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Display the specified branch.
     */
    public function show(Branch $branch)
    {
        // Ensure the branch belongs to the user's organization
        if ($branch->organization_id !== Auth::user()->organization_id) {
            abort(403, 'Unauthorized action.');
        }

        $branchUsers = User::where('branch_id', $branch->id)->get();

        return view('branches.show', compact('branch', 'branchUsers'));
    }

    /**
     * Show the form for editing the specified branch.
     */
    public function edit(Branch $branch)
    {
        // Ensure the branch belongs to the user's organization
        if ($branch->organization_id !== Auth::user()->organization_id) {
            abort(403, 'Unauthorized action.');
        }

        return view('branches.edit', compact('branch'));
    }

    /**
     * Update the specified branch in storage.
     */
    public function update(Request $request, Branch $branch)
    {
        // Ensure the branch belongs to the user's organization
        if ($branch->organization_id !== Auth::user()->organization_id) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['nullable', 'string', 'email', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
        ]);

        try {
            DB::beginTransaction();

            // Update branch information
            $branch->update([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'address' => $request->address,
                'description' => $request->description,
            ]);

            // Update corresponding receipt settings
            $setting = Setting::where('organization_id', $branch->organization_id)->first();

            if ($setting) {
                $setting->update([
                    'company_address' => $request->address,
                    'company_phone' => $request->phone,
                    'company_email' => $request->email,
                ]);
            }

            DB::commit();

            return redirect()->route('branches.index')
                ->with('success', 'Branch updated successfully and receipt settings synchronized.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Failed to update branch: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Remove the specified branch from storage.
     */
    public function destroy(Branch $branch)
    {
        // Ensure the branch belongs to the user's organization
        if ($branch->organization_id !== Auth::user()->organization_id) {
            abort(403, 'Unauthorized action.');
        }

        // Don't allow deleting a branch if it's the only one
        $branchCount = Branch::where('organization_id', Auth::user()->organization_id)->count();
        if ($branchCount <= 1) {
            return back()->with('error', 'Cannot delete the only branch. Organizations must have at least one branch.');
        }

        try {
            DB::beginTransaction();

            // Move users to another branch or null their branch_id
            $anotherBranch = Branch::where('organization_id', Auth::user()->organization_id)
                ->where('id', '!=', $branch->id)
                ->first();

            if ($anotherBranch) {
                User::where('branch_id', $branch->id)->update(['branch_id' => $anotherBranch->id]);
            } else {
                User::where('branch_id', $branch->id)->update(['branch_id' => null]);
            }

            // Delete the branch
            $branch->delete();

            DB::commit();

            return redirect()->route('branches.index')
                ->with('success', 'Branch deleted successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to delete branch: ' . $e->getMessage());
        }
    }

    /**
     * Switch the authenticated user to a different branch.
     */
    public function switchBranch(Request $request)
    {
        $request->validate([
            'branch_id' => [
                'required',
                Rule::exists('branches', 'id')->where(function ($query) {
                    $query->where('organization_id', Auth::user()->organization_id);
                }),
            ],
        ]);

        try {
            Auth::user()->update(['branch_id' => $request->branch_id]);

            return redirect()->back()->with('success', 'Switched to branch successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to switch branch: ' . $e->getMessage());
        }
    }
}
