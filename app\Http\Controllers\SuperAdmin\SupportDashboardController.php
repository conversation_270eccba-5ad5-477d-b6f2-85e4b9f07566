<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\SupportTicket;
use App\Models\OrganizationCommunication;
use App\Models\KnowledgeBaseArticle;
use App\Models\Organization;
use App\Models\User;
use App\Services\LogService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class SupportDashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('super_admin');
    }

    /**
     * Display the support dashboard
     */
    public function index()
    {
        // Get dashboard statistics
        $stats = $this->getDashboardStats();

        // Get recent tickets
        $recentTickets = SupportTicket::with(['user', 'organization', 'assignedAdmin'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get urgent tickets
        $urgentTickets = SupportTicket::urgent()
            ->open()
            ->with(['user', 'organization'])
            ->orderBy('created_at')
            ->limit(5)
            ->get();

        // Get overdue tickets
        $overdueTickets = SupportTicket::open()
            ->with(['user', 'organization'])
            ->get()
            ->filter(function($ticket) {
                return $ticket->is_overdue;
            })
            ->take(5);

        // Get recent communications
        $recentCommunications = OrganizationCommunication::with(['organization', 'sender'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get scheduled communications
        $scheduledCommunications = OrganizationCommunication::scheduled()
            ->with(['organization', 'sender'])
            ->orderBy('scheduled_at')
            ->limit(5)
            ->get();

        // Get popular knowledge base articles
        $popularArticles = KnowledgeBaseArticle::published()
            ->with(['category', 'author'])
            ->orderBy('view_count', 'desc')
            ->limit(5)
            ->get();

        return view('super_admin.support.dashboard', compact(
            'stats',
            'recentTickets',
            'urgentTickets',
            'overdueTickets',
            'recentCommunications',
            'scheduledCommunications',
            'popularArticles'
        ));
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats()
    {
        $today = now()->startOfDay();
        $thisWeek = now()->startOfWeek();
        $thisMonth = now()->startOfMonth();

        return [
            // Ticket statistics
            'tickets' => [
                'total' => SupportTicket::count(),
                'open' => SupportTicket::open()->count(),
                'urgent' => SupportTicket::urgent()->open()->count(),
                'overdue' => SupportTicket::open()->get()->filter(fn($t) => $t->is_overdue)->count(),
                'today' => SupportTicket::whereDate('created_at', $today)->count(),
                'this_week' => SupportTicket::where('created_at', '>=', $thisWeek)->count(),
                'this_month' => SupportTicket::where('created_at', '>=', $thisMonth)->count(),
                'avg_response_time' => $this->getAverageResponseTime(),
                'avg_resolution_time' => $this->getAverageResolutionTime(),
            ],

            // Communication statistics
            'communications' => [
                'total' => OrganizationCommunication::count(),
                'sent_today' => OrganizationCommunication::sent()
                    ->whereDate('sent_at', $today)
                    ->count(),
                'scheduled' => OrganizationCommunication::scheduled()->count(),
                'overdue' => OrganizationCommunication::overdue()->count(),
            ],

            // Knowledge base statistics
            'knowledge_base' => [
                'total_articles' => KnowledgeBaseArticle::count(),
                'published_articles' => KnowledgeBaseArticle::published()->count(),
                'draft_articles' => KnowledgeBaseArticle::where('status', KnowledgeBaseArticle::STATUS_DRAFT)->count(),
                'total_views' => KnowledgeBaseArticle::sum('view_count'),
                'avg_helpfulness' => $this->getAverageHelpfulness(),
            ],

            // Organization statistics
            'organizations' => [
                'total' => Organization::count(),
                'active' => Organization::where('is_active', true)->count(),
                'with_open_tickets' => SupportTicket::open()
                    ->distinct('organization_id')
                    ->count('organization_id'),
            ],

            // Performance metrics
            'performance' => [
                'tickets_by_priority' => SupportTicket::selectRaw('priority, COUNT(*) as count')
                    ->groupBy('priority')
                    ->pluck('count', 'priority')
                    ->toArray(),
                'tickets_by_status' => SupportTicket::selectRaw('status, COUNT(*) as count')
                    ->groupBy('status')
                    ->pluck('count', 'status')
                    ->toArray(),
                'tickets_by_category' => SupportTicket::selectRaw('category, COUNT(*) as count')
                    ->groupBy('category')
                    ->pluck('count', 'category')
                    ->toArray(),
            ],
        ];
    }

    /**
     * Get average response time in hours
     */
    private function getAverageResponseTime()
    {
        $tickets = SupportTicket::whereNotNull('first_response_at')->get();

        if ($tickets->isEmpty()) {
            return 0;
        }

        $totalHours = $tickets->sum(function($ticket) {
            return $ticket->response_time;
        });

        return round($totalHours / $tickets->count(), 1);
    }

    /**
     * Get average resolution time in hours
     */
    private function getAverageResolutionTime()
    {
        $tickets = SupportTicket::whereNotNull('resolved_at')->get();

        if ($tickets->isEmpty()) {
            return 0;
        }

        $totalHours = $tickets->sum(function($ticket) {
            return $ticket->resolution_time;
        });

        return round($totalHours / $tickets->count(), 1);
    }

    /**
     * Get average helpfulness percentage for knowledge base
     */
    private function getAverageHelpfulness()
    {
        $articles = KnowledgeBaseArticle::published()
            ->whereRaw('(helpful_count + not_helpful_count) > 0')
            ->get();

        if ($articles->isEmpty()) {
            return 0;
        }

        $totalPercentage = $articles->sum(function($article) {
            return $article->helpfulness_percentage;
        });

        return round($totalPercentage / $articles->count(), 1);
    }

    /**
     * Get support metrics for API/AJAX requests
     */
    public function metrics(Request $request)
    {
        $period = $request->get('period', '7d'); // 24h, 7d, 30d

        $startDate = match($period) {
            '24h' => now()->subDay(),
            '7d' => now()->subWeek(),
            '30d' => now()->subMonth(),
            default => now()->subWeek(),
        };

        // Ticket trends
        $ticketTrends = SupportTicket::where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('count', 'date');

        // Resolution trends
        $resolutionTrends = SupportTicket::where('resolved_at', '>=', $startDate)
            ->selectRaw('DATE(resolved_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('count', 'date');

        return response()->json([
            'ticket_trends' => $ticketTrends,
            'resolution_trends' => $resolutionTrends,
            'stats' => $this->getDashboardStats(),
        ]);
    }

    /**
     * Quick actions for dashboard
     */
    public function quickAction(Request $request)
    {
        $action = $request->get('action');
        $result = ['success' => false, 'message' => 'Unknown action'];

        switch ($action) {
            case 'send_overdue_communications':
                $count = $this->sendOverdueCommunications();
                $result = [
                    'success' => true,
                    'message' => "Sent {$count} overdue communications"
                ];
                break;

            case 'assign_unassigned_tickets':
                $count = $this->assignUnassignedTickets();
                $result = [
                    'success' => true,
                    'message' => "Assigned {$count} unassigned tickets"
                ];
                break;

            case 'update_kb_stats':
                $this->updateKnowledgeBaseStats();
                $result = [
                    'success' => true,
                    'message' => 'Knowledge base statistics updated'
                ];
                break;
        }

        // Log the action
        LogService::info("Support dashboard quick action: {$action}", [
            'action' => $action,
            'result' => $result,
            'admin_id' => auth('super_admin')->id(),
        ]);

        return response()->json($result);
    }

    /**
     * Send overdue scheduled communications
     */
    private function sendOverdueCommunications()
    {
        $overdueCommunications = OrganizationCommunication::overdue()->get();

        foreach ($overdueCommunications as $communication) {
            $communication->send();
        }

        return $overdueCommunications->count();
    }

    /**
     * Auto-assign unassigned tickets to available admins
     */
    private function assignUnassignedTickets()
    {
        $unassignedTickets = SupportTicket::unassigned()->open()->get();
        $admins = \App\Models\SuperAdmin::all();

        if ($admins->isEmpty()) {
            return 0;
        }

        $assignedCount = 0;
        foreach ($unassignedTickets as $ticket) {
            // Simple round-robin assignment
            $admin = $admins->random();
            $ticket->assignTo($admin->id);
            $assignedCount++;
        }

        return $assignedCount;
    }

    /**
     * Update knowledge base statistics
     */
    private function updateKnowledgeBaseStats()
    {
        // This could include updating search indexes,
        // recalculating helpfulness scores, etc.
        // For now, we'll just log the action
        LogService::info('Knowledge base statistics updated');
    }
}
