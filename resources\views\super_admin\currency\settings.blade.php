@extends('super_admin.layouts.app')

@section('title', 'Currency Settings')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Currency Settings</h1>
                <a href="{{ route('super_admin.currency.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Currency Management
                </a>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Currency Configuration</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('super_admin.currency.settings.update') }}">
                                @csrf
                                @method('PUT')

                                <div class="mb-4">
                                    <h6 class="text-primary">Basic Settings</h6>
                                    <hr>
                                </div>

                                <div class="mb-3">
                                    <label for="default_currency" class="form-label">Default Currency *</label>
                                    <select class="form-select @error('default_currency') is-invalid @enderror" 
                                            id="default_currency" 
                                            name="default_currency" 
                                            required>
                                        <option value="">Select default currency</option>
                                        <option value="USD" {{ old('default_currency', $settings['default_currency'] ?? '') === 'USD' ? 'selected' : '' }}>
                                            USD - US Dollar ($)
                                        </option>
                                        <option value="NGN" {{ old('default_currency', $settings['default_currency'] ?? '') === 'NGN' ? 'selected' : '' }}>
                                            NGN - Nigerian Naira (₦)
                                        </option>
                                    </select>
                                    @error('default_currency')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">This is the base currency for all calculations and storage.</div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="auto_currency_detection" 
                                               name="auto_currency_detection" 
                                               value="1"
                                               {{ old('auto_currency_detection', $settings['auto_currency_detection'] ?? true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="auto_currency_detection">
                                            Enable Automatic Currency Detection
                                        </label>
                                    </div>
                                    <div class="form-text">Automatically detect user currency based on their IP address location.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="currency_decimal_places" class="form-label">Currency Decimal Places *</label>
                                    <select class="form-select @error('currency_decimal_places') is-invalid @enderror" 
                                            id="currency_decimal_places" 
                                            name="currency_decimal_places" 
                                            required>
                                        @for($i = 0; $i <= 6; $i++)
                                            <option value="{{ $i }}" {{ old('currency_decimal_places', $settings['currency_decimal_places'] ?? 2) == $i ? 'selected' : '' }}>
                                                {{ $i }} decimal place{{ $i !== 1 ? 's' : '' }}
                                                @if($i === 2) (Recommended) @endif
                                            </option>
                                        @endfor
                                    </select>
                                    @error('currency_decimal_places')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-4">
                                    <h6 class="text-primary">IP Geolocation Settings</h6>
                                    <hr>
                                </div>

                                <div class="mb-3">
                                    <label for="nigerian_ip_ranges" class="form-label">Nigerian IP Ranges</label>
                                    <textarea class="form-control @error('nigerian_ip_ranges') is-invalid @enderror" 
                                              id="nigerian_ip_ranges" 
                                              name="nigerian_ip_ranges" 
                                              rows="8" 
                                              placeholder="Enter IP ranges, one per line (e.g., ********/8)">{{ old('nigerian_ip_ranges', is_array($settings['nigerian_ip_ranges'] ?? []) ? implode("\n", $settings['nigerian_ip_ranges']) : '') }}</textarea>
                                    @error('nigerian_ip_ranges')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        IP address ranges that should be detected as Nigerian users (NGN currency).
                                        Enter one range per line in CIDR notation (e.g., ********/8).
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('super_admin.currency.index') }}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Save Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Current Settings</h6>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-6">Default Currency:</dt>
                                <dd class="col-sm-6">
                                    <span class="badge bg-primary">{{ $settings['default_currency'] ?? 'USD' }}</span>
                                </dd>

                                <dt class="col-sm-6">Auto Detection:</dt>
                                <dd class="col-sm-6">
                                    <span class="badge bg-{{ ($settings['auto_currency_detection'] ?? true) ? 'success' : 'secondary' }}">
                                        {{ ($settings['auto_currency_detection'] ?? true) ? 'Enabled' : 'Disabled' }}
                                    </span>
                                </dd>

                                <dt class="col-sm-6">Decimal Places:</dt>
                                <dd class="col-sm-6">{{ $settings['currency_decimal_places'] ?? 2 }}</dd>

                                <dt class="col-sm-6">IP Ranges:</dt>
                                <dd class="col-sm-6">{{ count($settings['nigerian_ip_ranges'] ?? []) }} configured</dd>
                            </dl>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Default IP Ranges</h6>
                        </div>
                        <div class="card-body">
                            <p class="small text-muted mb-2">Common Nigerian IP ranges:</p>
                            <ul class="list-unstyled small">
                                <li><code>********/8</code></li>
                                <li><code>*********/8</code></li>
                                <li><code>***********/16</code></li>
                                <li><code>*********/8</code></li>
                                <li><code>*********/8</code></li>
                                <li><code>*********/8</code></li>
                            </ul>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="useDefaultRanges()">
                                Use Default Ranges
                            </button>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Test IP Detection</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <input type="text" class="form-control form-control-sm" id="testIp" placeholder="Enter IP address">
                            </div>
                            <button type="button" class="btn btn-sm btn-info w-100" onclick="testIpDetection()">
                                Test Detection
                            </button>
                            <div id="testResult" class="mt-2" style="display: none;">
                                <small class="text-muted">Result will appear here</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function useDefaultRanges() {
    const defaultRanges = [
        '********/8',
        '*********/8',
        '***********/16',
        '*********/8',
        '*********/8',
        '*********/8'
    ];
    
    document.getElementById('nigerian_ip_ranges').value = defaultRanges.join('\n');
}

async function testIpDetection() {
    const ip = document.getElementById('testIp').value;
    const resultDiv = document.getElementById('testResult');
    
    if (!ip) {
        alert('Please enter an IP address');
        return;
    }
    
    try {
        const response = await fetch('{{ route("super_admin.currency.test-geolocation") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ip_address: ip })
        });
        
        const data = await response.json();
        
        resultDiv.innerHTML = `
            <div class="alert alert-info alert-sm">
                <strong>IP:</strong> ${data.ip_address}<br>
                <strong>Country:</strong> ${data.location_info.country || 'Unknown'}<br>
                <strong>Currency:</strong> <span class="badge bg-primary">${data.detected_currency}</span>
            </div>
        `;
        resultDiv.style.display = 'block';
        
    } catch (error) {
        resultDiv.innerHTML = `<div class="alert alert-danger alert-sm">Error: ${error.message}</div>`;
        resultDiv.style.display = 'block';
    }
}
</script>
@endpush
