<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Order;

class OrderOverdueReminder extends Notification implements ShouldQueue
{
    use Queueable;

    private $order;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        $priorityLevel = $this->order->priority_level;
        $urgencyText = match($priorityLevel['level']) {
            'Critical' => 'This order is critically overdue',
            'High' => 'This order requires immediate attention',
            'Medium' => 'This order needs your attention'
        };

        return (new MailMessage)
            ->subject('Overdue Order Reminder - ' . $this->order->order_number)
            ->line('Dear ' . $this->order->customer_name . ',')
            ->line($urgencyText)
            ->line('Order Details:')
            ->line('- Order Number: ' . $this->order->order_number)
            ->line('- Expected Delivery: ' . $this->order->expected_delivery_date . ' ' . $this->order->expected_delivery_time)
            ->line('- Days Overdue: ' . $this->order->overdue_days)
            ->action('View Order Details', url('/orders/' . $this->order->id))
            ->line('Please contact us immediately to resolve this delay.')
            ->line('Thank you for your prompt attention to this matter.');
    }
}