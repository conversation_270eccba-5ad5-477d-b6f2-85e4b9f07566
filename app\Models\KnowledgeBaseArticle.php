<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class KnowledgeBaseArticle extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'excerpt',
        'category_id',
        'author_id',
        'status',
        'visibility',
        'featured',
        'view_count',
        'helpful_count',
        'not_helpful_count',
        'tags',
        'metadata',
        'published_at',
    ];

    protected $casts = [
        'featured' => 'boolean',
        'tags' => 'array',
        'metadata' => 'array',
        'published_at' => 'datetime',
    ];

    // Status options
    const STATUS_DRAFT = 'draft';
    const STATUS_PUBLISHED = 'published';
    const STATUS_ARCHIVED = 'archived';

    // Visibility options
    const VISIBILITY_PUBLIC = 'public';
    const VISIBILITY_CUSTOMERS_ONLY = 'customers_only';
    const VISIBILITY_ADMINS_ONLY = 'admins_only';

    /**
     * Get the category this article belongs to
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(KnowledgeBaseCategory::class, 'category_id');
    }

    /**
     * Get the author (super admin) of this article
     */
    public function author(): BelongsTo
    {
        return $this->belongsTo(SuperAdmin::class, 'author_id');
    }

    /**
     * Get feedback for this article
     */
    public function feedback(): HasMany
    {
        return $this->hasMany(KnowledgeBaseFeedback::class);
    }

    /**
     * Scope for published articles
     */
    public function scopePublished($query)
    {
        return $query->where('status', self::STATUS_PUBLISHED)
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope for featured articles
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope for filtering by category
     */
    public function scopeCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope for filtering by visibility
     */
    public function scopeVisibility($query, $visibility)
    {
        return $query->where('visibility', $visibility);
    }

    /**
     * Scope for searching articles
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
              ->orWhere('content', 'like', "%{$search}%")
              ->orWhere('excerpt', 'like', "%{$search}%")
              ->orWhereJsonContains('tags', $search);
        });
    }

    /**
     * Scope for popular articles (most viewed)
     */
    public function scopePopular($query, $limit = 10)
    {
        return $query->orderBy('view_count', 'desc')->limit($limit);
    }

    /**
     * Scope for helpful articles (best rated)
     */
    public function scopeHelpful($query, $limit = 10)
    {
        return $query->selectRaw('*, (helpful_count - not_helpful_count) as helpfulness_score')
                    ->orderBy('helpfulness_score', 'desc')
                    ->limit($limit);
    }

    /**
     * Get status badge HTML
     */
    public function getStatusBadgeAttribute()
    {
        $colors = [
            self::STATUS_DRAFT => 'secondary',
            self::STATUS_PUBLISHED => 'success',
            self::STATUS_ARCHIVED => 'warning',
        ];

        $color = $colors[$this->status] ?? 'secondary';
        return "<span class='badge bg-{$color}'>" . ucfirst($this->status) . "</span>";
    }

    /**
     * Get visibility badge HTML
     */
    public function getVisibilityBadgeAttribute()
    {
        $colors = [
            self::VISIBILITY_PUBLIC => 'success',
            self::VISIBILITY_CUSTOMERS_ONLY => 'primary',
            self::VISIBILITY_ADMINS_ONLY => 'warning',
        ];

        $labels = [
            self::VISIBILITY_PUBLIC => 'Public',
            self::VISIBILITY_CUSTOMERS_ONLY => 'Customers',
            self::VISIBILITY_ADMINS_ONLY => 'Admins Only',
        ];

        $color = $colors[$this->visibility] ?? 'secondary';
        $label = $labels[$this->visibility] ?? ucfirst($this->visibility);

        return "<span class='badge bg-{$color}'>{$label}</span>";
    }

    /**
     * Get helpfulness percentage
     */
    public function getHelpfulnessPercentageAttribute()
    {
        $total = $this->helpful_count + $this->not_helpful_count;
        if ($total === 0) {
            return null;
        }

        return round(($this->helpful_count / $total) * 100, 1);
    }

    /**
     * Get reading time estimate in minutes
     */
    public function getReadingTimeAttribute()
    {
        $wordCount = str_word_count(strip_tags($this->content));
        $wordsPerMinute = 200; // Average reading speed
        return max(1, round($wordCount / $wordsPerMinute));
    }

    /**
     * Get truncated excerpt
     */
    public function getTruncatedExcerptAttribute()
    {
        if ($this->excerpt) {
            return Str::limit($this->excerpt, 150);
        }

        return Str::limit(strip_tags($this->content), 150);
    }

    /**
     * Get all status options
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_DRAFT => 'Draft',
            self::STATUS_PUBLISHED => 'Published',
            self::STATUS_ARCHIVED => 'Archived',
        ];
    }

    /**
     * Get all visibility options
     */
    public static function getVisibilityOptions()
    {
        return [
            self::VISIBILITY_PUBLIC => 'Public (Everyone)',
            self::VISIBILITY_CUSTOMERS_ONLY => 'Customers Only',
            self::VISIBILITY_ADMINS_ONLY => 'Admins Only',
        ];
    }

    /**
     * Increment view count
     */
    public function incrementViews()
    {
        $this->increment('view_count');
    }

    /**
     * Mark as helpful
     */
    public function markAsHelpful()
    {
        $this->increment('helpful_count');
    }

    /**
     * Mark as not helpful
     */
    public function markAsNotHelpful()
    {
        $this->increment('not_helpful_count');
    }

    /**
     * Generate slug from title
     */
    public function generateSlug()
    {
        $slug = Str::slug($this->title);
        $originalSlug = $slug;
        $counter = 1;

        while (self::where('slug', $slug)->where('id', '!=', $this->id)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Publish article
     */
    public function publish()
    {
        $this->update([
            'status' => self::STATUS_PUBLISHED,
            'published_at' => now(),
        ]);
    }

    /**
     * Archive article
     */
    public function archive()
    {
        $this->update(['status' => self::STATUS_ARCHIVED]);
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($article) {
            if (!$article->slug) {
                $article->slug = $article->generateSlug();
            }
        });

        static::updating(function ($article) {
            if ($article->isDirty('title') && !$article->isDirty('slug')) {
                $article->slug = $article->generateSlug();
            }
        });
    }
}
