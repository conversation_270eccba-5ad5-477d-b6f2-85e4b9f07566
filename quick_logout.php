<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

echo "=== Quick Logout Tool ===\n\n";

try {
    // Check current authentication status
    echo "Current authentication status:\n";
    
    $guards = ['web', 'affiliate', 'super_admin'];
    $loggedOut = [];
    
    foreach ($guards as $guard) {
        if (Auth::guard($guard)->check()) {
            $user = Auth::guard($guard)->user();
            echo "- {$guard}: AUTHENTICATED (User: {$user->email})\n";
            
            // Logout from this guard
            Auth::guard($guard)->logout();
            $loggedOut[] = $guard;
        } else {
            echo "- {$guard}: GUEST\n";
        }
    }
    
    if (!empty($loggedOut)) {
        echo "\nLogged out from: " . implode(', ', $loggedOut) . "\n";
        
        // Clear session
        Session::flush();
        Session::regenerate();
        
        echo "Session cleared and regenerated\n";
    } else {
        echo "\nNo active sessions found\n";
    }
    
    echo "\n=== Logout Complete ===\n";
    echo "You can now test affiliate login without authentication conflicts\n";
    echo "Visit: http://localhost/SalesManagementSystem/affiliate/login\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
