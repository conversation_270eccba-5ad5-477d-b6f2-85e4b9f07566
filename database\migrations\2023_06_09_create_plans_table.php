<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2); // Monthly price
            $table->decimal('annual_price', 10, 2)->nullable(); // Annual price
            $table->string('billing_period')->default('monthly'); // monthly, annual, both
            $table->integer('annual_discount_percentage')->default(0); // Discount for annual billing
            $table->integer('branch_limit');
            $table->integer('user_limit');
            $table->integer('data_retention_days');
            $table->boolean('thermal_printing')->default(false);
            $table->boolean('advanced_reporting')->default(false);
            $table->boolean('api_access')->default(false);
            $table->boolean('white_label')->default(false);
            $table->boolean('custom_branding')->default(false);
            $table->json('additional_features')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};