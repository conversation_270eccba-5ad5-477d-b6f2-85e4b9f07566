<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\KnowledgeBaseArticle;
use App\Models\KnowledgeBaseCategory;
use App\Services\LogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class KnowledgeBaseController extends Controller
{
    /**
     * Display knowledge base management dashboard
     */
    public function index(Request $request)
    {
        $query = KnowledgeBaseArticle::with(['category', 'author']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        if ($request->filled('visibility')) {
            $query->where('visibility', $request->visibility);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->search($search);
        }

        $articles = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get categories for filter
        $categories = KnowledgeBaseCategory::active()->ordered()->get();

        // Get statistics
        $stats = [
            'total' => KnowledgeBaseArticle::count(),
            'published' => KnowledgeBaseArticle::published()->count(),
            'draft' => KnowledgeBaseArticle::where('status', KnowledgeBaseArticle::STATUS_DRAFT)->count(),
            'archived' => KnowledgeBaseArticle::where('status', KnowledgeBaseArticle::STATUS_ARCHIVED)->count(),
            'total_views' => KnowledgeBaseArticle::sum('view_count'),
        ];

        return view('super_admin.knowledge_base.index', compact('articles', 'categories', 'stats'));
    }

    /**
     * Show form to create new article
     */
    public function create()
    {
        $categories = KnowledgeBaseCategory::active()->ordered()->get();
        
        return view('super_admin.knowledge_base.create', compact('categories'));
    }

    /**
     * Store new article
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'category_id' => 'required|exists:knowledge_base_categories,id',
            'status' => 'required|in:' . implode(',', array_keys(KnowledgeBaseArticle::getStatusOptions())),
            'visibility' => 'required|in:' . implode(',', array_keys(KnowledgeBaseArticle::getVisibilityOptions())),
            'featured' => 'boolean',
            'tags' => 'nullable|string',
        ]);

        $admin = Auth::guard('super_admin')->user();

        $article = KnowledgeBaseArticle::create([
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'content' => $request->content,
            'excerpt' => $request->excerpt,
            'category_id' => $request->category_id,
            'author_id' => $admin->id,
            'status' => $request->status,
            'visibility' => $request->visibility,
            'featured' => $request->boolean('featured'),
            'tags' => $request->tags ? explode(',', $request->tags) : [],
            'published_at' => $request->status === KnowledgeBaseArticle::STATUS_PUBLISHED ? now() : null,
        ]);

        LogService::info('Knowledge base article created', [
            'article_id' => $article->id,
            'title' => $article->title,
            'author_id' => $admin->id,
            'status' => $article->status,
        ]);

        return redirect()->route('super.knowledge-base.show', $article)
            ->with('success', 'Article created successfully.');
    }

    /**
     * Display article
     */
    public function show(KnowledgeBaseArticle $article)
    {
        $article->load(['category', 'author', 'feedback.user']);
        
        return view('super_admin.knowledge_base.show', compact('article'));
    }

    /**
     * Show form to edit article
     */
    public function edit(KnowledgeBaseArticle $article)
    {
        $categories = KnowledgeBaseCategory::active()->ordered()->get();
        
        return view('super_admin.knowledge_base.edit', compact('article', 'categories'));
    }

    /**
     * Update article
     */
    public function update(Request $request, KnowledgeBaseArticle $article)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'category_id' => 'required|exists:knowledge_base_categories,id',
            'status' => 'required|in:' . implode(',', array_keys(KnowledgeBaseArticle::getStatusOptions())),
            'visibility' => 'required|in:' . implode(',', array_keys(KnowledgeBaseArticle::getVisibilityOptions())),
            'featured' => 'boolean',
            'tags' => 'nullable|string',
        ]);

        $oldStatus = $article->status;

        $article->update([
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'content' => $request->content,
            'excerpt' => $request->excerpt,
            'category_id' => $request->category_id,
            'status' => $request->status,
            'visibility' => $request->visibility,
            'featured' => $request->boolean('featured'),
            'tags' => $request->tags ? explode(',', $request->tags) : [],
            'published_at' => ($request->status === KnowledgeBaseArticle::STATUS_PUBLISHED && $oldStatus !== KnowledgeBaseArticle::STATUS_PUBLISHED) 
                ? now() 
                : $article->published_at,
        ]);

        LogService::info('Knowledge base article updated', [
            'article_id' => $article->id,
            'title' => $article->title,
            'old_status' => $oldStatus,
            'new_status' => $article->status,
        ]);

        return redirect()->route('super.knowledge-base.show', $article)
            ->with('success', 'Article updated successfully.');
    }

    /**
     * Delete article
     */
    public function destroy(KnowledgeBaseArticle $article)
    {
        $title = $article->title;
        
        $article->delete();

        LogService::info('Knowledge base article deleted', [
            'article_id' => $article->id,
            'title' => $title,
        ]);

        return redirect()->route('super.knowledge-base.index')
            ->with('success', 'Article deleted successfully.');
    }

    /**
     * Bulk actions for articles
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:publish,unpublish,archive,delete,feature,unfeature',
            'articles' => 'required|array',
            'articles.*' => 'exists:knowledge_base_articles,id',
        ]);

        $articles = KnowledgeBaseArticle::whereIn('id', $request->articles)->get();
        $count = 0;

        foreach ($articles as $article) {
            switch ($request->action) {
                case 'publish':
                    $article->update([
                        'status' => KnowledgeBaseArticle::STATUS_PUBLISHED,
                        'published_at' => $article->published_at ?: now(),
                    ]);
                    $count++;
                    break;
                case 'unpublish':
                    $article->update(['status' => KnowledgeBaseArticle::STATUS_DRAFT]);
                    $count++;
                    break;
                case 'archive':
                    $article->update(['status' => KnowledgeBaseArticle::STATUS_ARCHIVED]);
                    $count++;
                    break;
                case 'delete':
                    $article->delete();
                    $count++;
                    break;
                case 'feature':
                    $article->update(['featured' => true]);
                    $count++;
                    break;
                case 'unfeature':
                    $article->update(['featured' => false]);
                    $count++;
                    break;
            }
        }

        LogService::info('Knowledge base bulk action performed', [
            'action' => $request->action,
            'article_count' => $count,
            'article_ids' => $request->articles,
        ]);

        return back()->with('success', "Successfully performed {$request->action} on {$count} articles.");
    }

    /**
     * Analytics for knowledge base
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', 30);
        $startDate = now()->subDays($period);

        $metrics = [
            'total_articles' => KnowledgeBaseArticle::count(),
            'published_articles' => KnowledgeBaseArticle::published()->count(),
            'total_views' => KnowledgeBaseArticle::sum('view_count'),
            'avg_helpfulness' => $this->getAverageHelpfulness(),
            'popular_articles' => KnowledgeBaseArticle::published()
                ->with('category')
                ->orderBy('view_count', 'desc')
                ->limit(10)
                ->get(),
            'recent_feedback' => \App\Models\KnowledgeBaseFeedback::with(['article', 'user'])
                ->where('created_at', '>=', $startDate)
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get(),
        ];

        return view('super_admin.knowledge_base.analytics', compact('metrics'));
    }

    /**
     * Get average helpfulness percentage
     */
    private function getAverageHelpfulness()
    {
        $articles = KnowledgeBaseArticle::published()
            ->where('helpful_count', '>', 0)
            ->orWhere('not_helpful_count', '>', 0)
            ->get();

        if ($articles->isEmpty()) {
            return 0;
        }

        $totalHelpfulness = $articles->sum(function($article) {
            return $article->helpfulness_percentage;
        });

        return round($totalHelpfulness / $articles->count(), 1);
    }
}
