<?php

namespace App\Observers;

use App\Models\SubscriptionPayment;
use App\Jobs\ProcessCommissionJob;
use Illuminate\Support\Facades\Log;

class SubscriptionPaymentObserver
{

    /**
     * Handle the SubscriptionPayment "created" event.
     */
    public function created(SubscriptionPayment $subscriptionPayment): void
    {
        // Commission processing happens when payment is approved, not when created
    }

    /**
     * Handle the SubscriptionPayment "updated" event.
     */
    public function updated(SubscriptionPayment $subscriptionPayment): void
    {
        // Check if payment status changed to approved
        if ($subscriptionPayment->isDirty('status') && $subscriptionPayment->status === 'approved') {
            Log::info('Processing commission for approved payment', [
                'payment_id' => $subscriptionPayment->id,
                'organization_id' => $subscriptionPayment->organization_id,
                'amount' => $subscriptionPayment->amount
            ]);

            try {
                // Process commission synchronously for immediate results
                $commissionCalculationService = app(\App\Services\CommissionCalculationService::class);
                $referralTrackingService = app(\App\Services\ReferralTrackingService::class);

                // Convert referral if this is the first payment
                $referralTrackingService->convertReferral($subscriptionPayment->organization);

                // Calculate and create commission
                $earning = $commissionCalculationService->calculateCommission($subscriptionPayment);

                if ($earning) {
                    Log::info('Commission processed successfully', [
                        'payment_id' => $subscriptionPayment->id,
                        'earning_id' => $earning->id,
                        'amount' => $earning->amount,
                        'status' => $earning->status
                    ]);
                } else {
                    Log::info('No commission created - no referral found or other conditions not met', [
                        'payment_id' => $subscriptionPayment->id,
                        'organization_id' => $subscriptionPayment->organization_id
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Failed to process commission', [
                    'payment_id' => $subscriptionPayment->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                // Fallback to queue job if synchronous processing fails
                ProcessCommissionJob::dispatch($subscriptionPayment);
            }
        }
    }

    /**
     * Handle the SubscriptionPayment "deleted" event.
     */
    public function deleted(SubscriptionPayment $subscriptionPayment): void
    {
        // Handle commission reversal if needed
        // This would be implemented based on business requirements
    }

    /**
     * Handle the SubscriptionPayment "restored" event.
     */
    public function restored(SubscriptionPayment $subscriptionPayment): void
    {
        // Handle commission restoration if needed
    }

    /**
     * Handle the SubscriptionPayment "force deleted" event.
     */
    public function forceDeleted(SubscriptionPayment $subscriptionPayment): void
    {
        // Handle permanent commission removal if needed
    }
}
