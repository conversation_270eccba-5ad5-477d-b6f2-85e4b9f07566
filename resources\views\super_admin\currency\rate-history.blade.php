@extends('super_admin.layouts.app')

@section('title', 'Exchange Rate History')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    Exchange Rate History: {{ $fromCurrencyObj->code }} to {{ $toCurrencyObj->code }}
                </h1>
                <div>
                    <a href="{{ route('super_admin.currency.edit-rate', [$fromCurrencyObj->code, $toCurrencyObj->code]) }}" 
                       class="btn btn-primary me-2">
                        <i class="fas fa-edit"></i> Edit Rate
                    </a>
                    <a href="{{ route('super_admin.currency.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Currency Management
                    </a>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Rate Change History</h5>
                        </div>
                        <div class="card-body">
                            @if($history->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Action</th>
                                                <th>Old Rate</th>
                                                <th>New Rate</th>
                                                <th>Change</th>
                                                <th>Changed By</th>
                                                <th>Reason</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($history as $change)
                                                <tr>
                                                    <td>
                                                        <div>{{ $change->created_at->format('M d, Y') }}</div>
                                                        <small class="text-muted">{{ $change->created_at->format('H:i:s') }}</small>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-{{ $change->action === 'created' ? 'success' : ($change->action === 'updated' ? 'warning' : 'secondary') }}">
                                                            {{ ucfirst($change->action) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        @if($change->old_rate)
                                                            {{ number_format($change->old_rate, 6) }}
                                                        @else
                                                            <span class="text-muted">N/A</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <strong>{{ number_format($change->new_rate, 6) }}</strong>
                                                    </td>
                                                    <td>
                                                        @if($change->old_rate)
                                                            @php
                                                                $changePercent = (($change->new_rate - $change->old_rate) / $change->old_rate) * 100;
                                                                $isIncrease = $changePercent > 0;
                                                            @endphp
                                                            <span class="badge bg-{{ $isIncrease ? 'success' : 'danger' }}">
                                                                {{ $isIncrease ? '+' : '' }}{{ number_format($changePercent, 2) }}%
                                                            </span>
                                                        @else
                                                            <span class="text-muted">-</span>
                                                        @endif
                                                    </td>
                                                    <td>{{ $change->changed_by_name }}</td>
                                                    <td>
                                                        @if($change->reason)
                                                            <span title="{{ $change->reason }}">
                                                                {{ Str::limit($change->reason, 30) }}
                                                            </span>
                                                        @else
                                                            <span class="text-muted">No reason provided</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <div class="d-flex justify-content-center">
                                    {{ $history->links() }}
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No Rate Changes Found</h5>
                                    <p class="text-muted">No exchange rate changes have been recorded for this currency pair yet.</p>
                                    <a href="{{ route('super_admin.currency.edit-rate', [$fromCurrencyObj->code, $toCurrencyObj->code]) }}" 
                                       class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Set Initial Rate
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Currency Pair Info</h6>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-4">From:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-primary">{{ $fromCurrencyObj->code }}</span>
                                    {{ $fromCurrencyObj->name }}
                                </dd>

                                <dt class="col-sm-4">To:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-success">{{ $toCurrencyObj->code }}</span>
                                    {{ $toCurrencyObj->name }}
                                </dd>

                                <dt class="col-sm-4">Symbol:</dt>
                                <dd class="col-sm-8">
                                    {{ $fromCurrencyObj->symbol }} → {{ $toCurrencyObj->symbol }}
                                </dd>

                                <dt class="col-sm-4">Total Changes:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-info">{{ $history->total() }}</span>
                                </dd>
                            </dl>
                        </div>
                    </div>

                    @if($history->count() > 0)
                        @php
                            $latestChange = $history->first();
                            $currentRate = \App\Models\CurrencyRate::where('from_currency', $fromCurrencyObj->code)
                                ->where('to_currency', $toCurrencyObj->code)
                                ->where('is_active', true)
                                ->first();
                        @endphp
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Current Rate</h6>
                            </div>
                            <div class="card-body">
                                @if($currentRate)
                                    <div class="text-center">
                                        <h4 class="text-primary mb-1">
                                            1 {{ $fromCurrencyObj->code }} = {{ number_format($currentRate->rate, 2) }} {{ $toCurrencyObj->code }}
                                        </h4>
                                        <small class="text-muted">
                                            Last updated: {{ $currentRate->updated_at->diffForHumans() }}
                                        </small>
                                    </div>
                                @else
                                    <div class="text-center">
                                        <p class="text-muted">No active rate set</p>
                                        <a href="{{ route('super_admin.currency.edit-rate', [$fromCurrencyObj->code, $toCurrencyObj->code]) }}" 
                                           class="btn btn-sm btn-primary">
                                            Set Rate
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Quick Stats</h6>
                            </div>
                            <div class="card-body">
                                @php
                                    $rates = $history->pluck('new_rate');
                                    $minRate = $rates->min();
                                    $maxRate = $rates->max();
                                    $avgRate = $rates->avg();
                                @endphp
                                
                                <dl class="row small">
                                    <dt class="col-sm-6">Highest Rate:</dt>
                                    <dd class="col-sm-6">{{ number_format($maxRate, 6) }}</dd>

                                    <dt class="col-sm-6">Lowest Rate:</dt>
                                    <dd class="col-sm-6">{{ number_format($minRate, 6) }}</dd>

                                    <dt class="col-sm-6">Average Rate:</dt>
                                    <dd class="col-sm-6">{{ number_format($avgRate, 6) }}</dd>
                                </dl>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
