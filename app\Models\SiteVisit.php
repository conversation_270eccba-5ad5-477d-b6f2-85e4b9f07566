<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class SiteVisit extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'user_id',
        'organization_id',
        'ip_address',
        'user_agent',
        'device_type',
        'browser',
        'platform',
        'country',
        'city',
        'referrer',
        'landing_page',
        'current_page',
        'page_views',
        'first_visit_at',
        'last_activity_at',
        'duration_seconds',
        'is_bounce',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->first_visit_at) {
                $model->first_visit_at = now();
            }
            if (!$model->last_activity_at) {
                $model->last_activity_at = now();
            }
        });
    }

    protected $casts = [
        'first_visit_at' => 'datetime',
        'last_activity_at' => 'datetime',
        'is_bounce' => 'boolean',
    ];

    /**
     * Get the user that made this visit
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the organization associated with this visit
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Scope for visits within date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('first_visit_at', [$startDate, $endDate]);
    }

    /**
     * Scope for today's visits
     */
    public function scopeToday($query)
    {
        return $query->whereDate('first_visit_at', Carbon::today());
    }

    /**
     * Scope for this week's visits
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('first_visit_at', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ]);
    }

    /**
     * Scope for this month's visits
     */
    public function scopeThisMonth($query)
    {
        return $query->whereBetween('first_visit_at', [
            Carbon::now()->startOfMonth(),
            Carbon::now()->endOfMonth()
        ]);
    }

    /**
     * Scope for this year's visits
     */
    public function scopeThisYear($query)
    {
        return $query->whereBetween('first_visit_at', [
            Carbon::now()->startOfYear(),
            Carbon::now()->endOfYear()
        ]);
    }

    /**
     * Scope for online users (active in last 5 minutes)
     */
    public function scopeOnline($query)
    {
        return $query->where('last_activity_at', '>', Carbon::now()->subMinutes(5));
    }

    /**
     * Scope for authenticated users only
     */
    public function scopeAuthenticated($query)
    {
        return $query->whereNotNull('user_id');
    }

    /**
     * Get device type from user agent
     */
    public static function getDeviceType($userAgent): string
    {
        if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
            if (preg_match('/iPad/', $userAgent)) {
                return 'tablet';
            }
            return 'mobile';
        }
        return 'desktop';
    }

    /**
     * Get browser from user agent
     */
    public static function getBrowser($userAgent): string
    {
        if (preg_match('/Edg/', $userAgent)) {
            return 'Edge';
        } elseif (preg_match('/Chrome/', $userAgent)) {
            return 'Chrome';
        } elseif (preg_match('/Firefox/', $userAgent)) {
            return 'Firefox';
        } elseif (preg_match('/Safari/', $userAgent)) {
            return 'Safari';
        } elseif (preg_match('/Opera/', $userAgent)) {
            return 'Opera';
        }
        return 'Other';
    }

    /**
     * Get platform from user agent
     */
    public static function getPlatform($userAgent): string
    {
        if (preg_match('/Windows/', $userAgent)) {
            return 'Windows';
        } elseif (preg_match('/Mac/', $userAgent)) {
            return 'macOS';
        } elseif (preg_match('/Linux/', $userAgent)) {
            return 'Linux';
        } elseif (preg_match('/Android/', $userAgent)) {
            return 'Android';
        } elseif (preg_match('/iOS|iPhone|iPad/', $userAgent)) {
            return 'iOS';
        }
        return 'Other';
    }

    /**
     * Get visit statistics for a given period
     */
    public static function getVisitStats($period = 'today'): array
    {
        $query = static::query();

        switch ($period) {
            case 'today':
                $query->today();
                break;
            case 'week':
                $query->thisWeek();
                break;
            case 'month':
                $query->thisMonth();
                break;
            case 'year':
                $query->thisYear();
                break;
        }

        $totalVisits = $query->count();
        $uniqueVisitors = $query->distinct('ip_address')->count();
        $authenticatedVisits = $query->authenticated()->count();
        $bounceRate = $totalVisits > 0 ? round(($query->where('is_bounce', true)->count() / $totalVisits) * 100, 2) : 0;
        $avgDuration = $query->avg('duration_seconds') ?? 0;

        return [
            'total_visits' => $totalVisits,
            'unique_visitors' => $uniqueVisitors,
            'authenticated_visits' => $authenticatedVisits,
            'bounce_rate' => $bounceRate,
            'avg_duration' => round($avgDuration, 2),
        ];
    }

    /**
     * Get online users statistics
     */
    public static function getOnlineStats(): array
    {
        $onlineVisits = static::online()->get();
        
        $totalOnline = $onlineVisits->count();
        $authenticatedOnline = $onlineVisits->whereNotNull('user_id')->count();
        $organizationsOnline = $onlineVisits->whereNotNull('organization_id')->unique('organization_id')->count();
        
        return [
            'total_online' => $totalOnline,
            'authenticated_online' => $authenticatedOnline,
            'organizations_online' => $organizationsOnline,
            'guest_online' => $totalOnline - $authenticatedOnline,
        ];
    }

    /**
     * Get device breakdown
     */
    public static function getDeviceBreakdown($period = 'month'): array
    {
        $query = static::query();

        switch ($period) {
            case 'today':
                $query->today();
                break;
            case 'week':
                $query->thisWeek();
                break;
            case 'month':
                $query->thisMonth();
                break;
            case 'year':
                $query->thisYear();
                break;
        }

        return $query->selectRaw('device_type, COUNT(*) as count')
            ->groupBy('device_type')
            ->orderByDesc('count')
            ->get()
            ->toArray();
    }

    /**
     * Get browser breakdown
     */
    public static function getBrowserBreakdown($period = 'month'): array
    {
        $query = static::query();

        switch ($period) {
            case 'today':
                $query->today();
                break;
            case 'week':
                $query->thisWeek();
                break;
            case 'month':
                $query->thisMonth();
                break;
            case 'year':
                $query->thisYear();
                break;
        }

        return $query->selectRaw('browser, COUNT(*) as count')
            ->groupBy('browser')
            ->orderByDesc('count')
            ->get()
            ->toArray();
    }
}
