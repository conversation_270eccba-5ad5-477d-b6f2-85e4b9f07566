<?php

namespace App\Services;

use App\Models\User;
use App\Models\SuperAdmin;
use App\Services\LogService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ImpersonationService
{
    const SESSION_KEY = 'impersonation_data';
    const MAX_IMPERSONATION_TIME = 8; // hours

    /**
     * Start impersonating a user
     */
    public function startImpersonation(User $targetUser, SuperAdmin $superAdmin): bool
    {
        try {
            // Validate impersonation is allowed
            if (!$this->canImpersonate($targetUser, $superAdmin)) {
                return false;
            }

            // Store current super admin session data
            $impersonationData = [
                'super_admin_id' => $superAdmin->id,
                'super_admin_name' => $superAdmin->name,
                'super_admin_email' => $superAdmin->email,
                'target_user_id' => $targetUser->id,
                'target_user_name' => $targetUser->name,
                'target_user_email' => $targetUser->email,
                'target_organization_id' => $targetUser->organization_id,
                'started_at' => now(),
                'expires_at' => now()->addHours(self::MAX_IMPERSONATION_TIME),
                'original_ip' => request()->ip(),
                'original_user_agent' => request()->userAgent(),
            ];

            // Store impersonation data in session
            Session::put(self::SESSION_KEY, $impersonationData);

            // Log out super admin and log in as target user
            Auth::guard('super_admin')->logout();
            Auth::login($targetUser);

            // Log the impersonation start
            $this->logImpersonationActivity('started', $targetUser, $superAdmin, [
                'target_user_details' => [
                    'id' => $targetUser->id,
                    'name' => $targetUser->name,
                    'email' => $targetUser->email,
                    'organization_id' => $targetUser->organization_id,
                ],
                'session_expires_at' => $impersonationData['expires_at'],
            ]);

            // Log to centralized system logs
            LogService::impersonation('Impersonation started', [
                'super_admin_id' => $superAdmin->id,
                'super_admin_name' => $superAdmin->name,
                'target_user_id' => $targetUser->id,
                'target_user_name' => $targetUser->name,
                'target_organization_id' => $targetUser->organization_id,
                'session_expires_at' => $impersonationData['expires_at'],
            ]);

            // Log activity for the target user
            $targetUser->logActivity('impersonation_started', [
                'impersonated_by' => $superAdmin->name,
                'impersonated_by_email' => $superAdmin->email,
                'session_expires_at' => $impersonationData['expires_at'],
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to start impersonation', [
                'super_admin_id' => $superAdmin->id,
                'target_user_id' => $targetUser->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Stop impersonation and restore super admin session
     */
    public function stopImpersonation(): bool
    {
        try {
            $impersonationData = Session::get(self::SESSION_KEY);

            if (!$impersonationData) {
                return false;
            }

            $currentUser = Auth::user();
            $superAdmin = SuperAdmin::find($impersonationData['super_admin_id']);

            if (!$superAdmin) {
                Log::error('Super admin not found during impersonation stop', [
                    'super_admin_id' => $impersonationData['super_admin_id'],
                ]);
                return false;
            }

            // Calculate session duration
            $sessionDuration = now()->diffInMinutes(Carbon::parse($impersonationData['started_at']));

            // Log the impersonation end
            $this->logImpersonationActivity('ended', $currentUser, $superAdmin, [
                'session_duration_minutes' => $sessionDuration,
                'ended_by' => 'super_admin', // or 'timeout' if expired
            ]);

            // Log to centralized system logs
            LogService::impersonation('Impersonation ended', [
                'super_admin_id' => $superAdmin->id,
                'super_admin_name' => $superAdmin->name,
                'target_user_id' => $currentUser?->id,
                'target_user_name' => $currentUser?->name,
                'session_duration_minutes' => $sessionDuration,
                'ended_by' => 'super_admin',
            ]);

            // Log activity for the target user
            if ($currentUser) {
                $currentUser->logActivity('impersonation_ended', [
                    'impersonated_by' => $superAdmin->name,
                    'session_duration_minutes' => $sessionDuration,
                ]);
            }

            // Clear impersonation session data
            Session::forget(self::SESSION_KEY);

            // Log out current user and restore super admin
            Auth::logout();
            Auth::guard('super_admin')->login($superAdmin);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to stop impersonation', [
                'error' => $e->getMessage(),
                'session_data' => Session::get(self::SESSION_KEY),
            ]);
            return false;
        }
    }

    /**
     * Check if currently impersonating
     */
    public function isImpersonating(): bool
    {
        return Session::has(self::SESSION_KEY);
    }

    /**
     * Get impersonation data
     */
    public function getImpersonationData(): ?array
    {
        return Session::get(self::SESSION_KEY);
    }

    /**
     * Check if impersonation has expired
     */
    public function hasExpired(): bool
    {
        $data = $this->getImpersonationData();

        if (!$data) {
            return false;
        }

        return now()->isAfter(Carbon::parse($data['expires_at']));
    }

    /**
     * Get remaining impersonation time in minutes
     */
    public function getRemainingTime(): int
    {
        $data = $this->getImpersonationData();

        if (!$data) {
            return 0;
        }

        $expiresAt = Carbon::parse($data['expires_at']);
        return now()->diffInMinutes($expiresAt, false);
    }

    /**
     * Check if super admin can impersonate the target user
     */
    private function canImpersonate(User $targetUser, SuperAdmin $superAdmin): bool
    {
        // Check if target user is active
        if ($targetUser->status !== 'active') {
            Log::warning('Attempted to impersonate inactive user', [
                'super_admin_id' => $superAdmin->id,
                'target_user_id' => $targetUser->id,
                'target_user_status' => $targetUser->status,
            ]);
            return false;
        }

        // Check if target user's organization is active
        if (!$targetUser->organization || !$targetUser->organization->is_active) {
            Log::warning('Attempted to impersonate user from inactive organization', [
                'super_admin_id' => $superAdmin->id,
                'target_user_id' => $targetUser->id,
                'organization_is_active' => $targetUser->organization->is_active ?? 'null',
            ]);
            return false;
        }

        // Additional security checks can be added here
        return true;
    }

    /**
     * Log impersonation activity
     */
    private function logImpersonationActivity(string $action, User $targetUser, SuperAdmin $superAdmin, array $details = []): void
    {
        Log::info("Impersonation {$action}", array_merge([
            'action' => $action,
            'super_admin_id' => $superAdmin->id,
            'super_admin_name' => $superAdmin->name,
            'super_admin_email' => $superAdmin->email,
            'target_user_id' => $targetUser->id,
            'target_user_name' => $targetUser->name,
            'target_user_email' => $targetUser->email,
            'target_organization_id' => $targetUser->organization_id,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now(),
        ], $details));
    }

    /**
     * Force stop impersonation (for timeouts or security)
     */
    public function forceStopImpersonation(string $reason = 'timeout'): bool
    {
        $impersonationData = Session::get(self::SESSION_KEY);

        if (!$impersonationData) {
            return false;
        }

        $currentUser = Auth::user();
        $superAdmin = SuperAdmin::find($impersonationData['super_admin_id']);

        if ($currentUser && $superAdmin) {
            // Log forced stop
            $this->logImpersonationActivity('force_stopped', $currentUser, $superAdmin, [
                'reason' => $reason,
                'session_duration_minutes' => now()->diffInMinutes(Carbon::parse($impersonationData['started_at'])),
            ]);

            $currentUser->logActivity('impersonation_force_stopped', [
                'reason' => $reason,
                'impersonated_by' => $superAdmin->name,
            ]);
        }

        // Clear session and logout
        Session::forget(self::SESSION_KEY);
        Auth::logout();

        // Redirect to super admin login
        return true;
    }
}
