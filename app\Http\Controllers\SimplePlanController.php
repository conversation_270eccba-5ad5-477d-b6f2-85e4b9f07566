<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use App\Models\Organization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SimplePlanController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:Organization Owner|Manager');
    }

    /**
     * Show plan comparison and upgrade options
     */
    public function index()
    {
        $organization = Auth::user()->organization;
        $currentPlan = $organization->plan;
        
        // Get all active plans
        $allPlans = Plan::where('is_active', true)->orderBy('price')->get();
        
        // Filter out current plan from available plans
        $availablePlans = $allPlans->filter(function($plan) use ($currentPlan) {
            return !$currentPlan || $plan->id !== $currentPlan->id;
        });

        $activeSubscription = $organization->activeSubscription;
        
        // Simple plan comparisons without proration service
        $planComparisons = [];
        foreach ($availablePlans as $plan) {
            $planComparisons[] = [
                'plan' => $plan,
                'proration' => [
                    'type' => 'new_subscription',
                    'net_amount' => $plan->price,
                    'proration_details' => 'Plan change - see preview for exact costs'
                ],
                'is_upgrade' => !$currentPlan || $plan->price > $currentPlan->price,
                'is_downgrade' => $currentPlan && $plan->price < $currentPlan->price,
            ];
        }

        return view('plan-change.index', compact(
            'organization',
            'currentPlan',
            'availablePlans',
            'planComparisons',
            'activeSubscription'
        ));
    }

    /**
     * Show plan change preview
     */
    public function preview(Request $request, Plan $plan)
    {
        $organization = Auth::user()->organization;
        $currentPlan = $organization->plan;
        $activeSubscription = $organization->activeSubscription;

        // Simple proration calculation
        $proration = [
            'type' => 'new_subscription',
            'net_amount' => $plan->price,
            'proration_details' => 'Full monthly charge for new plan',
            'current_plan_credit' => 0,
            'new_plan_charge' => $plan->price,
            'remaining_days' => 30
        ];

        $changeType = $request->get('change_type', 'immediate');

        return view('plan-change.preview', compact(
            'organization',
            'currentPlan',
            'plan',
            'proration',
            'activeSubscription',
            'changeType'
        ));
    }

    /**
     * Process plan change (simplified)
     */
    public function change(Request $request, Plan $plan)
    {
        $request->validate([
            'change_type' => 'required|in:immediate,end_of_cycle',
            'confirm' => 'required|accepted',
        ]);

        $organization = Auth::user()->organization;
        
        // Simple plan change - just update the organization's plan
        $organization->update(['plan_id' => $plan->id]);

        return redirect()->route('plan-change.index')
            ->with('success', "Plan changed to {$plan->name} successfully!");
    }
}
