<?php
echo "Testing Sidebar Fix...\n\n";

// Check layout file
$layoutFile = 'resources/views/layouts/app.blade.php';
if (file_exists($layoutFile)) {
    $content = file_get_contents($layoutFile);
    
    echo "✓ Layout file exists\n";
    echo "✓ Has Bootstrap: " . (strpos($content, 'bootstrap') !== false ? "YES" : "NO") . "\n";
    echo "✓ Sidebar without collapse: " . (strpos($content, 'sidebar" id="sidebar"') !== false ? "YES" : "NO") . "\n";
    echo "✓ Mobile button with ID: " . (strpos($content, 'id="mobile-menu-btn"') !== false ? "YES" : "NO") . "\n";
    echo "✓ Custom JavaScript: " . (strpos($content, 'mobile-menu-btn') !== false ? "YES" : "NO") . "\n";
    echo "✓ Desktop CSS: " . (strpos($content, 'min-width: 768px') !== false ? "YES" : "NO") . "\n";
    echo "✓ Mobile CSS: " . (strpos($content, 'max-width: 767.98px') !== false ? "YES" : "NO") . "\n";
} else {
    echo "✗ Layout file not found\n";
}

echo "\n";

// Check sidebar component
$sidebarFile = 'resources/views/components/sidebar.blade.php';
if (file_exists($sidebarFile)) {
    $content = file_get_contents($sidebarFile);
    
    echo "✓ Sidebar component exists\n";
    echo "✓ Has navigation: " . (strpos($content, 'nav flex-column') !== false ? "YES" : "NO") . "\n";
    echo "✓ Has fallback menu: " . (strpos($content, 'Dashboard') !== false ? "YES" : "NO") . "\n";
    echo "✓ Has styling: " . (strpos($content, 'background: linear-gradient') !== false ? "YES" : "NO") . "\n";
    echo "✓ Has debug info: " . (strpos($content, 'Menu items count') !== false ? "YES" : "NO") . "\n";
} else {
    echo "✗ Sidebar component not found\n";
}

echo "\n";

// Check Sidebar component class
$sidebarClass = 'app/View/Components/Sidebar.php';
if (file_exists($sidebarClass)) {
    $content = file_get_contents($sidebarClass);
    
    echo "✓ Sidebar class exists\n";
    echo "✓ Has getMenuItems method: " . (strpos($content, 'getMenuItems') !== false ? "YES" : "NO") . "\n";
    echo "✓ Has menuItems property: " . (strpos($content, 'menuItems') !== false ? "YES" : "NO") . "\n";
} else {
    echo "✗ Sidebar class not found\n";
}

echo "\nSidebar fix test completed!\n";
echo "\nExpected behavior:\n";
echo "- Desktop: Sidebar should be visible on the left side\n";
echo "- Mobile: Sidebar hidden, hamburger button visible, clicking shows sidebar with backdrop\n";
?>
