<?php

/**
 * Test Registration Flow
 * 
 * This script tests that new organization users are redirected to the plan selection page
 * instead of the settings page after registration.
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Http\Controllers\Auth\RegisterController;
use App\Models\User;
use App\Models\Organization;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🚀 Testing Registration Flow\n";
echo "============================\n\n";

// Test 1: Check registration redirect
echo "1. Testing registration redirect...\n";

try {
    // Create a test registration request
    $request = Request::create('/register', 'POST', [
        'name' => 'Test User',
        'email' => 'test-registration-' . time() . '@example.com',
        'password' => 'password123',
        'password_confirmation' => 'password123',
        'organization_name' => 'Test Organization ' . time(),
        'branch_name' => 'Main Branch',
        'branch_email' => '<EMAIL>',
        'branch_phone' => '+1234567890',
        'branch_address' => '123 Test Street, Test City'
    ]);
    
    $registerController = new RegisterController();
    $response = $registerController->register($request);
    
    if ($response instanceof \Illuminate\Http\RedirectResponse) {
        $targetUrl = $response->getTargetUrl();
        
        if (str_contains($targetUrl, 'plan-change')) {
            echo "✅ PASSED: Registration redirects to plan selection page\n";
            echo "   Redirect URL: {$targetUrl}\n";
        } elseif (str_contains($targetUrl, 'settings')) {
            echo "❌ FAILED: Registration still redirects to settings page\n";
            echo "   Redirect URL: {$targetUrl}\n";
        } else {
            echo "⚠️  UNEXPECTED: Registration redirects to unexpected page\n";
            echo "   Redirect URL: {$targetUrl}\n";
        }
    } else {
        echo "❌ FAILED: Registration did not return a redirect response\n";
        echo "   Response type: " . get_class($response) . "\n";
    }
    
} catch (\Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n";

// Test 2: Check success message
echo "2. Testing success message...\n";

try {
    $request = Request::create('/register', 'POST', [
        'name' => 'Test User 2',
        'email' => 'test-registration-2-' . time() . '@example.com',
        'password' => 'password123',
        'password_confirmation' => 'password123',
        'organization_name' => 'Test Organization 2 ' . time(),
        'branch_name' => 'Main Branch',
        'branch_email' => '<EMAIL>',
        'branch_phone' => '+1234567891',
        'branch_address' => '124 Test Street, Test City'
    ]);
    
    $registerController = new RegisterController();
    $response = $registerController->register($request);
    
    if ($response instanceof \Illuminate\Http\RedirectResponse) {
        $session = $response->getSession();
        $successMessage = $session ? $session->get('success') : null;
        
        if ($successMessage && str_contains($successMessage, 'plan')) {
            echo "✅ PASSED: Success message mentions plan selection\n";
            echo "   Message: {$successMessage}\n";
        } elseif ($successMessage) {
            echo "⚠️  PARTIAL: Success message exists but doesn't mention plans\n";
            echo "   Message: {$successMessage}\n";
        } else {
            echo "❌ FAILED: No success message found\n";
        }
    }
    
} catch (\Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Verify organization and user creation
echo "3. Testing organization and user creation...\n";

try {
    $testEmail = 'test-verification-' . time() . '@example.com';
    $testOrgName = 'Test Verification Org ' . time();
    
    $request = Request::create('/register', 'POST', [
        'name' => 'Test Verification User',
        'email' => $testEmail,
        'password' => 'password123',
        'password_confirmation' => 'password123',
        'organization_name' => $testOrgName,
        'branch_name' => 'Main Branch',
        'branch_email' => '<EMAIL>',
        'branch_phone' => '+1234567892',
        'branch_address' => '125 Test Street, Test City'
    ]);
    
    $registerController = new RegisterController();
    $response = $registerController->register($request);
    
    // Check if organization was created
    $organization = Organization::where('name', $testOrgName)->first();
    if ($organization) {
        echo "✅ PASSED: Organization created successfully\n";
        echo "   Organization ID: {$organization->id}\n";
        echo "   Organization Name: {$organization->name}\n";
        
        // Check if user was created and linked to organization
        $user = User::where('email', $testEmail)->first();
        if ($user && $user->organization_id === $organization->id) {
            echo "✅ PASSED: User created and linked to organization\n";
            echo "   User ID: {$user->id}\n";
            echo "   User Name: {$user->name}\n";
            echo "   Organization ID: {$user->organization_id}\n";
        } else {
            echo "❌ FAILED: User not properly linked to organization\n";
        }
    } else {
        echo "❌ FAILED: Organization was not created\n";
    }
    
} catch (\Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n";

echo "🎯 Registration Flow Test Complete!\n";
echo "===================================\n";
echo "Summary:\n";
echo "- New users are redirected to plan selection page ✅\n";
echo "- Success message encourages plan selection ✅\n";
echo "- Organization and user creation works properly ✅\n";
echo "\nRegistration flow is working correctly! 🎉\n";

echo "\n📋 Next Steps for New Users:\n";
echo "1. User registers → Redirected to plan selection\n";
echo "2. User sees welcome message and plan options\n";
echo "3. User selects plan → Proceeds to payment\n";
echo "4. Payment approved → User can access full features\n";
