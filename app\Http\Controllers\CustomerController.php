<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CustomerController extends Controller
{
    public function index()
    {
        $query = Customer::query()
            ->where('organization_id', Auth::user()->organization_id);

        // Filter by branch if user is assigned to a branch
        if (Auth::user()->branch_id) {
            $query->where('branch_id', Auth::user()->branch_id);
        }

        $customers = $query->latest()->paginate(10);
        return view('customers.index', compact('customers'));
    }

    public function create()
    {
        return view('customers.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|max:255',
            'email' => 'required|email|unique:customers,email,NULL,id,organization_id,' . Auth::user()->organization_id,
            'phone' => 'nullable|max:20',
            'address' => 'nullable',
        ]);

        // Add organization and branch IDs
        $validated['organization_id'] = Auth::user()->organization_id;
        $validated['branch_id'] = Auth::user()->branch_id;

        Customer::create($validated);

        return redirect()->route('customers.index')->with('success', 'Customer created successfully.');
    }

    public function edit(Customer $customer)
    {
        // Ensure the customer belongs to the user's organization
        if ($customer->organization_id !== Auth::user()->organization_id) {
            abort(403, 'Unauthorized action.');
        }

        return view('customers.edit', compact('customer'));
    }

    public function update(Request $request, Customer $customer)
    {
        // Ensure the customer belongs to the user's organization
        if ($customer->organization_id !== Auth::user()->organization_id) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'name' => 'required|max:255',
            'email' => 'required|email|unique:customers,email,' . $customer->id . ',id,organization_id,' . Auth::user()->organization_id,
            'phone' => 'nullable|max:20',
            'address' => 'nullable',
        ]);

        $customer->update($validated);

        return redirect()->route('customers.index')->with('success', 'Customer updated successfully.');
    }

    public function destroy(Customer $customer)
    {
        // Ensure the customer belongs to the user's organization
        if ($customer->organization_id !== Auth::user()->organization_id) {
            abort(403, 'Unauthorized action.');
        }

        $customer->delete();

        return redirect()->route('customers.index')->with('success', 'Customer deleted successfully.');
    }
}
