@extends('layouts.app')

@section('title', 'Support Notification Test')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Support Notification System Test</h1>
                <div>
                    <a href="{{ route('super.support.tickets.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Tickets
                    </a>
                </div>
            </div>

            <!-- Test Results -->
            @if(session('test_results'))
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>Test Results:</h5>
                    <pre>{{ session('test_results') }}</pre>
                </div>
            @endif

            <!-- Test Actions -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-envelope me-2"></i>
                                Test Admin Reply Notification
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">
                                This will simulate a super admin replying to a user ticket and sending notifications.
                            </p>
                            <form method="POST" action="{{ route('test.support.admin-reply') }}">
                                @csrf
                                <div class="mb-3">
                                    <label for="ticket_id" class="form-label">Ticket ID</label>
                                    <input type="number" name="ticket_id" id="ticket_id" class="form-control" 
                                           value="1" min="1" required>
                                    <div class="form-text">Enter the ID of an existing ticket to test with</div>
                                </div>
                                <div class="mb-3">
                                    <label for="message" class="form-label">Test Reply Message</label>
                                    <textarea name="message" id="message" class="form-control" rows="3" required>Hello! This is a test reply from our support team. We're testing the notification system to ensure you receive updates when we respond to your tickets.</textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Send Test Admin Reply
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user me-2"></i>
                                Test User Reply Notification
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">
                                This will simulate a user replying to their ticket and notifying admins.
                            </p>
                            <form method="POST" action="{{ route('test.support.user-reply') }}">
                                @csrf
                                <div class="mb-3">
                                    <label for="user_ticket_id" class="form-label">Ticket ID</label>
                                    <input type="number" name="ticket_id" id="user_ticket_id" class="form-control" 
                                           value="1" min="1" required>
                                    <div class="form-text">Enter the ID of an existing ticket to test with</div>
                                </div>
                                <div class="mb-3">
                                    <label for="user_message" class="form-label">Test Reply Message</label>
                                    <textarea name="message" id="user_message" class="form-control" rows="3" required>Thank you for your response! I have a follow-up question about this issue. This is a test message to verify that admin notifications are working properly.</textarea>
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-reply me-2"></i>
                                    Send Test User Reply
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Change Test -->
            <div class="row mt-4">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-check-circle me-2"></i>
                                Test Status Change Notification
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">
                                This will test status change notifications when tickets are resolved.
                            </p>
                            <form method="POST" action="{{ route('test.support.status-change') }}">
                                @csrf
                                <div class="mb-3">
                                    <label for="status_ticket_id" class="form-label">Ticket ID</label>
                                    <input type="number" name="ticket_id" id="status_ticket_id" class="form-control" 
                                           value="1" min="1" required>
                                </div>
                                <div class="mb-3">
                                    <label for="new_status" class="form-label">New Status</label>
                                    <select name="new_status" id="new_status" class="form-control" required>
                                        <option value="in_progress">In Progress</option>
                                        <option value="waiting_customer">Waiting for Customer</option>
                                        <option value="resolved">Resolved</option>
                                        <option value="closed">Closed</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-exchange-alt me-2"></i>
                                    Test Status Change
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-database me-2"></i>
                                Check Notification Results
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">
                                Check the results of notification tests.
                            </p>
                            <div class="d-grid gap-2">
                                <a href="{{ route('test.support.check-logs') }}" class="btn btn-outline-info">
                                    <i class="fas fa-file-alt me-2"></i>
                                    View Email Logs
                                </a>
                                <a href="{{ route('test.support.check-database') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-bell me-2"></i>
                                    Check Database Notifications
                                </a>
                                <form method="POST" action="{{ route('test.support.clear-notifications') }}" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-outline-danger w-100">
                                        <i class="fas fa-trash me-2"></i>
                                        Clear Test Notifications
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-info-circle me-2"></i>
                                How to Test Notifications
                            </h5>
                            <ol>
                                <li><strong>Create a test ticket</strong> - Go to the user support center and create a ticket</li>
                                <li><strong>Test admin reply</strong> - Use the form above to simulate an admin replying to the ticket</li>
                                <li><strong>Check email logs</strong> - Since mail is configured to use 'log' driver, check the Laravel logs for email content</li>
                                <li><strong>Check database notifications</strong> - Verify that notifications are stored in the database</li>
                                <li><strong>Test user reply</strong> - Simulate a user replying to verify admin notifications</li>
                                <li><strong>Test status changes</strong> - Change ticket status to verify status change notifications</li>
                            </ol>
                            <div class="alert alert-info mt-3">
                                <strong>Note:</strong> Email notifications are logged to <code>storage/logs/laravel.log</code> since the mail driver is set to 'log'.
                                In production, configure SMTP settings to send actual emails.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
