@extends('super_admin.layouts.app')

@section('title', 'Affiliate Management')
@section('page-title', 'Affiliate Management')

@section('content')
<!-- Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 mb-1">Affiliate Management</h1>
        <p class="text-muted">Manage affiliate applications, approvals, and performance</p>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <!-- Total Affiliates -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Affiliates
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_affiliates'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-success">
                        <i class="fas fa-check-circle"></i>
                        {{ $stats['active_affiliates'] }} active
                    </small>
                    <span class="text-muted"> • </span>
                    <small class="text-warning">
                        <i class="fas fa-clock"></i>
                        {{ $stats['pending_affiliates'] }} pending
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Referrals -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Total Referrals
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_referrals'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-handshake fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-success">
                        <i class="fas fa-check-circle"></i>
                        {{ $stats['converted_referrals'] }} converted
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Earnings -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Earnings
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ format_price($stats['total_earnings']) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-warning">
                        <i class="fas fa-clock"></i>
                        {{ format_price($stats['pending_earnings']) }} pending
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Approvals -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Pending Approvals
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['pending_affiliates'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('super_admin.affiliates.index') }}">
            <div class="row">
                <div class="col-md-2 mb-3">
                    <label for="status" class="form-label">Status</label>
                    <select id="status" name="status" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                        <option value="suspended" {{ request('status') == 'suspended' ? 'selected' : '' }}>Suspended</option>
                    </select>
                </div>

                <div class="col-md-3 mb-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" id="search" name="search" value="{{ request('search') }}"
                           placeholder="Name or email" class="form-control">
                </div>

                <div class="col-md-2 mb-3">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" id="date_from" name="date_from" value="{{ request('date_from') }}"
                           class="form-control">
                </div>

                <div class="col-md-2 mb-3">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" id="date_to" name="date_to" value="{{ request('date_to') }}"
                           class="form-control">
                </div>

                <div class="col-md-2 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Filter
                        </button>
                    </div>
                </div>

                <div class="col-md-1 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <a href="{{ route('super_admin.affiliates.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Affiliates Table -->
<div class="card shadow mb-4">
    @if($affiliates->count() > 0)
        <!-- Bulk Actions -->
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Affiliate List</h6>
            <form method="POST" action="{{ route('super_admin.affiliates.bulk-action') }}" id="bulk-action-form" class="d-flex align-items-center">
                @csrf
                <div class="form-check me-3">
                    <input type="checkbox" id="select-all" class="form-check-input">
                    <label for="select-all" class="form-check-label">Select All</label>
                </div>

                <select name="action" class="form-select form-select-sm me-2" style="width: auto;">
                    <option value="">Bulk Actions</option>
                    <option value="approve">Approve</option>
                    <option value="suspend">Suspend</option>
                    <option value="reactivate">Reactivate</option>
                </select>

                <button type="submit" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-cogs me-1"></i>Apply
                </button>
            </form>
        </div>

        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th width="30">
                                <input type="checkbox" class="form-check-input">
                            </th>
                            <th>Affiliate</th>
                            <th>Status</th>
                            <th>Commission Rate</th>
                            <th>Referrals</th>
                            <th>Earnings</th>
                            <th>Joined</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($affiliates as $affiliate)
                        <tr>
                            <td>
                                <input type="checkbox" name="affiliate_ids[]" value="{{ $affiliate->id }}" class="affiliate-checkbox form-check-input">
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $affiliate->user->name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $affiliate->user->email }}</small>
                                    <br>
                                    <small class="text-muted">Code: {{ $affiliate->affiliate_code }}</small>
                                </div>
                            </td>
                            <td>
                                @if($affiliate->status === 'active')
                                    <span class="badge bg-success">Active</span>
                                @elseif($affiliate->status === 'pending')
                                    <span class="badge bg-warning">Pending</span>
                                @elseif($affiliate->status === 'suspended')
                                    <span class="badge bg-danger">Suspended</span>
                                @else
                                    <span class="badge bg-secondary">{{ ucfirst($affiliate->status) }}</span>
                                @endif
                            </td>
                            <td>
                                {{ number_format($affiliate->commission_rate, 1) }}%
                            </td>
                            <td>
                                <strong>{{ $affiliate->referrals()->count() }}</strong>
                                <br>
                                <small class="text-muted">{{ $affiliate->converted_referrals_count ?? 0 }} converted</small>
                            </td>
                            <td>
                                <strong>{{ format_price($affiliate->total_earnings ?? 0) }}</strong>
                                <br>
                                <small class="text-success">{{ format_price($affiliate->available_balance ?? 0) }} available</small>
                            </td>
                            <td>
                                {{ $affiliate->created_at->format('M j, Y') }}
                                <br>
                                <small class="text-muted">{{ $affiliate->created_at->format('h:i A') }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('super_admin.affiliates.show', $affiliate) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>

                                    @if($affiliate->status === 'pending')
                                        <form method="POST" action="{{ route('super_admin.affiliates.approve', $affiliate) }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('Approve this affiliate?')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                        <form method="POST" action="{{ route('super_admin.affiliates.reject', $affiliate) }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Reject this affiliate?')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </form>
                                    @endif

                                    @if($affiliate->status === 'active')
                                        <form method="POST" action="{{ route('super_admin.affiliates.suspend', $affiliate) }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-warning" onclick="return confirm('Suspend this affiliate?')">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                        </form>
                                    @endif

                                    @if(in_array($affiliate->status, ['inactive', 'suspended']))
                                        <form method="POST" action="{{ route('super_admin.affiliates.reactivate', $affiliate) }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('Reactivate this affiliate?')">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <div class="card-footer">
            <div class="d-flex justify-content-center">
                {{ $affiliates->links() }}
            </div>
        </div>
    @else
        <div class="card-body">
            <div class="text-center py-5">
                <i class="fas fa-users fa-4x text-gray-300 mb-3"></i>
                <h5 class="text-gray-600">No affiliates found</h5>
                <p class="text-muted">No affiliates match your current filters.</p>
                <a href="{{ route('super_admin.affiliates.index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-refresh me-2"></i>Clear Filters
                </a>
            </div>
        </div>
    @endif
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select-all');
    const affiliateCheckboxes = document.querySelectorAll('.affiliate-checkbox');
    const bulkActionForm = document.getElementById('bulk-action-form');

    if (selectAllCheckbox && affiliateCheckboxes.length > 0) {
        // Handle select all
        selectAllCheckbox.addEventListener('change', function() {
            affiliateCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // Handle individual checkboxes
        affiliateCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const checkedCount = document.querySelectorAll('.affiliate-checkbox:checked').length;
                selectAllCheckbox.checked = checkedCount === affiliateCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < affiliateCheckboxes.length;
            });
        });
    }

    // Handle bulk action form submission
    if (bulkActionForm) {
        bulkActionForm.addEventListener('submit', function(e) {
            const checkedBoxes = document.querySelectorAll('.affiliate-checkbox:checked');
            const action = document.querySelector('select[name="action"]').value;

            if (checkedBoxes.length === 0) {
                e.preventDefault();
                alert('Please select at least one affiliate.');
                return;
            }

            if (!action) {
                e.preventDefault();
                alert('Please select an action.');
                return;
            }

            if (!confirm(`Are you sure you want to ${action} ${checkedBoxes.length} affiliate(s)?`)) {
                e.preventDefault();
            }
        });
    }
});
</script>
@endpush
@endsection
