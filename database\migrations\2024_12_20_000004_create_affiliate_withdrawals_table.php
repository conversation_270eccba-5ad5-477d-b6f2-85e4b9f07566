<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('affiliate_withdrawals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('affiliate_id')->constrained('affiliates')->onDelete('cascade');
            $table->decimal('amount', 10, 2);
            $table->enum('payment_method', ['bank_transfer', 'paypal', 'stripe', 'manual'])->default('bank_transfer');
            $table->json('payment_details'); // Bank info, PayPal email, etc.
            $table->enum('status', ['pending', 'approved', 'rejected', 'paid', 'cancelled'])->default('pending');
            $table->timestamp('requested_at');
            $table->timestamp('processed_at')->nullable();
            $table->foreignId('processed_by')->nullable()->constrained('super_admins')->onDelete('set null');
            $table->text('notes')->nullable(); // Admin notes
            $table->string('transaction_reference')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->decimal('fee_amount', 10, 2)->default(0.00); // Processing fees
            $table->decimal('net_amount', 10, 2); // Amount after fees
            $table->timestamps();

            $table->index(['affiliate_id']);
            $table->index(['status']);
            $table->index(['payment_method']);
            $table->index(['requested_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('affiliate_withdrawals');
    }
};
