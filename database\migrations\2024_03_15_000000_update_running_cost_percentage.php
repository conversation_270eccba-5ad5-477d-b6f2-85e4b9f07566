<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Order;

return new class extends Migration
{
    public function up()
    {
        // Update all existing orders with new 7.5% running cost
        $orders = Order::all();
        foreach ($orders as $order) {
            // Calculate running cost at 7.5%
            $runningCost = $order->total_amount * 0.075;
            $balanceAfterRunningCost = $order->total_amount - $runningCost;
            
            // Recalculate commissions based on new balance
            $salesCommission = ($order->sales_team !== 'None') ? $balanceAfterRunningCost * 0.10 : 0;
            $operatorsCommission = $balanceAfterRunningCost * 0.05;
            
            // Update production amount
            $productionAmount = $balanceAfterRunningCost - ($salesCommission + $operatorsCommission);
            
            // Update order
            $order->running_cost = round($runningCost, 2);
            $order->sales_commission = round($salesCommission, 2);
            $order->operators_commission = round($operatorsCommission, 2);
            $order->production_amount = round($productionAmount, 2);
            $order->save();
        }
    }

    public function down()
    {
        // If needed, we could revert to 15% but it's probably better to leave it as is
        // since this is a business rule change
    }
};