document.addEventListener('DOMContentLoaded', function() {
    // Add interactive features to the role permissions guide
    
    // Highlight role cards on hover
    const roleCards = document.querySelectorAll('[class*="border-2"][class*="rounded-lg"]');
    roleCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('transform', 'scale-105', 'shadow-lg');
            this.style.transition = 'all 0.2s ease-in-out';
        });
        
        card.addEventListener('mouseleave', function() {
            this.classList.remove('transform', 'scale-105', 'shadow-lg');
        });
    });

    // Add tooltips to permission matrix
    const permissionCells = document.querySelectorAll('table tbody td');
    permissionCells.forEach(cell => {
        const checkmark = cell.querySelector('.text-green-600');
        const cross = cell.querySelector('.text-red-600');
        
        if (checkmark) {
            cell.title = 'This role has access to this feature';
            cell.style.cursor = 'help';
        } else if (cross) {
            cell.title = 'This role does not have access to this feature';
            cell.style.cursor = 'help';
        }
    });

    // Add collapsible functionality to sections
    const toggleButtons = document.querySelectorAll('[data-toggle]');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const target = document.querySelector(this.dataset.toggle);
            if (target) {
                target.classList.toggle('hidden');
                const icon = this.querySelector('i');
                if (icon) {
                    icon.classList.toggle('fa-chevron-down');
                    icon.classList.toggle('fa-chevron-up');
                }
            }
        });
    });

    // Role comparison feature
    let selectedRoles = [];
    const roleHeaders = document.querySelectorAll('table thead th:not(:first-child)');
    
    roleHeaders.forEach((header, index) => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            const roleIndex = index + 1; // +1 because first column is feature names
            
            if (selectedRoles.includes(roleIndex)) {
                // Deselect role
                selectedRoles = selectedRoles.filter(r => r !== roleIndex);
                this.classList.remove('bg-blue-200');
                
                // Remove highlighting from column
                const cells = document.querySelectorAll(`table tbody tr td:nth-child(${roleIndex + 1})`);
                cells.forEach(cell => cell.classList.remove('bg-blue-100'));
            } else {
                // Select role
                selectedRoles.push(roleIndex);
                this.classList.add('bg-blue-200');
                
                // Highlight column
                const cells = document.querySelectorAll(`table tbody tr td:nth-child(${roleIndex + 1})`);
                cells.forEach(cell => cell.classList.add('bg-blue-100'));
            }
        });
    });
});

// Helper function to filter table by role
function filterByRole(roleName) {
    const table = document.querySelector('table');
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let hasAccess = false;
        
        cells.forEach((cell, index) => {
            if (index > 0) { // Skip first column (feature names)
                const hasPermission = cell.querySelector('.text-green-600');
                if (hasPermission && cell.textContent.includes(roleName)) {
                    hasAccess = true;
                }
            }
        });
        
        if (roleName === 'all' || hasAccess) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Export for global use
window.RolePermissionsGuide = {
    filterByRole: filterByRole
};