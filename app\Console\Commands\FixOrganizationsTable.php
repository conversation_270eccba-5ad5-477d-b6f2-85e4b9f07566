<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Schema\Blueprint;

class FixOrganizationsTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:organizations-table';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix the organizations table by adding missing columns';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking organizations table structure...');

        try {
            // Check if is_active column exists
            if (!Schema::hasColumn('organizations', 'is_active')) {
                $this->info('Adding is_active column...');
                Schema::table('organizations', function (Blueprint $table) {
                    $table->boolean('is_active')->default(true)->after('name');
                });
                $this->info('✓ is_active column added');
            } else {
                $this->info('✓ is_active column already exists');
            }

            // Check if trial_ends_at column exists
            if (!Schema::hasColumn('organizations', 'trial_ends_at')) {
                $this->info('Adding trial_ends_at column...');
                Schema::table('organizations', function (Blueprint $table) {
                    $table->dateTime('trial_ends_at')->nullable()->after('is_active');
                });
                $this->info('✓ trial_ends_at column added');
            } else {
                $this->info('✓ trial_ends_at column already exists');
            }

            // Check if plan_id column exists
            if (!Schema::hasColumn('organizations', 'plan_id')) {
                $this->info('Adding plan_id column...');
                Schema::table('organizations', function (Blueprint $table) {
                    $table->foreignId('plan_id')->nullable()->after('id')->constrained()->onDelete('set null');
                });
                $this->info('✓ plan_id column added');
            } else {
                $this->info('✓ plan_id column already exists');
            }

            // Update existing organizations to be active
            $updated = DB::table('organizations')
                ->whereNull('is_active')
                ->update(['is_active' => true]);
            
            if ($updated > 0) {
                $this->info("✓ Updated {$updated} organizations to be active");
            }

            $this->info('');
            $this->info('✅ Organizations table structure fixed successfully!');
            $this->info('You can now access the super admin dashboard.');

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Error: ' . $e->getMessage());
            $this->error('Please check your database connection and try again.');
            return 1;
        }
    }
}
