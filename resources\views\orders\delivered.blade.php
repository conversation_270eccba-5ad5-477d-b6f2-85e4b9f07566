@extends('layouts.app')

@section('title', 'Delivered Orders')

@section('content')
<div class="container mx-auto px-4">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Delivered Orders</h2>
        <div class="flex items-center space-x-4">
            <span class="text-gray-600">
                Total Delivered: {{ $deliveredOrders->total() }}
            </span>
        </div>
    </div>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Info</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Details</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job Details</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery Info</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse ($deliveredOrders as $order)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900">{{ $order->order_number }}</div>
                            <div class="text-sm text-gray-500">
                                Registered: {{ $order->created_at->format('M d, Y') }}
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm font-medium text-gray-900">{{ $order->customer_name }}</div>
                            <div class="text-sm text-gray-500">{{ $order->phone_number }}</div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900">
                                <span class="font-medium">Dept:</span> {{ $order->department }}
                            </div>
                            <div class="text-sm text-gray-500">
                                {{ $order->quantity }} pcs | {{ $order->size }}
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm">
                                <div class="font-medium text-gray-900">
                                    {{ $order->receiver_name }}
                                </div>
                                <div class="text-gray-500">{{ $order->receiver_phone }}</div>
                                <div class="text-gray-500 text-xs">
                                    Delivered: {{ $order->date_delivered->format('M d, Y g:i A') }}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm">
                                <div class="font-medium text-gray-900">
                                    ₦{{ number_format($order->total_amount, 2) }}
                                </div>
                                @if($order->pending_payment > 0)
                                    <div class="text-red-600 text-xs">
                                        Balance: ₦{{ number_format($order->pending_payment, 2) }}
                                        <button onclick="showPaymentModal('{{ $order->id }}', {{ $order->pending_payment }})"
                                            class="ml-2 text-blue-600 hover:text-blue-800">
                                            Update Payment
                                        </button>
                                    </div>
                                @else
                                    <div class="text-green-600 text-xs">Fully Paid</div>
                                @endif
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <a href="{{ route('orders.show', $order) }}" 
                                class="text-blue-600 hover:text-blue-900 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                                View Details
                            </a>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            No delivered orders found
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <div class="mt-4">
        {{ $deliveredOrders->links() }}
    </div>
</div>

@if(session('success'))
    <div class="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded shadow-lg" id="notification">
        {{ session('success') }}
    </div>
    <script>
        setTimeout(() => {
            document.getElementById('notification').style.display = 'none';
        }, 3000);
    </script>
@endif

<!-- Payment Modal -->
<div id="paymentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Update Payment</h3>
            <form id="paymentForm" method="POST" action="">
                @csrf
                @method('PATCH')
                
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="additional_payment">
                        Payment Amount
                    </label>
                    <div class="flex items-center">
                        <span class="text-gray-500 mr-2">₦</span>
                        <input type="number" 
                            name="additional_payment" 
                            id="additional_payment" 
                            step="0.01"
                            required
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            min="0">
                    </div>
                    <p class="text-sm text-gray-500 mt-1">
                        Remaining Balance: ₦<span id="remainingBalance"></span>
                    </p>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closePaymentModal()"
                        class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                        Cancel
                    </button>
                    <button type="submit"
                        class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        Update Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
let currentPendingPayment = 0;

function showPaymentModal(orderId, pendingPayment) {
    const modal = document.getElementById('paymentModal');
    const form = document.getElementById('paymentForm');
    const input = document.getElementById('additional_payment');
    const remainingBalance = document.getElementById('remainingBalance');
    
    currentPendingPayment = pendingPayment;
    form.action = `{{ url('/orders') }}/${orderId}/update-payment`;
    input.max = pendingPayment;
    input.value = '';
    remainingBalance.textContent = pendingPayment.toLocaleString('en-NG', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
    
    modal.classList.remove('hidden');

    // Add input event listener for real-time remaining balance update
    input.addEventListener('input', function() {
        const payment = parseFloat(this.value) || 0;
        const remaining = Math.max(0, currentPendingPayment - payment);
        remainingBalance.textContent = remaining.toLocaleString('en-NG', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    });
}

function closePaymentModal() {
    const modal = document.getElementById('paymentModal');
    modal.classList.add('hidden');
}
</script>
@endpush
@endsection 