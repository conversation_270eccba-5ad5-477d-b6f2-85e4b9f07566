<?php

namespace App\Console\Commands;

use App\Models\Affiliate;
use App\Models\AffiliateWithdrawal;
use Illuminate\Console\Command;

class FixAffiliateWithdrawnAmounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'affiliate:fix-withdrawn-amounts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix affiliate withdrawn amounts based on paid withdrawals';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to fix affiliate withdrawn amounts...');

        $affiliates = Affiliate::all();
        $fixedCount = 0;

        foreach ($affiliates as $affiliate) {
            // Calculate total paid withdrawals for this affiliate
            $totalPaidWithdrawals = AffiliateWithdrawal::where('affiliate_id', $affiliate->id)
                ->where('status', AffiliateWithdrawal::STATUS_PAID)
                ->sum('amount');

            // Check if the affiliate's withdrawn_amount matches
            if ($affiliate->withdrawn_amount != $totalPaidWithdrawals) {
                $oldAmount = $affiliate->withdrawn_amount;
                $affiliate->withdrawn_amount = $totalPaidWithdrawals;
                $affiliate->save();

                $this->info("Fixed affiliate {$affiliate->id} ({$affiliate->user->name}): {$oldAmount} -> {$totalPaidWithdrawals}");
                $fixedCount++;
            }
        }

        $this->info("Fixed {$fixedCount} affiliate records.");
        $this->info('Command completed successfully!');

        return 0;
    }
}
