<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;

class CheckUserRoles extends Command
{
    protected $signature = 'user:check-roles {email? : The email of the user to check}';
    protected $description = 'Check the roles assigned to a user';

    public function handle()
    {
        $email = $this->argument('email');
        
        if (!$email) {
            $users = User::with('roles')->get();
            $this->info('Checking all users and their roles:');
            
            foreach ($users as $user) {
                $this->info("\nUser: {$user->email}");
                $this->info('Roles: ' . $user->roles->pluck('name')->join(', '));
            }
            return;
        }

        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User with email {$email} not found!");
            return;
        }

        $this->info("Roles for user {$user->email}:");
        $roles = $user->roles()->pluck('name');
        
        if ($roles->isEmpty()) {
            $this->warn('This user has no roles assigned!');
        } else {
            $this->info($roles->join(', '));
        }
    }
}