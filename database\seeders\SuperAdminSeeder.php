<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SuperAdmin;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default super admin
        SuperAdmin::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('password'), // Change this in production!
                'role' => 'super_admin',
                'is_active' => true,
            ]
        );

        // Create support user
        SuperAdmin::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Support Team',
                'password' => Hash::make('password'), // Change this in production!
                'role' => 'support',
                'is_active' => true,
            ]
        );
    }
}
