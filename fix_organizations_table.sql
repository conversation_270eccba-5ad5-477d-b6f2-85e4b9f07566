-- Fix organizations table by adding missing columns
-- Run this SQL script in your MySQL database

-- Check if is_active column exists, if not add it
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'organizations'
    AND COLUMN_NAME = 'is_active'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE organizations ADD COLUMN is_active BOOLEAN DEFAULT TRUE AFTER name',
    'SELECT "is_active column already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if trial_ends_at column exists, if not add it
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'organizations'
    AND COLUMN_NAME = 'trial_ends_at'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE organizations ADD COLUMN trial_ends_at DATETIME NULL AFTER is_active',
    'SELECT "trial_ends_at column already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if plan_id column exists, if not add it
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'organizations'
    AND COLUMN_NAME = 'plan_id'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE organizations ADD COLUMN plan_id BIGINT UNSIGNED NULL AFTER id',
    'SELECT "plan_id column already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add foreign key constraint for plan_id if it doesn't exist
SET @constraint_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'organizations'
    AND CONSTRAINT_NAME = 'organizations_plan_id_foreign'
);

SET @sql = IF(@constraint_exists = 0,
    'ALTER TABLE organizations ADD CONSTRAINT organizations_plan_id_foreign FOREIGN KEY (plan_id) REFERENCES plans(id) ON DELETE SET NULL',
    'SELECT "plan_id foreign key already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing organizations to be active by default
UPDATE organizations SET is_active = TRUE WHERE is_active IS NULL;

SELECT 'Organizations table structure fixed successfully!' as result;
