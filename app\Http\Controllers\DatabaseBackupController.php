<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class DatabaseBackupController extends Controller
{
    public function __construct()
    {
        $this->middleware('role:Organization Owner');
    }

    public function backup()
    {
        try {
            // Create backup filename with timestamp
            $filename = 'backup_' . Carbon::now()->format('Y-m-d_H-i-s') . '.sql';
            $backupPath = storage_path('app/backups');
            
            // Create backups directory if it doesn't exist
            if (!Storage::exists('backups')) {
                Storage::makeDirectory('backups');
                if (!file_exists($backupPath)) {
                    mkdir($backupPath, 0755, true);
                }
            }
            
            // Get database configuration
            $host = config('database.connections.mysql.host');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');
            $database = config('database.connections.mysql.database');
            
            // Full path for the backup file
            $fullPath = $backupPath . DIRECTORY_SEPARATOR . $filename;
            
            // Build mysqldump command for Windows
            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                $mysqldump = 'C:\\xampp\\mysql\\bin\\mysqldump.exe';  // Adjust this path according to your XAMPP installation
                $command = sprintf(
                    '"%s" --host=%s --user=%s --password=%s %s --result-file="%s" --routines --triggers --add-drop-table --comments',
                    $mysqldump,
                    escapeshellarg($host),
                    escapeshellarg($username),
                    escapeshellarg($password),
                    escapeshellarg($database),
                    str_replace('/', '\\', $fullPath)
                );
            } else {
                // Command for Linux/Mac
                $command = sprintf(
                    'mysqldump --host=%s --user=%s --password=%s %s --routines --triggers --add-drop-table --comments > %s',
                    escapeshellarg($host),
                    escapeshellarg($username),
                    escapeshellarg($password),
                    escapeshellarg($database),
                    escapeshellarg($fullPath)
                );
            }
            
            // Execute backup command
            $output = [];
            $returnVar = 0;
            exec($command . ' 2>&1', $output, $returnVar);
            
            if ($returnVar !== 0) {
                \Log::error('Database backup failed: ' . implode("\n", $output));
                throw new \Exception('Database backup failed. Error: ' . implode("\n", $output));
            }
            
            if (!file_exists($fullPath)) {
                throw new \Exception('Backup file was not created');
            }

            // Return file for immediate download
            return response()->download($fullPath, $filename, [
                'Content-Type' => 'application/sql',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"'
            ])->deleteFileAfterSend(false);
            
        } catch (\Exception $e) {
            \Log::error('Backup error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to create backup: ' . $e->getMessage()
            ], 500);
        }
    }

    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        return round($bytes / pow(1024, $pow), $precision) . ' ' . $units[$pow];
    }

    public function index()
    {
        $backups = collect(Storage::files('backups'))
            ->filter(function($file) {
                return pathinfo($file, PATHINFO_EXTENSION) === 'sql';
            })
            ->map(function($file) {
                return [
                    'filename' => basename($file),
                    'size' => Storage::size($file),
                    'created_at' => Carbon::createFromTimestamp(Storage::lastModified($file)),
                    'path' => $file
                ];
            })
            ->sortByDesc('created_at')
            ->values();

        return view('admin.backups.index', compact('backups'));
    }

    public function download($filename)
    {
        try {
            $path = "backups/{$filename}";
            
            if (!Storage::exists($path)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Backup file not found'
                ], 404);
            }

            return Storage::download($path);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to download backup: ' . $e->getMessage()
            ], 500);
        }
    }

    public function delete($filename)
    {
        try {
            $path = "backups/{$filename}";
            
            if (!Storage::exists($path)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Backup file not found'
                ], 404);
            }

            Storage::delete($path);

            return response()->json([
                'success' => true,
                'message' => 'Backup deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete backup: ' . $e->getMessage()
            ], 500);
        }
    }
}