@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom form styles */
.form-checkbox {
    @apply rounded border-gray-300 text-primary shadow-sm focus:ring-primary focus:ring-opacity-50;
}

.form-input {
    @apply border-gray-300 focus:border-primary focus:ring-primary rounded-md shadow-sm;
}

/* Primary color variants */
.bg-primary-100 {
    background-color: color-mix(in srgb, var(--primary-color) 10%, white);
}

.text-primary-800 {
    color: color-mix(in srgb, var(--primary-color) 80%, black);
}

/* Offline mode styles */
.offline {
    .sidebar-icon {
        @apply w-5 h-5;
    }
    .sidebar {
        @apply w-64;
    }
}

/* Modal backdrop color */
.modal-backdrop {
    background-color: color-mix(in srgb, var(--primary-color) 60%, black);
}
