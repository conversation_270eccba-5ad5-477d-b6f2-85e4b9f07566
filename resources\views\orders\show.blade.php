@extends('layouts.app')

@section('title', 'Order Details')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- Order Status Progress -->
        <div class="bg-gray-50 px-6 py-4">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900">Order #{{ $order->order_number }}</h2>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-500">Created {{ $order->created_at->diffForHumans() }}</span>
                    @if($order->status_updated_at)
                        <span class="text-sm text-gray-500">• Last updated {{ $order->status_updated_at->diffForHumans() }}</span>
                    @endif

                    <!-- Receipt Buttons -->
                    <div class="flex space-x-2">
                        <a href="{{ route('orders.receipt', ['order' => $order, 'format' => 'pdf']) }}"
                           class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                           target="_blank">
                            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                            PDF Receipt
                        </a>
                        <form action="{{ route('orders.print-thermal', $order) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit"
                                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                </svg>
                                Print Receipt
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Status Steps -->
            <div class="flex items-center justify-between">
                @php
                    $statuses = ['Pending', 'Processing', 'Completed', 'Delivered'];
                    $currentStep = array_search($order->status, $statuses);
                @endphp

                @foreach($statuses as $index => $status)
                    <div class="flex-1 {{ !$loop->last ? 'relative' : '' }}">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center
                                    {{ $index <= $currentStep ?
                                        ($status === $order->status ? 'bg-blue-600' : 'bg-green-600') :
                                        'bg-gray-200' }}
                                    transition-colors duration-200">
                                    @if($index < $currentStep)
                                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                    @elseif($index === $currentStep)
                                        <span class="text-white font-medium text-sm">•</span>
                                    @else
                                        <span class="text-gray-500 font-medium text-sm">{{ $index + 1 }}</span>
                                    @endif
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium {{ $index <= $currentStep ? 'text-gray-900' : 'text-gray-500' }}">
                                    {{ $status }}
                                </p>
                                @if($status === $order->status)
                                    <p class="text-xs text-gray-500 mt-0.5">Current status</p>
                                @endif
                            </div>
                        </div>
                        @if(!$loop->last)
                            <div class="hidden md:block absolute top-4 left-0 w-full">
                                <div class="h-0.5 {{ $index < $currentStep ? 'bg-green-600' : 'bg-gray-200' }} w-full"></div>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>

        <div class="mb-4">
            <h2 class="text-2xl font-bold">Order Details</h2>
        </div>

        <!-- Order Information -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Order Information
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Created by {{ $order->user ? $order->user->name : 'System' }}
                </p>
            </div>
            <div class="border-t border-gray-200">
                <dl>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Order Number</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $order->order_number }}</dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Customer Name</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $order->customer_name }}</dd>
                    </div>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Phone Number</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $order->phone_number }}</dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Order Description</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $order->job_description }}</dd>
                    </div>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Department</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $order->department }}</dd>
                    </div>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Media</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $order->media }}</dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Pages</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $order->pages }}</dd>
                    </div>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Size</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $order->size }}</dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Quantity</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $order->quantity }}</dd>
                    </div>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Unit Cost</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ format_money($order->unit_cost) }}</dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Total Amount</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ format_money($order->total_amount) }}</dd>
                    </div>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Amount Paid</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ format_money($order->amount_paid) }}</dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Pending Payment</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ format_money($order->pending_payment) }}</dd>
                    </div>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Financial Details</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <p>Total Amount: {{ format_money($order->total_amount) }}</p>
                            <p>Amount Paid: {{ format_money($order->amount_paid) }}</p>
                            <p>Pending Payment: {{ format_money($order->pending_payment) }}</p>
                        </dd>
                    </div>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Created By</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            @if($order->user)
                                {{ $order->user->name }} ({{ $order->user->roles->pluck('name')->join(', ') }})
                            @else
                                System
                            @endif
                        </dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Creation Date</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {{ $order->created_at->format('M d, Y h:i A') }}
                        </dd>
                    </div>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Expected Delivery Date</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $order->expected_delivery_date->format('Y-m-d') }}</dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Expected Delivery Time</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $order->expected_delivery_time }}</dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Order Status History -->
        <div class="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Order Status History
                </h3>
            </div>
            <div class="border-t border-gray-200">
                <div class="flow-root p-6">
                    <ul role="list" class="-mb-8">
                        @foreach($order->statusHistory()->latest()->get() as $history)
                            <li>
                                <div class="relative pb-8">
                                    @unless($loop->last)
                                        <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                    @endunless
                                    <div class="relative flex space-x-3">
                                        <div>
                                            <span class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white
                                                @if($history->new_status === 'Delivered')
                                                    bg-green-500
                                                @elseif($history->new_status === 'Completed')
                                                    bg-purple-500
                                                @elseif($history->new_status === 'Processing')
                                                    bg-blue-500
                                                @else
                                                    bg-yellow-500
                                                @endif">
                                                <svg class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm1 11.414l4.707-4.707-1.414-1.414L11 10.586V6H9v4.586L5.707 7.293 4.293 8.707 9 13.414z" clip-rule="evenodd"/>
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="min-w-0 flex-1 flex justify-between space-x-4">
                                            <div>
                                                <p class="text-sm text-gray-500">
                                                    Status changed from
                                                    <span class="font-medium text-gray-900">
                                                        {{ $history->old_status ?: 'Created' }}
                                                    </span>
                                                    to
                                                    <span class="font-medium text-gray-900">
                                                        {{ $history->new_status }}
                                                    </span>
                                                </p>
                                                @if($history->notes)
                                                    <p class="mt-0.5 text-sm text-gray-500">{{ $history->notes }}</p>
                                                @endif
                                            </div>
                                            <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                <div>{{ $history->created_at->format('M d, Y') }}</div>
                                                <div class="text-xs">{{ $history->created_at->format('h:i A') }}</div>
                                                <div class="text-xs text-gray-400">by {{ $history->changed_by }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>

        <!-- Status Update Section -->
        @if(auth()->user()->hasAnyRole(['Organization Owner', 'Operator', 'Production']) && $order->isStatusChangeable())
            <div class="mt-8 bg-gray-50 p-4 rounded-md border">
                <h4 class="text-lg font-medium mb-4">Update Order Status</h4>
                <div class="flex space-x-4">
                    @if($order->status === 'Pending')
                        <form action="{{ route('orders.updateStatus', $order) }}" method="POST">
                            @csrf
                            @method('PATCH')
                            <input type="hidden" name="status" value="Processing">
                            <button type="submit"
                                onclick="return confirm('Start processing this order?')"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                Start Processing
                            </button>
                        </form>
                    @endif

                    @if($order->status === 'Processing')
                        <form action="{{ route('orders.updateStatus', $order) }}" method="POST">
                            @csrf
                            @method('PATCH')
                            <input type="hidden" name="status" value="Completed">
                            <button type="submit"
                                onclick="return confirm('Mark this order as completed?')"
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Mark as Completed
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        @endif

        <!-- Payment Update Section for Account Users -->
        @if($order->pending_payment > 0 && auth()->user()->hasAnyRole(['Organization Owner', 'Account']))
            <div class="mt-8 bg-white p-4 rounded-md border">
                <h4 class="text-lg font-medium mb-4">Update Payment</h4>
                <form action="{{ route('orders.updatePayment', $order) }}" method="POST" class="space-y-4">
                    @csrf
                    @method('PATCH')
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                        <div>
                            <label for="additional_payment" class="block text-sm font-medium text-gray-700">
                                Additional Payment Amount ({{ currency_code() }})
                            </label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm">{{ currency_symbol() }}</span>
                                </div>
                                <input type="number"
                                    name="additional_payment"
                                    id="additional_payment"
                                    step="0.01"
                                    max="{{ $order->pending_payment }}"
                                    required
                                    class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md"
                                    placeholder="0.00">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm">{{ currency_code() }}</span>
                                </div>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">
                                Maximum allowed: {{ format_money($order->pending_payment) }}
                            </p>
                        </div>
                        <div class="flex items-end">
                            <button type="submit"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md inline-flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                Update Payment
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        @endif

        <!-- Delivery Update Section -->
        @if($order->status === 'Completed' && auth()->user()->hasAnyRole(['Organization Owner', 'Delivery']))
            <div class="mt-8 bg-gray-50 p-4 rounded-md border">
                <h4 class="text-lg font-medium mb-4">Update Delivery Status</h4>
                @if($order->pending_payment > 0)
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-yellow-700">
                                    Cannot mark as delivered. This order has a pending payment of {{ format_money($order->pending_payment) }}.
                                </p>
                                <p class="mt-1 text-xs text-yellow-600">
                                    Please ensure all payments are completed before marking the order as delivered.
                                </p>
                            </div>
                        </div>
                    </div>
                @else
                    <form action="{{ route('orders.updateDelivery', $order) }}" method="POST">
                        @csrf
                        @method('PATCH')
                        <div class="flex items-center space-x-4">
                            <div>
                                <label for="receiver_name" class="block text-sm font-medium text-gray-700">Receiver Name</label>
                                <input type="text" name="receiver_name" id="receiver_name" required
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            </div>
                            <div>
                                <label for="receiver_phone" class="block text-sm font-medium text-gray-700">Receiver Phone</label>
                                <input type="text" name="receiver_phone" id="receiver_phone" required
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            </div>
                            <div class="self-end">
                                <button type="submit"
                                    class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Mark as Delivered
                                </button>
                            </div>
                        </div>
                    </form>
                @endif
            </div>
        @endif

        @if($order->status === 'Delivered')
        <div class="bg-white shadow overflow-hidden sm:rounded-lg mt-6">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Delivery Information
                </h3>
            </div>
            <div class="border-t border-gray-200">
                <dl>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Delivery Date & Time</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {{ $order->date_delivered ? $order->date_delivered->format('M d, Y h:i A') : 'N/A' }}
                        </dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Receiver's Name</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {{ $order->receiver_name }}
                        </dd>
                    </div>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Receiver's Phone</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            {{ $order->receiver_phone }}
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
        @endif

        <x-notification />
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.8.2/dist/alpine.min.js" defer></script>
<script>
let currentPendingPayment = 0;

function showPaymentModal(orderId, pendingPayment) {
    const modal = document.getElementById('paymentModal');
    const form = document.getElementById('paymentForm');
    const input = document.getElementById('additional_payment');
    const remainingBalance = document.getElementById('remainingBalance');

    currentPendingPayment = pendingPayment;
    form.action = `{{ url('/orders') }}/${orderId}/update-payment`;
    input.max = pendingPayment;
    input.value = '';
    remainingBalance.textContent = pendingPayment.toLocaleString('en-NG', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });

    modal.classList.remove('hidden');

    // Add input event listener for real-time remaining balance update
    input.addEventListener('input', function() {
        const payment = parseFloat(this.value) || 0;
        const remaining = Math.max(0, currentPendingPayment - payment);
        remainingBalance.textContent = remaining.toLocaleString('en-NG', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    });
}

function closePaymentModal() {
    const modal = document.getElementById('paymentModal');
    modal.classList.add('hidden');
}
</script>
@endpush
@endsection




