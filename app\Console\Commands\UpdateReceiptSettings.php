<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Setting;

class UpdateReceiptSettings extends Command
{
    protected $signature = 'receipt:settings {--view : Display current settings} {--update : Update settings}';
    protected $description = 'View or update receipt settings';

    public function handle()
    {
        $setting = Setting::first();

        if (!$setting) {
            $this->info('No settings found. Creating default settings...');
            $setting = new Setting();
            $setting->save();
            $this->info('Default settings created.');
        }

        if ($this->option('view')) {
            $this->info('Current Receipt Settings:');
            $this->info('----------------------');
            $this->info('Company Address: ' . ($setting->company_address ?? 'Not set'));
            $this->info('Company Phone: ' . ($setting->company_phone ?? 'Not set'));
            $this->info('Company Email: ' . ($setting->company_email ?? 'Not set'));
            return;
        }

        if ($this->option('update')) {
            $this->info('Updating Receipt Settings:');
            $this->info('----------------------');

            $address = $this->ask('Enter company address (Use \n for new lines):');
            $phone = $this->ask('Enter company phone:');
            $email = $this->ask('Enter company email:');

            $setting->company_address = $address;
            $setting->company_phone = $phone;
            $setting->company_email = $email;

            $setting->save();

            $this->info('Settings updated successfully!');
            return;
        }

        $this->info('Use --view to display current settings or --update to change them.');
    }
}
