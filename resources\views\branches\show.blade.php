@extends('layouts.app')

@section('content')
<div class="container-fluid px-4">
    <h1 class="mt-4">Branch Details</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{{ route('branches.index') }}">Branches</a></li>
        <li class="breadcrumb-item active">{{ $branch->name }}</li>
    </ol>

    <div class="row">
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-code-branch me-1"></i>
                    Branch Information
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>Name:</th>
                            <td>{{ $branch->name }}</td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td>{{ $branch->email ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Phone:</th>
                            <td>{{ $branch->phone ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Address:</th>
                            <td>{{ $branch->address ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Description:</th>
                            <td>{{ $branch->description ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Current Branch:</th>
                            <td>
                                @if(auth()->user()->branch_id == $branch->id)
                                    <span class="badge bg-success">Yes</span>
                                @else
                                    <span class="badge bg-secondary">No</span>
                                    <form action="{{ route('branches.switch') }}" method="POST" class="d-inline ms-2">
                                        @csrf
                                        <input type="hidden" name="branch_id" value="{{ $branch->id }}">
                                        <button type="submit" class="btn btn-success btn-sm">
                                            <i class="fas fa-exchange-alt"></i> Switch to this branch
                                        </button>
                                    </form>
                                @endif
                            </td>
                        </tr>
                    </table>

                    <div class="mt-3">
                        <a href="{{ route('branches.edit', $branch) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit Branch
                        </a>
                        <a href="{{ route('branches.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Branches
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-users me-1"></i>
                    Branch Users ({{ $branchUsers->count() }})
                </div>
                <div class="card-body">
                    @if($branchUsers->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($branchUsers as $user)
                                    <tr>
                                        <td>{{ $user->name }}</td>
                                        <td>{{ $user->email }}</td>
                                        <td>
                                            @foreach($user->roles as $role)
                                                <span class="badge bg-info">{{ $role->name }}</span>
                                            @endforeach
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            No users are assigned to this branch.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
