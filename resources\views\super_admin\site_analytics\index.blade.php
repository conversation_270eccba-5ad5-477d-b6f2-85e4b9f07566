@extends('super_admin.layouts.app')

@section('title', 'Site Analytics')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line me-2"></i>
            Site Analytics & Traffic
        </h1>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-sm btn-outline-primary {{ $period === 'today' ? 'active' : '' }}" onclick="changePeriod('today')">Today</button>
            <button type="button" class="btn btn-sm btn-outline-primary {{ $period === 'week' ? 'active' : '' }}" onclick="changePeriod('week')">Week</button>
            <button type="button" class="btn btn-sm btn-outline-primary {{ $period === 'month' ? 'active' : '' }}" onclick="changePeriod('month')">Month</button>
            <button type="button" class="btn btn-sm btn-outline-primary {{ $period === 'year' ? 'active' : '' }}" onclick="changePeriod('year')">Year</button>
        </div>
    </div>

    <!-- Visit Statistics Cards -->
    <div class="row mb-4">
        <!-- Total Visits -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Visits
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-visits">{{ number_format($visitStats['total_visits']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-eye fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-users"></i>
                            <span id="unique-visitors">{{ number_format($visitStats['unique_visitors']) }}</span> unique visitors
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Online Users -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Currently Online
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-online">{{ number_format($onlineStats['total_online']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-circle fa-2x text-success"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-success">
                            <i class="fas fa-user-check"></i>
                            <span id="authenticated-online">{{ number_format($onlineStats['authenticated_online']) }}</span> logged in
                        </small>
                        <span class="text-muted"> • </span>
                        <small class="text-info">
                            <i class="fas fa-building"></i>
                            <span id="organizations-online">{{ number_format($onlineStats['organizations_online']) }}</span> orgs
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bounce Rate -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Bounce Rate
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="bounce-rate">{{ number_format($visitStats['bounce_rate'], 1) }}%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i>
                            Avg: <span id="avg-duration">{{ number_format($visitStats['avg_duration']) }}s</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Totals -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Users
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($systemTotals['total_users']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-info">
                            <i class="fas fa-building"></i>
                            {{ number_format($systemTotals['total_organizations']) }} organizations
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Daily Visits Chart -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-area me-2"></i>
                        Daily Visits (Last 30 Days)
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="dailyVisitsChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Device & Browser Breakdown -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-mobile-alt me-2"></i>
                        Device & Browser Breakdown
                    </h6>
                </div>
                <div class="card-body">
                    <!-- Device Types -->
                    <h6 class="text-gray-800 mb-3">Device Types</h6>
                    <div id="device-breakdown">
                        @foreach($deviceBreakdown as $device)
                            @php
                                $total = collect($deviceBreakdown)->sum('count');
                                $percentage = $total > 0 ? round(($device['count'] / $total) * 100, 1) : 0;
                                $deviceIcon = $device['device_type'] === 'mobile' ? 'fa-mobile-alt' :
                                            ($device['device_type'] === 'tablet' ? 'fa-tablet-alt' : 'fa-desktop');
                            @endphp
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="small">
                                    <i class="fas {{ $deviceIcon }} me-2"></i>
                                    {{ ucfirst($device['device_type'] ?? 'Unknown') }}
                                </span>
                                <span class="badge bg-primary">{{ $device['count'] }} ({{ $percentage }}%)</span>
                            </div>
                        @endforeach
                    </div>

                    <hr class="my-3">

                    <!-- Browser Types -->
                    <h6 class="text-gray-800 mb-3">Browsers</h6>
                    <div id="browser-breakdown">
                        @foreach($browserBreakdown as $browser)
                            @php
                                $total = collect($browserBreakdown)->sum('count');
                                $percentage = $total > 0 ? round(($browser['count'] / $total) * 100, 1) : 0;
                                $browserIcon = strtolower($browser['browser']) === 'chrome' ? 'fa-chrome' :
                                             (strtolower($browser['browser']) === 'firefox' ? 'fa-firefox' :
                                             (strtolower($browser['browser']) === 'safari' ? 'fa-safari' :
                                             (strtolower($browser['browser']) === 'edge' ? 'fa-edge' : 'fa-globe')));
                            @endphp
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="small">
                                    <i class="fab {{ $browserIcon }} me-2"></i>
                                    {{ $browser['browser'] ?? 'Unknown' }}
                                </span>
                                <span class="badge bg-secondary">{{ $browser['count'] }} ({{ $percentage }}%)</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Online Users & Top Organizations -->
    <div class="row">
        <!-- Currently Online Users -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-users me-2"></i>
                        Currently Online Users
                    </h6>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshOnlineUsers()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm" id="online-users-table">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Organization</th>
                                    <th>Device</th>
                                    <th>Browser</th>
                                    <th>Duration</th>
                                    <th>Pages</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($onlineUsers as $user)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="status-indicator bg-success rounded-circle me-2" style="width: 8px; height: 8px;"></div>
                                                <div>
                                                    <div class="font-weight-bold">{{ $user['user_name'] }}</div>
                                                    @if($user['user_email'])
                                                        <small class="text-muted">{{ $user['user_email'] }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ $user['organization_name'] }}</td>
                                        <td>
                                            <i class="fas fa-{{ $user['device_type'] === 'mobile' ? 'mobile-alt' : ($user['device_type'] === 'tablet' ? 'tablet-alt' : 'desktop') }} me-1"></i>
                                            {{ ucfirst($user['device_type']) }}
                                        </td>
                                        <td>{{ $user['browser'] }}</td>
                                        <td>{{ $user['duration'] }}</td>
                                        <td>{{ $user['page_views'] }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center text-muted">No users currently online</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Organizations -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-building me-2"></i>
                        Top Organizations
                    </h6>
                </div>
                <div class="card-body">
                    <div id="top-organizations">
                        @forelse($topOrganizations as $org)
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <div class="font-weight-bold">{{ $org['organization']['name'] ?? 'Unknown' }}</div>
                                    <small class="text-muted">{{ $org['unique_users'] }} users • {{ $org['total_page_views'] }} page views</small>
                                </div>
                                <span class="badge bg-primary">{{ $org['visit_count'] }} visits</span>
                            </div>
                        @empty
                            <p class="text-muted text-center">No organization data available</p>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let dailyChart;
let currentPeriod = '{{ $period }}';

// Initialize daily visits chart
function initDailyChart() {
    const ctx = document.getElementById('dailyVisitsChart').getContext('2d');
    const dailyData = @json($dailyVisits);

    dailyChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dailyData.map(d => d.formatted_date),
            datasets: [{
                label: 'Total Visits',
                data: dailyData.map(d => d.visits),
                borderColor: 'rgb(78, 115, 223)',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                tension: 0.3,
                fill: true
            }, {
                label: 'Unique Visitors',
                data: dailyData.map(d => d.unique_visitors),
                borderColor: 'rgb(28, 200, 138)',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                tension: 0.3,
                fill: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            }
        }
    });
}

// Change time period
function changePeriod(period) {
    currentPeriod = period;

    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    // Reload page with new period
    window.location.href = `{{ route('super_admin.site_analytics.index') }}?period=${period}`;
}

// Refresh online users
function refreshOnlineUsers() {
    fetch(`{{ route('super_admin.site_analytics.api') }}?type=online_users`)
        .then(response => response.json())
        .then(data => {
            updateOnlineUsersTable(data);
        })
        .catch(error => {
            console.error('Error refreshing online users:', error);
        });
}

// Update online users table
function updateOnlineUsersTable(users) {
    const tbody = document.querySelector('#online-users-table tbody');

    if (users.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No users currently online</td></tr>';
        return;
    }

    tbody.innerHTML = users.map(user => `
        <tr>
            <td>
                <div class="d-flex align-items-center">
                    <div class="status-indicator bg-success rounded-circle me-2" style="width: 8px; height: 8px;"></div>
                    <div>
                        <div class="font-weight-bold">${user.user_name}</div>
                        ${user.user_email ? `<small class="text-muted">${user.user_email}</small>` : ''}
                    </div>
                </div>
            </td>
            <td>${user.organization_name}</td>
            <td>
                <i class="fas fa-${user.device_type === 'mobile' ? 'mobile-alt' : (user.device_type === 'tablet' ? 'tablet-alt' : 'desktop')} me-1"></i>
                ${user.device_type.charAt(0).toUpperCase() + user.device_type.slice(1)}
            </td>
            <td>${user.browser}</td>
            <td>${user.duration}</td>
            <td>${user.page_views}</td>
        </tr>
    `).join('');
}

// Auto-refresh online users every 30 seconds
setInterval(refreshOnlineUsers, 30000);

// Initialize chart when page loads
document.addEventListener('DOMContentLoaded', function() {
    initDailyChart();
});
</script>
@endsection
