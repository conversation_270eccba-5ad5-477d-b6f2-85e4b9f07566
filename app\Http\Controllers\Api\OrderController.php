public function updateStatus(OrderStatusUpdateRequest $request, Order $order)
{
    // Authorization handled by FormRequest
    $this->authorize('updateStatus', $order);

    // Update logic here
}

public function updateDelivery(OrderDeliveryUpdateRequest $request, Order $order)
{
    // Authorization handled by FormRequest
    $this->authorize('updateDelivery', $order);

    // Update logic here
}

public function updatePayment(OrderPaymentUpdateRequest $request, Order $order)
{
    // Authorization handled by FormRequest and Policy
    $this->authorize('updatePayment', $order);

    // Update logic here
}
