<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Organization;
use App\Models\Branch;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\URL;
use Illuminate\Auth\Events\Registered;
use Tests\TestCase;

class OrganizationEmailVerificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'Organization Owner', 'description' => 'Organization Owner']);
    }

    /** @test */
    public function organization_registration_redirects_to_email_verification()
    {
        Mail::fake();

        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'organization_name' => 'Test Organization',
            'branch_name' => 'Main Branch',
            'branch_email' => '<EMAIL>',
            'branch_phone' => '1234567890',
            'branch_address' => '123 Test St',
            'branch_description' => 'Test branch',
        ]);

        // Should redirect to email verification page
        $response->assertRedirect(route('verification.notice'));
        $response->assertSessionHas('success', 'Registration successful! Please verify your email address to continue.');

        // User should be created but not verified
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertNull($user->email_verified_at);
        
        // Organization and branch should be created
        $this->assertNotNull($user->organization);
        $this->assertNotNull($user->branch);
    }

    /** @test */
    public function organization_login_redirects_unverified_users_to_verification()
    {
        // Create an unverified organization user
        $organization = Organization::create([
            'name' => 'Test Organization',
            'email' => '<EMAIL>',
        ]);

        $branch = Branch::create([
            'name' => 'Main Branch',
            'organization_id' => $organization->id,
        ]);

        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'organization_id' => $organization->id,
            'branch_id' => $branch->id,
            'status' => 'active',
            'email_verified_at' => null, // Not verified
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // Should redirect to email verification page
        $response->assertRedirect(route('verification.notice'));
        $response->assertSessionHas('info', 'Please verify your email address to continue.');
    }

    /** @test */
    public function verified_organization_users_can_access_dashboard()
    {
        // Create a verified organization user
        $organization = Organization::create([
            'name' => 'Test Organization',
            'email' => '<EMAIL>',
        ]);

        $branch = Branch::create([
            'name' => 'Main Branch',
            'organization_id' => $organization->id,
        ]);

        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'organization_id' => $organization->id,
            'branch_id' => $branch->id,
            'status' => 'active',
            'email_verified_at' => now(), // Verified
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        // Should redirect to dashboard
        $response->assertRedirect(route('dashboard'));
    }

    /** @test */
    public function email_verification_redirects_to_plan_selection()
    {
        // Create an unverified organization user
        $organization = Organization::create([
            'name' => 'Test Organization',
            'email' => '<EMAIL>',
        ]);

        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'organization_id' => $organization->id,
            'status' => 'active',
            'email_verified_at' => null,
        ]);

        // Generate a signed verification URL
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            [
                'id' => $user->id,
                'hash' => sha1($user->email),
            ]
        );

        // Act as the user and visit the verification URL
        $response = $this->actingAs($user)->get($verificationUrl);

        // Should redirect to plan selection with success message
        $response->assertRedirect(route('plan-change.index'));
        $response->assertSessionHas('success', 'Email verified successfully! Please select a subscription plan to get started.');

        // User should now be verified
        $user->refresh();
        $this->assertNotNull($user->email_verified_at);
    }

    /** @test */
    public function dashboard_requires_email_verification()
    {
        // Create an unverified organization user
        $organization = Organization::create([
            'name' => 'Test Organization',
            'email' => '<EMAIL>',
        ]);

        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'organization_id' => $organization->id,
            'status' => 'active',
            'email_verified_at' => null,
        ]);

        // Try to access dashboard without verification
        $response = $this->actingAs($user)->get('/dashboard');

        // Should redirect to verification notice
        $response->assertRedirect(route('verification.notice'));
    }
}
