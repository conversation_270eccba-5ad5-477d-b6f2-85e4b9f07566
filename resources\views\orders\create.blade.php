@extends('layouts.app')

@section('title', 'Create New Order')

@section('content')
<style>
    /* Custom styles for intl-tel-input to match form design */
    .iti {
        width: 100%;
    }
    .iti__flag-container {
        display: flex;
    }
    .iti__selected-flag {
        border-radius: 0.375rem 0 0 0.375rem;
        background-color: #f9fafb;
        border: 1px solid #d1d5db;
        border-right: none;
    }
    .iti--allow-dropdown input {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
    }
</style>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-2xl font-bold mb-6">Create New Order</h2>

            <!-- Order Limit Warning -->
            @if(isset($planUsage))
                @if(!$canCreateOrder)
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-red-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="flex-1">
                                <h3 class="text-sm font-medium text-red-800">Monthly Order Limit Reached</h3>
                                <p class="mt-1 text-sm text-red-700">
                                    You have reached your {{ $planUsage['plan_name'] }} plan limit of {{ $planUsage['orders']['limit'] }} orders this month.
                                    You cannot create more orders until next month or upgrade your plan.
                                </p>
                                <div class="mt-3 flex space-x-3">
                                    <a href="{{ route('billing.index') }}" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                                        </svg>
                                        Upgrade Plan Now
                                    </a>
                                    <a href="{{ route('orders.index') }}" class="inline-flex items-center px-3 py-2 border border-red-300 text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                        View Existing Orders
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @elseif($remainingSlots <= 10 && $remainingSlots !== PHP_INT_MAX)
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="flex-1">
                                <h3 class="text-sm font-medium text-yellow-800">Approaching Monthly Order Limit</h3>
                                <p class="mt-1 text-sm text-yellow-700">
                                    You have {{ $remainingSlots }} order slots remaining this month in your {{ $planUsage['plan_name'] }} plan.
                                    Consider upgrading to avoid hitting the limit.
                                </p>
                                <div class="mt-3">
                                    <a href="{{ route('billing.index') }}" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-yellow-800 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                        </svg>
                                        View Upgrade Options
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            @endif

            <!-- Limit Exceeded Error -->
            @if($errors->has('limit_exceeded'))
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-red-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <div class="flex-1">
                            <h3 class="text-sm font-medium text-red-800">Order Creation Failed</h3>
                            <p class="mt-1 text-sm text-red-700">{{ $errors->first('limit_exceeded') }}</p>
                            @if(session('upgrade_required'))
                                <div class="mt-3 flex space-x-3">
                                    <a href="{{ route('billing.index') }}" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                                        </svg>
                                        Upgrade Plan Now
                                    </a>
                                    <a href="{{ route('orders.index') }}" class="inline-flex items-center px-3 py-2 border border-red-300 text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                        View Existing Orders
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endif

            <form action="{{ route('orders.store') }}" method="POST" id="orderForm">
                @csrf

                <!-- Customer Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="customer_name" class="block text-sm font-medium text-gray-700">Customer Name</label>
                        <input type="text" name="customer_name" id="customer_name" value="{{ old('customer_name') }}"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('customer_name') border-red-500 @enderror">
                        @error('customer_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="phone_number" class="block text-sm font-medium text-gray-700">Phone Number</label>
                        <input type="tel" name="phone_number" id="phone_number" value="{{ old('phone_number') }}"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('phone_number') border-red-500 @enderror">
                        <input type="hidden" name="phone_number_full" id="phone_number_full">
                        @error('phone_number')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Orders Container -->
                <div id="orders-container">
                    @if(old('orders'))
                        @foreach(old('orders') as $index => $oldOrder)
                            <div class="order-entry border-b border-gray-200 pb-6 mb-6" data-order-index="{{ $index }}">
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-lg font-semibold">Order #{{ $index + 1 }}</h3>
                                    <button type="button" class="text-red-600 hover:text-red-800" onclick="removeOrder(this)"
                                            style="display: {{ $index > 0 ? 'block' : 'none' }};">
                                        <i class="fas fa-trash"></i> Remove Order
                                    </button>
                                </div>

                                <div class="space-y-6">
                                    <div>
                                        <label for="orders[{{ $index }}][order_title]" class="block text-sm font-medium text-gray-700">Order Title</label>
                                        <input type="text" name="orders[{{ $index }}][order_title]" value="{{ $oldOrder['order_title'] ?? '' }}"
                                            class="order-title mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500
                                            @error('orders.'.$index.'.order_title') border-red-500 @enderror">
                                        @error('orders.'.$index.'.order_title')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>
                                    <div>
                                        <label for="orders[{{ $index }}][job_description]" class="block text-sm font-medium text-gray-700">Job Description</label>
                                        <textarea name="orders[{{ $index }}][job_description]" rows="3"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500
                                            @error('orders.'.$index.'.job_description') border-red-500 @enderror">{{ $oldOrder['job_description'] ?? '' }}</textarea>
                                        @error('orders.'.$index.'.job_description')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>
                                    <div>
                                        <label for="orders[{{ $index }}][department]" class="block text-sm font-medium text-gray-700">Department</label>
                                        <select name="orders[{{ $index }}][department]"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500
                                            @error('orders.'.$index.'.department') border-red-500 @enderror">
                                            <option value="">Select Department</option>
                                            @foreach(['DI', 'Flex', 'DTF', '3D', 'Monogram', 'Offset', 'UV', 'Stationary'] as $dept)
                                                <option value="{{ $dept }}" {{ ($oldOrder['department'] ?? '') == $dept ? 'selected' : '' }}>
                                                    {{ $dept }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('orders.'.$index.'.department')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Media</label>
                                            <input type="text" name="orders[{{ $index }}][media]" value="{{ $oldOrder['media'] ?? '' }}"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Pages</label>
                                            <input type="number" name="orders[{{ $index }}][pages]" value="{{ $oldOrder['pages'] ?? '' }}"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Size</label>
                                            <input type="text" name="orders[{{ $index }}][size]" value="{{ $oldOrder['size'] ?? '' }}"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Quantity</label>
                                            <input type="number" name="orders[{{ $index }}][quantity]" value="{{ $oldOrder['quantity'] ?? '' }}"
                                                class="quantity-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500
                                                @error('orders.'.$index.'.quantity') border-red-500 @enderror"
                                                min="1" onchange="calculateOrderTotal(this)" oninput="calculateOrderTotal(this)">
                                            @error('orders.'.$index.'.quantity')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Unit Cost</label>
                                            <input type="number" name="orders[{{ $index }}][unit_cost]" value="{{ $oldOrder['unit_cost'] ?? '' }}"
                                                class="unit-cost-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500
                                                @error('orders.'.$index.'.unit_cost') border-red-500 @enderror"
                                                min="0" step="0.01" onchange="calculateOrderTotal(this)" oninput="calculateOrderTotal(this)">
                                            @error('orders.'.$index.'.unit_cost')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Amount Paid</label>
                                            <input type="number" name="orders[{{ $index }}][amount_paid]" value="{{ $oldOrder['amount_paid'] ?? '0' }}"
                                                class="amount-paid-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500
                                                @error('orders.'.$index.'.amount_paid') border-red-500 @enderror"
                                                min="0" step="0.01" onchange="calculateOrderTotal(this)" oninput="calculateOrderTotal(this)">
                                            @error('orders.'.$index.'.amount_paid')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>
                                    </div>
                                    <!-- Calculated Totals Display -->
                                    <div class="mt-4 p-4 bg-gray-50 rounded-md">
                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Total Amount</label>
                                                <div class="mt-1 text-lg font-semibold text-gray-900">{{ currency_symbol() }}<span class="total-amount">0.00</span></div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Amount Paid</label>
                                                <div class="mt-1 text-lg font-semibold text-gray-900">{{ currency_symbol() }}<span class="amount-paid-display">0.00</span></div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Pending Payment</label>
                                                <div class="mt-1 text-lg font-semibold text-gray-900">{{ currency_symbol() }}<span class="pending-payment">0.00</span></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Expected Delivery Date</label>
                                            <input type="date" name="orders[{{ $index }}][expected_delivery_date]"
                                                value="{{ $oldOrder['expected_delivery_date'] ?? '' }}"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500
                                                @error('orders.'.$index.'.expected_delivery_date') border-red-500 @enderror"
                                                min="{{ date('Y-m-d') }}">
                                            @error('orders.'.$index.'.expected_delivery_date')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Expected Delivery Time</label>
                                            <input type="time" name="orders[{{ $index }}][expected_delivery_time]"
                                                value="{{ $oldOrder['expected_delivery_time'] ?? '' }}"
                                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500
                                                @error('orders.'.$index.'.expected_delivery_time') border-red-500 @enderror">
                                            @error('orders.'.$index.'.expected_delivery_time')
                                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <!-- Default first order entry -->
                        <div class="order-entry border-b border-gray-200 pb-6 mb-6" data-order-index="0">
                            <!-- Order Details -->
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold">Order #1</h3>
                                <button type="button" class="text-red-600 hover:text-red-800" onclick="removeOrder(this)" style="display: none;">
                                    <i class="fas fa-trash"></i> Remove Order
                                </button>
                            </div>

                            <div class="space-y-6">
                                <div>
                                    <label for="orders[0][order_title]" class="block text-sm font-medium text-gray-700">Order Title</label>
                                    <input type="text" name="orders[0][order_title]" class="order-title mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label for="orders[0][job_description]" class="block text-sm font-medium text-gray-700">Job Description</label>
                                    <textarea name="orders[0][job_description]" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
                                </div>
                                <div>
                                    <label for="orders[0][department]" class="block text-sm font-medium text-gray-700">Department</label>
                                    <select name="orders[0][department]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <option value="">Select Department</option>
                                        <option value="DI">DI</option>
                                        <option value="Flex">Flex</option>
                                        <option value="DTF">DTF</option>
                                        <option value="3D">3D</option>
                                        <option value="Monogram">Monogram</option>
                                        <option value="Offset">Offset</option>
                                        <option value="UV">UV</option>
                                        <option value="Stationary">Stationary</option>
                                    </select>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Media</label>
                                        <input type="text" name="orders[0][media]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Pages</label>
                                        <input type="number" name="orders[0][pages]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Size</label>
                                        <input type="text" name="orders[0][size]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Quantity</label>
                                        <input type="number" name="orders[0][quantity]" class="quantity-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" min="1" onchange="calculateOrderTotal(this)" oninput="calculateOrderTotal(this)">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Unit Cost</label>
                                        <input type="number" name="orders[0][unit_cost]" class="unit-cost-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" min="0" step="0.01" onchange="calculateOrderTotal(this)" oninput="calculateOrderTotal(this)">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Amount Paid</label>
                                        <input type="number" name="orders[0][amount_paid]" class="amount-paid-input mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" min="0" step="0.01" value="0" onchange="calculateOrderTotal(this)" oninput="calculateOrderTotal(this)">
                                    </div>
                                </div>
                                <!-- Calculated Totals Display -->
                                <div class="mt-4 p-4 bg-gray-50 rounded-md">
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Total Amount</label>
                                            <div class="mt-1 text-lg font-semibold text-gray-900">{{ currency_symbol() }}<span class="total-amount">0.00</span></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Amount Paid</label>
                                            <div class="mt-1 text-lg font-semibold text-gray-900">{{ currency_symbol() }}<span class="amount-paid-display">0.00</span></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Pending Payment</label>
                                            <div class="mt-1 text-lg font-semibold text-gray-900">{{ currency_symbol() }}<span class="pending-payment">0.00</span></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Expected Delivery Date</label>
                                        <input type="date" name="orders[0][expected_delivery_date]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" min="{{ date('Y-m-d') }}">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Expected Delivery Time</label>
                                        <input type="time" name="orders[0][expected_delivery_time]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Add Order Button -->
                <div class="mt-4">
                    <button type="button" onclick="addOrder()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                        <i class="fas fa-plus mr-2"></i> Add Another Order
                    </button>
                </div>

                <!-- Grand Total Display -->
                <div class="mt-6 p-4 bg-gray-100 rounded-md">
                    <h3 class="text-lg font-semibold mb-4">Grand Total</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Total Amount</label>
                            <div class="mt-1 text-xl font-bold text-gray-900">{{ currency_symbol() }}<span id="grandTotalAmount">0.00</span></div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Total Paid</label>
                            <div class="mt-1 text-xl font-bold text-gray-900">{{ currency_symbol() }}<span id="grandTotalPaid">0.00</span></div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Total Pending</label>
                            <div class="mt-1 text-xl font-bold text-gray-900">{{ currency_symbol() }}<span id="grandTotalPending">0.00</span></div>
                        </div>
                    </div>
                </div>

                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('orders.index') }}" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Cancel
                    </a>
                    @if(isset($canCreateOrder) && !$canCreateOrder)
                        <button type="button" disabled class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-gray-500 bg-gray-300 cursor-not-allowed">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                            </svg>
                            Monthly Limit Reached
                        </button>
                    @else
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Create Orders
                            @if(isset($remainingSlots) && $remainingSlots !== PHP_INT_MAX && $remainingSlots <= 10)
                                <span class="ml-2 text-xs bg-yellow-200 text-yellow-800 px-2 py-1 rounded-full">
                                    {{ $remainingSlots }} left
                                </span>
                            @endif
                        </button>
                    @endif
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let orderIndex = 0;
const currencySymbol = '{{ currency_symbol() }}';

function calculateOrderTotal(input) {
    const orderEntry = input.closest('.order-entry');
    const quantity = parseFloat(orderEntry.querySelector('.quantity-input').value) || 0;
    const unitCost = parseFloat(orderEntry.querySelector('.unit-cost-input').value) || 0;
    const amountPaid = parseFloat(orderEntry.querySelector('.amount-paid-input').value) || 0;

    const totalAmount = quantity * unitCost;
    const pendingPayment = Math.max(0, totalAmount - amountPaid);

    // Update displays with proper formatting
    orderEntry.querySelector('.total-amount').textContent = totalAmount.toLocaleString('en-NG', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
    orderEntry.querySelector('.amount-paid-display').textContent = amountPaid.toLocaleString('en-NG', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
    orderEntry.querySelector('.pending-payment').textContent = pendingPayment.toLocaleString('en-NG', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });

    // Validate amount paid doesn't exceed total
    const amountPaidInput = orderEntry.querySelector('.amount-paid-input');
    if (amountPaid > totalAmount) {
        amountPaidInput.value = totalAmount;
        calculateOrderTotal(amountPaidInput);
        return;
    }

    updateGrandTotal();
}

function updateGrandTotal() {
    let grandTotal = 0;
    let grandTotalPaid = 0;
    let grandTotalPending = 0;

    document.querySelectorAll('.order-entry').forEach(order => {
        const totalAmount = parseFloat(order.querySelector('.total-amount').textContent.replace(/,/g, '')) || 0;
        const amountPaid = parseFloat(order.querySelector('.amount-paid-display').textContent.replace(/,/g, '')) || 0;
        const pendingPayment = parseFloat(order.querySelector('.pending-payment').textContent.replace(/,/g, '')) || 0;

        grandTotal += totalAmount;
        grandTotalPaid += amountPaid;
        grandTotalPending += pendingPayment;
    });

    document.getElementById('grandTotalAmount').textContent = grandTotal.toLocaleString('en-NG', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
    document.getElementById('grandTotalPaid').textContent = grandTotalPaid.toLocaleString('en-NG', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
    document.getElementById('grandTotalPending').textContent = grandTotalPending.toLocaleString('en-NG', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function addOrder() {
    orderIndex++;
    const template = document.querySelector('.order-entry').cloneNode(true);
    template.setAttribute('data-order-index', orderIndex);

    // Update all input names with new index
    template.querySelectorAll('input, select, textarea').forEach(input => {
        const name = input.getAttribute('name');
        if (name) {
            const newName = name.replace(/\[\d+\]/, `[${orderIndex}]`);
            input.setAttribute('name', newName);
            input.setAttribute('id', newName);
            // Clear values except for hidden fields
            if (input.type !== 'hidden') {
                input.value = '';
            }
        }
    });

    // Reset validation states
    template.querySelectorAll('.border-red-500').forEach(el => {
        el.classList.remove('border-red-500');
    });
    template.querySelectorAll('.text-red-600').forEach(error => {
        error.textContent = '';
    });

    // Update order number in heading
    template.querySelector('h3').textContent = `Order #${orderIndex + 1}`;

    // Show remove button
    template.querySelector('button[onclick="removeOrder(this)"]').style.display = 'block';

    // Reset calculations
    template.querySelector('.total-amount').textContent = '0.00';
    template.querySelector('.amount-paid-display').textContent = '0.00';
    template.querySelector('.pending-payment').textContent = '0.00';

    // Initialize new order entry with default values
    document.getElementById('orders-container').appendChild(template);

    // Set default values and trigger calculation
    const quantityInput = template.querySelector('.quantity-input');
    const unitCostInput = template.querySelector('.unit-cost-input');
    const amountPaidInput = template.querySelector('.amount-paid-input');

    // Set minimum delivery date
    setMinDeliveryDates();

    // Set default values
    quantityInput.value = '1';
    unitCostInput.value = '0';
    amountPaidInput.value = '0';

    // Initialize calculations for new order
    calculateOrderTotal(quantityInput);
}

function removeOrder(button) {
    if (document.querySelectorAll('.order-entry').length > 1) {
        const orderEntry = button.closest('.order-entry');
        orderEntry.remove();
        updateGrandTotal();
    }
}

function validateForm() {
    let isValid = true;
    const errors = [];

    // Validate customer information
    ['customer_name', 'phone_number'].forEach(field => {
        const input = document.getElementById(field);
        if (!input.value.trim()) {
            isValid = false;
            input.classList.add('border-red-500');
            errors.push(`${field.replace('_', ' ')} is required`);
        } else {
            input.classList.remove('border-red-500');
        }
    });

    // Validate each order
    document.querySelectorAll('.order-entry').forEach((order, index) => {
        const orderNum = index + 1;
        const requiredFields = order.querySelectorAll('input[required], select[required], textarea[required]');

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('border-red-500');
                const fieldName = field.getAttribute('name').split('[')[2].replace(']', '');
                errors.push(`Order #${orderNum}: ${fieldName.replace('_', ' ')} is required`);
            } else {
                field.classList.remove('border-red-500');
            }
        });

        // Validate amount paid doesn't exceed total
        const quantity = parseFloat(order.querySelector('.quantity-input').value) || 0;
        const unitCost = parseFloat(order.querySelector('.unit-cost-input').value) || 0;
        const amountPaid = parseFloat(order.querySelector('.amount-paid-input').value) || 0;
        const totalAmount = quantity * unitCost;

        if (amountPaid > totalAmount) {
            isValid = false;
            order.querySelector('.amount-paid-input').classList.add('border-red-500');
            errors.push(`Order #${orderNum}: Amount paid cannot exceed total amount`);
        }
    });

    return { isValid, errors };
}

// Initialize date constraints and calculations
document.addEventListener('DOMContentLoaded', function() {
    setMinDeliveryDates();

    // Calculate initial totals
    document.querySelectorAll('.quantity-input').forEach(input => {
        calculateOrderTotal(input);
    });

    // Add validation before form submission
    document.getElementById('orderForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const { isValid, errors } = validateForm();

        if (!isValid) {
            const errorMessage = errors.join('\\n');
            Swal.fire({
                title: 'Validation Error',
                html: errors.map(error => `<p class="text-left">${error}</p>`).join(''),
                icon: 'error',
                confirmButtonText: 'Ok'
            });
            return;
        }

        // Prepare order summary
        let orderSummary = '';
        document.querySelectorAll('.order-entry').forEach((order, index) => {
            const title = order.querySelector('input[class*="order-title"]').value;
            const total = order.querySelector('.total-amount').textContent;
            orderSummary += `<li>Order #${index + 1}: ${title} - ${currencySymbol}${total}</li>`;
        });

        // Show confirmation dialog
        Swal.fire({
            title: 'Confirm Order Creation',
            html: `<div class="text-left">
                    <p class="mb-2">Customer: ${document.getElementById('customer_name').value}</p>
                    <p class="mb-4">Orders:</p>
                    <ul class="list-disc pl-5 space-y-1">
                        ${orderSummary}
                    </ul>
                    <p class="mt-4">Grand Total: ${currencySymbol}${document.getElementById('grandTotalAmount').textContent}</p>
                    <p class="mt-4 font-medium text-red-600">Note: Orders cannot be modified after creation.</p>
                   </div>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, create orders',
            cancelButtonText: 'No, review again'
        }).then((result) => {
            if (result.isConfirmed) {
                this.submit();
            }
        });
    });

    // Initialize and configure the international telephone input
    const phoneInputField = document.querySelector("#phone_number");
    const phoneInput = window.intlTelInput(phoneInputField, {
        utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js",
        initialCountry: "auto",
        geoIpLookup: function(callback) {
            fetch("https://ipapi.co/json")
              .then(function(res) { return res.json(); })
              .then(function(data) { callback(data.country_code); })
              .catch(function() { callback("us"); });
        },
        preferredCountries: ["us", "gb", "ca"],
        separateDialCode: true,
        formatOnDisplay: true,
    });

    // Store the full number with country code when submitting the form
    document.getElementById('orderForm').addEventListener('submit', function() {
        const fullNumber = phoneInput.getNumber();
        document.getElementById('phone_number_full').value = fullNumber;
    });
});

function setMinDeliveryDates() {
    const today = new Date().toISOString().split('T')[0];
    document.querySelectorAll('input[type="date"]').forEach(input => {
        input.min = today;
        if (!input.value) {
            input.value = today;
        }
    });
}
</script>
@endpush

