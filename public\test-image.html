<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Test</title>
    <style>
        body { padding: 2rem; font-family: Arial, sans-serif; }
        .test-section { margin-bottom: 2rem; padding: 1rem; border: 1px solid #ddd; border-radius: 0.5rem; }
        img { max-width: 300px; border: 1px solid #ccc; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Dashboard Image Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Direct Path</h3>
        <p>Testing: <code>images/dashboard_1.png</code></p>
        <img src="images/dashboard_1.png" alt="Dashboard 1" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
        <div class="error" style="display:none;">❌ Image not found at this path</div>
    </div>
    
    <div class="test-section">
        <h3>Test 2: Relative Path</h3>
        <p>Testing: <code>./images/dashboard_1.png</code></p>
        <img src="./images/dashboard_1.png" alt="Dashboard 1" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
        <div class="error" style="display:none;">❌ Image not found at this path</div>
    </div>
    
    <div class="test-section">
        <h3>Test 3: Absolute Path from Root</h3>
        <p>Testing: <code>/SalesManagementSystem/images/dashboard_1.png</code></p>
        <img src="/SalesManagementSystem/images/dashboard_1.png" alt="Dashboard 1" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
        <div class="error" style="display:none;">❌ Image not found at this path</div>
    </div>
    
    <div class="test-section">
        <h3>Test 4: Full URL</h3>
        <p>Testing: <code>http://localhost/SalesManagementSystem/images/dashboard_1.png</code></p>
        <img src="http://localhost/SalesManagementSystem/images/dashboard_1.png" alt="Dashboard 1" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
        <div class="error" style="display:none;">❌ Image not found at this path</div>
    </div>
    
    <div class="test-section">
        <h3>File Information</h3>
        <p>Expected file location: <code>public/images/dashboard_1.png</code></p>
        <p>Web accessible URL should be: <code>http://localhost/SalesManagementSystem/images/dashboard_1.png</code></p>
        
        <h4>Troubleshooting Steps:</h4>
        <ol>
            <li>Check if XAMPP is running</li>
            <li>Verify the file exists in public/images/ directory</li>
            <li>Check file permissions</li>
            <li>Try accessing other files in the public directory</li>
        </ol>
    </div>
    
    <script>
        // Test which images loaded successfully
        window.addEventListener('load', function() {
            const images = document.querySelectorAll('img');
            let loadedCount = 0;
            
            images.forEach((img, index) => {
                if (img.complete && img.naturalHeight !== 0) {
                    loadedCount++;
                    const successDiv = document.createElement('div');
                    successDiv.className = 'success';
                    successDiv.textContent = '✅ Image loaded successfully!';
                    img.parentNode.appendChild(successDiv);
                }
            });
            
            if (loadedCount === 0) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error';
                errorDiv.innerHTML = '<h3>❌ No images loaded successfully</h3><p>The dashboard_1.png file may not be accessible through the web server.</p>';
                document.body.appendChild(errorDiv);
            }
        });
    </script>
</body>
</html>
