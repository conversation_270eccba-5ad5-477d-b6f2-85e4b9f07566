<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Remove the old migration record from migrations table
        DB::table('migrations')->where('migration', '2024_03_15_000000_update_running_cost_percentage')->delete();
    }

    public function down()
    {
        // This is a cleanup migration, no down action needed
    }
};