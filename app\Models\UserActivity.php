<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserActivity extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'ip_address',
        'user_agent',
        'details',
        'is_impersonated',
        'impersonated_by',
        'impersonation_session_id',
        'impersonation_started_at',
    ];

    protected $casts = [
        'details' => 'array',
        'is_impersonated' => 'boolean',
        'impersonation_started_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the super admin who performed the impersonation
     */
    public function impersonator()
    {
        return $this->belongsTo(\App\Models\SuperAdmin::class, 'impersonated_by');
    }

    /**
     * Scope for impersonated activities
     */
    public function scopeImpersonated($query)
    {
        return $query->where('is_impersonated', true);
    }

    /**
     * Scope for non-impersonated activities
     */
    public function scopeNotImpersonated($query)
    {
        return $query->where('is_impersonated', false);
    }

    // Get login count for a user
    public static function getLoginCount($userId)
    {
        return self::where('user_id', $userId)
            ->where('type', 'login')
            ->count();
    }

    // Get last password change date for a user
    public static function getLastPasswordChange($userId)
    {
        return self::where('user_id', $userId)
            ->where('type', 'password_change')
            ->latest()
            ->first();
    }

    // Get recent activities for a user
    public static function getRecentActivities($userId, $limit = 5)
    {
        return self::where('user_id', $userId)
            ->latest()
            ->limit($limit)
            ->get();
    }
}
