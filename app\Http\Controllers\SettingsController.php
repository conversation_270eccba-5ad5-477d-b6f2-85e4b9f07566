<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Mike42\Escpos\PrintConnectors\WindowsPrintConnector;
use Mike42\Escpos\Printer;
use Illuminate\Support\Facades\DB;
use App\Models\Branch;

class SettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('role:Organization Owner|Manager');
    }

    public function index()
    {
        $setting = Setting::where('organization_id', Auth::user()->organization_id)
                    ->first() ?? new Setting(['organization_id' => Auth::user()->organization_id]);

        return view('settings.index', compact('setting'));
    }

    public function update(Request $request)
    {
        $setting = Setting::where('organization_id', Auth::user()->organization_id)->first();

        if (!$setting) {
            $setting = new Setting(['organization_id' => Auth::user()->organization_id]);
        }

        $validated = $request->validate([
            'theme_mode' => 'nullable|in:light,dark',
            'primary_color' => 'nullable|string|max:7',
            'sidebar_color' => 'nullable|string|max:7',
        ]);

        $setting->update($validated);

        return redirect()->route('settings.index')
            ->with('success', 'Settings updated successfully.')
            ->withInput(['refresh' => '1'])
            ->with('active_tab', 'app');
    }

    public function receiptSettings()
    {
        $setting = Setting::where('organization_id', Auth::user()->organization_id)
                    ->first() ?? new Setting(['organization_id' => Auth::user()->organization_id]);

        return view('settings.receipt', compact('setting'));
    }

    public function updateReceiptSettings(Request $request)
    {
        $setting = Setting::where('organization_id', Auth::user()->organization_id)->first();

        if (!$setting) {
            $setting = new Setting(['organization_id' => Auth::user()->organization_id]);
        }

        $validated = $request->validate([
            'company_address' => 'nullable|string',
            'company_phone' => 'nullable|string|max:255',
            'company_email' => 'nullable|email|max:255',
        ]);

        try {
            DB::beginTransaction();

            // Use the full phone number if provided
            if ($request->has('company_phone_full') && !empty($request->company_phone_full)) {
                $validated['company_phone'] = $request->company_phone_full;
            }

            // Update receipt settings
            $setting->fill($validated);
            $setting->save();

            // Update current branch information if the user has a branch assigned
            if (Auth::user()->branch_id) {
                $branch = Branch::find(Auth::user()->branch_id);
                if ($branch && $branch->organization_id === Auth::user()->organization_id) {
                    $branch->address = $request->company_address;
                    $branch->phone = $request->company_phone;
                    $branch->email = $request->company_email;
                    $branch->save();
                }
            }

            DB::commit();

            return redirect()->route('settings.index')
                ->with('success', 'Receipt settings updated successfully and branch information synchronized.')
                ->withInput(['refresh' => '1'])
                ->with('active_tab', 'receipt');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Failed to update settings: ' . $e->getMessage()])->withInput();
        }
    }

    public function businessSettings()
    {
        $setting = Setting::where('organization_id', Auth::user()->organization_id)
                    ->first() ?? new Setting(['organization_id' => Auth::user()->organization_id]);

        return view('settings.business', compact('setting'));
    }

    public function updateBusinessSettings(Request $request)
    {
        $setting = Setting::where('organization_id', Auth::user()->organization_id)->first();

        if (!$setting) {
            $setting = new Setting(['organization_id' => Auth::user()->organization_id]);
        }

        $validated = $request->validate([
            'business_registration_number' => 'nullable|string|max:255',
            'tax_identification_number' => 'nullable|string|max:255',
            'default_departments' => 'nullable|string',
            'require_payment_upfront' => 'boolean',
            'default_payment_due_days' => 'integer|min:0|max:90',
            'currency_symbol' => 'nullable|string|max:3',
            'currency_code' => 'nullable|string|max:3',
            'timezone' => 'required|string|in:UTC,Africa/Lagos,Africa/Cairo,Africa/Johannesburg,America/New_York,America/Chicago,America/Denver,America/Los_Angeles,Asia/Dubai,Asia/Kolkata,Asia/Singapore,Asia/Tokyo,Europe/London,Europe/Paris,Australia/Sydney',
            'order_number_prefix' => 'nullable|string|max:8',
            'include_year_in_order_number' => 'boolean',
            'order_number_format' => 'nullable|string|in:alphanumeric,numeric,sequential',
            'order_number_digits' => 'nullable|integer|min:4|max:10',
        ]);

        // Set default values if needed
        if (empty($validated['order_number_format'])) {
            $validated['order_number_format'] = 'sequential';
        }

        if (empty($validated['order_number_digits'])) {
            $validated['order_number_digits'] = 6;
        }

        $setting->update($validated);

        return redirect()->route('settings.index')
            ->with('success', 'Business settings updated successfully.')
            ->withInput(['refresh' => '1'])
            ->with('active_tab', 'business');
    }

    public function notificationSettings()
    {
        $setting = Setting::where('organization_id', Auth::user()->organization_id)
                    ->first() ?? new Setting(['organization_id' => Auth::user()->organization_id]);

        return view('settings.notifications', compact('setting'));
    }

    public function updateNotificationSettings(Request $request)
    {
        $setting = Setting::where('organization_id', Auth::user()->organization_id)->first();

        if (!$setting) {
            $setting = new Setting(['organization_id' => Auth::user()->organization_id]);
        }

        $validated = $request->validate([
            'order_confirmation_emails' => 'boolean',
            'order_status_update_emails' => 'boolean',
            'payment_reminder_emails' => 'boolean',
        ]);

        $validated['order_confirmation_emails'] = isset($request->order_confirmation_emails);
        $validated['order_status_update_emails'] = isset($request->order_status_update_emails);
        $validated['payment_reminder_emails'] = isset($request->payment_reminder_emails);

        $setting->update($validated);

        return redirect()->route('settings.index')
            ->with('success', 'Notification settings updated successfully.')
            ->withInput(['refresh' => '1'])
            ->with('active_tab', 'notifications');
    }

    public function printerSettings()
    {
        $setting = Setting::where('organization_id', Auth::user()->organization_id)
                    ->first() ?? new Setting(['organization_id' => Auth::user()->organization_id]);

        return view('settings.printer', compact('setting'));
    }

    public function updatePrinterSettings(Request $request)
    {
        $setting = Setting::where('organization_id', Auth::user()->organization_id)->first();

        if (!$setting) {
            $setting = new Setting(['organization_id' => Auth::user()->organization_id]);
        }

        $validated = $request->validate([
            'thermal_printer_name' => 'nullable|string|max:255',
            'thermal_paper_width' => 'integer|min:58|max:80',
        ]);

        $setting->update($validated);

        return redirect()->route('settings.index')
            ->with('success', 'Printer settings updated successfully.')
            ->withInput(['refresh' => '1'])
            ->with('active_tab', 'printer');
    }

    public function testPrinter(Request $request)
    {
        try {
            $request->validate([
                'printer_name' => 'required|string',
                'paper_width' => 'required|integer|min:58|max:80',
            ]);

            $printerName = $request->printer_name;
            $connector = new WindowsPrintConnector($printerName);
            $printer = new Printer($connector);

            $printer->setJustification(Printer::JUSTIFY_CENTER);
            $printer->text("TEST RECEIPT\n");
            $printer->text("------------------------\n");
            $printer->text("Printer: " . $printerName . "\n");
            $printer->text("Paper Width: " . $request->paper_width . "mm\n");
            $printer->text("Date: " . now()->format('Y-m-d H:i:s') . "\n");
            $printer->text("------------------------\n");
            $printer->text("If you can read this,\nyour printer is working!\n\n");
            $printer->text("------------------------\n");
            $printer->cut();
            $printer->close();

            return response()->json([
                'success' => true,
                'message' => 'Test print sent successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function allSettings()
    {
        $setting = Setting::where('organization_id', Auth::user()->organization_id)
                    ->first() ?? new Setting(['organization_id' => Auth::user()->organization_id]);

        return view('settings.all', compact('setting'));
    }
}
