@extends('super_admin.layouts.app')

@section('title', 'User Details - ' . $user->name)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">User Details</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('super_admin.impersonation.index') }}">Impersonation</a>
                            </li>
                            <li class="breadcrumb-item active">{{ $user->name }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('super_admin.impersonation.index') }}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                    <button type="button"
                            class="btn btn-warning"
                            onclick="confirmImpersonation('{{ $user->name }}', '{{ route('super_admin.impersonation.start', $user) }}')">
                        <i class="fas fa-user-secret"></i> Start Impersonation
                    </button>
                </div>
            </div>

            <div class="row">
                <!-- User Information -->
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user me-2"></i>
                                User Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Full Name</label>
                                        <p class="form-control-plaintext">{{ $user->name }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Email Address</label>
                                        <p class="form-control-plaintext">{{ $user->email }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Phone</label>
                                        <p class="form-control-plaintext">{{ $user->phone ?? 'Not provided' }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Position</label>
                                        <p class="form-control-plaintext">{{ $user->position ?? 'Not specified' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Status</label>
                                        <p class="form-control-plaintext">
                                            <span class="badge bg-{{ $user->status === 'active' ? 'success' : 'danger' }}">
                                                {{ ucfirst($user->status) }}
                                            </span>
                                        </p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Member Since</label>
                                        <p class="form-control-plaintext">{{ $user->created_at->format('M d, Y') }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Last Updated</label>
                                        <p class="form-control-plaintext">{{ $user->updated_at->diffForHumans() }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">User ID</label>
                                        <p class="form-control-plaintext">#{{ $user->id }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Organization Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-building me-2"></i>
                                Organization Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Organization Name</label>
                                        <p class="form-control-plaintext">{{ $user->organization->name }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Organization Status</label>
                                        <p class="form-control-plaintext">
                                            <span class="badge bg-{{ $user->organization->status === 'active' ? 'success' : 'danger' }}">
                                                {{ ucfirst($user->organization->status) }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Subscription Plan</label>
                                        <p class="form-control-plaintext">
                                            {{ $user->organization->plan->name ?? 'No Plan' }}
                                            @if($user->organization->plan)
                                                <span class="badge bg-info ms-2">
                                                    {{ format_price($user->organization->plan->price) }}/month
                                                </span>
                                            @endif
                                        </p>
                                    </div>
                                    @if($user->branch)
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Branch</label>
                                            <p class="form-control-plaintext">{{ $user->branch->name }}</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-history me-2"></i>
                                Recent Activities
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($recentActivities->count() > 0)
                                <div class="timeline">
                                    @foreach($recentActivities as $activity)
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-primary"></div>
                                            <div class="timeline-content">
                                                <div class="d-flex justify-content-between">
                                                    <h6 class="mb-1">{{ ucfirst(str_replace('_', ' ', $activity->type)) }}</h6>
                                                    <small class="text-muted">{{ $activity->created_at->diffForHumans() }}</small>
                                                </div>
                                                @if($activity->is_impersonated)
                                                    <div class="text-warning small mb-1">
                                                        <i class="fas fa-user-secret me-1"></i>
                                                        Performed during impersonation by {{ $activity->impersonator->name ?? 'Unknown' }}
                                                    </div>
                                                @endif
                                                <p class="text-muted small mb-1">
                                                    IP: {{ $activity->ip_address }}
                                                </p>
                                                @if($activity->details)
                                                    <div class="small text-muted">
                                                        @foreach($activity->details as $key => $value)
                                                            <span class="badge bg-light text-dark me-1">
                                                                {{ $key }}: {{ is_array($value) ? json_encode($value) : $value }}
                                                            </span>
                                                        @endforeach
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-history fa-2x text-muted mb-3"></i>
                                    <p class="text-muted">No recent activities found.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-md-4">
                    <!-- Roles -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-tag me-2"></i>
                                User Roles
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($user->roles->count() > 0)
                                @foreach($user->roles as $role)
                                    <span class="badge bg-secondary me-1 mb-2">{{ $role->name }}</span>
                                @endforeach
                            @else
                                <p class="text-muted">No roles assigned</p>
                            @endif
                        </div>
                    </div>

                    <!-- Impersonation Warning -->
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Before Impersonating
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Verify this is for legitimate support
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    User and organization are active
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    You have proper authorization
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Remember to stop when done
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Impersonation Confirmation Modal -->
<div class="modal fade" id="impersonationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-secret me-2"></i>
                    Confirm User Impersonation
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Important:</strong> You are about to impersonate another user.
                </div>
                <p>You are about to start impersonating: <strong id="targetUserName"></strong></p>
                <p>This action will:</p>
                <ul>
                    <li>Log you out as super admin</li>
                    <li>Log you in as the target user</li>
                    <li>Record all activities during impersonation</li>
                    <li>Automatically expire after 8 hours</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="impersonationForm" method="POST" style="display: inline;">
                    @csrf
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-user-secret me-1"></i>
                        Start Impersonation
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -31px;
    top: 15px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}
</style>

<script>
function confirmImpersonation(userName, actionUrl) {
    document.getElementById('targetUserName').textContent = userName;
    document.getElementById('impersonationForm').action = actionUrl;

    const modal = new bootstrap.Modal(document.getElementById('impersonationModal'));
    modal.show();
}
</script>
@endsection
