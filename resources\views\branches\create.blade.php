@extends('layouts.app')

@section('content')
<div class="container-fluid px-4">
    <h1 class="mt-4">Add New Branch</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{{ route('branches.index') }}">Branches</a></li>
        <li class="breadcrumb-item active">Add New Branch</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-code-branch me-1"></i>
            Branch Information
        </div>
        <div class="card-body">
            <!-- Plan Limit Warning -->
            @if(isset($planUsage))
                @if(!$canAddBranch)
                    <div class="alert alert-danger d-flex align-items-start mb-4">
                        <i class="fas fa-exclamation-triangle me-3 mt-1"></i>
                        <div class="flex-grow-1">
                            <h6 class="alert-heading mb-2">Branch Limit Reached</h6>
                            <p class="mb-2">
                                You have reached your {{ $planUsage['plan_name'] }} plan limit of {{ $planUsage['branches']['limit'] }} branches.
                                You cannot add more branches until you upgrade your plan or remove existing branches.
                            </p>
                            <div class="d-flex gap-2">
                                <a href="{{ route('billing.index') }}" class="btn btn-sm btn-outline-danger">
                                    <i class="fas fa-arrow-up"></i> Upgrade Plan
                                </a>
                                <a href="{{ route('branches.index') }}" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-cog"></i> Manage Branches
                                </a>
                            </div>
                        </div>
                    </div>
                @elseif($remainingSlots <= 1 && $remainingSlots !== PHP_INT_MAX)
                    <div class="alert alert-warning d-flex align-items-start mb-4">
                        <i class="fas fa-exclamation-triangle me-3 mt-1"></i>
                        <div class="flex-grow-1">
                            <h6 class="alert-heading mb-2">Approaching Branch Limit</h6>
                            <p class="mb-2">
                                You have {{ $remainingSlots }} branch slots remaining in your {{ $planUsage['plan_name'] }} plan.
                                Consider upgrading to avoid hitting the limit.
                            </p>
                            <a href="{{ route('billing.index') }}" class="btn btn-sm btn-outline-warning">
                                <i class="fas fa-eye"></i> View Upgrade Options
                            </a>
                        </div>
                    </div>
                @endif
            @endif

            @if (session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
            @endif

            @if($errors->has('limit_exceeded'))
                <div class="alert alert-danger d-flex align-items-start mb-4">
                    <i class="fas fa-exclamation-triangle me-3 mt-1"></i>
                    <div class="flex-grow-1">
                        <h6 class="alert-heading mb-2">Branch Limit Reached</h6>
                        <p class="mb-2">{{ $errors->first('limit_exceeded') }}</p>
                        @if(session('upgrade_required'))
                            <div class="d-flex gap-2">
                                <a href="{{ route('billing.index') }}" class="btn btn-sm btn-outline-danger">
                                    <i class="fas fa-arrow-up"></i> Upgrade Plan Now
                                </a>
                                <a href="{{ route('branches.index') }}" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-cog"></i> Manage Existing Branches
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            @elseif (session('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
            @endif

            <form action="{{ route('branches.store') }}" method="POST">
                @csrf

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">Branch Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror"
                                   id="email" name="email" value="{{ old('email') }}">
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone</label>
                            <input type="tel" class="form-control @error('phone') is-invalid @enderror"
                                   id="phone" name="phone" value="{{ old('phone') }}">
                            <input type="hidden" name="phone_full" id="phone_full">
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control @error('address') is-invalid @enderror"
                                      id="address" name="address" rows="3">{{ old('address') }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control @error('description') is-invalid @enderror"
                              id="description" name="description" rows="4">{{ old('description') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="switch_to_branch" name="switch_to_branch" value="1" {{ old('switch_to_branch') ? 'checked' : '' }}>
                    <label class="form-check-label" for="switch_to_branch">Switch to this branch after creation</label>
                </div>

                @if(request()->is('register/*') || !auth()->user()->branch_id)
                <input type="hidden" name="redirect_to_dashboard" value="1">
                @endif

                <div class="mt-4">
                    @if(isset($canAddBranch) && !$canAddBranch)
                        <button type="button" disabled class="btn btn-secondary">
                            <i class="fas fa-ban"></i> Branch Limit Reached
                        </button>
                    @else
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Create Branch
                        </button>
                    @endif
                    <a href="{{ route('branches.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    /* Custom styles for intl-tel-input to match Bootstrap form design */
    .iti {
        width: 100%;
    }
    .iti__flag-container {
        display: flex;
    }
    .iti__selected-flag {
        border-radius: 0.375rem 0 0 0.375rem;
        background-color: #fff;
        border: 1px solid #ced4da;
        border-right: none;
        height: calc(1.5em + 0.75rem + 2px);
    }
    .iti--allow-dropdown input {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
    }
    .iti__country-list {
        z-index: 1050;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize international telephone input for branch phone
    const phoneInputField = document.querySelector("#phone");
    const phoneInput = window.intlTelInput(phoneInputField, {
        utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js",
        initialCountry: "auto",
        geoIpLookup: function(callback) {
            fetch("https://ipapi.co/json")
              .then(function(res) { return res.json(); })
              .then(function(data) { callback(data.country_code); })
              .catch(function() { callback("us"); });
        },
        preferredCountries: ["ng", "us", "gb", "ca"],
        separateDialCode: true,
        formatOnDisplay: true,
    });

    // Store the full number with country code when submitting the form
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function() {
            const fullNumber = phoneInput.getNumber();
            document.getElementById('phone_full').value = fullNumber;
        });
    }
});
</script>
@endsection
