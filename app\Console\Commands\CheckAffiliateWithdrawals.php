<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\AffiliateWithdrawal;
use App\Models\Affiliate;
use App\Models\User;

class CheckAffiliateWithdrawals extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'affiliate:check-withdrawals';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check affiliate withdrawal data integrity and fix issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking affiliate withdrawal data integrity...');

        $withdrawals = AffiliateWithdrawal::all();
        $issues = [];
        $fixed = 0;

        foreach ($withdrawals as $withdrawal) {
            $this->line("Checking withdrawal #{$withdrawal->id}...");

            // Check if affiliate exists
            if (!$withdrawal->affiliate_id) {
                $issues[] = "Withdrawal #{$withdrawal->id}: No affiliate_id";
                continue;
            }

            $affiliate = Affiliate::find($withdrawal->affiliate_id);
            if (!$affiliate) {
                $issues[] = "Withdrawal #{$withdrawal->id}: Affiliate #{$withdrawal->affiliate_id} not found";
                continue;
            }

            // Check if user exists
            if (!$affiliate->user_id) {
                $issues[] = "Withdrawal #{$withdrawal->id}: Affiliate #{$affiliate->id} has no user_id";
                continue;
            }

            $user = User::find($affiliate->user_id);
            if (!$user) {
                $issues[] = "Withdrawal #{$withdrawal->id}: User #{$affiliate->user_id} not found for affiliate #{$affiliate->id}";
                continue;
            }

            $this->info("  ✓ Withdrawal #{$withdrawal->id} is valid");
        }

        // Display results
        $this->newLine();
        if (empty($issues)) {
            $this->info('✅ All affiliate withdrawals have valid relationships!');
        } else {
            $this->error('❌ Found ' . count($issues) . ' issues:');
            foreach ($issues as $issue) {
                $this->line("  - {$issue}");
            }
        }

        // Show summary
        $this->newLine();
        $this->info('Summary:');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Withdrawals', $withdrawals->count()],
                ['Valid Withdrawals', $withdrawals->count() - count($issues)],
                ['Issues Found', count($issues)],
                ['Issues Fixed', $fixed],
            ]
        );

        // Show detailed withdrawal info
        $this->newLine();
        $this->info('Withdrawal Details:');
        $this->table(
            ['ID', 'Affiliate ID', 'Amount', 'Status', 'Affiliate Name', 'User Email'],
            $withdrawals->map(function ($withdrawal) {
                return [
                    $withdrawal->id,
                    $withdrawal->affiliate_id ?? 'NULL',
                    '$' . number_format($withdrawal->amount, 2),
                    $withdrawal->status,
                    $withdrawal->affiliate && $withdrawal->affiliate->user ? $withdrawal->affiliate->user->name : 'N/A',
                    $withdrawal->affiliate && $withdrawal->affiliate->user ? $withdrawal->affiliate->user->email : 'N/A',
                ];
            })
        );

        return 0;
    }
}
