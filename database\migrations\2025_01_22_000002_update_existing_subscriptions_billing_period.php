<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing subscriptions to have monthly billing period
        DB::table('subscriptions')
            ->whereNull('billing_period')
            ->orWhere('billing_period', '')
            ->update(['billing_period' => 'monthly']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Optionally revert to null if needed
        // DB::table('subscriptions')->update(['billing_period' => null]);
    }
};
