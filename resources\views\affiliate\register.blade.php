@extends('affiliate.layouts.auth')

@section('title', 'Join Affiliate Program - Sales Management System')

@section('content')
<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left Side - Branding -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-primary text-white">
            <div class="text-center">
                <div class="mb-4">
                    <i class="fas fa-star" style="font-size: 4rem;"></i>
                </div>
                <h2 class="display-4 fw-bold mb-3">Join Our Team!</h2>
                <p class="lead mb-4">
                    Become an affiliate partner and start earning commissions by referring
                    new customers to our platform.
                </p>
                <div class="row text-center">
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-percentage fa-2x"></i>
                        </div>
                        <small>High Commissions</small>
                    </div>
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-link fa-2x"></i>
                        </div>
                        <small>Unique Links</small>
                    </div>
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                        <small>Fast Payouts</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Registration Form -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center auth-form-side">
            <div class="w-100" style="max-width: 500px;">
                <div class="text-center mb-4">
                    <h3 class="fw-bold text-dark">Join Affiliate Program</h3>
                    <p class="text-muted">Start earning commissions today</p>
                </div>

                @if($referralData)
                <div class="alert alert-info border-0 rounded-3 mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-2"></i>
                        <div>
                            <strong>Referral Detected!</strong> You were referred by an affiliate. Complete your registration to help them earn a commission.
                        </div>
                    </div>
                </div>
                @endif

                @if ($errors->any())
                <div class="alert alert-danger border-0 rounded-3 mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <div>
                            <strong>Please fix the following errors:</strong>
                            <ul class="mb-0 mt-2">
                                @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
                @endif

                <form method="POST" action="{{ route('affiliate.register') }}">
                    @csrf

                    <!-- Personal Information Section -->
                    <div class="mb-4">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-user me-2"></i>
                            Personal Information
                        </h6>

                        <div class="form-floating mb-3">
                            <input type="text"
                                   class="form-control @error('name') is-invalid @enderror"
                                   id="name"
                                   name="name"
                                   value="{{ old('name') }}"
                                   required
                                   autofocus
                                   placeholder="Enter your full name">
                            <label for="name">
                                <i class="fas fa-user me-2"></i>Full Name
                            </label>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-floating mb-3">
                            <input type="email"
                                   class="form-control @error('email') is-invalid @enderror"
                                   id="email"
                                   name="email"
                                   value="{{ old('email') }}"
                                   required
                                   placeholder="Enter your email address">
                            <label for="email">
                                <i class="fas fa-envelope me-2"></i>Email Address
                            </label>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-2"></i>Phone Number
                            </label>
                            <input type="tel"
                                   class="form-control @error('phone') is-invalid @enderror"
                                   id="phone"
                                   name="phone"
                                   value="{{ old('phone') }}"
                                   required
                                   placeholder="Enter your phone number">
                            <input type="hidden" id="phone_full" name="phone_full" value="{{ old('phone_full') }}">
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            @error('phone_full')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3 position-relative">
                                    <input type="password"
                                           class="form-control @error('password') is-invalid @enderror"
                                           id="password"
                                           name="password"
                                           required
                                           placeholder="Enter password">
                                    <label for="password">
                                        <i class="fas fa-lock me-2"></i>Password
                                    </label>
                                    <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y text-decoration-none password-toggle" style="z-index: 10; margin-right: 10px;" onclick="togglePasswordVisibility('password')">
                                        <i class="fas fa-eye-slash" id="password-toggle-icon"></i>
                                    </button>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating mb-3 position-relative">
                                    <input type="password"
                                           class="form-control @error('password_confirmation') is-invalid @enderror"
                                           id="password_confirmation"
                                           name="password_confirmation"
                                           required
                                           placeholder="Confirm password">
                                    <label for="password_confirmation">
                                        <i class="fas fa-lock me-2"></i>Confirm Password
                                    </label>
                                    <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y text-decoration-none password-toggle" style="z-index: 10; margin-right: 10px;" onclick="togglePasswordVisibility('password_confirmation')">
                                        <i class="fas fa-eye-slash" id="password_confirmation-toggle-icon"></i>
                                    </button>
                                    @error('password_confirmation')
                                        <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="mb-4">
        <h5 class="text-primary mb-3">
            <i class="fas fa-info-circle me-2"></i>
            Additional Information (Optional)
        </h5>

        <div class="mb-3">
            <label for="website" class="form-label">Website URL</label>
            <input type="url"
                   class="form-control @error('website') is-invalid @enderror"
                   id="website"
                   name="website"
                   value="{{ old('website') }}"
                   placeholder="https://example.com">
            @error('website')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-3">
            <label for="social_media" class="form-label">Social Media Profile</label>
            <input type="text"
                   class="form-control @error('social_media') is-invalid @enderror"
                   id="social_media"
                   name="social_media"
                   value="{{ old('social_media') }}"
                   placeholder="e.g., @username or profile URL">
            @error('social_media')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <div class="mb-3">
            <label for="bio" class="form-label">Brief Bio</label>
            <textarea id="bio"
                      name="bio"
                      rows="3"
                      class="form-control @error('bio') is-invalid @enderror"
                      placeholder="Tell us about yourself and how you plan to promote our services">{{ old('bio') }}</textarea>
            @error('bio')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
    </div>

    <!-- Payment Information -->
    <div class="mb-4">
        <h5 class="text-primary mb-3">
            <i class="fas fa-credit-card me-2"></i>
            Payment Information
        </h5>

        <div class="mb-3">
            <label for="payment_method" class="form-label">Payment Method</label>
            <input type="hidden" name="payment_method" value="bank_transfer">
            <div class="alert alert-info">
                <i class="fas fa-university me-2"></i>
                <strong>Bank Transfer</strong> - All affiliate payments are processed via bank transfer for security and compliance.
            </div>
        </div>

        <!-- Bank Transfer Fields -->
        <div id="bank_fields" class="d-none">
            <div class="mb-3">
                <label for="bank_name" class="form-label">Bank Name</label>
                <input type="text"
                       class="form-control @error('bank_name') is-invalid @enderror"
                       id="bank_name"
                       name="bank_name"
                       value="{{ old('bank_name') }}"
                       placeholder="Enter bank name">
                @error('bank_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="account_name" class="form-label">Account Holder Name</label>
                <input type="text"
                       class="form-control @error('account_name') is-invalid @enderror"
                       id="account_name"
                       name="account_name"
                       value="{{ old('account_name') }}"
                       placeholder="Enter account holder name">
                @error('account_name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="account_number" class="form-label">Account Number</label>
                <input type="text"
                       class="form-control @error('account_number') is-invalid @enderror"
                       id="account_number"
                       name="account_number"
                       value="{{ old('account_number') }}"
                       placeholder="Enter account number">
                @error('account_number')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="routing_number" class="form-label">Routing Number (Optional)</label>
                <input type="text"
                       class="form-control @error('routing_number') is-invalid @enderror"
                       id="routing_number"
                       name="routing_number"
                       value="{{ old('routing_number') }}"
                       placeholder="Enter routing number">
                @error('routing_number')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>


    </div>

    <!-- Terms and Conditions -->
    <div class="mb-4">
        <div class="form-check">
            <input type="checkbox"
                   class="form-check-input @error('terms_accepted') is-invalid @enderror"
                   id="terms_accepted"
                   name="terms_accepted"
                   value="1"
                   {{ old('terms_accepted') ? 'checked' : '' }}
                   required>
            <label for="terms_accepted" class="form-check-label">
                I agree to the <a href="#" class="text-primary">Affiliate Program Terms and Conditions</a>
            </label>
            @error('terms_accepted')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
    </div>

    <!-- Commission Information -->
    <div class="alert alert-success mb-4">
        <h6 class="alert-heading">
            <i class="fas fa-percentage me-2"></i>Commission Structure
        </h6>
        <ul class="mb-0">
            <li>Earn {{ number_format($settings->default_commission_rate, 1) }}% commission on each successful referral</li>
            <li>Minimum withdrawal amount: ${{ number_format($settings->minimum_withdrawal, 2) }}</li>
            <li>Monthly payouts for approved earnings</li>
            <li>Real-time tracking of your referrals and earnings</li>
        </ul>
    </div>

                    <!-- Submit Button -->
                    <div class="d-grid mb-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Join Affiliate Program
                        </button>
                    </div>
                </form>

                <!-- Additional Info -->
                <div class="text-center mt-4 pt-4 border-top">
                    <p class="text-muted mb-2">
                        Already have an account?
                        <a href="{{ route('affiliate.login') }}" class="text-decoration-none">
                            <strong>Sign in here</strong>
                        </a>
                    </p>
                    <p class="text-muted small mb-0">
                        <a href="{{ route('organization.login') }}" class="text-decoration-none">
                            <i class="fas fa-arrow-left me-1"></i>
                            Back to organization login
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    /* Custom styles for intl-tel-input to match Bootstrap form design */
    .iti {
        width: 100%;
        display: block;
    }

    .iti__flag-container {
        display: flex;
        align-items: center;
    }

    .iti__selected-flag {
        border-radius: 10px 0 0 10px;
        background-color: #fff;
        border: 2px solid #e9ecef;
        border-right: none;
        height: calc(3.5rem + 2px);
        padding: 0 12px;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
    }

    .iti--allow-dropdown input {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        border-left: none !important;
        height: calc(3.5rem + 2px);
        padding: 12px 16px;
    }

    .iti__country-list {
        z-index: 1050;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .iti__selected-flag:hover {
        border-color: var(--affiliate-primary);
    }

    .iti--allow-dropdown input:focus + .iti__selected-flag,
    .iti__selected-flag:focus {
        border-color: var(--affiliate-primary);
        box-shadow: 0 0 0 0.2rem rgba(142, 68, 173, 0.25);
    }

    /* Ensure proper styling for the phone input */
    #phone {
        border-radius: 0 10px 10px 0 !important;
        border: 2px solid #e9ecef !important;
        transition: all 0.3s ease !important;
    }

    #phone:focus {
        border-color: var(--affiliate-primary) !important;
        box-shadow: 0 0 0 0.2rem rgba(142, 68, 173, 0.25) !important;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize international telephone input
    const phoneInputField = document.querySelector("#phone");
    const phoneInput = window.intlTelInput(phoneInputField, {
        utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js",
        initialCountry: "auto",
        geoIpLookup: function(callback) {
            fetch("https://ipapi.co/json")
              .then(function(res) { return res.json(); })
              .then(function(data) { callback(data.country_code); })
              .catch(function() { callback("us"); });
        },
        preferredCountries: ["us", "gb", "ca", "ng"],
        separateDialCode: true,
        formatOnDisplay: true,
    });

    // Show bank fields since bank transfer is the only payment method
    const bankFields = document.getElementById('bank_fields');
    if (bankFields) {
        bankFields.classList.remove('d-none');
    }

    // Store the full number with country code when submitting the form
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function() {
            const fullNumber = phoneInput.getNumber();
            document.getElementById('phone_full').value = fullNumber;
        });
    }
});

// Password toggle functionality
function togglePasswordVisibility(inputId) {
    const passwordInput = document.getElementById(inputId);
    const toggleIcon = document.getElementById(inputId + '-toggle-icon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    }
}
</script>
@endpush
@endsection
