<?php

echo "Testing database connection...\n";

$host = '127.0.0.1';
$port = '4306';
$database = 'ofp_pro';
$username = 'root';
$password = '';

try {
    $dsn = "mysql:host={$host};port={$port};dbname={$database}";
    $pdo = new PDO($dsn, $username, $password);
    echo "✓ Database connection successful\n";
    
    // Test query
    $stmt = $pdo->query("SELECT 1");
    echo "✓ Database query successful\n";
    
} catch (PDOException $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "\n";
}

echo "Test complete.\n";
