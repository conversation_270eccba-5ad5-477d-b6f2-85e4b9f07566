<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Organization;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        // Get key metrics
        $totalOrganizations = Organization::count();

        // Check if is_active column exists before querying
        try {
            $activeOrganizations = Organization::where('is_active', true)->count();
        } catch (\Exception $e) {
            // If column doesn't exist, assume all organizations are active
            $activeOrganizations = $totalOrganizations;
        }
        $totalSubscriptions = Subscription::count();
        $activeSubscriptions = Subscription::where('status', 'active')->count();
        $totalUsers = User::count();
        $totalOrders = Order::count();

        // Revenue metrics
        $monthlyRevenue = Subscription::where('status', 'active')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('amount_paid');

        $totalRevenue = Subscription::sum('amount_paid');

        // Recent organizations
        $recentOrganizations = Organization::with('plan')
            ->latest()
            ->take(5)
            ->get();

        // Subscription status breakdown
        $subscriptionStats = Subscription::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status');

        // Plan popularity
        $planStats = Plan::withCount('subscriptions')
            ->orderBy('subscriptions_count', 'desc')
            ->get();

        // Growth metrics (last 30 days)
        $organizationGrowth = Organization::where('created_at', '>=', now()->subDays(30))->count();
        $subscriptionGrowth = Subscription::where('created_at', '>=', now()->subDays(30))->count();

        // Trial organizations
        try {
            $trialOrganizations = Organization::whereNotNull('trial_ends_at')
                ->where('trial_ends_at', '>', now())
                ->count();

            $expiredTrials = Organization::whereNotNull('trial_ends_at')
                ->where('trial_ends_at', '<', now())
                ->whereDoesntHave('subscriptions', function($query) {
                    $query->where('status', 'active');
                })
                ->count();
        } catch (\Exception $e) {
            // If trial_ends_at column doesn't exist, set to 0
            $trialOrganizations = 0;
            $expiredTrials = 0;
        }

        return view('super_admin.dashboard', compact(
            'totalOrganizations',
            'activeOrganizations',
            'totalSubscriptions',
            'activeSubscriptions',
            'totalUsers',
            'totalOrders',
            'monthlyRevenue',
            'totalRevenue',
            'recentOrganizations',
            'subscriptionStats',
            'planStats',
            'organizationGrowth',
            'subscriptionGrowth',
            'trialOrganizations',
            'expiredTrials'
        ));
    }
}
