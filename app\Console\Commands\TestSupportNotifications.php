<?php

namespace App\Console\Commands;

use App\Models\SupportTicket;
use App\Models\SupportTicketReply;
use App\Models\SuperAdmin;
use App\Models\User;
use App\Notifications\SupportTicketReplyNotification;
use App\Notifications\SupportTicketStatusNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestSupportNotifications extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'support:test-notifications {--ticket-id=1}';

    /**
     * The console command description.
     */
    protected $description = 'Test support ticket notification system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $ticketId = $this->option('ticket-id');
        
        $this->info("Testing support notification system...");
        
        // Find or create a test ticket
        $ticket = SupportTicket::find($ticketId);
        
        if (!$ticket) {
            $this->error("Ticket with ID {$ticketId} not found.");
            
            // Create a test ticket
            $user = User::first();
            if (!$user) {
                $this->error("No users found. Please create a user first.");
                return 1;
            }
            
            $ticket = SupportTicket::create([
                'ticket_number' => SupportTicket::generateTicketNumber(),
                'title' => 'Test Notification Ticket',
                'description' => 'This is a test ticket to verify notification system.',
                'priority' => SupportTicket::PRIORITY_NORMAL,
                'status' => SupportTicket::STATUS_OPEN,
                'category' => SupportTicket::CATEGORY_TECHNICAL,
                'user_id' => $user->id,
                'organization_id' => $user->organization_id,
            ]);
            
            $this->info("Created test ticket: {$ticket->ticket_number}");
        }
        
        $this->info("Using ticket: {$ticket->ticket_number}");
        
        // Test 1: User reply notification
        $this->info("\n1. Testing user reply notification...");
        try {
            $userReply = SupportTicketReply::createFromUser(
                $ticket, 
                $ticket->user, 
                "This is a test reply from the user to verify notifications are working."
            );
            $this->info("✓ User reply created and notifications sent");
        } catch (\Exception $e) {
            $this->error("✗ Failed to create user reply: " . $e->getMessage());
        }
        
        // Test 2: Admin reply notification
        $this->info("\n2. Testing admin reply notification...");
        try {
            $admin = SuperAdmin::first();
            if (!$admin) {
                $this->error("No super admin found. Please create a super admin first.");
                return 1;
            }
            
            $adminReply = SupportTicketReply::createFromAdmin(
                $ticket,
                $admin,
                "This is a test reply from support team to verify notifications are working.",
                false // not internal
            );
            $this->info("✓ Admin reply created and notifications sent");
        } catch (\Exception $e) {
            $this->error("✗ Failed to create admin reply: " . $e->getMessage());
        }
        
        // Test 3: Status change notification
        $this->info("\n3. Testing status change notification...");
        try {
            $ticket->markAsResolved($admin->id);
            $this->info("✓ Ticket marked as resolved and notifications sent");
        } catch (\Exception $e) {
            $this->error("✗ Failed to mark ticket as resolved: " . $e->getMessage());
        }
        
        // Test 4: Direct notification test
        $this->info("\n4. Testing direct notification delivery...");
        try {
            $user = $ticket->user;
            $user->notify(new SupportTicketReplyNotification($ticket, $adminReply, true));
            $this->info("✓ Direct notification sent to user");
        } catch (\Exception $e) {
            $this->error("✗ Failed to send direct notification: " . $e->getMessage());
        }
        
        // Show notification summary
        $this->info("\n=== Notification Test Summary ===");
        $this->info("Ticket: {$ticket->ticket_number}");
        $this->info("User: {$ticket->user->name} ({$ticket->user->email})");
        $this->info("Organization: {$ticket->organization->name}");
        $this->info("Status: {$ticket->status}");
        $this->info("Replies: " . $ticket->replies()->count());
        
        // Check database notifications
        $dbNotifications = $ticket->user->notifications()->count();
        $this->info("Database notifications for user: {$dbNotifications}");
        
        $this->info("\n✓ Support notification test completed!");
        $this->info("Check the user's email and database notifications table for results.");
        
        return 0;
    }
}
