@extends('layouts.settings')

@section('title', 'Printer Settings')

@section('printer_settings')
<h2 class="text-2xl font-bold mb-6">Printer Settings</h2>
<p class="text-gray-600 mb-4">Configure your thermal printer for receipt printing.</p>

<form action="{{ route('settings.printer.update') }}" method="POST">
    @csrf
    @method('PUT')

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Thermal Printer Configuration</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="mb-4">
                <label for="thermal_printer_name" class="block text-gray-700 text-sm font-bold mb-2">
                    Printer Name
                </label>
                <input
                    type="text"
                    name="thermal_printer_name"
                    id="thermal_printer_name"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    value="{{ old('thermal_printer_name', $setting->thermal_printer_name) }}"
                    placeholder="XP-58"
                >
                <p class="text-gray-500 text-xs mt-1">Enter the exact name of your thermal printer as it appears in Windows Devices & Printers.</p>
                @error('thermal_printer_name')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-4">
                <label for="thermal_paper_width" class="block text-gray-700 text-sm font-bold mb-2">
                    Paper Width (mm)
                </label>
                <input
                    type="number"
                    name="thermal_paper_width"
                    id="thermal_paper_width"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    value="{{ old('thermal_paper_width', $setting->thermal_paper_width) }}"
                    min="58"
                    max="80"
                    step="1"
                >
                <p class="text-gray-500 text-xs mt-1">Common widths: 58mm, 80mm</p>
                @error('thermal_paper_width')
                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                @enderror
            </div>
        </div>
    </div>

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-4">Test Print</h3>
        <p class="text-gray-600 mb-4">Test your printer configuration with a sample receipt.</p>

        <div class="flex items-center justify-start">
            <button
                type="button"
                id="test-print-button"
                class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mr-4"
                onclick="testPrint()"
            >
                Print Test Receipt
            </button>
            <span id="test-print-result" class="text-sm"></span>
        </div>
    </div>

    <div class="flex items-center justify-end">
        <button
            type="submit"
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded focus:outline-none focus:shadow-outline"
        >
            Save Printer Settings
        </button>
    </div>
</form>

@push('scripts')
<script>
    function testPrint() {
        const button = document.getElementById('test-print-button');
        const result = document.getElementById('test-print-result');

        button.disabled = true;
        button.innerText = 'Printing...';
        result.innerText = '';

        fetch('{{ route("settings.printer.test") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                printer_name: document.getElementById('thermal_printer_name').value,
                paper_width: document.getElementById('thermal_paper_width').value
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                result.innerText = '✓ Test print sent successfully!';
                result.className = 'text-sm text-green-600';
            } else {
                result.innerText = '✗ Error: ' + data.message;
                result.className = 'text-sm text-red-600';
            }
        })
        .catch(error => {
            result.innerText = '✗ Error connecting to server: ' + error;
            result.className = 'text-sm text-red-600';
        })
        .finally(() => {
            button.disabled = false;
            button.innerText = 'Print Test Receipt';
        });
    }
</script>
@endpush
@endsection
