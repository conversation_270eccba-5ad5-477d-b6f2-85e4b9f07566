<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;
use Illuminate\View\View;

class ProfileController extends Controller
{
    /**
     * Display the user's profile.
     */
    public function show(): View
    {
        $user = Auth::user();

        // Get user activity data
        $loginCount = \App\Models\UserActivity::getLoginCount($user->id);
        $lastPasswordChange = \App\Models\UserActivity::getLastPasswordChange($user->id);
        $recentActivities = \App\Models\UserActivity::getRecentActivities($user->id);

        // Calculate days since last password change
        $daysSincePasswordChange = $lastPasswordChange ? now()->diffInDays($lastPasswordChange->created_at) : null;

        return view('users.profile', [
            'loginCount' => $loginCount,
            'daysSincePasswordChange' => $daysSincePasswordChange,
            'recentActivities' => $recentActivities
        ]);
    }

    /**
     * Display the user's profile form.
     */
    public function edit(Request $request): View
    {
        return view('profile.edit', [
            'user' => $request->user(),
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(Request $request): RedirectResponse
    {
        $user = Auth::user();

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'phone' => ['nullable', 'string', 'max:20'],
            'position' => ['nullable', 'string', 'max:100'],
            'bio' => ['nullable', 'string', 'max:500'],
        ]);

        // Check if email is changing
        if ($validated['email'] !== $user->email) {
            $validated['email_verified_at'] = null;
        }

        $user->fill($validated);
        $user->save();

        // Log profile update activity
        $user->logActivity('profile_update');

        return redirect()->route('profile.show')->with('success', 'Profile updated successfully.');
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'current_password' => ['required', 'current_password'],
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        $user = Auth::user();
        $user->password = Hash::make($validated['password']);
        $user->save();

        // Log password change activity
        $user->logActivity('password_change');

        return redirect()->route('profile.show', ['#security'])->with('success', 'Password updated successfully.');
    }

    /**
     * Update the user's profile photo.
     */
    public function updatePhoto(Request $request): RedirectResponse
    {
        $request->validate([
            'photo' => ['required', 'image', 'max:2048'],
        ]);

        $user = Auth::user();

        // Delete old photo if exists
        if ($user->profile_photo_path) {
            Storage::delete('public/' . $user->profile_photo_path);
        }

        // Store new photo - Fix the path handling
        $path = $request->file('photo')->store('profile-photos', 'public');

        // Debug the path
        \Log::info('Stored image path: ' . $path);

        // Store path without 'public/' prefix since asset('storage/') expects it that way
        $user->profile_photo_path = $path;
        $user->save();

        \Log::info('Saved profile photo path: ' . $user->profile_photo_path);

        return redirect()->route('profile.show')->with('success', 'Profile photo updated successfully.');
    }

    /**
     * Remove the user's profile photo.
     */
    public function removePhoto(): RedirectResponse
    {
        $user = Auth::user();

        if ($user->profile_photo_path) {
            Storage::delete('public/' . $user->profile_photo_path);

            $user->profile_photo_path = null;
            $user->save();
        }

        return redirect()->route('profile.show')->with('success', 'Profile photo removed successfully.');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validateWithBag('userDeletion', [
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }

    /**
     * View another user's profile.
     *
     * @param int $id The ID of the user to view
     * @return \Illuminate\View\View
     */
    public function viewUser($id): View
    {
        // Get the requested user
        $user = \App\Models\User::findOrFail($id);

        // Check if the user belongs to the same organization as the authenticated user
        if ($user->organization_id !== Auth::user()->organization_id) {
            abort(403, 'Unauthorized action.');
        }

        // Get user activity data
        $loginCount = \App\Models\UserActivity::getLoginCount($user->id);
        $lastPasswordChange = \App\Models\UserActivity::getLastPasswordChange($user->id);
        $recentActivities = \App\Models\UserActivity::getRecentActivities($user->id);

        // Calculate days since last password change
        $daysSincePasswordChange = $lastPasswordChange ? now()->diffInDays($lastPasswordChange->created_at) : null;

        return view('users.view', [
            'user' => $user,
            'loginCount' => $loginCount,
            'daysSincePasswordChange' => $daysSincePasswordChange,
            'recentActivities' => $recentActivities
        ]);
    }
}
