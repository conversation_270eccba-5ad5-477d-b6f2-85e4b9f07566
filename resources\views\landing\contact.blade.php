@extends('landing.layouts.app')

@section('title', 'Contact Us - Sales Management System')
@section('description', 'Get in touch with our team. We\'re here to help you with any questions about our sales management system and how it can benefit your business.')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto text-center hero-content">
                <h1 class="display-4 fw-bold mb-4" data-aos="fade-up">
                    Get in 
                    <span class="text-warning">Touch</span>
                </h1>
                <p class="lead mb-4" data-aos="fade-up" data-aos-delay="100">
                    Have questions? We'd love to hear from you. Send us a message 
                    and we'll respond as soon as possible.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="section-padding">
    <div class="container">
        <div class="row g-5">
            <!-- Contact Form -->
            <div class="col-lg-8" data-aos="fade-right">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-5">
                        <h3 class="fw-bold mb-4">Send us a Message</h3>
                        
                        @if(session('success'))
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                {{ session('success') }}
                            </div>
                        @endif

                        @if($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        
                        <form method="POST" action="{{ route('landing.contact.submit') }}">
                            @csrf
                            
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           id="name" 
                                           name="name" 
                                           value="{{ old('name') }}" 
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" 
                                           class="form-control @error('email') is-invalid @enderror" 
                                           id="email" 
                                           name="email" 
                                           value="{{ old('email') }}" 
                                           required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-12">
                                    <label for="subject" class="form-label">Subject *</label>
                                    <input type="text" 
                                           class="form-control @error('subject') is-invalid @enderror" 
                                           id="subject" 
                                           name="subject" 
                                           value="{{ old('subject') }}" 
                                           required>
                                    @error('subject')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-12">
                                    <label for="message" class="form-label">Message *</label>
                                    <textarea class="form-control @error('message') is-invalid @enderror" 
                                              id="message" 
                                              name="message" 
                                              rows="6" 
                                              required>{{ old('message') }}</textarea>
                                    @error('message')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>Send Message
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="col-lg-4" data-aos="fade-left">
                <div class="h-100">
                    <h3 class="fw-bold mb-4">Contact Information</h3>
                    
                    <div class="mb-4">
                        <div class="d-flex align-items-start mb-3">
                            <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <h5 class="fw-bold mb-1">Email</h5>
                                <p class="text-muted mb-0"><EMAIL></p>
                                <p class="text-muted mb-0"><EMAIL></p>
                                <p class="text-muted"><EMAIL></p>
                                
                            </div>
                        </div>
                        
                        <div class="d-flex align-items-start mb-3">
                            <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div>
                                <h5 class="fw-bold mb-1">Phone</h5>
                                <p class="text-muted mb-0">+234 90 6045 1026</p>
                                <p class="text-muted">Mon-Fri 9AM-6PM +1GMT</p>
                            </div>
                        </div>
                        
                        <div class="d-flex align-items-start mb-3">
                            <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div>
                                <h5 class="fw-bold mb-1">Address</h5>
                                <p class="text-muted mb-0">21 Osiri Street</p>
                                <p class="text-muted">Ekeki, Yenagoa, Bayelsa State</p>
                            </div>
                        </div>
                        
                        <div class="d-flex align-items-start">
                            <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <h5 class="fw-bold mb-1">Business Hours</h5>
                                <p class="text-muted mb-0">Monday - Friday: 9:00 AM - 6:00 PM</p>
                                <p class="text-muted">Saturday: 10:00 AM - 4:00 PM</p>
                                <p class="text-muted">Sunday: Closed</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card bg-light border-0">
                        <div class="card-body">
                            <h5 class="fw-bold mb-3">Quick Links</h5>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <a href="{{ route('landing.features') }}" class="text-decoration-none">
                                        <i class="fas fa-arrow-right me-2 text-primary"></i>View Features
                                    </a>
                                </li>
                                <li class="mb-2">
                                    <a href="{{ route('landing.pricing') }}" class="text-decoration-none">
                                        <i class="fas fa-arrow-right me-2 text-primary"></i>See Pricing
                                    </a>
                                </li>
                                <li class="mb-2">
                                    <a href="{{ route('register') }}" class="text-decoration-none">
                                        <i class="fas fa-arrow-right me-2 text-primary"></i>Start Free Trial
                                    </a>
                                </li>
                                <li class="mb-2">
                                    <a href="{{ route('organization.login') }}" class="text-decoration-none">
                                        <i class="fas fa-arrow-right me-2 text-primary"></i>Login to Account
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="text-center mb-5" data-aos="fade-up">
                    <h2 class="display-5 fw-bold text-gradient mb-3">
                        Frequently Asked Questions
                    </h2>
                    <p class="lead text-muted">
                        Quick answers to common questions about our platform.
                    </p>
                </div>
                
                <div class="accordion" id="contactFAQ" data-aos="fade-up">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                How quickly can I get started?
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#contactFAQ">
                            <div class="accordion-body">
                                You can start using our platform immediately! Sign up for a free trial and you'll have access to all features within minutes. Our setup wizard will guide you through the initial configuration.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                Do you offer training and support?
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#contactFAQ">
                            <div class="accordion-body">
                                Yes! We provide comprehensive onboarding, training materials, video tutorials, and 24/7 customer support. Our team is always ready to help you succeed.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                Can I migrate my existing data?
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#contactFAQ">
                            <div class="accordion-body">
                                Absolutely! We offer data migration services to help you transfer your existing customer data, orders, and business information. Our team will assist you throughout the process.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                Is my data secure?
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#contactFAQ">
                            <div class="accordion-body">
                                Security is our top priority. We use enterprise-grade encryption, regular security audits, and comply with industry standards to ensure your data is always protected.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8" data-aos="fade-right">
                <h2 class="display-5 fw-bold mb-3">
                    Ready to Transform Your Business?
                </h2>
                <p class="lead mb-0">
                    Don't wait! Start your free trial today and see the difference 
                    our platform can make for your business.
                </p>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                <a href="{{ route('register') }}" class="btn btn-warning btn-lg">
                    <i class="fas fa-rocket me-2"></i>Start Free Trial
                </a>
            </div>
        </div>
    </div>
</section>
@endsection
