<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\AffiliateWithdrawal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AffiliateWithdrawalController extends Controller
{
    public function __construct()
    {
        $this->middleware('super_admin');
    }

    /**
     * Display withdrawal requests
     */
    public function index(Request $request)
    {
        $query = AffiliateWithdrawal::with(['affiliate.user', 'processedBy']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Search by affiliate name
        if ($request->filled('search')) {
            $query->whereHas('affiliate.user', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('requested_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('requested_at', '<=', $request->date_to);
        }

        $withdrawals = $query->latest('requested_at')->paginate(20);

        // Get summary statistics
        $stats = [
            'total_requests' => AffiliateWithdrawal::count(),
            'pending_requests' => AffiliateWithdrawal::where('status', AffiliateWithdrawal::STATUS_PENDING)->count(),
            'pending_amount' => AffiliateWithdrawal::where('status', AffiliateWithdrawal::STATUS_PENDING)->sum('amount'),
            'total_paid' => AffiliateWithdrawal::where('status', AffiliateWithdrawal::STATUS_PAID)->sum('amount'),
            'total_rejected' => AffiliateWithdrawal::where('status', AffiliateWithdrawal::STATUS_REJECTED)->count(),
        ];

        return view('super_admin.affiliate_withdrawals.index', compact('withdrawals', 'stats'));
    }

    /**
     * Show withdrawal details
     */
    public function show($id)
    {
        // Debug: Log the incoming ID
        \Log::info('Super Admin Withdrawal Show Debug', [
            'incoming_id' => $id,
            'route_params' => request()->route()->parameters(),
            'all_withdrawals_count' => AffiliateWithdrawal::count()
        ]);

        // Try to find the withdrawal manually first
        $withdrawal = AffiliateWithdrawal::find($id);

        if (!$withdrawal) {
            // Log available withdrawals for debugging
            $availableWithdrawals = AffiliateWithdrawal::select('id', 'amount', 'status', 'created_at')->get();
            \Log::error('Withdrawal not found', [
                'requested_id' => $id,
                'available_withdrawals' => $availableWithdrawals->toArray()
            ]);

            return redirect()->route('super.affiliate-withdrawals.index')
                ->withErrors(['error' => "Withdrawal #{$id} not found. Available withdrawals: " . $availableWithdrawals->pluck('id')->implode(', ')]);
        }

        $withdrawal->load(['affiliate.user', 'processedBy']);

        // Log successful retrieval
        \Log::info('Withdrawal found successfully', [
            'withdrawal_id' => $withdrawal->id,
            'amount' => $withdrawal->amount,
            'status' => $withdrawal->status,
            'affiliate_id' => $withdrawal->affiliate_id
        ]);

        // Log warning if affiliate relationship is missing
        if (!$withdrawal->affiliate) {
            \Log::warning('Withdrawal has no affiliate relationship', [
                'withdrawal_id' => $withdrawal->id,
                'affiliate_id' => $withdrawal->affiliate_id
            ]);
        } elseif (!$withdrawal->affiliate->user) {
            \Log::warning('Affiliate has no user relationship', [
                'withdrawal_id' => $withdrawal->id,
                'affiliate_id' => $withdrawal->affiliate_id
            ]);
        }

        return view('super_admin.affiliate_withdrawals.show', compact('withdrawal'));
    }

    /**
     * Approve withdrawal
     */
    public function approve(Request $request, $id)
    {
        // Find the withdrawal manually
        $withdrawal = AffiliateWithdrawal::find($id);

        if (!$withdrawal) {
            return back()->withErrors(['error' => 'Withdrawal not found.']);
        }

        if (!$withdrawal->isPending()) {
            return back()->withErrors(['error' => 'Only pending withdrawals can be approved.']);
        }

        $request->validate([
            'notes' => ['nullable', 'string', 'max:500'],
        ]);

        try {
            $withdrawal->approve(
                Auth::guard('super_admin')->id(),
                $request->notes
            );

            return back()->with('success', 'Withdrawal approved successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to approve withdrawal: ' . $e->getMessage()]);
        }
    }

    /**
     * Reject withdrawal
     */
    public function reject(Request $request, $id)
    {
        // Find the withdrawal manually
        $withdrawal = AffiliateWithdrawal::find($id);

        if (!$withdrawal) {
            return back()->withErrors(['error' => 'Withdrawal not found.']);
        }

        if (!$withdrawal->isPending()) {
            return back()->withErrors(['error' => 'Only pending withdrawals can be rejected.']);
        }

        $request->validate([
            'rejection_reason' => ['required', 'string', 'max:500'],
        ]);

        try {
            $withdrawal->reject(
                Auth::guard('super_admin')->id(),
                $request->rejection_reason,
                null
            );

            return back()->with('success', 'Withdrawal rejected.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to reject withdrawal: ' . $e->getMessage()]);
        }
    }

    /**
     * Mark withdrawal as paid
     */
    public function markAsPaid(Request $request, $id)
    {
        // Find the withdrawal manually
        $withdrawal = AffiliateWithdrawal::find($id);

        if (!$withdrawal) {
            return back()->withErrors(['error' => 'Withdrawal not found.']);
        }

        if (!$withdrawal->isApproved()) {
            return back()->withErrors(['error' => 'Only approved withdrawals can be marked as paid.']);
        }

        $request->validate([
            'transaction_reference' => ['required', 'string', 'max:255'],
            'notes' => ['nullable', 'string', 'max:500'],
        ]);

        try {
            $withdrawal->markAsPaid(
                $request->transaction_reference,
                Auth::guard('super_admin')->id()
            );

            if ($request->notes) {
                $withdrawal->update(['notes' => $request->notes]);
            }

            return back()->with('success', 'Withdrawal marked as paid successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to mark withdrawal as paid: ' . $e->getMessage()]);
        }
    }

    /**
     * Bulk approve withdrawals
     */
    public function bulkApprove(Request $request)
    {
        $request->validate([
            'withdrawal_ids' => ['required', 'array'],
            'withdrawal_ids.*' => ['exists:affiliate_withdrawals,id'],
            'notes' => ['nullable', 'string', 'max:500'],
        ]);

        $withdrawalIds = $request->withdrawal_ids;
        $results = ['success' => [], 'failed' => []];

        foreach ($withdrawalIds as $withdrawalId) {
            try {
                $withdrawal = AffiliateWithdrawal::find($withdrawalId);

                if ($withdrawal->isPending()) {
                    $withdrawal->approve(
                        Auth::guard('super_admin')->id(),
                        $request->notes
                    );
                    $results['success'][] = "#{$withdrawal->id}";
                } else {
                    $results['failed'][] = "#{$withdrawal->id} (not pending)";
                }
            } catch (\Exception $e) {
                $results['failed'][] = "#{$withdrawal->id} ({$e->getMessage()})";
            }
        }

        $message = '';
        if (!empty($results['success'])) {
            $message .= 'Successfully approved: ' . implode(', ', $results['success']) . '. ';
        }
        if (!empty($results['failed'])) {
            $message .= 'Failed: ' . implode(', ', $results['failed']) . '. ';
        }

        return back()->with('success', $message);
    }

    /**
     * Bulk reject withdrawals
     */
    public function bulkReject(Request $request)
    {
        $request->validate([
            'withdrawal_ids' => ['required', 'array'],
            'withdrawal_ids.*' => ['exists:affiliate_withdrawals,id'],
            'rejection_reason' => ['required', 'string', 'max:500'],
            'notes' => ['nullable', 'string', 'max:500'],
        ]);

        $withdrawalIds = $request->withdrawal_ids;
        $results = ['success' => [], 'failed' => []];

        foreach ($withdrawalIds as $withdrawalId) {
            try {
                $withdrawal = AffiliateWithdrawal::find($withdrawalId);

                if ($withdrawal->isPending()) {
                    $withdrawal->reject(
                        Auth::guard('super_admin')->id(),
                        $request->rejection_reason,
                        $request->notes
                    );
                    $results['success'][] = "#{$withdrawal->id}";
                } else {
                    $results['failed'][] = "#{$withdrawal->id} (not pending)";
                }
            } catch (\Exception $e) {
                $results['failed'][] = "#{$withdrawal->id} ({$e->getMessage()})";
            }
        }

        $message = '';
        if (!empty($results['success'])) {
            $message .= 'Successfully rejected: ' . implode(', ', $results['success']) . '. ';
        }
        if (!empty($results['failed'])) {
            $message .= 'Failed: ' . implode(', ', $results['failed']) . '. ';
        }

        return back()->with('success', $message);
    }

    /**
     * Export withdrawals to CSV
     */
    public function export(Request $request)
    {
        $query = AffiliateWithdrawal::with(['affiliate.user', 'processedBy']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }
        if ($request->filled('search')) {
            $query->whereHas('affiliate.user', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }
        if ($request->filled('date_from')) {
            $query->where('requested_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('requested_at', '<=', $request->date_to);
        }

        $withdrawals = $query->latest('requested_at')->get();

        $filename = 'affiliate_withdrawals_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($withdrawals) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID',
                'Affiliate Name',
                'Affiliate Email',
                'Amount',
                'Fee Amount',
                'Net Amount',
                'Payment Method',
                'Status',
                'Requested At',
                'Processed At',
                'Processed By',
                'Transaction Reference',
                'Notes'
            ]);

            // CSV data
            foreach ($withdrawals as $withdrawal) {
                fputcsv($file, [
                    $withdrawal->id,
                    $withdrawal->affiliate->user->name,
                    $withdrawal->affiliate->user->email,
                    $withdrawal->amount,
                    $withdrawal->fee_amount,
                    $withdrawal->net_amount,
                    $withdrawal->payment_method_display,
                    ucfirst($withdrawal->status),
                    $withdrawal->requested_at->format('Y-m-d H:i:s'),
                    $withdrawal->processed_at ? $withdrawal->processed_at->format('Y-m-d H:i:s') : '',
                    $withdrawal->processedBy ? $withdrawal->processedBy->name : '',
                    $withdrawal->transaction_reference ?? '',
                    $withdrawal->notes ?? ''
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
