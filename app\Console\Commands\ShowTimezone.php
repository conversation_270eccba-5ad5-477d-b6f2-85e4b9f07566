<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Setting;
use Carbon\Carbon;

class ShowTimezone extends Command
{
    protected $signature = 'app:show-timezone';
    protected $description = 'Show current timezone settings';

    public function handle()
    {
        $setting = Setting::first();
        $dbTimezone = $setting->timezone ?? 'Not set';
        $appTimezone = config('app.timezone');
        $currentTime = Carbon::now()->format('Y-m-d H:i:s');
        $utcTime = Carbon::now('UTC')->format('Y-m-d H:i:s');

        $this->info("Database timezone setting: {$dbTimezone}");
        $this->info("Application timezone: {$appTimezone}");
        $this->info("Current time: {$currentTime}");
        $this->info("UTC time: {$utcTime}");

        return Command::SUCCESS;
    }
}
