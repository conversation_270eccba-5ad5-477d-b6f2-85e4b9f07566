@props(['order'])

@if(auth()->user()->hasAnyRole(['Organization Owner', 'Operator', 'Production']) && $order->isStatusChangeable())
    <div class="order-status-controls">
        @if($order->status === 'Pending')
            <button class="btn btn-primary" onclick="updateOrderStatus({{ $order->id }}, 'Processing')">
                Start Processing
            </button>
        @elseif($order->status === 'Processing')
            <button class="btn btn-success" onclick="updateOrderStatus({{ $order->id }}, 'Completed')">
                Mark Complete
            </button>
        @endif
    </div>
@endif

@if(auth()->user()->hasAnyRole(['Organization Owner', 'Delivery']) && $order->status === 'Completed')
    <div class="delivery-controls">
        <button class="btn btn-info" onclick="showDeliveryForm({{ $order->id }})">
            Update Delivery
        </button>
    </div>
@endif