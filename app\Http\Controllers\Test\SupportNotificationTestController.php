<?php

namespace App\Http\Controllers\Test;

use App\Http\Controllers\Controller;
use App\Models\SupportTicket;
use App\Models\SupportTicketReply;
use App\Models\SuperAdmin;
use App\Models\User;
use App\Notifications\SupportTicketReplyNotification;
use App\Notifications\SupportTicketStatusNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SupportNotificationTestController extends Controller
{
    /**
     * Show the notification test page
     */
    public function index()
    {
        return view('test.support-notifications');
    }

    /**
     * Test admin reply notification
     */
    public function testAdminReply(Request $request)
    {
        $request->validate([
            'ticket_id' => 'required|exists:support_tickets,id',
            'message' => 'required|string|max:1000',
        ]);

        try {
            $ticket = SupportTicket::findOrFail($request->ticket_id);
            $admin = Auth::guard('super_admin')->user();
            
            if (!$admin) {
                $admin = SuperAdmin::first();
                if (!$admin) {
                    return back()->with('error', 'No super admin found for testing.');
                }
            }

            // Create admin reply (this will trigger notifications)
            $reply = SupportTicketReply::createFromAdmin(
                $ticket,
                $admin,
                $request->message,
                false // not internal
            );

            $results = "✓ Admin reply created successfully\n";
            $results .= "✓ Ticket ID: {$ticket->id}\n";
            $results .= "✓ Ticket Number: {$ticket->ticket_number}\n";
            $results .= "✓ Admin: {$admin->name}\n";
            $results .= "✓ User to notify: {$ticket->user->name} ({$ticket->user->email})\n";
            $results .= "✓ Reply ID: {$reply->id}\n";
            $results .= "✓ Notifications should be sent to user and organization admins\n";
            $results .= "✓ Check email logs and database notifications for results\n";

            return back()->with('success', 'Admin reply test completed successfully!')
                        ->with('test_results', $results);

        } catch (\Exception $e) {
            Log::error('Admin reply test failed', [
                'error' => $e->getMessage(),
                'ticket_id' => $request->ticket_id
            ]);

            return back()->with('error', 'Test failed: ' . $e->getMessage());
        }
    }

    /**
     * Test user reply notification
     */
    public function testUserReply(Request $request)
    {
        $request->validate([
            'ticket_id' => 'required|exists:support_tickets,id',
            'message' => 'required|string|max:1000',
        ]);

        try {
            $ticket = SupportTicket::findOrFail($request->ticket_id);
            $user = $ticket->user;

            // Create user reply (this will trigger notifications to admins)
            $reply = SupportTicketReply::createFromUser(
                $ticket,
                $user,
                $request->message
            );

            $adminCount = SuperAdmin::count();
            
            $results = "✓ User reply created successfully\n";
            $results .= "✓ Ticket ID: {$ticket->id}\n";
            $results .= "✓ Ticket Number: {$ticket->ticket_number}\n";
            $results .= "✓ User: {$user->name} ({$user->email})\n";
            $results .= "✓ Reply ID: {$reply->id}\n";
            $results .= "✓ Notifications should be sent to {$adminCount} super admin(s)\n";
            $results .= "✓ Ticket status updated to: {$ticket->fresh()->status}\n";
            $results .= "✓ Check admin notifications for results\n";

            return back()->with('success', 'User reply test completed successfully!')
                        ->with('test_results', $results);

        } catch (\Exception $e) {
            Log::error('User reply test failed', [
                'error' => $e->getMessage(),
                'ticket_id' => $request->ticket_id
            ]);

            return back()->with('error', 'Test failed: ' . $e->getMessage());
        }
    }

    /**
     * Test status change notification
     */
    public function testStatusChange(Request $request)
    {
        $request->validate([
            'ticket_id' => 'required|exists:support_tickets,id',
            'new_status' => 'required|in:in_progress,waiting_customer,resolved,closed',
        ]);

        try {
            $ticket = SupportTicket::findOrFail($request->ticket_id);
            $oldStatus = $ticket->status;
            $newStatus = $request->new_status;
            
            $admin = Auth::guard('super_admin')->user();
            if (!$admin) {
                $admin = SuperAdmin::first();
            }

            // Update ticket status (this will trigger notifications)
            if ($newStatus === 'resolved') {
                $ticket->markAsResolved($admin ? $admin->id : null);
            } else {
                $ticket->update(['status' => $newStatus]);
                $ticket->sendStatusChangeNotification($oldStatus, $newStatus, $admin ? $admin->id : null);
            }

            $results = "✓ Status change test completed successfully\n";
            $results .= "✓ Ticket ID: {$ticket->id}\n";
            $results .= "✓ Ticket Number: {$ticket->ticket_number}\n";
            $results .= "✓ Old Status: {$oldStatus}\n";
            $results .= "✓ New Status: {$newStatus}\n";
            $results .= "✓ User to notify: {$ticket->user->name} ({$ticket->user->email})\n";
            if ($admin) {
                $results .= "✓ Changed by admin: {$admin->name}\n";
            }
            $results .= "✓ Notifications should be sent to user and organization admins\n";
            $results .= "✓ Check email logs and database notifications for results\n";

            return back()->with('success', 'Status change test completed successfully!')
                        ->with('test_results', $results);

        } catch (\Exception $e) {
            Log::error('Status change test failed', [
                'error' => $e->getMessage(),
                'ticket_id' => $request->ticket_id,
                'new_status' => $request->new_status
            ]);

            return back()->with('error', 'Test failed: ' . $e->getMessage());
        }
    }

    /**
     * Check email logs
     */
    public function checkLogs()
    {
        $logFile = storage_path('logs/laravel.log');
        
        if (!file_exists($logFile)) {
            return back()->with('error', 'Log file not found.');
        }

        // Get the last 50 lines of the log file
        $lines = [];
        $file = new \SplFileObject($logFile);
        $file->seek(PHP_INT_MAX);
        $totalLines = $file->key();
        
        $startLine = max(0, $totalLines - 100);
        $file->seek($startLine);
        
        while (!$file->eof()) {
            $line = $file->current();
            if (strpos($line, 'SupportTicket') !== false || 
                strpos($line, 'mail.sending') !== false ||
                strpos($line, 'notification') !== false) {
                $lines[] = $line;
            }
            $file->next();
        }

        $results = "Recent email/notification logs:\n\n";
        $results .= implode("\n", array_slice($lines, -20));

        return back()->with('info', 'Email logs retrieved.')
                    ->with('test_results', $results);
    }

    /**
     * Check database notifications
     */
    public function checkDatabase()
    {
        try {
            $notifications = DB::table('notifications')
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            $results = "Recent database notifications:\n\n";
            
            foreach ($notifications as $notification) {
                $data = json_decode($notification->data, true);
                $results .= "ID: {$notification->id}\n";
                $results .= "Type: {$notification->type}\n";
                $results .= "Notifiable: {$notification->notifiable_type} #{$notification->notifiable_id}\n";
                $results .= "Read: " . ($notification->read_at ? 'Yes' : 'No') . "\n";
                $results .= "Created: {$notification->created_at}\n";
                if (isset($data['ticket_number'])) {
                    $results .= "Ticket: {$data['ticket_number']}\n";
                }
                $results .= "---\n";
            }

            return back()->with('info', 'Database notifications retrieved.')
                        ->with('test_results', $results);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to retrieve notifications: ' . $e->getMessage());
        }
    }

    /**
     * Clear test notifications
     */
    public function clearNotifications()
    {
        try {
            $deleted = DB::table('notifications')->delete();
            
            return back()->with('success', "Cleared {$deleted} notifications from database.");

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to clear notifications: ' . $e->getMessage());
        }
    }
}
