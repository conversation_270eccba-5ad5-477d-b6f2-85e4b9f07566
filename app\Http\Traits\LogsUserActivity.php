<?php

namespace App\Http\Traits;

use App\Models\UserActivity;
use Illuminate\Support\Facades\Request;

trait LogsUserActivity
{
    /**
     * Log a user activity
     *
     * @param string $type The type of activity (login, profile_update, password_change, etc)
     * @param array $details Additional details to store
     * @return void
     */
    public function logActivity($type, $details = [])
    {
        // Check if currently being impersonated
        $impersonationData = session('impersonation_data');
        $isImpersonated = !is_null($impersonationData);

        UserActivity::create([
            'user_id' => $this->id,
            'type' => $type,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'details' => $details,
            'is_impersonated' => $isImpersonated,
            'impersonated_by' => $isImpersonated ? $impersonationData['super_admin_id'] : null,
            'impersonation_session_id' => $isImpersonated ? session()->getId() : null,
            'impersonation_started_at' => $isImpersonated ? $impersonationData['started_at'] : null,
        ]);
    }
}
