<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AffiliateSetting;

class AffiliateSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default affiliate settings
        AffiliateSetting::updateOrCreate(
            ['id' => 1], // Ensure only one settings record
            [
                'default_commission_rate' => 10.00, // 10%
                'minimum_withdrawal' => 50.00, // $50 minimum
                'payment_methods' => ['bank_transfer', 'paypal'],
                'terms_and_conditions' => 'Default affiliate program terms and conditions. Please update these in the admin panel.',
                'auto_approve_earnings' => false,
                'auto_approve_affiliates' => false,
                'cookie_duration_days' => 30,
                'withdrawal_fee_percentage' => 0.00,
                'withdrawal_fee_fixed' => 0.00,
                'program_active' => true,
                'welcome_message' => 'Welcome to our affiliate program! Start earning commissions by referring new customers.',
                'commission_tiers' => [
                    [
                        'min_referrals' => 0,
                        'commission_rate' => 10.00,
                        'name' => 'Bronze'
                    ],
                    [
                        'min_referrals' => 10,
                        'commission_rate' => 12.00,
                        'name' => 'Silver'
                    ],
                    [
                        'min_referrals' => 25,
                        'commission_rate' => 15.00,
                        'name' => 'Gold'
                    ],
                    [
                        'min_referrals' => 50,
                        'commission_rate' => 20.00,
                        'name' => 'Platinum'
                    ]
                ],
                'recurring_commissions' => false,
                'max_referrals_per_affiliate' => null, // No limit
            ]
        );
    }
}
