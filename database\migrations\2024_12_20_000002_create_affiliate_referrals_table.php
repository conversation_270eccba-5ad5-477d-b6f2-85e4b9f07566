<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('affiliate_referrals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('affiliate_id')->constrained('affiliates')->onDelete('cascade');
            $table->foreignId('organization_id')->constrained('organizations')->onDelete('cascade');
            $table->string('referral_code', 10);
            $table->timestamp('registration_date');
            $table->timestamp('first_payment_date')->nullable();
            $table->enum('status', ['pending', 'converted', 'cancelled'])->default('pending');
            $table->decimal('commission_earned', 10, 2)->default(0.00);
            $table->boolean('commission_paid')->default(false);
            $table->string('utm_source')->nullable();
            $table->string('utm_medium')->nullable();
            $table->string('utm_campaign')->nullable();
            $table->json('tracking_data')->nullable(); // Additional tracking info
            $table->timestamps();

            $table->index(['affiliate_id']);
            $table->index(['organization_id']);
            $table->index(['referral_code']);
            $table->index(['status']);
            $table->index(['registration_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('affiliate_referrals');
    }
};
