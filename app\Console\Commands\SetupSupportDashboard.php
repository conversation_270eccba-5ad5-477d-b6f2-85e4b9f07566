<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use App\Models\SupportTicket;
use App\Models\KnowledgeBaseCategory;
use App\Models\KnowledgeBaseArticle;
use App\Models\OrganizationCommunication;
use App\Models\User;
use App\Models\Organization;
use App\Models\SuperAdmin;

class SetupSupportDashboard extends Command
{
    protected $signature = 'setup:support-dashboard';
    protected $description = 'Create support dashboard tables and add sample data';

    public function handle()
    {
        $this->info('Setting up Support Dashboard...');

        // Create tables
        $this->createSupportTicketsTable();
        $this->createSupportTicketRepliesTable();
        $this->createOrganizationCommunicationsTable();
        $this->createKnowledgeBaseCategoriesTable();
        $this->createKnowledgeBaseArticlesTable();
        $this->createCommunicationReadReceiptsTable();
        $this->createKnowledgeBaseFeedbackTable();

        // Add sample data
        $this->addSampleData();

        $this->info('🎉 Support Dashboard setup complete!');
        $this->info('You can now access:');
        $this->info('- Support Dashboard: /super-admin/support');
        $this->info('- Support Tickets: /super-admin/support/tickets');
        $this->info('- Knowledge Base: /super-admin/support/knowledge-base');

        return 0;
    }

    private function createSupportTicketsTable()
    {
        if (!Schema::hasTable('support_tickets')) {
            $this->info('Creating support_tickets table...');
            
            Schema::create('support_tickets', function (Blueprint $table) {
                $table->id();
                $table->string('ticket_number')->unique();
                $table->string('title');
                $table->text('description');
                $table->enum('priority', ['low', 'normal', 'high', 'urgent', 'critical'])->default('normal');
                $table->enum('status', ['open', 'in_progress', 'waiting_customer', 'waiting_admin', 'resolved', 'closed'])->default('open');
                $table->enum('category', ['technical', 'billing', 'account', 'feature_request', 'bug_report', 'general'])->default('general');
                $table->unsignedBigInteger('user_id')->nullable();
                $table->unsignedBigInteger('organization_id');
                $table->unsignedBigInteger('assigned_to')->nullable();
                $table->timestamp('resolved_at')->nullable();
                $table->timestamp('first_response_at')->nullable();
                $table->json('tags')->nullable();
                $table->json('metadata')->nullable();
                $table->timestamps();

                $table->index(['status', 'priority']);
                $table->index(['organization_id', 'status']);
                $table->index(['assigned_to', 'status']);
                $table->index('created_at');
            });
        }
    }

    private function createSupportTicketRepliesTable()
    {
        if (!Schema::hasTable('support_ticket_replies')) {
            $this->info('Creating support_ticket_replies table...');
            
            Schema::create('support_ticket_replies', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('support_ticket_id');
                $table->text('message');
                $table->boolean('is_internal')->default(false);
                $table->boolean('is_solution')->default(false);
                $table->string('replier_type'); // User or SuperAdmin
                $table->unsignedBigInteger('replier_id');
                $table->json('attachments')->nullable();
                $table->json('metadata')->nullable();
                $table->timestamps();

                $table->index(['support_ticket_id', 'created_at']);
                $table->index(['replier_type', 'replier_id']);
            });
        }
    }

    private function createOrganizationCommunicationsTable()
    {
        if (!Schema::hasTable('organization_communications')) {
            $this->info('Creating organization_communications table...');
            
            Schema::create('organization_communications', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->text('message');
                $table->enum('type', ['announcement', 'maintenance', 'feature_update', 'billing_notice', 'security_alert', 'general'])->default('general');
                $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
                $table->unsignedBigInteger('organization_id');
                $table->unsignedBigInteger('sender_id');
                $table->timestamp('scheduled_at')->nullable();
                $table->timestamp('sent_at')->nullable();
                $table->enum('status', ['draft', 'scheduled', 'sent', 'cancelled'])->default('draft');
                $table->json('target_audience')->nullable();
                $table->json('metadata')->nullable();
                $table->timestamps();

                $table->index(['organization_id', 'status']);
                $table->index(['status', 'scheduled_at']);
            });
        }
    }

    private function createKnowledgeBaseCategoriesTable()
    {
        if (!Schema::hasTable('knowledge_base_categories')) {
            $this->info('Creating knowledge_base_categories table...');
            
            Schema::create('knowledge_base_categories', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('slug')->unique();
                $table->text('description')->nullable();
                $table->string('icon')->nullable();
                $table->string('color', 7)->nullable();
                $table->integer('sort_order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();

                $table->index(['is_active', 'sort_order']);
            });
        }
    }

    private function createKnowledgeBaseArticlesTable()
    {
        if (!Schema::hasTable('knowledge_base_articles')) {
            $this->info('Creating knowledge_base_articles table...');
            
            Schema::create('knowledge_base_articles', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('slug')->unique();
                $table->longText('content');
                $table->text('excerpt')->nullable();
                $table->unsignedBigInteger('category_id');
                $table->unsignedBigInteger('author_id');
                $table->enum('status', ['draft', 'published', 'archived'])->default('draft');
                $table->enum('visibility', ['public', 'customers_only', 'admins_only'])->default('public');
                $table->boolean('featured')->default(false);
                $table->integer('view_count')->default(0);
                $table->integer('helpful_count')->default(0);
                $table->integer('not_helpful_count')->default(0);
                $table->json('tags')->nullable();
                $table->json('metadata')->nullable();
                $table->timestamp('published_at')->nullable();
                $table->timestamps();

                $table->index(['status', 'visibility']);
                $table->index(['category_id', 'status']);
                $table->index(['featured', 'status']);
                $table->index('view_count');
            });
        }
    }

    private function createCommunicationReadReceiptsTable()
    {
        if (!Schema::hasTable('communication_read_receipts')) {
            $this->info('Creating communication_read_receipts table...');
            
            Schema::create('communication_read_receipts', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('organization_communication_id');
                $table->unsignedBigInteger('user_id');
                $table->timestamp('read_at');
                $table->timestamps();

                $table->unique(['organization_communication_id', 'user_id'], 'comm_read_receipt_unique');
                $table->index('read_at');
            });
        }
    }

    private function createKnowledgeBaseFeedbackTable()
    {
        if (!Schema::hasTable('knowledge_base_feedback')) {
            $this->info('Creating knowledge_base_feedback table...');
            
            Schema::create('knowledge_base_feedback', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('knowledge_base_article_id');
                $table->unsignedBigInteger('user_id')->nullable();
                $table->boolean('is_helpful');
                $table->text('comment')->nullable();
                $table->string('ip_address', 45)->nullable();
                $table->timestamps();

                $table->index(['knowledge_base_article_id', 'is_helpful']);
                $table->index('user_id');
            });
        }
    }

    private function addSampleData()
    {
        $this->info('Adding sample data...');

        // Create knowledge base categories
        $categories = [
            ['name' => 'Getting Started', 'icon' => 'fas fa-play', 'color' => '#28a745'],
            ['name' => 'Account Management', 'icon' => 'fas fa-user-cog', 'color' => '#007bff'],
            ['name' => 'Billing & Payments', 'icon' => 'fas fa-credit-card', 'color' => '#ffc107'],
            ['name' => 'Technical Support', 'icon' => 'fas fa-tools', 'color' => '#dc3545'],
            ['name' => 'Features & Updates', 'icon' => 'fas fa-star', 'color' => '#6f42c1'],
        ];

        foreach ($categories as $index => $categoryData) {
            KnowledgeBaseCategory::firstOrCreate(
                ['slug' => \Illuminate\Support\Str::slug($categoryData['name'])],
                array_merge($categoryData, ['sort_order' => $index + 1])
            );
        }

        // Create sample knowledge base articles
        $admin = SuperAdmin::first();
        if ($admin) {
            $articles = [
                [
                    'title' => 'How to Create Your First Order',
                    'content' => 'This guide will walk you through creating your first order in the system...',
                    'category' => 'Getting Started',
                    'status' => 'published',
                ],
                [
                    'title' => 'Managing User Accounts',
                    'content' => 'Learn how to add, edit, and manage user accounts in your organization...',
                    'category' => 'Account Management',
                    'status' => 'published',
                ],
                [
                    'title' => 'Understanding Your Billing Cycle',
                    'content' => 'This article explains how billing works and when payments are processed...',
                    'category' => 'Billing & Payments',
                    'status' => 'published',
                ],
            ];

            foreach ($articles as $articleData) {
                $category = KnowledgeBaseCategory::where('name', $articleData['category'])->first();
                if ($category) {
                    KnowledgeBaseArticle::firstOrCreate(
                        ['title' => $articleData['title']],
                        [
                            'content' => $articleData['content'],
                            'category_id' => $category->id,
                            'author_id' => $admin->id,
                            'status' => $articleData['status'],
                            'published_at' => now(),
                            'view_count' => rand(10, 100),
                            'helpful_count' => rand(5, 20),
                            'not_helpful_count' => rand(0, 3),
                        ]
                    );
                }
            }
        }

        // Create sample support tickets
        $users = User::limit(3)->get();
        $organizations = Organization::limit(2)->get();

        if ($users->isNotEmpty() && $organizations->isNotEmpty()) {
            $tickets = [
                [
                    'title' => 'Unable to create new orders',
                    'description' => 'I am getting an error when trying to create a new order. The page shows "Internal Server Error".',
                    'priority' => 'high',
                    'category' => 'technical',
                    'status' => 'open',
                ],
                [
                    'title' => 'Billing question about subscription',
                    'description' => 'I need clarification about my current subscription plan and billing cycle.',
                    'priority' => 'normal',
                    'category' => 'billing',
                    'status' => 'in_progress',
                ],
                [
                    'title' => 'Feature request: Export functionality',
                    'description' => 'It would be great to have an export feature for order data.',
                    'priority' => 'low',
                    'category' => 'feature_request',
                    'status' => 'open',
                ],
            ];

            foreach ($tickets as $ticketData) {
                $user = $users->random();
                $organization = $organizations->random();

                SupportTicket::firstOrCreate(
                    ['title' => $ticketData['title']],
                    array_merge($ticketData, [
                        'ticket_number' => SupportTicket::generateTicketNumber(),
                        'user_id' => $user->id,
                        'organization_id' => $organization->id,
                        'assigned_to' => $admin?->id,
                    ])
                );
            }
        }

        $this->info('✅ Sample data added successfully!');
    }
}
