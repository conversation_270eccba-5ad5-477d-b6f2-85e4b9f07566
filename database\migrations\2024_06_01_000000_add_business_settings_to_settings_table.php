<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('settings', function (Blueprint $table) {
            // Business Details
            $table->string('business_registration_number')->nullable();
            $table->string('tax_identification_number')->nullable();

            // Default Department Options (comma-separated list)
            $table->text('default_departments')->nullable();

            // Default Payment Terms
            $table->boolean('require_payment_upfront')->default(false);
            $table->integer('default_payment_due_days')->default(0);

            // Notification Settings
            $table->boolean('order_confirmation_emails')->default(true);
            $table->boolean('order_status_update_emails')->default(true);
            $table->boolean('payment_reminder_emails')->default(true);

            // Thermal Printer Settings
            $table->string('thermal_printer_name')->nullable();
            $table->integer('thermal_paper_width')->default(80); // millimeters

            // Currency Settings
            $table->string('currency_symbol')->default('₦');
            $table->string('currency_code')->default('NGN');

            // Order Number Settings
            $table->string('order_number_prefix')->default('ORD-');
            $table->boolean('include_year_in_order_number')->default(false);
        });
    }

    public function down()
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->dropColumn([
                'business_registration_number',
                'tax_identification_number',
                'default_departments',
                'require_payment_upfront',
                'default_payment_due_days',
                'order_confirmation_emails',
                'order_status_update_emails',
                'payment_reminder_emails',
                'thermal_printer_name',
                'thermal_paper_width',
                'currency_symbol',
                'currency_code',
                'order_number_prefix',
                'include_year_in_order_number'
            ]);
        });
    }
};
