<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OrderPaymentUpdateRequest extends FormRequest
{
    public function authorize()
    {
        return $this->user()->hasAnyRole(['Organization Owner', 'Account'])
            && $this->user()->organization_id === $this->route('order')->organization_id;
    }

    public function rules()
    {
        $order = $this->route('order');
        
        return [
            'additional_payment' => "required|numeric|min:0|max:{$order->pending_payment}"
        ];
    }
}