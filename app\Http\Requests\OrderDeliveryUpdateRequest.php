<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OrderDeliveryUpdateRequest extends FormRequest
{
    public function authorize()
    {
        return $this->user()->hasAnyRole(['Organization Owner', 'Delivery'])
            && $this->user()->organization_id === $this->route('order')->organization_id;
    }

    public function rules()
    {
        return [
            'receiver_name' => 'required|string|max:255',
            'receiver_phone' => 'required|string|max:20'
        ];
    }
}