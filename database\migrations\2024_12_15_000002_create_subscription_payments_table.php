<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_payments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('subscription_id'); // Will add foreign key later
            $table->unsignedBigInteger('organization_id'); // Will add foreign key later
            $table->string('payment_reference')->nullable(); // Bank transfer reference, receipt number
            $table->decimal('amount', 10, 2);
            $table->string('payment_method')->default('manual'); // manual, bank_transfer, cash, etc.
            $table->string('status')->default('pending'); // pending, approved, rejected
            $table->text('notes')->nullable(); // Admin notes about the payment
            $table->dateTime('payment_date'); // When customer made the payment
            $table->dateTime('approved_at')->nullable(); // When admin approved
            $table->unsignedBigInteger('approved_by')->nullable(); // Super admin who approved (no foreign key constraint)
            $table->string('invoice_number')->nullable(); // Generated invoice number
            $table->dateTime('invoice_generated_at')->nullable();
            $table->timestamps();

            // Indexes for better performance
            $table->index(['subscription_id', 'status']);
            $table->index(['organization_id', 'payment_date']);
            $table->index('payment_reference');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_payments');
    }
};
