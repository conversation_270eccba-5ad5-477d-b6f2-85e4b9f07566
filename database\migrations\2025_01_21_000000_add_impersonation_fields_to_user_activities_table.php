<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_activities', function (Blueprint $table) {
            $table->boolean('is_impersonated')->default(false)->after('details');
            $table->unsignedBigInteger('impersonated_by')->nullable()->after('is_impersonated');
            $table->string('impersonation_session_id')->nullable()->after('impersonated_by');
            $table->timestamp('impersonation_started_at')->nullable()->after('impersonation_session_id');
            
            // Add index for better performance
            $table->index(['is_impersonated', 'impersonated_by']);
            $table->index('impersonation_session_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_activities', function (Blueprint $table) {
            $table->dropIndex(['is_impersonated', 'impersonated_by']);
            $table->dropIndex(['impersonation_session_id']);
            $table->dropColumn([
                'is_impersonated',
                'impersonated_by',
                'impersonation_session_id',
                'impersonation_started_at'
            ]);
        });
    }
};
