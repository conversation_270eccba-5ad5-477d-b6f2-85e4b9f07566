<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function __construct()
    {
        $this->middleware('web');
    }

    public function index()
    {
        if (auth()->check()) {
            return redirect()->route('dashboard');
        }

        // Ensure session is started and CSRF token is generated
        if (!session()->isStarted()) {
            session()->start();
        }
        
        // Always regenerate token for login page
        session()->regenerateToken();

        return view('auth.login');
    }
}