<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Plan;

class UpdatePlansWithAnnualBillingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update existing plans with annual billing options
        $plans = Plan::all();
        
        foreach ($plans as $plan) {
            // Calculate annual price with 15% discount
            $monthlyPrice = $plan->price;
            $annualPrice = ($monthlyPrice * 12) * 0.85; // 15% discount
            
            $plan->update([
                'annual_price' => $annualPrice,
                'billing_period' => 'both', // Support both monthly and annual
                'annual_discount_percentage' => 15,
            ]);
            
            echo "Updated {$plan->name}: Monthly \${$monthlyPrice}, Annual \${$annualPrice} (15% discount)\n";
        }
        
        // Create sample plans if none exist
        if ($plans->count() === 0) {
            $samplePlans = [
                [
                    'name' => 'Starter',
                    'slug' => 'starter',
                    'description' => 'Perfect for small businesses getting started',
                    'price' => 29.99,
                    'annual_price' => 305.89, // 15% discount
                    'billing_period' => 'both',
                    'annual_discount_percentage' => 15,
                    'branch_limit' => 1,
                    'user_limit' => 5,
                    'data_retention_days' => 30,
                    'order_limit' => 100,
                    'thermal_printing' => false,
                    'advanced_reporting' => false,
                    'api_access' => false,
                    'white_label' => false,
                    'custom_branding' => false,
                    'is_active' => true,
                    'is_featured' => false,
                ],
                [
                    'name' => 'Professional',
                    'slug' => 'professional',
                    'description' => 'Ideal for growing businesses with advanced needs',
                    'price' => 59.99,
                    'annual_price' => 611.89, // 15% discount
                    'billing_period' => 'both',
                    'annual_discount_percentage' => 15,
                    'branch_limit' => 5,
                    'user_limit' => 25,
                    'data_retention_days' => 90,
                    'order_limit' => 500,
                    'thermal_printing' => true,
                    'advanced_reporting' => true,
                    'api_access' => false,
                    'white_label' => false,
                    'custom_branding' => false,
                    'is_active' => true,
                    'is_featured' => true,
                ],
                [
                    'name' => 'Enterprise',
                    'slug' => 'enterprise',
                    'description' => 'Complete solution for large organizations',
                    'price' => 99.99,
                    'annual_price' => 1019.89, // 15% discount
                    'billing_period' => 'both',
                    'annual_discount_percentage' => 15,
                    'branch_limit' => 999,
                    'user_limit' => 999,
                    'data_retention_days' => 999,
                    'order_limit' => null, // Unlimited
                    'thermal_printing' => true,
                    'advanced_reporting' => true,
                    'api_access' => true,
                    'white_label' => true,
                    'custom_branding' => true,
                    'is_active' => true,
                    'is_featured' => false,
                ],
            ];
            
            foreach ($samplePlans as $planData) {
                Plan::create($planData);
                echo "Created sample plan: {$planData['name']}\n";
            }
        }
        
        echo "\nAnnual billing setup completed!\n";
        echo "All plans now support both monthly and annual billing with 15% annual discount.\n";
    }
}
