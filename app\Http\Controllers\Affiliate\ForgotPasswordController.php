<?php

namespace App\Http\Controllers\Affiliate;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use Illuminate\View\View;

class ForgotPasswordController extends Controller
{
    /**
     * Display the affiliate password reset link request view.
     */
    public function create(): View
    {
        return view('affiliate.forgot-password');
    }

    /**
     * Handle an incoming affiliate password reset link request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        // Log the request for debugging
        \Illuminate\Support\Facades\Log::info('Affiliate password reset request', [
            'email' => $request->email,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        $request->validate([
            'email' => ['required', 'email'],
        ]);

        // Check if the email belongs to an affiliate user
        $user = \App\Models\User::where('email', $request->email)->first();

        if (!$user) {
            \Illuminate\Support\Facades\Log::warning('Affiliate password reset: User not found', ['email' => $request->email]);
            return back()->withInput($request->only('email'))
                        ->withErrors(['email' => 'We can\'t find a user with that email address.']);
        }

        // Check if user has an affiliate account
        $affiliate = \App\Models\Affiliate::where('user_id', $user->id)->first();

        if (!$affiliate) {
            \Illuminate\Support\Facades\Log::warning('Affiliate password reset: Not an affiliate', ['email' => $request->email, 'user_id' => $user->id]);
            return back()->withInput($request->only('email'))
                        ->withErrors(['email' => 'This email is not registered as an affiliate. Please use the main login page.']);
        }

        \Illuminate\Support\Facades\Log::info('Affiliate password reset: Sending email', [
            'email' => $request->email,
            'user_id' => $user->id,
            'affiliate_id' => $affiliate->id,
            'affiliate_code' => $affiliate->referral_code
        ]);

        // Send the password reset link using the affiliate broker
        $status = Password::broker('affiliates')->sendResetLink(
            $request->only('email')
        );

        \Illuminate\Support\Facades\Log::info('Affiliate password reset: Status', [
            'email' => $request->email,
            'status' => $status,
            'success' => $status == Password::RESET_LINK_SENT
        ]);

        return $status == Password::RESET_LINK_SENT
                    ? back()->with('status', __($status))
                    : back()->withInput($request->only('email'))
                        ->withErrors(['email' => __($status)]);
    }
}
