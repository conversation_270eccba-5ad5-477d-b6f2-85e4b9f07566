<?php

namespace App\Services;

use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Organization;
use Carbon\Carbon;

class ProrationService
{
    /**
     * Calculate proration for plan change
     */
    public function calculatePlanChange(Subscription $currentSubscription, Plan $newPlan, int $newBillingPeriodMonths = 1): array
    {
        $currentPlan = $currentSubscription->plan;
        $today = Carbon::now();
        $subscriptionEnd = $currentSubscription->end_date;

        // Calculate remaining days in current subscription
        $remainingDays = $today->diffInDays($subscriptionEnd, false);

        // If subscription has already expired, no proration needed
        if ($remainingDays <= 0) {
            $newPlanPrice = $newPlan->getPriceForPeriod($newBillingPeriodMonths);
            return [
                'type' => 'new_subscription',
                'current_plan_credit' => 0,
                'new_plan_charge' => $newPlanPrice,
                'net_amount' => $newPlanPrice,
                'remaining_days' => 0,
                'billing_period_months' => $newBillingPeriodMonths,
                'proration_details' => 'Subscription expired - full charge for new plan'
            ];
        }

        // If current plan is free (price = 0), treat as new subscription - no proration needed
        if ($currentPlan->isFree()) {
            $newPlanPrice = $newPlan->getPriceForPeriod($newBillingPeriodMonths);
            return [
                'type' => 'new_subscription',
                'current_plan_credit' => 0,
                'new_plan_charge' => $newPlanPrice,
                'net_amount' => $newPlanPrice,
                'remaining_days' => $remainingDays,
                'billing_period_months' => $newBillingPeriodMonths,
                'proration_details' => 'Upgrading from free plan - full charge for new plan'
            ];
        }

        // Calculate daily rates based on current subscription period
        $currentPeriodDays = $currentSubscription->getPeriodInMonths() * 30;
        $currentPlanPrice = $currentSubscription->getCurrentPeriodAmount();
        $currentDailyRate = $currentPlanPrice > 0 ? $currentPlanPrice / $currentPeriodDays : 0;

        // Calculate new plan daily rate based on selected billing period
        $newPlanPrice = $newPlan->getPriceForPeriod($newBillingPeriodMonths);
        $newPeriodDays = $newBillingPeriodMonths * 30;
        $newDailyRate = $newPlanPrice > 0 ? $newPlanPrice / $newPeriodDays : 0;

        // Calculate credit for unused portion of current plan
        $currentPlanCredit = $remainingDays * $currentDailyRate;

        // Calculate charge for new plan for remaining period
        $newPlanCharge = $remainingDays * $newDailyRate;

        // Net amount (positive = charge, negative = credit)
        $netAmount = $newPlanCharge - $currentPlanCredit;

        $type = $this->determineChangeType($currentPlan, $newPlan);

        return [
            'type' => $type,
            'current_plan_credit' => round($currentPlanCredit, 2),
            'new_plan_charge' => round($newPlanCharge, 2),
            'net_amount' => round($netAmount, 2),
            'remaining_days' => $remainingDays,
            'current_daily_rate' => round($currentDailyRate, 2),
            'new_daily_rate' => round($newDailyRate, 2),
            'billing_period_months' => $newBillingPeriodMonths,
            'new_plan_price' => $newPlanPrice,
            'proration_details' => $this->generateProrationDetails($currentPlan, $newPlan, $remainingDays, $netAmount, $newBillingPeriodMonths)
        ];
    }

    /**
     * Calculate immediate upgrade cost
     */
    public function calculateImmediateUpgrade(Organization $organization, Plan $newPlan): array
    {
        $activeSubscription = $organization->activeSubscription;

        if (!$activeSubscription) {
            return [
                'type' => 'new_subscription',
                'immediate_charge' => $newPlan->price,
                'next_billing_date' => Carbon::now()->addMonth(),
                'details' => 'New subscription - full monthly charge'
            ];
        }

        $proration = $this->calculatePlanChange($activeSubscription, $newPlan);

        return [
            'type' => 'upgrade',
            'immediate_charge' => max(0, $proration['net_amount']), // Only charge if upgrade
            'credit_applied' => $proration['current_plan_credit'],
            'next_billing_date' => $activeSubscription->end_date,
            'details' => $proration['proration_details']
        ];
    }

    /**
     * Calculate downgrade credit
     */
    public function calculateDowngrade(Subscription $currentSubscription, Plan $newPlan): array
    {
        $proration = $this->calculatePlanChange($currentSubscription, $newPlan);

        // For downgrades, apply credit to next billing cycle
        $creditAmount = abs(min(0, $proration['net_amount']));

        return [
            'type' => 'downgrade',
            'credit_amount' => $creditAmount,
            'immediate_charge' => 0,
            'next_billing_amount' => max(0, $newPlan->price - $creditAmount),
            'next_billing_date' => $currentSubscription->end_date,
            'details' => "Downgrade credit of $" . number_format($creditAmount, 2) . " will be applied to next billing cycle"
        ];
    }

    /**
     * Process plan change with proration
     */
    public function processPlanChange(Organization $organization, Plan $newPlan, string $changeType = 'immediate'): array
    {
        $activeSubscription = $organization->activeSubscription;

        if (!$activeSubscription) {
            // Create new subscription
            return $this->createNewSubscription($organization, $newPlan);
        }

        $currentPlan = $activeSubscription->plan;
        $proration = $this->calculatePlanChange($activeSubscription, $newPlan);

        switch ($changeType) {
            case 'immediate':
                return $this->processImmediateChange($organization, $activeSubscription, $newPlan, $proration);

            case 'end_of_cycle':
                return $this->scheduleEndOfCycleChange($activeSubscription, $newPlan);

            default:
                throw new \InvalidArgumentException("Invalid change type: {$changeType}");
        }
    }

    /**
     * Determine if change is upgrade or downgrade
     */
    private function determineChangeType(Plan $currentPlan, Plan $newPlan): string
    {
        if ($newPlan->price > $currentPlan->price) {
            return 'upgrade';
        } elseif ($newPlan->price < $currentPlan->price) {
            return 'downgrade';
        } else {
            return 'lateral'; // Same price, different features
        }
    }

    /**
     * Generate human-readable proration details
     */
    private function generateProrationDetails(Plan $currentPlan, Plan $newPlan, int $remainingDays, float $netAmount, int $newBillingPeriodMonths = 1): string
    {
        $type = $this->determineChangeType($currentPlan, $newPlan);

        $billingLabel = $newBillingPeriodMonths === 1 ? 'monthly' : "{$newBillingPeriodMonths} months";
        $details = "Plan change from {$currentPlan->name} to {$newPlan->name} ({$billingLabel} billing) with {$remainingDays} days remaining. ";

        if ($netAmount > 0) {
            $details .= "Additional charge of " . format_price($netAmount) . " for the upgrade.";
        } elseif ($netAmount < 0) {
            $details .= "Credit of " . format_price(abs($netAmount)) . " for the downgrade.";
        } else {
            $details .= "No additional charge or credit required.";
        }

        return $details;
    }

    /**
     * Create new subscription
     */
    private function createNewSubscription(Organization $organization, Plan $newPlan, int $billingPeriodMonths = 1): array
    {
        $startDate = Carbon::now();
        $endDate = $startDate->copy()->addMonths($billingPeriodMonths);
        $planPrice = $newPlan->getPriceForPeriod($billingPeriodMonths);

        $subscription = Subscription::create([
            'organization_id' => $organization->id,
            'plan_id' => $newPlan->id,
            'status' => 'active',
            'billing_period_months' => $billingPeriodMonths,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'amount_paid' => $planPrice,
            'auto_renew' => true,
        ]);

        // Update organization plan
        $organization->update(['plan_id' => $newPlan->id]);

        return [
            'success' => true,
            'subscription' => $subscription,
            'charge_amount' => $planPrice,
            'billing_period' => $billingPeriod,
            'type' => 'new_subscription',
            'message' => "New {$billingPeriod} subscription created for {$newPlan->name} plan"
        ];
    }

    /**
     * Process immediate plan change
     */
    private function processImmediateChange(Organization $organization, Subscription $activeSubscription, Plan $newPlan, array $proration): array
    {
        // Update current subscription to reflect plan change
        $activeSubscription->update([
            'plan_id' => $newPlan->id,
            'amount_paid' => $activeSubscription->amount_paid + $proration['net_amount'],
        ]);

        // Update organization plan
        $organization->update(['plan_id' => $newPlan->id]);

        return [
            'success' => true,
            'subscription' => $activeSubscription->fresh(),
            'charge_amount' => max(0, $proration['net_amount']),
            'credit_amount' => abs(min(0, $proration['net_amount'])),
            'type' => $proration['type'],
            'proration' => $proration,
            'message' => "Plan changed to {$newPlan->name} with proration applied"
        ];
    }

    /**
     * Schedule plan change for end of billing cycle
     */
    private function scheduleEndOfCycleChange(Subscription $activeSubscription, Plan $newPlan): array
    {
        // Store the scheduled change (you might want to create a separate table for this)
        $activeSubscription->update([
            'scheduled_plan_id' => $newPlan->id,
            'scheduled_change_date' => $activeSubscription->end_date,
        ]);

        return [
            'success' => true,
            'subscription' => $activeSubscription,
            'charge_amount' => 0,
            'type' => 'scheduled_change',
            'effective_date' => $activeSubscription->end_date,
            'message' => "Plan change to {$newPlan->name} scheduled for {$activeSubscription->end_date->format('M j, Y')}"
        ];
    }

    /**
     * Calculate refund amount for cancellation
     */
    public function calculateCancellationRefund(Subscription $subscription): array
    {
        $today = Carbon::now();
        $subscriptionEnd = $subscription->end_date;
        $subscriptionStart = $subscription->start_date;

        $totalDays = $subscriptionStart->diffInDays($subscriptionEnd);
        $usedDays = $subscriptionStart->diffInDays($today);
        $remainingDays = max(0, $today->diffInDays($subscriptionEnd, false));

        $dailyRate = $subscription->amount_paid / $totalDays;
        $refundAmount = $remainingDays * $dailyRate;

        return [
            'refund_amount' => round($refundAmount, 2),
            'used_days' => $usedDays,
            'remaining_days' => $remainingDays,
            'daily_rate' => round($dailyRate, 2),
            'total_paid' => $subscription->amount_paid,
            'refund_percentage' => round(($refundAmount / $subscription->amount_paid) * 100, 1)
        ];
    }
}
