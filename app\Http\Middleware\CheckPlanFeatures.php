<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckPlanFeatures
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $feature
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, string $feature): Response
    {
        $user = Auth::user();

        if (!$user || !$user->organization) {
            return redirect()->route('login')->with('error', 'Please log in to access this feature.');
        }

        $organization = $user->organization;

        // Check if organization is active
        if (!$organization->is_active) {
            return redirect()->route('dashboard')->with('error', 'Your organization account is inactive. Please contact support.');
        }

        // Check if organization has any form of access
        if (!$organization->hasAccess()) {
            return redirect()->route('dashboard')->with('error', 'Your subscription has expired and grace period has ended. Please renew to access this feature.');
        }

        // Restrict certain features during grace period
        if ($organization->isInGracePeriod()) {
            $restrictedFeatures = ['api_access', 'advanced_reporting', 'thermal_printing'];
            if (in_array($feature, $restrictedFeatures)) {
                return redirect()->route('dashboard')->with('error', "This feature is not available during the grace period. Please renew your subscription to access {$feature}.");
            }
        }

        // Check if the organization's plan has the required feature
        if (!$organization->hasFeature($feature)) {
            return redirect()->route('dashboard')->with('error', "This feature is not available in your current plan. Please upgrade to access {$feature}.");
        }

        return $next($request);
    }
}
