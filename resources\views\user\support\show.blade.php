@extends('layouts.app')

@section('title', 'Ticket #' . $ticket->ticket_number)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ route('user.support.index') }}">Support Center</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Ticket #{{ $ticket->ticket_number }}</li>
                </ol>
            </nav>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Ticket #{{ $ticket->ticket_number }}</h1>
                    <p class="text-muted mb-0">{{ $ticket->title }}</p>
                </div>
                <a href="{{ route('user.support.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Support
                </a>
            </div>

            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <!-- Ticket Details -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Ticket Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>Status:</strong> {!! $ticket->status_badge !!}
                                </div>
                                <div class="col-md-6">
                                    <strong>Priority:</strong> {!! $ticket->priority_badge !!}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>Category:</strong> 
                                    <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $ticket->category)) }}</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>Created:</strong> {{ $ticket->created_at->format('M d, Y H:i') }}
                                </div>
                            </div>
                            @if($ticket->assignedAdmin)
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>Assigned To:</strong> 
                                    <span class="badge bg-info">{{ $ticket->assignedAdmin->name }}</span>
                                </div>
                                @if($ticket->resolved_at)
                                <div class="col-md-6">
                                    <strong>Resolved:</strong> {{ $ticket->resolved_at->format('M d, Y H:i') }}
                                </div>
                                @endif
                            </div>
                            @endif
                            <div class="mb-3">
                                <strong>Description:</strong>
                                <div class="mt-2 p-3 bg-light rounded">
                                    {!! nl2br(e($ticket->description)) !!}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Conversation -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-comments me-2"></i>
                                Conversation ({{ $ticket->replies->where('is_internal', false)->count() }} replies)
                            </h5>
                        </div>
                        <div class="card-body">
                            @php
                                $publicReplies = $ticket->replies->where('is_internal', false);
                            @endphp
                            
                            @if($publicReplies->count() > 0)
                                @foreach($publicReplies as $reply)
                                <div class="d-flex mb-4">
                                    <div class="flex-shrink-0 me-3">
                                        <div class="bg-{{ $reply->is_from_admin ? 'primary' : 'success' }} text-white rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            {{ $reply->replier_avatar }}
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <strong>{{ $reply->replier_name }}</strong>
                                                <span class="badge bg-{{ $reply->is_from_admin ? 'primary' : 'success' }} ms-2">
                                                    {{ $reply->is_from_admin ? 'Support Team' : 'You' }}
                                                </span>
                                                @if($reply->is_solution)
                                                    <span class="badge bg-warning ms-1">
                                                        <i class="fas fa-check"></i> Solution
                                                    </span>
                                                @endif
                                            </div>
                                            <small class="text-muted">{{ $reply->created_at->format('M d, Y H:i') }}</small>
                                        </div>
                                        <div class="message-content">
                                            {!! $reply->formatted_message !!}
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No replies yet. Our support team will respond soon!</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Add Reply -->
                    @if($ticket->status !== 'closed')
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-reply me-2"></i>
                                Add Reply
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('user.support.reply', $ticket) }}">
                                @csrf
                                <div class="mb-3">
                                    <label for="message" class="form-label">Your Message</label>
                                    <textarea name="message" id="message" class="form-control @error('message') is-invalid @enderror" 
                                              rows="5" placeholder="Type your reply here..." required></textarea>
                                    @error('message')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        You'll receive an email notification when our team responds.
                                    </small>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i> Send Reply
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    @else
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        This ticket has been closed. If you need further assistance, please create a new ticket.
                    </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Ticket Status -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info me-2"></i>
                                Ticket Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>Ticket Number:</strong><br>
                                <code>{{ $ticket->ticket_number }}</code>
                            </div>
                            <div class="mb-2">
                                <strong>Status:</strong><br>
                                {!! $ticket->status_badge !!}
                            </div>
                            <div class="mb-2">
                                <strong>Priority:</strong><br>
                                {!! $ticket->priority_badge !!}
                            </div>
                            <div class="mb-2">
                                <strong>Category:</strong><br>
                                <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $ticket->category)) }}</span>
                            </div>
                            <div class="mb-2">
                                <strong>Created:</strong><br>
                                {{ $ticket->created_at->format('M d, Y H:i') }}<br>
                                <small class="text-muted">{{ $ticket->created_at->diffForHumans() }}</small>
                            </div>
                            @if($ticket->first_response_at)
                            <div class="mb-2">
                                <strong>First Response:</strong><br>
                                {{ $ticket->first_response_at->format('M d, Y H:i') }}<br>
                                <small class="text-muted">{{ $ticket->response_time }}h response time</small>
                            </div>
                            @endif
                            @if($ticket->resolved_at)
                            <div class="mb-2">
                                <strong>Resolved:</strong><br>
                                {{ $ticket->resolved_at->format('M d, Y H:i') }}<br>
                                <small class="text-muted">{{ $ticket->resolution_time }}h resolution time</small>
                            </div>
                            @endif
                            <div class="mb-2">
                                <strong>Last Updated:</strong><br>
                                {{ $ticket->updated_at->format('M d, Y H:i') }}<br>
                                <small class="text-muted">{{ $ticket->updated_at->diffForHumans() }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Expected Response Time -->
                    @if($ticket->status !== 'resolved' && $ticket->status !== 'closed')
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Expected Response Time
                            </h6>
                        </div>
                        <div class="card-body text-center">
                            @php
                                $expectedHours = match($ticket->priority) {
                                    'urgent', 'critical' => 4,
                                    'high' => 12,
                                    default => 24
                                };
                            @endphp
                            <div class="h4 mb-0 text-primary">{{ $expectedHours }}h</div>
                            <small class="text-muted">During business hours</small>
                            <hr>
                            <p class="text-muted small mb-0">
                                Business hours: 9 AM - 6 PM, Monday - Friday
                            </p>
                        </div>
                    </div>
                    @endif

                    <!-- Quick Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('user.support.create') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-plus me-2"></i>Create New Ticket
                                </a>
                                <a href="{{ route('user.support.knowledge-base') }}" class="btn btn-outline-info">
                                    <i class="fas fa-book me-2"></i>Browse Knowledge Base
                                </a>
                                <a href="{{ route('user.support.search') }}" class="btn btn-outline-success">
                                    <i class="fas fa-search me-2"></i>Search Help Articles
                                </a>
                                <a href="{{ route('user.support.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-home me-2"></i>Support Center
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Help -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-question-circle me-2"></i>
                                Need More Help?
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="text-muted small">
                                If this ticket doesn't resolve your issue, you can always create a new ticket 
                                or search our knowledge base for additional help.
                            </p>
                            <div class="d-grid">
                                <a href="{{ route('user.support.create') }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-envelope me-2"></i>Create New Ticket
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.message-content {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textarea
    const textarea = document.getElementById('message');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    }
});
</script>
@endsection
