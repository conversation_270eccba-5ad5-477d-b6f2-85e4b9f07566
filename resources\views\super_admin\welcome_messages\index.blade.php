@extends('super_admin.layouts.app')

@section('title', 'Welcome Messages')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Welcome Messages</h1>
                <div class="btn-group" role="group">
                    <a href="{{ route('super_admin.welcome-messages.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Message
                    </a>
                    <form method="POST" action="{{ route('super_admin.welcome-messages.initialize-defaults') }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-outline-secondary" 
                                onclick="return confirm('This will create default welcome messages for all user types. Continue?')">
                            <i class="fas fa-magic"></i> Initialize Defaults
                        </button>
                    </form>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error') || $errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') ?? $errors->first() }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Manage Welcome Email Templates</h5>
                </div>
                <div class="card-body">
                    @if($messages->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>User Type</th>
                                        <th>Subject</th>
                                        <th>Status</th>
                                        <th>Created By</th>
                                        <th>Last Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($messages as $message)
                                        <tr>
                                            <td>
                                                <span class="badge bg-info">{{ $message->formatted_user_type }}</span>
                                            </td>
                                            <td>
                                                <strong>{{ $message->subject }}</strong>
                                                <br>
                                                <small class="text-muted">{{ Str::limit($message->greeting, 50) }}</small>
                                            </td>
                                            <td>
                                                @if($message->is_active)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-secondary">Inactive</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($message->creator)
                                                    {{ $message->creator->name }}
                                                    <br>
                                                    <small class="text-muted">{{ $message->created_at->format('M d, Y') }}</small>
                                                @else
                                                    <span class="text-muted">System</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($message->updater)
                                                    {{ $message->updater->name }}
                                                    <br>
                                                @endif
                                                <small class="text-muted">{{ $message->updated_at->format('M d, Y g:i A') }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('super_admin.welcome-messages.show', $message) }}" 
                                                       class="btn btn-sm btn-outline-info" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('super_admin.welcome-messages.preview', $message) }}" 
                                                       class="btn btn-sm btn-outline-primary" title="Preview">
                                                        <i class="fas fa-search"></i>
                                                    </a>
                                                    <a href="{{ route('super_admin.welcome-messages.edit', $message) }}" 
                                                       class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form method="POST" action="{{ route('super_admin.welcome-messages.toggle-status', $message) }}" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-sm btn-outline-{{ $message->is_active ? 'danger' : 'success' }}"
                                                                title="{{ $message->is_active ? 'Deactivate' : 'Activate' }}"
                                                                onclick="return confirm('{{ $message->is_active ? 'Deactivate' : 'Activate' }} this message?')">
                                                            <i class="fas fa-{{ $message->is_active ? 'ban' : 'check' }}"></i>
                                                        </button>
                                                    </form>
                                                    <form method="POST" action="{{ route('super_admin.welcome-messages.destroy', $message) }}" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete"
                                                                onclick="return confirm('Are you sure you want to delete this welcome message?')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                            <h5>No Welcome Messages</h5>
                            <p class="text-muted">Create welcome email templates for different user types.</p>
                            <div class="mt-3">
                                <a href="{{ route('super_admin.welcome-messages.create') }}" class="btn btn-primary me-2">
                                    <i class="fas fa-plus"></i> Create Message
                                </a>
                                <form method="POST" action="{{ route('super_admin.welcome-messages.initialize-defaults') }}" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-outline-secondary"
                                            onclick="return confirm('This will create default welcome messages for all user types. Continue?')">
                                        <i class="fas fa-magic"></i> Initialize Defaults
                                    </button>
                                </form>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            @if($messages->count() > 0)
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Organization Users</h5>
                                @php $orgMessage = $messages->where('user_type', 'organization')->first(); @endphp
                                @if($orgMessage)
                                    <span class="badge bg-{{ $orgMessage->is_active ? 'success' : 'secondary' }}">
                                        {{ $orgMessage->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                @else
                                    <span class="badge bg-warning">Not Set</span>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Affiliate Users</h5>
                                @php $affMessage = $messages->where('user_type', 'affiliate')->first(); @endphp
                                @if($affMessage)
                                    <span class="badge bg-{{ $affMessage->is_active ? 'success' : 'secondary' }}">
                                        {{ $affMessage->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                @else
                                    <span class="badge bg-warning">Not Set</span>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Super Admin Users</h5>
                                @php $adminMessage = $messages->where('user_type', 'super_admin')->first(); @endphp
                                @if($adminMessage)
                                    <span class="badge bg-{{ $adminMessage->is_active ? 'success' : 'secondary' }}">
                                        {{ $adminMessage->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                @else
                                    <span class="badge bg-warning">Not Set</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
