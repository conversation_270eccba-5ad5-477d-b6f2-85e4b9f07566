@extends('layouts.settings')

@section('title', 'App Settings')

@section('app_settings')
<h2 class="text-2xl font-bold mb-6">Application Settings</h2>

<form action="{{ route('settings.update') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
    @csrf
    @method('PATCH')

    <!-- Theme Mode -->
    <div>
        <label for="theme_mode" class="block text-sm font-medium text-gray-700">Theme Mode</label>
        <select name="theme_mode" id="theme_mode"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
            <option value="light" {{ old('theme_mode', $setting->theme_mode) === 'light' ? 'selected' : '' }}>Light</option>
            <option value="dark" {{ old('theme_mode', $setting->theme_mode) === 'dark' ? 'selected' : '' }}>Dark</option>
        </select>
        @error('theme_mode')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>

    <!-- Primary Color -->
    <div>
        <label for="primary_color" class="block text-sm font-medium text-gray-700">Primary Color</label>
        <div class="mt-1 flex items-center space-x-2">
            <input type="color" name="primary_color" id="primary_color"
                class="h-10 w-20 p-1 rounded border border-gray-300"
                value="{{ old('primary_color', $setting->primary_color) }}">
            <input type="text" id="primary_color_hex"
                class="block w-32 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                value="{{ old('primary_color', $setting->primary_color) }}"
                readonly>
        </div>
        @error('primary_color')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>

    <!-- Sidebar Color -->
    <div>
        <label for="sidebar_color" class="block text-sm font-medium text-gray-700">Sidebar Color</label>
        <div class="mt-1 flex items-center space-x-2">
            <input type="color" name="sidebar_color" id="sidebar_color"
                class="h-10 w-20 p-1 rounded border border-gray-300"
                value="{{ old('sidebar_color', $setting->sidebar_color) }}">
            <input type="text" id="sidebar_color_hex"
                class="block w-32 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                value="{{ old('sidebar_color', $setting->sidebar_color) }}"
                readonly>
        </div>
        @error('sidebar_color')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>

    <!-- Preview Section -->
    <div class="mt-8 border rounded-lg bg-gray-50">
        <h3 class="text-lg font-medium text-gray-900 p-4 border-b">Preview</h3>

        <!-- Sidebar Preview -->
        <div class="p-4 space-y-6">
            <div class="w-64 border rounded-lg overflow-hidden">
                <!-- Header Preview -->
                <div class="p-4" style="background-color: {{ $setting->sidebar_color }}">
                    <!-- Color Preview -->
                    <div class="flex justify-center">
                        <div class="w-16 h-16 rounded-full" style="background-color: {{ $setting->primary_color }}"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="flex justify-end pt-6">
        <button type="submit"
            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            Save Settings
        </button>
    </div>
</form>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update hex input when color picker changes
    function setupColorPicker(colorId, hexId) {
        const colorPicker = document.getElementById(colorId);
        const hexInput = document.getElementById(hexId);

        colorPicker.addEventListener('input', function(e) {
            hexInput.value = e.target.value.toUpperCase();
            updatePreview();
        });
    }

    setupColorPicker('primary_color', 'primary_color_hex');
    setupColorPicker('sidebar_color', 'sidebar_color_hex');

    // Update preview
    function updatePreview() {
        const sidebarPreview = document.querySelector('.rounded-lg .p-4');
        const primaryColorPreview = document.querySelector('.rounded-full');
        const primaryColor = document.getElementById('primary_color').value;
        const sidebarColor = document.getElementById('sidebar_color').value;

        if (sidebarPreview) sidebarPreview.style.backgroundColor = sidebarColor;
        if (primaryColorPreview) primaryColorPreview.style.backgroundColor = primaryColor;
    }
});
</script>
@endpush
