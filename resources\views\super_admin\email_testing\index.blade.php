@extends('super_admin.layouts.app')

@section('title', 'Email Testing Dashboard')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">📧 Email Testing Dashboard</h1>
                <div class="btn-group" role="group">
                    <a href="{{ route('super_admin.email-testing.mailtrap-diagnostics') }}" class="btn btn-outline-danger">
                        <i class="fas fa-stethoscope"></i> Mailtrap Diagnostics
                    </a>
                    <a href="{{ route('super_admin.email-testing.system-check') }}" class="btn btn-outline-warning">
                        <i class="fas fa-heartbeat"></i> System Check
                    </a>
                    <a href="{{ route('super_admin.email-testing.auth-emails') }}" class="btn btn-outline-primary">
                        <i class="fas fa-shield-alt"></i> Auth Email Testing
                    </a>
                    <a href="{{ route('super_admin.email-testing.account-creation') }}" class="btn btn-outline-success">
                        <i class="fas fa-user-plus"></i> Account Creation Testing
                    </a>
                    <a href="{{ route('super_admin.welcome-messages.index') }}" class="btn btn-outline-info">
                        <i class="fas fa-envelope"></i> Manage Welcome Messages
                    </a>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle"></i> 
                    @foreach($errors->all() as $error)
                        {{ $error }}<br>
                    @endforeach
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('test_results'))
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <h5><i class="fas fa-test-tube"></i> Comprehensive Test Results</h5>
                    @foreach(session('test_results') as $result)
                        <div class="mb-2">
                            @if($result['status'] === 'success')
                                <i class="fas fa-check-circle text-success"></i>
                            @elseif($result['status'] === 'warning')
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                            @else
                                <i class="fas fa-times-circle text-danger"></i>
                            @endif
                            <strong>{{ $result['type'] }}:</strong> {{ $result['message'] }}
                        </div>
                    @endforeach
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('welcome_diagnostics'))
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <h5><i class="fas fa-stethoscope"></i> Welcome Email Diagnostics Results</h5>
                    <div class="table-responsive">
                        <table class="table table-sm mb-0">
                            <thead>
                                <tr>
                                    <th>Check</th>
                                    <th>Result</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach(session('welcome_diagnostics') as $diagnostic)
                                    <tr>
                                        <td>{{ $diagnostic['check'] }}</td>
                                        <td>
                                            <span class="badge bg-{{ $diagnostic['result'] === 'PASS' ? 'success' : 'danger' }}">
                                                {{ $diagnostic['result'] }}
                                            </span>
                                        </td>
                                        <td>{{ $diagnostic['details'] }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('registration_diagnostics'))
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <h5><i class="fas fa-user-check"></i> Affiliate Registration Diagnostics Results</h5>
                    <div class="table-responsive">
                        <table class="table table-sm mb-0">
                            <thead>
                                <tr>
                                    <th>Check</th>
                                    <th>Result</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach(session('registration_diagnostics') as $diagnostic)
                                    <tr>
                                        <td>{{ $diagnostic['check'] }}</td>
                                        <td>
                                            @php
                                                $badgeClass = match($diagnostic['result']) {
                                                    'FOUND', 'SUCCESS', 'VERIFIED', 'ACTIVE', 'ENABLED' => 'success',
                                                    'PENDING', 'DISABLED' => 'warning',
                                                    'NOT FOUND', 'FAILED', 'MISSING' => 'danger',
                                                    default => 'secondary'
                                                };
                                            @endphp
                                            <span class="badge bg-{{ $badgeClass }}">
                                                {{ $diagnostic['result'] }}
                                            </span>
                                        </td>
                                        <td>{{ $diagnostic['details'] }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('debug_info'))
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <h5><i class="fas fa-bug"></i> Verification URL Debug Information</h5>
                    <div class="table-responsive">
                        <table class="table table-sm mb-0">
                            <tr>
                                <td><strong>User ID:</strong></td>
                                <td>{{ session('debug_info')['user_id'] }}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>{{ session('debug_info')['email'] }}</td>
                            </tr>
                            <tr>
                                <td><strong>Is Affiliate:</strong></td>
                                <td>{{ session('debug_info')['is_affiliate'] }}</td>
                            </tr>
                            <tr>
                                <td><strong>Route Name:</strong></td>
                                <td><code>{{ session('debug_info')['route_name'] }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Verification URL:</strong></td>
                                <td><a href="{{ session('debug_info')['verification_url'] }}" target="_blank" class="btn btn-sm btn-outline-primary">Test Verification Link</a></td>
                            </tr>
                        </table>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">Click the "Test Verification Link" button to test the verification process.</small>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Email Configuration Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog"></i> Email Configuration Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Mail Driver:</strong></td>
                                    <td>{{ $mailConfig['mailer'] }}</td>
                                </tr>
                                <tr>
                                    <td><strong>SMTP Host:</strong></td>
                                    <td>{{ $mailConfig['host'] }}</td>
                                </tr>
                                <tr>
                                    <td><strong>SMTP Port:</strong></td>
                                    <td>{{ $mailConfig['port'] }}</td>
                                </tr>
                                <tr>
                                    <td><strong>From Address:</strong></td>
                                    <td>{{ $mailConfig['from'] }}</td>
                                </tr>
                                <tr>
                                    <td><strong>From Name:</strong></td>
                                    <td>{{ $mailConfig['from_name'] }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-{{ $mailConfig['host'] === 'live.smtp.mailtrap.io' ? 'success' : 'warning' }} mb-0">
                                <i class="fas fa-{{ $mailConfig['host'] === 'live.smtp.mailtrap.io' ? 'check-circle' : 'exclamation-triangle' }}"></i>
                                @if($mailConfig['host'] === 'live.smtp.mailtrap.io')
                                    Mailtrap configuration detected and ready for testing
                                @else
                                    Mailtrap not configured - please check email settings
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Testing Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-paper-plane"></i> Send Test Emails
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="emailTestForm">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="test_email" class="form-label">Test Email Address</label>
                                    <input type="email" class="form-control" id="test_email" name="test_email" 
                                           value="{{ old('test_email', '<EMAIL>') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user_type" class="form-label">Welcome Email Type</label>
                                    <select class="form-select" id="user_type" name="user_type">
                                        <option value="organization">Organization</option>
                                        <option value="affiliate">Affiliate</option>
                                        <option value="super_admin">Super Admin</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" formaction="{{ route('super_admin.email-testing.send-basic-test') }}" class="btn btn-primary">
                                    <i class="fas fa-envelope"></i> Send Basic Test Email
                                </button>
                                <button type="submit" formaction="{{ route('super_admin.email-testing.send-welcome-test') }}" class="btn btn-success">
                                    <i class="fas fa-heart"></i> Send Welcome Email Test
                                </button>
                                <button type="submit" formaction="{{ route('super_admin.email-testing.diagnose-welcome-email') }}" class="btn btn-warning">
                                    <i class="fas fa-stethoscope"></i> Diagnose Welcome Email
                                </button>
                                <button type="submit" formaction="{{ route('super_admin.email-testing.send-all-tests') }}" class="btn btn-danger">
                                    <i class="fas fa-rocket"></i> Send All Email Tests
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Welcome Messages Status -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-comments"></i> Welcome Messages Status
                    </h5>
                    @if($welcomeMessages->count() === 0)
                        <form method="POST" action="{{ route('super_admin.email-testing.initialize-defaults') }}" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-outline-secondary btn-sm" 
                                    onclick="return confirm('This will create default welcome messages for all user types. Continue?')">
                                <i class="fas fa-magic"></i> Initialize Defaults
                            </button>
                        </form>
                    @endif
                </div>
                <div class="card-body">
                    @if($welcomeMessages->count() > 0)
                        <div class="row">
                            @foreach($welcomeMessages as $message)
                                <div class="col-md-4 mb-3">
                                    <div class="card border-{{ $message->is_active ? 'success' : 'secondary' }}">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ ucfirst($message->user_type) }}</h6>
                                            <p class="card-text small">{{ Str::limit($message->subject, 50) }}</p>
                                            <span class="badge bg-{{ $message->is_active ? 'success' : 'secondary' }}">
                                                {{ $message->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle"></i> 
                            No welcome messages found. Click "Initialize Defaults" to create default messages for all user types.
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Navigation -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-compass"></i> Quick Navigation
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ route('super_admin.email-testing.system-check') }}" class="btn btn-outline-warning">
                                    <i class="fas fa-heartbeat"></i><br>
                                    <small>System Check</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ route('super_admin.email-testing.auth-emails') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-shield-alt"></i><br>
                                    <small>Authentication Email Testing</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ route('super_admin.email-testing.account-creation') }}" class="btn btn-outline-success">
                                    <i class="fas fa-user-plus"></i><br>
                                    <small>Account Creation Testing</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ route('super_admin.welcome-messages.index') }}" class="btn btn-outline-info">
                                    <i class="fas fa-envelope"></i><br>
                                    <small>Manage Welcome Messages</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Affiliate Registration Check -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-check"></i> Check Affiliate Registration Email Flow
                    </h5>
                </div>
                <div class="card-body">
                    <p>Check if an affiliate account was created successfully and diagnose email delivery issues:</p>
                    <form method="POST" action="{{ route('super_admin.email-testing.check-affiliate-registration') }}">
                        @csrf
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="check_email" class="form-label">Affiliate Email Address</label>
                                    <input type="email" class="form-control" id="check_email" name="email"
                                           value="<EMAIL>" required>
                                    <div class="form-text">Enter the email address used for affiliate registration</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-2">
                                        <button type="submit" formaction="{{ route('super_admin.email-testing.check-affiliate-registration') }}" class="btn btn-warning">
                                            <i class="fas fa-search"></i> Check Registration & Send Welcome Email
                                        </button>
                                        <button type="submit" formaction="{{ route('super_admin.email-testing.debug-verification-url') }}" class="btn btn-info">
                                            <i class="fas fa-bug"></i> Debug Verification URL
                                        </button>
                                        <button type="submit" formaction="{{ route('super_admin.email-testing.test-affiliate-password-reset') }}" class="btn btn-warning">
                                            <i class="fas fa-key"></i> Test Affiliate Password Reset
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Testing Instructions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-book"></i> Testing Instructions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📧 Email Testing Process</h6>
                            <ol class="small">
                                <li>Enter a test email address</li>
                                <li>Send test emails using the buttons above</li>
                                <li>Check your Mailtrap inbox for received emails</li>
                                <li>Verify email templates render correctly</li>
                                <li>Test all email links and buttons</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6>✅ Expected Results</h6>
                            <ul class="small">
                                <li>All emails delivered to Mailtrap</li>
                                <li>Professional styling with correct branding</li>
                                <li>Functional buttons and links</li>
                                <li>No errors during sending process</li>
                                <li>Appropriate content for each user type</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
