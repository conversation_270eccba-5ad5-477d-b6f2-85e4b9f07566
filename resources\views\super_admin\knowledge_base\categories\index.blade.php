@extends('super_admin.layouts.app')

@section('title', 'Knowledge Base Categories')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Knowledge Base Categories</h1>
                    <p class="text-muted mb-0">Organize your help articles into categories</p>
                </div>
                <div>
                    <a href="{{ route('super_admin.knowledge-base.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Articles
                    </a>
                    <a href="{{ route('super_admin.knowledge-base.categories.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Category
                    </a>
                </div>
            </div>

            <!-- Categories Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-folder me-2"></i>
                        Categories
                    </h5>
                </div>
                <div class="card-body">
                    @if($categories->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover" id="categoriesTable">
                                <thead>
                                    <tr>
                                        <th width="30">
                                            <i class="fas fa-grip-vertical text-muted" title="Drag to reorder"></i>
                                        </th>
                                        <th>Category</th>
                                        <th>Description</th>
                                        <th>Articles</th>
                                        <th>Status</th>
                                        <th>Sort Order</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sortable-categories">
                                    @foreach($categories as $category)
                                    <tr data-category-id="{{ $category->id }}">
                                        <td>
                                            <i class="fas fa-grip-vertical text-muted handle" style="cursor: move;"></i>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($category->icon)
                                                    <i class="{{ $category->icon }} me-2"
                                                       style="color: {{ $category->color ?? '#007bff' }}"></i>
                                                @else
                                                    <i class="fas fa-folder me-2 text-secondary"></i>
                                                @endif
                                                <div>
                                                    <div class="fw-bold">{{ $category->name }}</div>
                                                    <small class="text-muted">{{ $category->slug }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-muted">
                                                {{ $category->description ?: 'No description' }}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-primary me-2">{{ $category->articles_count }}</span>
                                                <small class="text-muted">
                                                    ({{ $category->published_articles_count }} published)
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <form method="POST" action="{{ route('super_admin.knowledge-base.categories.toggle-active', $category) }}"
                                                  class="d-inline">
                                                @csrf
                                                <button type="submit"
                                                        class="btn btn-sm {{ $category->is_active ? 'btn-success' : 'btn-secondary' }}">
                                                    <i class="fas {{ $category->is_active ? 'fa-check' : 'fa-times' }}"></i>
                                                    {{ $category->is_active ? 'Active' : 'Inactive' }}
                                                </button>
                                            </form>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $category->sort_order }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('super_admin.knowledge-base.categories.edit', $category) }}"
                                                   class="btn btn-outline-primary btn-sm" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if($category->articles_count == 0)
                                                    <form method="POST" action="{{ route('super_admin.knowledge-base.categories.destroy', $category) }}"
                                                          class="d-inline" onsubmit="return confirm('Are you sure you want to delete this category?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-outline-danger btn-sm" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                @else
                                                    <button class="btn btn-outline-secondary btn-sm" disabled title="Cannot delete category with articles">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-folder fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Categories Found</h4>
                            <p class="text-muted">Create your first category to organize your knowledge base articles.</p>
                            <a href="{{ route('super_admin.knowledge-base.categories.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create First Category
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-plus-circle fa-2x text-primary mb-3"></i>
                            <h5>Create Category</h5>
                            <p class="text-muted">Add a new category to organize articles</p>
                            <a href="{{ route('super_admin.knowledge-base.categories.create') }}" class="btn btn-primary">
                                Create Category
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-book fa-2x text-info mb-3"></i>
                            <h5>Manage Articles</h5>
                            <p class="text-muted">View and edit knowledge base articles</p>
                            <a href="{{ route('super_admin.knowledge-base.index') }}" class="btn btn-info">
                                Manage Articles
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-bar fa-2x text-success mb-3"></i>
                            <h5>View Analytics</h5>
                            <p class="text-muted">See knowledge base performance metrics</p>
                            <a href="{{ route('super_admin.knowledge-base.analytics') }}" class="btn btn-success">
                                View Analytics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include SortableJS for drag and drop -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize sortable for category reordering
    const sortableElement = document.getElementById('sortable-categories');
    if (sortableElement) {
        const sortable = Sortable.create(sortableElement, {
            handle: '.handle',
            animation: 150,
            onEnd: function(evt) {
                // Get the new order
                const categoryIds = Array.from(sortableElement.children).map(row =>
                    row.getAttribute('data-category-id')
                );

                // Send AJAX request to update order
                fetch('{{ route("super.knowledge-base.categories.reorder") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        categories: categoryIds
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update sort order badges
                        categoryIds.forEach((id, index) => {
                            const row = document.querySelector(`tr[data-category-id="${id}"]`);
                            const badge = row.querySelector('.badge.bg-info');
                            if (badge) {
                                badge.textContent = index + 1;
                            }
                        });

                        // Show success message
                        showAlert('Categories reordered successfully!', 'success');
                    } else {
                        showAlert('Failed to reorder categories.', 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('An error occurred while reordering categories.', 'danger');
                });
            }
        });
    }
});

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto-dismiss after 3 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}
</script>
@endsection
