import './bootstrap';

// Service Worker Registration
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js').then(registration => {
            console.log('ServiceWorker registered');
        }).catch(error => {
            console.log('ServiceWorker registration failed:', error);
        });
    });
}

// Offline class handling
function updateOfflineStatus() {
    if (!navigator.onLine) {
        document.documentElement.classList.add('offline');
    } else {
        document.documentElement.classList.remove('offline');
    }
}

window.addEventListener('online', updateOfflineStatus);
window.addEventListener('offline', updateOfflineStatus);
updateOfflineStatus();
