<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>International Phone Input Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/css/intlTelInput.css" />
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="tel"] {
            width: 100%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        button {
            background-color: #4299e1;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .validation-message {
            color: red;
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }
    </style>
</head>
<body>
    <h1>International Phone Input Test</h1>
    <p>This is a direct test to verify the international phone input component.</p>

    <form id="test-form">
        <div class="form-group">
            <label for="phone">Phone Number:</label>
            <input type="tel" id="phone" name="phone">
            <div id="validation-message" class="validation-message">Please enter a valid phone number</div>
        </div>
        <input type="hidden" id="full-phone" name="full-phone">
        <button type="submit">Submit</button>
    </form>

    <div id="result" style="margin-top: 20px;"></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/intlTelInput.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize phone input
            const phoneInput = window.intlTelInput(document.querySelector("#phone"), {
                utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js",
                initialCountry: "auto",
                geoIpLookup: function(callback) {
                    fetch("https://ipapi.co/json")
                        .then(function(res) { return res.json(); })
                        .then(function(data) { callback(data.country_code); })
                        .catch(function() { callback("us"); });
                },
                preferredCountries: ["ng", "us", "gb"],
                separateDialCode: true,
                allowDropdown: true,
                autoPlaceholder: "polite"
            });

            // Validation function
            const validatePhone = function() {
                const validationMessage = document.getElementById("validation-message");
                const phoneField = document.getElementById("phone");

                if (phoneField.value.trim()) {
                    if (phoneInput.isValidNumber()) {
                        phoneField.style.borderColor = "#ccc";
                        validationMessage.style.display = "none";
                        return true;
                    } else {
                        phoneField.style.borderColor = "red";
                        validationMessage.style.display = "block";
                        return false;
                    }
                }
                return true; // Empty is considered valid
            };

            // Add event listeners
            document.getElementById("phone").addEventListener('blur', validatePhone);
            document.getElementById("phone").addEventListener('change', validatePhone);
            document.getElementById("phone").addEventListener('keyup', validatePhone);

            // Form submission
            document.getElementById("test-form").addEventListener('submit', function(e) {
                e.preventDefault();

                if (!validatePhone()) {
                    return false;
                }

                const fullNumber = phoneInput.getNumber();
                document.getElementById("full-phone").value = fullNumber;

                // Show result
                document.getElementById("result").innerHTML = `
                    <h3>Form Data:</h3>
                    <p><strong>Phone number:</strong> ${document.getElementById("phone").value}</p>
                    <p><strong>Full number with country code:</strong> ${fullNumber}</p>
                    <p><strong>Country:</strong> ${phoneInput.getSelectedCountryData().name}</p>
                    <p><strong>Country Code:</strong> ${phoneInput.getSelectedCountryData().iso2}</p>
                    <p><strong>Dial Code:</strong> ${phoneInput.getSelectedCountryData().dialCode}</p>
                `;
            });
        });
    </script>
</body>
</html>
