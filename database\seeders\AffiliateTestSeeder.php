<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Affiliate;
use App\Models\AffiliateSetting;
use Illuminate\Support\Facades\Hash;

class AffiliateTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create affiliate settings if not exists
        if (!AffiliateSetting::first()) {
            AffiliateSetting::create([
                'default_commission_rate' => 10.00,
                'minimum_withdrawal' => 50.00,
                'payment_methods' => ['bank_transfer', 'paypal'],
                'terms_and_conditions' => 'Default affiliate program terms and conditions.',
                'auto_approve_earnings' => false,
                'auto_approve_affiliates' => false,
                'cookie_duration_days' => 30,
                'withdrawal_fee_percentage' => 0.00,
                'withdrawal_fee_fixed' => 0.00,
                'program_active' => true,
                'welcome_message' => 'Welcome to our affiliate program!',
                'commission_tiers' => [
                    [
                        'min_referrals' => 0,
                        'commission_rate' => 10.00,
                        'name' => 'Bronze'
                    ],
                    [
                        'min_referrals' => 10,
                        'commission_rate' => 12.00,
                        'name' => 'Silver'
                    ],
                    [
                        'min_referrals' => 25,
                        'commission_rate' => 15.00,
                        'name' => 'Gold'
                    ],
                    [
                        'min_referrals' => 50,
                        'commission_rate' => 20.00,
                        'name' => 'Platinum'
                    ]
                ],
                'recurring_commissions' => false,
                'max_referrals_per_affiliate' => null,
            ]);
        }

        // Create test affiliate users
        $affiliateUsers = [
            [
                'name' => 'John Affiliate',
                'email' => '<EMAIL>',
                'status' => Affiliate::STATUS_ACTIVE,
                'commission_rate' => 10.00,
            ],
            [
                'name' => 'Jane Partner',
                'email' => '<EMAIL>',
                'status' => Affiliate::STATUS_PENDING,
                'commission_rate' => 10.00,
            ],
            [
                'name' => 'Mike Referrer',
                'email' => '<EMAIL>',
                'status' => Affiliate::STATUS_ACTIVE,
                'commission_rate' => 12.00,
            ],
        ];

        foreach ($affiliateUsers as $userData) {
            // Create user
            $user = User::create([
                'name' => $userData['name'],
                'email' => $userData['email'],
                'password' => Hash::make('password'),
                'phone' => '+1234567890',
                'status' => User::STATUS_ACTIVE,
            ]);

            // Create affiliate
            Affiliate::create([
                'user_id' => $user->id,
                'status' => $userData['status'],
                'commission_rate' => $userData['commission_rate'],
                'payment_details' => [
                    'method' => 'paypal',
                    'paypal_email' => $userData['email'],
                ],
                'bio' => 'Test affiliate bio for ' . $userData['name'],
                'website' => 'https://example.com',
                'social_media' => '@' . strtolower(str_replace(' ', '', $userData['name'])),
            ]);
        }

        $this->command->info('Affiliate test data seeded successfully!');
        $this->command->info('Test affiliate login credentials:');
        $this->command->info('Email: <EMAIL> | Password: password');
        $this->command->info('Email: <EMAIL> | Password: password');
        $this->command->info('Pending approval: <EMAIL> | Password: password');
    }
}
