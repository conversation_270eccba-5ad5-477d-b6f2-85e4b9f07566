@extends('layouts.auth')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-5">
        <div class="card shadow-lg">
            <div class="card-header text-white">
                <div class="shape-1"></div>
                <div class="shape-2"></div>
                <h3 class="text-center font-weight-light my-2">Set New Password</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('password.store') }}">
                    @csrf

                    <!-- Password Reset Token -->
                    <input type="hidden" name="token" value="{{ $request->route('token') }}">

                    <div class="form-floating mb-4">
                        <input class="form-control @error('email') is-invalid @enderror"
                               id="email" type="email" name="email"
                               value="{{ old('email', $request->email) }}" required autofocus />
                        <label for="email"><i class="fas fa-envelope me-2"></i>Email address</label>
                        @error('email')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                        @enderror
                    </div>

                    <div class="form-floating mb-4">
                        <input class="form-control @error('password') is-invalid @enderror"
                               id="password" type="password" name="password" required />
                        <label for="password"><i class="fas fa-lock me-2"></i>New Password</label>
                        @error('password')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                        @enderror
                    </div>

                    <div class="form-floating mb-4">
                        <input class="form-control @error('password_confirmation') is-invalid @enderror"
                               id="password_confirmation" type="password" name="password_confirmation" required />
                        <label for="password_confirmation"><i class="fas fa-check-circle me-2"></i>Confirm Password</label>
                        @error('password_confirmation')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                        @enderror
                    </div>

                    <div class="d-flex align-items-center justify-content-between mt-4 mb-2">
                        <a class="small text-info" href="{{ route('login') }}">
                            <i class="fas fa-arrow-left me-1"></i>Back to login
                        </a>
                        <button type="submit" class="btn btn-primary px-4">
                            <i class="fas fa-save me-2"></i>Reset Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
