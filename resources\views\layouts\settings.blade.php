@extends('layouts.app')

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <h1 class="text-3xl font-semibold mb-6">Settings</h1>

        <!-- Success Toast Notification -->
        @if(session('success'))
        <div
            x-data="{ show: true }"
            x-show="show"
            x-init="setTimeout(() => show = false, 4000)"
            class="fixed top-4 right-4 z-50 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-md"
            style="max-width: 24rem;"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform translate-x-8"
            x-transition:enter-end="opacity-100 transform translate-x-0"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 transform translate-x-0"
            x-transition:leave-end="opacity-0 transform translate-x-8"
        >
            <div class="flex items-center">
                <div class="py-1">
                    <svg class="h-6 w-6 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <div>
                    <p class="font-medium">Success</p>
                    <p class="text-sm">{{ session('success') }}</p>
                </div>
                <div class="ml-auto">
                    <button @click="show = false" class="text-green-700 hover:text-green-900">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        @endif

        <div x-data="{
            activeTab: '{{ session('active_tab') ?? 'app' }}',
            init() {
                // If no active tab set from session, check for hash in URL
                if (!this.activeTab || this.activeTab === 'app') {
                    const hash = window.location.hash.substring(1);
                    if (hash) {
                        this.activeTab = hash;
                    }
                }

                // Update hash on tab change
                this.$watch('activeTab', value => {
                    window.location.hash = value;
                });
            }
        }">
            <div class="mb-8">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-6 overflow-x-auto" aria-label="Settings Navigation">
                        <button @click="activeTab = 'app'"
                            :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'app', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'app' }"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline-block" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
                            </svg>
                            App Settings
                        </button>

                        <button @click="activeTab = 'business'"
                            :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'business', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'business' }"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline-block" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 01-1 1h-2a1 1 0 01-1-1v-2a1 1 0 00-1-1H7a1 1 0 00-1 1v2a1 1 0 01-1 1H3a1 1 0 01-1-1V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd" />
                            </svg>
                            Business
                        </button>

                        <button @click="activeTab = 'receipt'"
                            :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'receipt', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'receipt' }"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline-block" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5 2a2 2 0 00-2 2v14l3.5-2 3.5 2 3.5-2 3.5 2V4a2 2 0 00-2-2H5zm4.707 3.707a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L8.414 9H10a3 3 0 013 3v1a1 1 0 102 0v-1a5 5 0 00-5-5H8.414l1.293-1.293z" clip-rule="evenodd" />
                            </svg>
                            Receipts
                        </button>

                        <button @click="activeTab = 'notifications'"
                            :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'notifications', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'notifications' }"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline-block" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
                            </svg>
                            Notifications
                        </button>

                        <button @click="activeTab = 'printer'"
                            :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'printer', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'printer' }"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 inline-block" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clip-rule="evenodd" />
                            </svg>
                            Printer
                        </button>
                    </nav>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <!-- Tab contents with transitions -->
                    <div x-show="activeTab === 'app'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                        @yield('app_settings')
                    </div>

                    <div x-show="activeTab === 'business'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                        @yield('business_settings')
                    </div>

                    <div x-show="activeTab === 'receipt'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                        @yield('receipt_settings')
                    </div>

                    <div x-show="activeTab === 'notifications'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                        @yield('notification_settings')
                    </div>

                    <div x-show="activeTab === 'printer'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                        @yield('printer_settings')
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Check for refresh parameter and reload page if present -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        if ("{{ old('refresh') }}" === "1") {
            // Get the base settings URL
            let baseUrl = "{{ route('settings.index') }}";

            // Get the active tab from session or hash
            let activeTab = "{{ session('active_tab') }}" ||
                            (window.location.hash ? window.location.hash.substring(1) : 'app');

            // Force a full page reload with the correct URL structure
            window.location.href = baseUrl + '#' + activeTab;
        }
    });
</script>
@endsection
