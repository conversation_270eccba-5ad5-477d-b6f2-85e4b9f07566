<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('jobs', function (Blueprint $table) {
            $table->id();
            $table->string('customer_name');
            $table->string('phone_number');
            $table->text('job_description');
            $table->enum('department', ['Flex', 'DI', 'DTF', '3D', 'Monogram', 'Offset', 'UV', 'Stationary']);
            $table->enum('sales_team', ['Papers & Friends', 'Smart Sales']);
            $table->string('media');
            $table->integer('pages');
            $table->string('size');
            $table->integer('quantity');
            $table->decimal('unit_cost', 10, 2);
            $table->decimal('total_amount', 10, 2);
            $table->decimal('amount_paid', 10, 2);
            $table->decimal('pending_payment', 10, 2);
            $table->decimal('papers_and_friends', 10, 2);
            $table->decimal('smart_sales', 10, 2);
            $table->decimal('operators', 10, 2);
            $table->decimal('vat', 10, 2);
            $table->decimal('production', 10, 2);
            $table->integer('quantity_delivered')->default(0);
            $table->dateTime('date_delivered')->nullable();
            $table->dateTime('date_registered');
            $table->date('expected_delivery_date');
            $table->time('expected_delivery_time');
            $table->enum('status', ['Pending', 'Processing', 'Completed'])->default('Pending');
            $table->string('receiver_name')->nullable();
            $table->string('receiver_phone')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('jobs');
    }
};