<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice {{ $payment->invoice_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .header {
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .company-info {
            float: left;
            width: 50%;
        }
        .invoice-info {
            float: right;
            width: 50%;
            text-align: right;
        }
        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }
        .bill-to {
            margin: 30px 0;
        }
        .bill-to-left {
            float: left;
            width: 50%;
        }
        .bill-to-right {
            float: right;
            width: 50%;
            text-align: right;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .total-row {
            background-color: #e3f2fd;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            border-radius: 4px;
            background-color: #28a745;
            color: white;
        }
        h1, h2, h3, h4, h5, h6 {
            margin: 0 0 10px 0;
        }
        p {
            margin: 0 0 8px 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header clearfix">
        <div class="company-info">
            <h2 style="color: #007bff;">Sales Management System</h2>
            <p>Subscription Services</p>
            <p>Email: <EMAIL></p>
            <p>Phone: +****************</p>
        </div>
        <div class="invoice-info">
            <h3>INVOICE</h3>
            <p><strong>Invoice #:</strong> {{ $payment->invoice_number }}</p>
            <p><strong>Date:</strong> {{ $payment->invoice_generated_at->format('M d, Y') }}</p>
            <p><strong>Payment Date:</strong> {{ $payment->payment_date->format('M d, Y') }}</p>
        </div>
    </div>

    <!-- Bill To -->
    <div class="bill-to clearfix">
        <div class="bill-to-left">
            <h4>Bill To:</h4>
            <p><strong>{{ $payment->organization->name }}</strong></p>
            @if($payment->organization->email)
                <p>{{ $payment->organization->email }}</p>
            @endif
            @if($payment->organization->phone)
                <p>{{ $payment->organization->phone }}</p>
            @endif
            @if($payment->organization->address)
                <p>{{ $payment->organization->address }}</p>
            @endif
        </div>
        <div class="bill-to-right">
            <h4>Subscription Details:</h4>
            <p><strong>Plan:</strong> {{ $payment->subscription->plan->name }}</p>
            <p><strong>Period:</strong> {{ $payment->subscription->start_date->format('M d, Y') }} - {{ $payment->subscription->end_date->format('M d, Y') }}</p>
            <p><strong>Status:</strong> <span class="badge">{{ ucfirst($payment->subscription->status) }}</span></p>
        </div>
    </div>

    <!-- Invoice Items -->
    <table>
        <thead>
            <tr>
                <th>Description</th>
                <th>Period</th>
                <th>Payment Method</th>
                <th class="text-right">Amount</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    <strong>{{ $payment->subscription->plan->name }} Subscription</strong><br>
                    <small>Payment for subscription services</small>
                    @if($payment->payment_reference)
                        <br><small>Reference: {{ $payment->payment_reference }}</small>
                    @endif
                </td>
                <td>{{ $payment->payment_date->format('M Y') }}</td>
                <td>{{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}</td>
                <td class="text-right">{{ format_price($payment->amount) }}</td>
            </tr>
        </tbody>
        <tfoot>
            <tr>
                <td colspan="3" class="text-right"><strong>Subtotal:</strong></td>
                <td class="text-right"><strong>{{ format_price($payment->amount) }}</strong></td>
            </tr>
            <tr>
                <td colspan="3" class="text-right"><strong>Tax (0%):</strong></td>
                <td class="text-right"><strong>{{ format_price(0) }}</strong></td>
            </tr>
            <tr class="total-row">
                <td colspan="3" class="text-right"><strong>Total:</strong></td>
                <td class="text-right"><strong>{{ format_price($payment->amount) }}</strong></td>
            </tr>
        </tfoot>
    </table>

    <!-- Payment Information -->
    <div style="margin-top: 30px;">
        <div style="float: left; width: 50%;">
            <h4>Payment Information:</h4>
            <p><strong>Payment Date:</strong> {{ $payment->payment_date->format('M d, Y') }}</p>
            <p><strong>Payment Method:</strong> {{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}</p>
            @if($payment->payment_reference)
                <p><strong>Reference:</strong> {{ $payment->payment_reference }}</p>
            @endif
            <p><strong>Status:</strong> <span class="badge">Paid</span></p>
        </div>
        <div style="float: right; width: 50%; text-align: right;">
            <h4>Approval Information:</h4>
            <p><strong>Approved By:</strong> {{ $payment->approvedBy->name ?? 'System' }}</p>
            <p><strong>Approved Date:</strong> {{ $payment->approved_at->format('M d, Y H:i') }}</p>
            @if($payment->notes)
                <div>
                    <strong>Notes:</strong>
                    @php
                        // Try to parse JSON data for plan change details
                        $notesData = null;
                        if (str_contains($payment->notes, 'Plan Change Details:')) {
                            $parts = explode('Plan Change Details:', $payment->notes);
                            if (count($parts) > 1) {
                                $jsonPart = trim($parts[1]);
                                $notesData = json_decode($jsonPart, true);
                            }
                        }
                    @endphp

                    @if($notesData && is_array($notesData))
                        {{-- Display formatted plan change details --}}
                        <div style="margin-top: 8px;">
                            @if(str_contains($payment->notes, 'Plan Change:'))
                                <div style="margin-bottom: 8px;">
                                    <em style="color: #007bff;">Plan Change Request</em>
                                </div>
                            @endif

                            <table style="width: 100%; font-size: 12px; border-collapse: collapse; margin-top: 8px;">
                                <tr>
                                    <td style="width: 50%; vertical-align: top; padding-right: 10px; border: none;">
                                        <strong>Change Details:</strong><br>
                                        • Type: {{ ucfirst(str_replace('_', ' ', $notesData['type'] ?? 'N/A')) }}<br>
                                        • Change Type: {{ ucfirst($notesData['change_type'] ?? 'N/A') }}<br>
                                        @if(isset($notesData['current_plan_id']))
                                            • From Plan ID: {{ $notesData['current_plan_id'] ?? 'No Plan' }}<br>
                                        @endif
                                        @if(isset($notesData['requested_plan_id']))
                                            • To Plan ID: {{ $notesData['requested_plan_id'] }}<br>
                                        @endif
                                    </td>
                                    @if(isset($notesData['proration']) && is_array($notesData['proration']))
                                        <td style="width: 50%; vertical-align: top; padding-left: 10px; border: none;">
                                            <strong>Pricing Details:</strong><br>
                                            • Amount: {{ format_price($notesData['proration']['net_amount'] ?? 0) }}<br>
                                            @if(isset($notesData['proration']['current_plan_credit']) && $notesData['proration']['current_plan_credit'] > 0)
                                                • Credit: {{ format_price($notesData['proration']['current_plan_credit']) }}<br>
                                            @endif
                                            @if(isset($notesData['proration']['new_plan_charge']))
                                                • New Plan Charge: {{ format_price($notesData['proration']['new_plan_charge']) }}<br>
                                            @endif
                                            @if(isset($notesData['proration']['remaining_days']))
                                                • Remaining Days: {{ number_format($notesData['proration']['remaining_days'], 0) }} days<br>
                                            @endif
                                        </td>
                                    @else
                                        <td style="width: 50%; border: none;"></td>
                                    @endif
                                </tr>
                            </table>

                                @if(isset($notesData['proration']['proration_details']))
                                    <div style="margin-top: 8px; font-size: 11px; color: #666;">
                                        <strong>Details:</strong> {{ $notesData['proration']['proration_details'] }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    @else
                        {{-- Display regular notes --}}
                        <div style="margin-top: 4px;">{{ $payment->notes }}</div>
                    @endif
                </div>
            @endif
        </div>
    </div>

    <div class="clearfix"></div>

    <!-- Footer -->
    <div class="footer">
        <p>Thank you for your business!</p>
        <p>This is a computer-generated invoice and does not require a signature.</p>
        <p>Generated on {{ now()->format('M d, Y H:i:s') }}</p>
    </div>
</body>
</html>
