<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Role;

return new class extends Migration
{
    public function up()
    {
        // Add Delivery and Production roles if they don't exist
        foreach (['Delivery', 'Production'] as $roleName) {
            Role::firstOrCreate(['name' => $roleName]);
        }
    }

    public function down()
    {
        Role::whereIn('name', ['Delivery', 'Production'])->delete();
    }
};