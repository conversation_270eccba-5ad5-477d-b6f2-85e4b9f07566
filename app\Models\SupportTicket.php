<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SupportTicket extends Model
{
    use HasFactory;

    protected $fillable = [
        'ticket_number',
        'title',
        'description',
        'priority',
        'status',
        'category',
        'user_id',
        'organization_id',
        'assigned_to',
        'resolved_at',
        'first_response_at',
        'tags',
        'metadata',
    ];

    protected $casts = [
        'tags' => 'array',
        'metadata' => 'array',
        'resolved_at' => 'datetime',
        'first_response_at' => 'datetime',
    ];

    // Priority levels
    const PRIORITY_LOW = 'low';
    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';
    const PRIORITY_CRITICAL = 'critical';

    // Status levels
    const STATUS_OPEN = 'open';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_WAITING_CUSTOMER = 'waiting_customer';
    const STATUS_WAITING_ADMIN = 'waiting_admin';
    const STATUS_RESOLVED = 'resolved';
    const STATUS_CLOSED = 'closed';

    // Categories
    const CATEGORY_TECHNICAL = 'technical';
    const CATEGORY_BILLING = 'billing';
    const CATEGORY_ACCOUNT = 'account';
    const CATEGORY_FEATURE_REQUEST = 'feature_request';
    const CATEGORY_BUG_REPORT = 'bug_report';
    const CATEGORY_GENERAL = 'general';

    /**
     * Get the user who created the ticket
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the organization associated with the ticket
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the super admin assigned to the ticket
     */
    public function assignedAdmin(): BelongsTo
    {
        return $this->belongsTo(SuperAdmin::class, 'assigned_to');
    }

    /**
     * Get all replies for this ticket
     */
    public function replies(): HasMany
    {
        return $this->hasMany(SupportTicketReply::class)->orderBy('created_at');
    }

    /**
     * Get the latest reply
     */
    public function latestReply()
    {
        return $this->hasOne(SupportTicketReply::class)->latest();
    }

    /**
     * Scope for filtering by status
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for filtering by priority
     */
    public function scopePriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope for filtering by category
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for open tickets
     */
    public function scopeOpen($query)
    {
        return $query->whereIn('status', [
            self::STATUS_OPEN,
            self::STATUS_IN_PROGRESS,
            self::STATUS_WAITING_CUSTOMER,
            self::STATUS_WAITING_ADMIN
        ]);
    }

    /**
     * Scope for closed tickets
     */
    public function scopeClosed($query)
    {
        return $query->whereIn('status', [self::STATUS_RESOLVED, self::STATUS_CLOSED]);
    }

    /**
     * Scope for urgent tickets
     */
    public function scopeUrgent($query)
    {
        return $query->whereIn('priority', [self::PRIORITY_URGENT, self::PRIORITY_CRITICAL]);
    }

    /**
     * Scope for assigned tickets
     */
    public function scopeAssigned($query, $adminId = null)
    {
        if ($adminId) {
            return $query->where('assigned_to', $adminId);
        }
        return $query->whereNotNull('assigned_to');
    }

    /**
     * Scope for unassigned tickets
     */
    public function scopeUnassigned($query)
    {
        return $query->whereNull('assigned_to');
    }

    /**
     * Get priority badge HTML
     */
    public function getPriorityBadgeAttribute()
    {
        $colors = [
            self::PRIORITY_LOW => 'secondary',
            self::PRIORITY_NORMAL => 'primary',
            self::PRIORITY_HIGH => 'warning',
            self::PRIORITY_URGENT => 'danger',
            self::PRIORITY_CRITICAL => 'dark',
        ];

        $color = $colors[$this->priority] ?? 'secondary';
        return "<span class='badge bg-{$color}'>" . ucfirst(str_replace('_', ' ', $this->priority)) . "</span>";
    }

    /**
     * Get status badge HTML
     */
    public function getStatusBadgeAttribute()
    {
        $colors = [
            self::STATUS_OPEN => 'success',
            self::STATUS_IN_PROGRESS => 'primary',
            self::STATUS_WAITING_CUSTOMER => 'warning',
            self::STATUS_WAITING_ADMIN => 'info',
            self::STATUS_RESOLVED => 'secondary',
            self::STATUS_CLOSED => 'dark',
        ];

        $color = $colors[$this->status] ?? 'secondary';
        return "<span class='badge bg-{$color}'>" . ucfirst(str_replace('_', ' ', $this->status)) . "</span>";
    }

    /**
     * Check if ticket is overdue (no response in 24 hours for urgent, 48 hours for others)
     */
    public function getIsOverdueAttribute()
    {
        if ($this->status === self::STATUS_CLOSED || $this->status === self::STATUS_RESOLVED) {
            return false;
        }

        $threshold = in_array($this->priority, [self::PRIORITY_URGENT, self::PRIORITY_CRITICAL]) ? 24 : 48;

        $lastActivity = $this->latestReply?->created_at ?? $this->created_at;

        return $lastActivity->diffInHours(now()) > $threshold;
    }

    /**
     * Get response time in hours
     */
    public function getResponseTimeAttribute()
    {
        if (!$this->first_response_at) {
            return null;
        }

        return $this->created_at->diffInHours($this->first_response_at);
    }

    /**
     * Get resolution time in hours
     */
    public function getResolutionTimeAttribute()
    {
        if (!$this->resolved_at) {
            return null;
        }

        return $this->created_at->diffInHours($this->resolved_at);
    }

    /**
     * Generate unique ticket number
     */
    public static function generateTicketNumber()
    {
        do {
            $number = 'TKT-' . date('Y') . '-' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
        } while (self::where('ticket_number', $number)->exists());

        return $number;
    }

    /**
     * Get all priority options
     */
    public static function getPriorityOptions()
    {
        return [
            self::PRIORITY_LOW => 'Low',
            self::PRIORITY_NORMAL => 'Normal',
            self::PRIORITY_HIGH => 'High',
            self::PRIORITY_URGENT => 'Urgent',
            self::PRIORITY_CRITICAL => 'Critical',
        ];
    }

    /**
     * Get all status options
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_OPEN => 'Open',
            self::STATUS_IN_PROGRESS => 'In Progress',
            self::STATUS_WAITING_CUSTOMER => 'Waiting for Customer',
            self::STATUS_WAITING_ADMIN => 'Waiting for Admin',
            self::STATUS_RESOLVED => 'Resolved',
            self::STATUS_CLOSED => 'Closed',
        ];
    }

    /**
     * Get all category options
     */
    public static function getCategoryOptions()
    {
        return [
            self::CATEGORY_TECHNICAL => 'Technical Support',
            self::CATEGORY_BILLING => 'Billing & Payments',
            self::CATEGORY_ACCOUNT => 'Account Management',
            self::CATEGORY_FEATURE_REQUEST => 'Feature Request',
            self::CATEGORY_BUG_REPORT => 'Bug Report',
            self::CATEGORY_GENERAL => 'General Inquiry',
        ];
    }

    /**
     * Mark ticket as resolved
     */
    public function markAsResolved($adminId = null)
    {
        $oldStatus = $this->status;

        $this->update([
            'status' => self::STATUS_RESOLVED,
            'resolved_at' => now(),
            'assigned_to' => $adminId ?? $this->assigned_to,
        ]);

        // Send status change notification
        $this->sendStatusChangeNotification($oldStatus, self::STATUS_RESOLVED, $adminId);
    }

    /**
     * Assign ticket to admin
     */
    public function assignTo($adminId)
    {
        $oldStatus = $this->status;
        $newStatus = $this->status === self::STATUS_OPEN ? self::STATUS_IN_PROGRESS : $this->status;

        $this->update([
            'assigned_to' => $adminId,
            'status' => $newStatus,
        ]);

        // Send status change notification if status changed
        if ($oldStatus !== $newStatus) {
            $this->sendStatusChangeNotification($oldStatus, $newStatus, $adminId);
        }
    }

    /**
     * Send status change notification to user
     */
    public function sendStatusChangeNotification(string $oldStatus, string $newStatus, int $adminId = null)
    {
        try {
            $adminName = null;
            if ($adminId) {
                $admin = \App\Models\SuperAdmin::find($adminId);
                $adminName = $admin ? $admin->name : null;
            }

            // Notify the ticket owner
            if ($this->user) {
                $this->user->notify(new \App\Notifications\SupportTicketStatusNotification(
                    $this, $oldStatus, $newStatus, $adminName
                ));
            }

            // Also notify organization admins if different from ticket owner
            if ($this->organization) {
                $orgAdmins = $this->organization->users()
                    ->whereHas('roles', function($query) {
                        $query->whereIn('name', ['Organization Owner', 'Manager']);
                    })
                    ->where('id', '!=', $this->user_id)
                    ->get();

                foreach ($orgAdmins as $admin) {
                    $admin->notify(new \App\Notifications\SupportTicketStatusNotification(
                        $this, $oldStatus, $newStatus, $adminName
                    ));
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to send status change notification', [
                'ticket_id' => $this->id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'error' => $e->getMessage()
            ]);
        }
    }
}
