<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use App\Models\Branch;
use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserRequest;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Http\Traits\RespondsWithJson;

class UserController extends Controller
{
    use RespondsWithJson;

    public function __construct()
    {
        $this->middleware('role:Organization Owner');
    }

    // Helper method to safely get authenticated user ID
    private function getCurrentUserId()
    {
        return Auth::check() ? Auth::id() : null;
    }

    public function index()
    {
        try {
            // Get the current user's organization ID
            $organizationId = Auth::user()->organization_id;

            // Get users by status and filter by organization
            $activeUsers = User::active()->where('organization_id', $organizationId)
                              ->with('roles')->orderBy('created_at', 'desc')->get();
            $inactiveUsers = User::inactive()->where('organization_id', $organizationId)
                                ->with('roles')->orderBy('created_at', 'desc')->get();
            $archivedUsers = User::archived()->where('organization_id', $organizationId)
                                ->with('roles')->orderBy('created_at', 'desc')->get();

            // All users for statistics
            $allUsers = $activeUsers->concat($inactiveUsers)->concat($archivedUsers);

            // Get all roles for filtering
            $roles = Role::all();

            // Calculate user statistics
            $totalUsers = $allUsers->count();
            $activeCount = $activeUsers->count();
            $inactiveCount = $inactiveUsers->count();
            $archivedCount = $archivedUsers->count();

            // Additional statistics for cards
            $userStats = [
                'active' => $activeCount,
                'inactive' => $inactiveCount + $archivedCount, // Combined inactive and archived
                'total' => $totalUsers,
                'percentage_active' => $totalUsers > 0 ? round(($activeCount / $totalUsers) * 100, 1) : 0,
            ];

            return view('users.index', compact(
                'activeUsers',
                'inactiveUsers',
                'archivedUsers',
                'roles',
                'totalUsers',
                'activeCount',
                'inactiveCount',
                'archivedCount',
                'userStats'
            ));
        } catch (\Exception $e) {
            Log::error('Error in UserController@index: ' . $e->getMessage());
            return back()->with('error', 'An error occurred while loading users.');
        }
    }

    public function create()
    {
        $organization = Auth::user()->organization;

        // Check if organization can add more users
        $canAddUser = $organization->canAddUser();
        $remainingSlots = $organization->getRemainingUserSlots();
        $planUsage = $organization->getPlanUsage();

        // If at limit, show warning but still allow access to form
        if (!$canAddUser) {
            session()->flash('warning',
                "You have reached your plan limit of {$organization->plan->user_limit} users. " .
                "Please upgrade your plan to add more users."
            );
        } elseif ($remainingSlots <= 2 && $remainingSlots !== PHP_INT_MAX) {
            session()->flash('info',
                "You have {$remainingSlots} user slots remaining in your {$organization->plan->name} plan."
            );
        }

        $roles = Role::all();
        // Get branches from the user's organization
        $branches = Branch::where('organization_id', Auth::user()->organization_id)->get();

        return view('users.create', compact('roles', 'branches', 'canAddUser', 'remainingSlots', 'planUsage'));
    }

    public function store(StoreUserRequest $request)
    {
        $organization = Auth::user()->organization;

        // Double-check plan limits before creating user
        if (!$organization->canAddUser()) {
            return back()
                ->withErrors(['limit_exceeded' => "You have reached your plan limit of {$organization->plan->user_limit} users. Please upgrade your plan to add more users."])
                ->withInput()
                ->with('upgrade_required', true);
        }

        try {
            DB::beginTransaction();

            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'status' => $request->status,
                'organization_id' => Auth::user()->organization_id,
                'branch_id' => $request->branch_id,
            ]);

            $user->roles()->sync($request->roles);

            DB::commit();

            Log::info('User created successfully', [
                'user_id' => $user->id,
                'created_by' => $this->getCurrentUserId(),
                'roles' => $request->roles,
                'branch_id' => $request->branch_id
            ]);

            return $this->respondWithSuccess(
                'User created successfully',
                ['user' => $user->load('roles')],
                'users.index'
            );

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create user', [
                'error' => $e->getMessage(),
                'created_by' => $this->getCurrentUserId()
            ]);

            return $this->respondWithError(
                'Failed to create user: ' . $e->getMessage()
            );
        }
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        // Ensure user belongs to the same organization
        if ($user->organization_id !== Auth::user()->organization_id) {
            abort(403, 'Unauthorized access to user.');
        }

        // Load relationships
        $user->load(['roles', 'branch', 'organization']);

        return view('users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        // Ensure user belongs to the same organization
        if ($user->organization_id !== Auth::user()->organization_id) {
            abort(403, 'Unauthorized access to user.');
        }

        $roles = Role::all();
        $branches = Branch::where('organization_id', Auth::user()->organization_id)->get();
        
        return view('users.edit', compact('user', 'roles', 'branches'));
    }

    public function update(UpdateUserRequest $request, User $user)
    {
        try {
            DB::beginTransaction();

            if ($user->email === '<EMAIL>' && $this->getCurrentUserId() !== $user->id) {
                return $this->respondWithError(
                    'Organization Owner profile can only be edited by itself.'
                );
            }

            $updateData = [
                'name' => $request->name,
                'status' => $request->status,
                'branch_id' => $request->branch_id,
            ];

            // Only update email if it's provided and different from current
            if ($request->filled('email') && $request->email !== $user->email) {
                $updateData['email'] = $request->email;
            }

            // Only update password if provided
            if ($request->filled('password')) {
                $updateData['password'] = Hash::make($request->password);
            }

            $user->update($updateData);

            // Log role data before sync for debugging
            Log::info('Syncing roles for user', [
                'user_id' => $user->id,
                'current_roles' => $user->roles()->get()->pluck('id')->toArray(),
                'request_roles' => $request->input('roles', []),
                'request_all' => $request->all()
            ]);

            // Sync the roles
            $user->roles()->sync($request->input('roles', []));

            DB::commit();

            Log::info('User updated successfully', [
                'user_id' => $user->id,
                'updated_by' => $this->getCurrentUserId(),
                'roles' => $request->input('roles', []),
                'new_roles' => $user->fresh()->load('roles')->roles->pluck('id')->toArray(),
                'branch_id' => $request->branch_id,
                'fields_updated' => array_keys($updateData)
            ]);

            // Handle AJAX requests differently
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'User updated successfully',
                    'user' => $user->fresh()->load('roles')
                ]);
            }

            return $this->respondWithSuccess(
                'User updated successfully',
                ['user' => $user->fresh()->load('roles')],
                'users.index'
            );

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update user', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'updated_by' => $this->getCurrentUserId()
            ]);

            // Handle AJAX requests differently
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update user: ' . $e->getMessage()
                ], 422);
            }

            return $this->respondWithError(
                'Failed to update user: ' . $e->getMessage()
            );
        }
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(User $user)
    {
        try {
            // Prevent deletion of admin user
            if ($user->email === '<EMAIL>') {
                return back()->with('error', 'Cannot delete the admin user.');
            }

            // Ensure user belongs to the same organization
            if ($user->organization_id !== Auth::user()->organization_id) {
                abort(403, 'Unauthorized access to user.');
            }

            // Prevent self-deletion
            if ($user->id === Auth::id()) {
                return back()->with('error', 'You cannot delete your own account.');
            }

            $user->delete();

            return back()->with('success', 'User deleted successfully.');
        } catch (\Exception $e) {
            Log::error('Error deleting user: ' . $e->getMessage());
            return back()->with('error', 'An error occurred while deleting the user.');
        }
    }

    public function toggleStatus(User $user)
    {
        // Add debug logging at the very start
        Log::info('toggleStatus method called', [
            'user_id' => $user->id,
            'request_method' => request()->method(),
            'request_headers' => request()->headers->all(),
            'auth_check' => auth()->check(),
            'auth_user_id' => auth()->id(),
            'wants_json' => request()->wantsJson(),
            'is_ajax' => request()->ajax(),
            'x_requested_with' => request()->header('X-Requested-With')
        ]);

        try {
            // Prevent modification of admin user
            if ($user->email === '<EMAIL>') {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot modify Organization Owner status'
                ], 403);
            }

            // Check if authenticated user has Organization Owner role
            if (!Auth::user()->hasRole('Organization Owner')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized: Only Organization Owners can toggle user status'
                ], 403);
            }

            // Ensure user belongs to the same organization
            if ($user->organization_id !== Auth::user()->organization_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to user from different organization'
                ], 403);
            }

            // Check if user is Organization Owner and if deactivating would leave no active owners
            if ($user->hasRole('Organization Owner') && $user->status === 'active') {
                $activeOwnerCount = User::whereHas('roles', function($query) {
                    $query->where('name', 'Organization Owner');
                })
                ->where('organization_id', $user->organization_id)
                ->where('status', 'active')
                ->count();

                if ($activeOwnerCount <= 1) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot deactivate the last active Organization Owner'
                    ], 422);
                }
            }

            $oldStatus = $user->status;
            
            // Toggle the status
            $user->status = $user->status === 'active' ? 'inactive' : 'active';
            $user->save();

            // Log the successful action
            Log::info('User status updated successfully', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'updated_by' => Auth::id(),
                'old_status' => $oldStatus,
                'new_status' => $user->status
            ]);

            $message = $user->status === 'active' 
                ? "User '{$user->name}' has been activated successfully" 
                : "User '{$user->name}' has been deactivated successfully";

            return response()->json([
                'success' => true,
                'message' => $message,
                'status' => $user->status,
                'user_id' => $user->id
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update user status', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'updated_by' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update user status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Deactivate a user instead of deleting them
     * This allows for better user management and potential reactivation
     */
    public function deactivate(User $user)
    {
        if ($user->email === '<EMAIL>') {
            return back()->with('error', 'The Organization Owner account cannot be deactivated.');
        }

        try {
            DB::beginTransaction();

            // Keep the user but mark as inactive and revoke access
            $user->status = 'inactive';
            $user->last_deactivated_at = now();
            $user->deactivated_by = $this->getCurrentUserId();
            $user->save();

            // Log the action
            Log::info('User deactivated', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'action_by' => $this->getCurrentUserId()
            ]);

            DB::commit();

            // Return success response
            return back()->with('success', 'User deactivated successfully. They can no longer access the system.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to deactivate user', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'Failed to deactivate user: ' . $e->getMessage());
        }
    }

    /**
     * Archive a user - this moves them to a separate archived section
     * but preserves their data and associations
     */
    public function archive(User $user)
    {
        if ($user->email === '<EMAIL>') {
            return back()->with('error', 'The Organization Owner account cannot be archived.');
        }

        try {
            DB::beginTransaction();

            // Archive the user
            $user->status = 'archived';
            $user->archived_at = now();
            $user->archived_by = $this->getCurrentUserId();
            $user->save();

            // Log the action
            Log::info('User archived', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'action_by' => $this->getCurrentUserId()
            ]);

            DB::commit();

            // Return success response
            return back()->with('success', 'User archived successfully. Their account has been preserved but deactivated.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to archive user', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'Failed to archive user: ' . $e->getMessage());
        }
    }
}








