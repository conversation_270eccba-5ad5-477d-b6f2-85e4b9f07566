@extends('super_admin.layouts.app')

@section('title', 'Edit Welcome Message')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Edit Welcome Message</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('super_admin.welcome-messages.index') }}">Welcome Messages</a>
                            </li>
                            <li class="breadcrumb-item active">{{ $welcomeMessage->formatted_user_type }}</li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-group" role="group">
                    <a href="{{ route('super_admin.welcome-messages.preview', $welcomeMessage) }}" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> Preview
                    </a>
                    <a href="{{ route('super_admin.welcome-messages.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Messages
                    </a>
                </div>
            </div>

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Welcome Message Details</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('super_admin.welcome-messages.update', $welcomeMessage) }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user_type" class="form-label">User Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('user_type') is-invalid @enderror" 
                                            id="user_type" name="user_type" required>
                                        @foreach($userTypes as $type => $label)
                                            <option value="{{ $type }}" {{ old('user_type', $welcomeMessage->user_type) == $type ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('user_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="subject" class="form-label">Email Subject <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('subject') is-invalid @enderror" 
                                           id="subject" name="subject" value="{{ old('subject', $welcomeMessage->subject) }}" required>
                                    @error('subject')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="greeting" class="form-label">Greeting <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('greeting') is-invalid @enderror" 
                                   id="greeting" name="greeting" value="{{ old('greeting', $welcomeMessage->greeting) }}" required>
                            @error('greeting')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">The greeting line that appears at the top of the email</div>
                        </div>

                        <div class="mb-3">
                            <label for="content" class="form-label">Message Content <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror" 
                                      id="content" name="content" rows="8" required>{{ old('content', $welcomeMessage->content) }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">The main content of the welcome email. Use line breaks to separate paragraphs.</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="call_to_action_text" class="form-label">Call to Action Text</label>
                                    <input type="text" class="form-control @error('call_to_action_text') is-invalid @enderror" 
                                           id="call_to_action_text" name="call_to_action_text" 
                                           value="{{ old('call_to_action_text', $welcomeMessage->call_to_action_text) }}">
                                    @error('call_to_action_text')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Text for the main button</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="call_to_action_url" class="form-label">Call to Action URL</label>
                                    <input type="url" class="form-control @error('call_to_action_url') is-invalid @enderror" 
                                           id="call_to_action_url" name="call_to_action_url" 
                                           value="{{ old('call_to_action_url', $welcomeMessage->call_to_action_url) }}">
                                    @error('call_to_action_url')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">URL the button should link to</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                       {{ old('is_active', $welcomeMessage->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active Message
                                </label>
                            </div>
                            <div class="form-text">Only active messages will be sent to new users. Only one message per user type can be active.</div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('super_admin.welcome-messages.index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Welcome Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Test Email Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-paper-plane"></i> Send Test Email
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('super_admin.welcome-messages.send-test', $welcomeMessage) }}">
                        @csrf
                        <div class="row align-items-end">
                            <div class="col-md-8">
                                <label for="test_email" class="form-label">Test Email Address</label>
                                <input type="email" class="form-control" id="test_email" name="test_email" 
                                       placeholder="Enter email address to send test" required>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-paper-plane"></i> Send Test
                                </button>
                            </div>
                        </div>
                        <div class="form-text">Send a test email to verify how the message will look to recipients.</div>
                    </form>
                </div>
            </div>

            <!-- Message Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> Message Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Created:</strong> {{ $welcomeMessage->created_at->format('M d, Y \a\t g:i A') }}</p>
                            @if($welcomeMessage->creator)
                                <p><strong>Created by:</strong> {{ $welcomeMessage->creator->name }}</p>
                            @endif
                        </div>
                        <div class="col-md-6">
                            <p><strong>Last Updated:</strong> {{ $welcomeMessage->updated_at->format('M d, Y \a\t g:i A') }}</p>
                            @if($welcomeMessage->updater)
                                <p><strong>Updated by:</strong> {{ $welcomeMessage->updater->name }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
