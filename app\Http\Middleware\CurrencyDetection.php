<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\IpGeolocationService;
use App\Services\CurrencyService;
use Symfony\Component\HttpFoundation\Response;

class CurrencyDetection
{
    protected $ipGeolocationService;
    protected $currencyService;

    public function __construct(IpGeolocationService $ipGeolocationService, CurrencyService $currencyService)
    {
        $this->ipGeolocationService = $ipGeolocationService;
        $this->currencyService = $currencyService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip currency detection for API routes or AJAX requests that don't need it
        if ($request->is('api/*') || $this->shouldSkipDetection($request)) {
            return $next($request);
        }

        // Determine session key based on authentication guard
        $sessionKey = $this->getSessionKey($request);

        // Check if currency is already set in session for this user type
        if (!session()->has($sessionKey) || $this->shouldRefreshCurrency($request, $sessionKey)) {
            $this->detectAndSetCurrency($request, $sessionKey);
        }

        // Get currency for current user type
        $userCurrency = session($sessionKey, 'USD');

        // Ensure currency service has the current currency
        $this->currencyService->setCurrentCurrency($userCurrency);

        // Add currency info to view data
        view()->share('userCurrency', $userCurrency);
        view()->share('currencySymbol', $this->currencyService->getCurrentCurrencySymbol());
        view()->share('locationInfo', session('location_info_' . $this->getUserType($request), []));

        return $next($request);
    }

    /**
     * Get session key based on user type
     */
    protected function getSessionKey(Request $request)
    {
        $userType = $this->getUserType($request);
        return "user_currency_{$userType}";
    }

    /**
     * Get user type based on authentication guard
     */
    protected function getUserType(Request $request)
    {
        if (auth()->guard('super_admin')->check()) {
            return 'super_admin';
        }

        if (auth()->guard('affiliate')->check()) {
            return 'affiliate';
        }

        return 'web';
    }

    /**
     * Detect and set user currency based on IP
     */
    protected function detectAndSetCurrency(Request $request, $sessionKey = null)
    {
        $sessionKey = $sessionKey ?: 'user_currency';
        $userType = $this->getUserType($request);

        try {
            // Check for system-wide currency override first
            $overrideEnabled = \App\Models\CurrencySetting::get('system_currency_override_enabled', false);

            if ($overrideEnabled) {
                // Use system override currency for ALL users
                $detectedCurrency = \App\Models\CurrencySetting::get('system_currency_override', 'USD');
                $overrideMethod = \App\Models\CurrencySetting::get('override_method', 'direct');
                $overrideIp = \App\Models\CurrencySetting::get('override_ip_address', '');

                \Log::info('Using system currency override', [
                    'user_type' => $userType,
                    'override_currency' => $detectedCurrency,
                    'override_method' => $overrideMethod,
                    'override_ip' => $overrideIp,
                    'actual_ip' => $request->ip(),
                ]);
            } else {
                // Normal currency detection logic
                $defaultCurrency = \App\Models\CurrencySetting::get('default_currency', 'USD');
                $autoDetection = \App\Models\CurrencySetting::get('auto_currency_detection', true);

                // For Super Admin and Affiliate, prefer default currency setting
                if (in_array($userType, ['super_admin', 'affiliate']) && !$autoDetection) {
                    $detectedCurrency = $defaultCurrency;
                } else {
                    // Detect currency based on IP
                    $detectedCurrency = $this->ipGeolocationService->detectCurrencyByIp($request->ip());

                    // If default currency is set to NGN, use that for all users
                    if ($defaultCurrency === 'NGN') {
                        $detectedCurrency = 'NGN';
                    }
                }
            }

            // Get location info (use override IP if system override is enabled)
            if ($overrideEnabled && $overrideMethod === 'ip_simulation' && $overrideIp) {
                $locationInfo = $this->ipGeolocationService->getLocationInfo($overrideIp);
                $locationInfo['override_active'] = true;
                $locationInfo['override_ip'] = $overrideIp;
                $locationInfo['actual_ip'] = $request->ip();
            } else {
                $locationInfo = $this->ipGeolocationService->getLocationInfo($request->ip());
                if ($overrideEnabled) {
                    $locationInfo['override_active'] = true;
                    $locationInfo['override_method'] = 'direct';
                }
            }

            // Store in session with user type specific keys
            session([
                $sessionKey => $detectedCurrency,
                "location_info_{$userType}" => $locationInfo,
                "currency_detected_at_{$userType}" => now()->toISOString(),
                "currency_detection_ip_{$userType}" => $request->ip(),
                "system_override_active_{$userType}" => $overrideEnabled,
            ]);

            // Log currency detection for debugging
            \Log::info('Currency detected', [
                'user_type' => $userType,
                'ip' => $request->ip(),
                'detected_currency' => $detectedCurrency,
                'default_currency' => $defaultCurrency,
                'auto_detection' => $autoDetection,
                'location' => $locationInfo['country'] ?? 'Unknown',
                'user_agent' => $request->userAgent(),
            ]);

        } catch (\Exception $e) {
            \Log::error('Currency detection failed', [
                'user_type' => $userType,
                'ip' => $request->ip(),
                'error' => $e->getMessage(),
            ]);

            // Fallback to default currency
            $fallbackCurrency = \App\Models\CurrencySetting::get('default_currency', 'USD');
            session([
                $sessionKey => $fallbackCurrency,
                "location_info_{$userType}" => [
                    'ip' => $request->ip(),
                    'country' => 'Unknown',
                    'currency' => $fallbackCurrency,
                    'error' => 'Detection failed'
                ],
                "currency_detected_at_{$userType}" => now()->toISOString(),
            ]);
        }
    }

    /**
     * Check if currency detection should be skipped
     */
    protected function shouldSkipDetection(Request $request)
    {
        // Skip for certain routes
        $skipRoutes = [
            'admin/currency*',
            'api/*',
            '_debugbar/*',
            'telescope/*',
        ];

        foreach ($skipRoutes as $pattern) {
            if ($request->is($pattern)) {
                return true;
            }
        }

        // Skip for AJAX requests that don't need currency info
        if ($request->ajax() && !$this->ajaxNeedsCurrency($request)) {
            return true;
        }

        return false;
    }

    /**
     * Check if AJAX request needs currency information
     */
    protected function ajaxNeedsCurrency(Request $request)
    {
        // AJAX requests that need currency info
        $currencyRoutes = [
            'products*',
            'orders*',
            'invoices*',
            'reports*',
            'dashboard*',
        ];

        foreach ($currencyRoutes as $pattern) {
            if ($request->is($pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if currency should be refreshed
     */
    protected function shouldRefreshCurrency(Request $request, $sessionKey = null)
    {
        $userType = $this->getUserType($request);

        // Refresh if IP changed
        if (session("currency_detection_ip_{$userType}") !== $request->ip()) {
            return true;
        }

        // Refresh if detection is older than 24 hours
        $detectedAt = session("currency_detected_at_{$userType}");
        if ($detectedAt && now()->diffInHours($detectedAt) > 24) {
            return true;
        }

        // Refresh if user manually requested it
        if ($request->has('refresh_currency')) {
            return true;
        }

        return false;
    }
}
