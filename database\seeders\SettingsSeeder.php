<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;
use Carbon\Carbon;

class SettingsSeeder extends Seeder
{
    public function run(): void
    {
        // Truncate the settings table to ensure we're starting fresh
        Setting::truncate();

        // Insert the default settings with specific timestamps
        Setting::create([
            'app_name' => 'Order Flow Pro',
            'app_slogan' => 'Manage Orders & Reports',
            'theme_mode' => 'light',
            'primary_color' => '#1f2937',
            'sidebar_color' => '#1f2937',
            'site_title' => 'OFP',
            'created_at' => '2025-04-08 05:48:59',
            'updated_at' => '2025-04-08 16:45:08'
        ]);

        $this->command->info('Default settings created successfully.');
    }
}
