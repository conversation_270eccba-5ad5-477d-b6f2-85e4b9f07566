<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\Log;

class SupportTicketReply extends Model
{
    use HasFactory;

    protected $fillable = [
        'support_ticket_id',
        'message',
        'is_internal',
        'is_solution',
        'replier_type',
        'replier_id',
        'attachments',
        'metadata',
    ];

    protected $casts = [
        'is_internal' => 'boolean',
        'is_solution' => 'boolean',
        'attachments' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Get the support ticket this reply belongs to
     */
    public function supportTicket(): BelongsTo
    {
        return $this->belongsTo(SupportTicket::class);
    }

    /**
     * Get the replier (User or SuperAdmin)
     */
    public function replier(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope for public replies (visible to customers)
     */
    public function scopePublic($query)
    {
        return $query->where('is_internal', false);
    }

    /**
     * Scope for internal replies (admin only)
     */
    public function scopeInternal($query)
    {
        return $query->where('is_internal', true);
    }

    /**
     * Scope for solution replies
     */
    public function scopeSolutions($query)
    {
        return $query->where('is_solution', true);
    }

    /**
     * Check if reply is from admin
     */
    public function getIsFromAdminAttribute()
    {
        return $this->replier_type === SuperAdmin::class;
    }

    /**
     * Check if reply is from user
     */
    public function getIsFromUserAttribute()
    {
        return $this->replier_type === User::class;
    }

    /**
     * Get replier name
     */
    public function getReplierNameAttribute()
    {
        return $this->replier?->name ?? 'Unknown';
    }

    /**
     * Get replier avatar or initials
     */
    public function getReplierAvatarAttribute()
    {
        $name = $this->replier_name;
        return strtoupper(substr($name, 0, 1));
    }

    /**
     * Get formatted message with line breaks
     */
    public function getFormattedMessageAttribute()
    {
        return nl2br(e($this->message));
    }

    /**
     * Mark reply as solution
     */
    public function markAsSolution()
    {
        // Unmark other solutions for this ticket
        self::where('support_ticket_id', $this->support_ticket_id)
            ->where('id', '!=', $this->id)
            ->update(['is_solution' => false]);

        // Mark this as solution
        $this->update(['is_solution' => true]);

        // Update ticket status if not already resolved
        if (!in_array($this->supportTicket->status, [SupportTicket::STATUS_RESOLVED, SupportTicket::STATUS_CLOSED])) {
            $this->supportTicket->markAsResolved();
        }
    }

    /**
     * Create a reply from user
     */
    public static function createFromUser(SupportTicket $ticket, User $user, string $message, array $attachments = [])
    {
        $reply = self::create([
            'support_ticket_id' => $ticket->id,
            'message' => $message,
            'is_internal' => false,
            'replier_type' => User::class,
            'replier_id' => $user->id,
            'attachments' => $attachments,
        ]);

        // Update ticket status to waiting for admin
        if ($ticket->status !== SupportTicket::STATUS_RESOLVED && $ticket->status !== SupportTicket::STATUS_CLOSED) {
            $ticket->update(['status' => SupportTicket::STATUS_WAITING_ADMIN]);
        }

        // Send notification to super admins
        self::notifyAdminsOfUserReply($ticket, $reply);

        return $reply;
    }

    /**
     * Create a reply from admin
     */
    public static function createFromAdmin(SupportTicket $ticket, SuperAdmin $admin, string $message, bool $isInternal = false, array $attachments = [])
    {
        $reply = self::create([
            'support_ticket_id' => $ticket->id,
            'message' => $message,
            'is_internal' => $isInternal,
            'replier_type' => SuperAdmin::class,
            'replier_id' => $admin->id,
            'attachments' => $attachments,
        ]);

        // Set first response time if this is the first admin response
        if (!$ticket->first_response_at && !$isInternal) {
            $ticket->update(['first_response_at' => now()]);
        }

        // Update ticket status to waiting for customer (if not internal)
        if (!$isInternal && $ticket->status !== SupportTicket::STATUS_RESOLVED && $ticket->status !== SupportTicket::STATUS_CLOSED) {
            $ticket->update(['status' => SupportTicket::STATUS_WAITING_CUSTOMER]);
        }

        // Assign ticket to admin if not already assigned
        if (!$ticket->assigned_to) {
            $ticket->assignTo($admin->id);
        }

        // Send notification to user (if not internal)
        if (!$isInternal) {
            self::notifyUserOfAdminReply($ticket, $reply);
        }

        return $reply;
    }

    /**
     * Notify user when admin replies
     */
    private static function notifyUserOfAdminReply(SupportTicket $ticket, SupportTicketReply $reply)
    {
        try {
            // Get the ticket owner
            $user = $ticket->user;
            if ($user) {
                $user->notify(new \App\Notifications\SupportTicketReplyNotification($ticket, $reply, true));
            }

            // Also notify organization admins if different from ticket owner
            if ($ticket->organization) {
                $orgAdmins = $ticket->organization->users()
                    ->whereHas('roles', function($query) {
                        $query->whereIn('name', ['Organization Owner', 'Manager']);
                    })
                    ->where('id', '!=', $ticket->user_id)
                    ->get();

                foreach ($orgAdmins as $admin) {
                    $admin->notify(new \App\Notifications\SupportTicketReplyNotification($ticket, $reply, true));
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to send admin reply notification', [
                'ticket_id' => $ticket->id,
                'reply_id' => $reply->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Notify admins when user replies
     */
    private static function notifyAdminsOfUserReply(SupportTicket $ticket, SupportTicketReply $reply)
    {
        try {
            // Get all super admins
            $superAdmins = \App\Models\SuperAdmin::all();

            foreach ($superAdmins as $admin) {
                $admin->notify(new \App\Notifications\SupportTicketReplyNotification($ticket, $reply, false));
            }

            // If ticket is assigned, prioritize notification to assigned admin
            if ($ticket->assigned_to) {
                $assignedAdmin = \App\Models\SuperAdmin::find($ticket->assigned_to);
                if ($assignedAdmin) {
                    // Send a priority notification (could be implemented differently)
                    $assignedAdmin->notify(new \App\Notifications\SupportTicketReplyNotification($ticket, $reply, false));
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to send user reply notification', [
                'ticket_id' => $ticket->id,
                'reply_id' => $reply->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
