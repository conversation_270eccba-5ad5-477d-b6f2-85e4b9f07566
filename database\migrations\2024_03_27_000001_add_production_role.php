<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Check if the role already exists before inserting
        $roleExists = DB::table('roles')->where('name', 'Production')->exists();

        if (!$roleExists) {
            DB::table('roles')->insert([
                'name' => 'Production',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    public function down()
    {
        DB::table('roles')->where('name', 'Production')->delete();
    }
};
