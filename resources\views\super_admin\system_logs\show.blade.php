@extends('super_admin.layouts.app')

@section('title', 'Log Details - #' . $systemLog->id)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Log Details</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('super_admin.system-logs.index') }}">System Logs</a>
                            </li>
                            <li class="breadcrumb-item active">#{{ $systemLog->id }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ route('super_admin.system-logs.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Logs
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Main Log Details -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Log Entry Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Log ID</label>
                                        <p class="form-control-plaintext">#{{ $systemLog->id }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">DateTime</label>
                                        <p class="form-control-plaintext">
                                            {{ $systemLog->datetime->format('M d, Y H:i:s') }}
                                            <small class="text-muted">({{ $systemLog->datetime->diffForHumans() }})</small>
                                        </p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Level</label>
                                        <p class="form-control-plaintext">{!! $systemLog->level_badge !!}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Channel</label>
                                        <p class="form-control-plaintext">
                                            <span class="badge bg-secondary">{{ ucfirst($systemLog->channel) }}</span>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Environment</label>
                                        <p class="form-control-plaintext">
                                            <span class="badge bg-info">{{ ucfirst($systemLog->environment) }}</span>
                                        </p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Session ID</label>
                                        <p class="form-control-plaintext">
                                            <code>{{ $systemLog->session_id ?? 'N/A' }}</code>
                                        </p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Request ID</label>
                                        <p class="form-control-plaintext">
                                            <code>{{ $systemLog->request_id ?? 'N/A' }}</code>
                                        </p>
                                    </div>
                                    @if($systemLog->response_time)
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Response Time</label>
                                            <p class="form-control-plaintext">{{ $systemLog->formatted_response_time }}</p>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Message</label>
                                <div class="alert alert-light">
                                    {{ $systemLog->message }}
                                </div>
                            </div>

                            @if($systemLog->context)
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Context Data</label>
                                    <pre class="bg-light p-3 rounded"><code>{{ $systemLog->formatted_context }}</code></pre>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Request Details -->
                    @if($systemLog->url)
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-globe me-2"></i>
                                    Request Details
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Method</label>
                                            <p class="form-control-plaintext">
                                                <span class="badge bg-primary">{{ $systemLog->method }}</span>
                                            </p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">URL</label>
                                            <p class="form-control-plaintext">
                                                <code>{{ $systemLog->url }}</code>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">IP Address</label>
                                            <p class="form-control-plaintext">{{ $systemLog->ip_address }}</p>
                                        </div>
                                        @if($systemLog->status_code)
                                            <div class="mb-3">
                                                <label class="form-label fw-bold">Status Code</label>
                                                <p class="form-control-plaintext">
                                                    <span class="badge bg-{{ $systemLog->status_code >= 400 ? 'danger' : 'success' }}">
                                                        {{ $systemLog->status_code }}
                                                    </span>
                                                </p>
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                @if($systemLog->user_agent)
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">User Agent</label>
                                        <p class="form-control-plaintext">
                                            <small>{{ $systemLog->user_agent }}</small>
                                        </p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- Related Logs -->
                    @if($relatedLogs->count() > 0)
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-link me-2"></i>
                                    Related Log Entries ({{ $relatedLogs->count() }})
                                </h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-sm mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Time</th>
                                                <th>Level</th>
                                                <th>Message</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($relatedLogs as $relatedLog)
                                                <tr>
                                                    <td>
                                                        <small>{{ $relatedLog->datetime->format('H:i:s') }}</small>
                                                    </td>
                                                    <td>{!! $relatedLog->level_badge !!}</td>
                                                    <td>
                                                        <div class="text-truncate" style="max-width: 400px;">
                                                            {{ $relatedLog->message }}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <a href="{{ route('super_admin.system-logs.show', $relatedLog) }}" 
                                                           class="btn btn-sm btn-outline-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- User Information -->
                    @if($systemLog->user)
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-user me-2"></i>
                                    User Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                        <span class="text-white fw-bold">
                                            {{ strtoupper(substr($systemLog->user->name, 0, 1)) }}
                                        </span>
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ $systemLog->user->name }}</div>
                                        <div class="text-muted small">{{ $systemLog->user->email }}</div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <strong>Status:</strong>
                                    <span class="badge bg-{{ $systemLog->user->status === 'active' ? 'success' : 'danger' }}">
                                        {{ ucfirst($systemLog->user->status) }}
                                    </span>
                                </div>
                                @if($systemLog->user->roles->count() > 0)
                                    <div class="mb-2">
                                        <strong>Roles:</strong><br>
                                        @foreach($systemLog->user->roles as $role)
                                            <span class="badge bg-secondary me-1">{{ $role->name }}</span>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- Organization Information -->
                    @if($systemLog->organization)
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-building me-2"></i>
                                    Organization
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <strong>Name:</strong> {{ $systemLog->organization->name }}
                                </div>
                                <div class="mb-2">
                                    <strong>Status:</strong>
                                    <span class="badge bg-{{ $systemLog->organization->is_active ? 'success' : 'danger' }}">
                                        {{ $systemLog->organization->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </div>
                                @if($systemLog->organization->plan)
                                    <div class="mb-2">
                                        <strong>Plan:</strong> {{ $systemLog->organization->plan->name }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- Performance Metrics -->
                    @if($systemLog->response_time || $systemLog->memory_usage)
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    Performance
                                </h5>
                            </div>
                            <div class="card-body">
                                @if($systemLog->response_time)
                                    <div class="mb-2">
                                        <strong>Response Time:</strong>
                                        <span class="badge bg-{{ $systemLog->response_time > 1 ? 'warning' : 'success' }}">
                                            {{ $systemLog->formatted_response_time }}
                                        </span>
                                    </div>
                                @endif
                                @if($systemLog->memory_usage)
                                    <div class="mb-2">
                                        <strong>Memory Usage:</strong>
                                        <span class="badge bg-info">{{ $systemLog->formatted_memory_usage }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-tools me-2"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            @if($systemLog->user)
                                <a href="{{ route('super_admin.impersonation.show', $systemLog->user) }}" 
                                   class="btn btn-sm btn-outline-warning mb-2 w-100">
                                    <i class="fas fa-user-secret me-1"></i>
                                    Impersonate User
                                </a>
                            @endif
                            
                            @if($systemLog->organization)
                                <a href="{{ route('super_admin.organizations.show', $systemLog->organization) }}" 
                                   class="btn btn-sm btn-outline-info mb-2 w-100">
                                    <i class="fas fa-building me-1"></i>
                                    View Organization
                                </a>
                            @endif

                            <a href="{{ route('super_admin.system-logs.index', ['session_id' => $systemLog->session_id]) }}" 
                               class="btn btn-sm btn-outline-secondary mb-2 w-100">
                                <i class="fas fa-search me-1"></i>
                                View Session Logs
                            </a>

                            @if($systemLog->request_id)
                                <a href="{{ route('super_admin.system-logs.index', ['request_id' => $systemLog->request_id]) }}" 
                                   class="btn btn-sm btn-outline-secondary w-100">
                                    <i class="fas fa-link me-1"></i>
                                    View Request Logs
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}
</style>
@endsection
