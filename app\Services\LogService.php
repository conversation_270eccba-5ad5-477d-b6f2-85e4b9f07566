<?php

namespace App\Services;

use App\Models\SystemLog;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class LogService
{
    /**
     * Log an emergency event
     */
    public static function emergency(string $message, array $context = [], string $channel = SystemLog::CHANNEL_APPLICATION): void
    {
        self::log(SystemLog::LEVEL_EMERGENCY, $message, $context, $channel);
    }

    /**
     * Log an alert event
     */
    public static function alert(string $message, array $context = [], string $channel = SystemLog::CHANNEL_APPLICATION): void
    {
        self::log(SystemLog::LEVEL_ALERT, $message, $context, $channel);
    }

    /**
     * Log a critical event
     */
    public static function critical(string $message, array $context = [], string $channel = SystemLog::CHANNEL_APPLICATION): void
    {
        self::log(SystemLog::LEVEL_CRITICAL, $message, $context, $channel);
    }

    /**
     * Log an error event
     */
    public static function error(string $message, array $context = [], string $channel = SystemLog::CHANNEL_APPLICATION): void
    {
        self::log(SystemLog::LEVEL_ERROR, $message, $context, $channel);
    }

    /**
     * Log a warning event
     */
    public static function warning(string $message, array $context = [], string $channel = SystemLog::CHANNEL_APPLICATION): void
    {
        self::log(SystemLog::LEVEL_WARNING, $message, $context, $channel);
    }

    /**
     * Log a notice event
     */
    public static function notice(string $message, array $context = [], string $channel = SystemLog::CHANNEL_APPLICATION): void
    {
        self::log(SystemLog::LEVEL_NOTICE, $message, $context, $channel);
    }

    /**
     * Log an info event
     */
    public static function info(string $message, array $context = [], string $channel = SystemLog::CHANNEL_APPLICATION): void
    {
        self::log(SystemLog::LEVEL_INFO, $message, $context, $channel);
    }

    /**
     * Log a debug event
     */
    public static function debug(string $message, array $context = [], string $channel = SystemLog::CHANNEL_APPLICATION): void
    {
        self::log(SystemLog::LEVEL_DEBUG, $message, $context, $channel);
    }

    /**
     * Log authentication events
     */
    public static function auth(string $message, array $context = [], string $level = SystemLog::LEVEL_INFO): void
    {
        self::log($level, $message, $context, SystemLog::CHANNEL_AUTHENTICATION);
    }

    /**
     * Log security events
     */
    public static function security(string $message, array $context = [], string $level = SystemLog::LEVEL_WARNING): void
    {
        self::log($level, $message, $context, SystemLog::CHANNEL_SECURITY);
    }

    /**
     * Log database events
     */
    public static function database(string $message, array $context = [], string $level = SystemLog::LEVEL_INFO): void
    {
        self::log($level, $message, $context, SystemLog::CHANNEL_DATABASE);
    }

    /**
     * Log performance events
     */
    public static function performance(string $message, array $context = [], string $level = SystemLog::LEVEL_INFO): void
    {
        self::log($level, $message, $context, SystemLog::CHANNEL_PERFORMANCE);
    }

    /**
     * Log API events
     */
    public static function api(string $message, array $context = [], string $level = SystemLog::LEVEL_INFO): void
    {
        self::log($level, $message, $context, SystemLog::CHANNEL_API);
    }

    /**
     * Log impersonation events
     */
    public static function impersonation(string $message, array $context = [], string $level = SystemLog::LEVEL_INFO): void
    {
        self::log($level, $message, $context, SystemLog::CHANNEL_IMPERSONATION);
    }

    /**
     * Log HTTP request/response
     */
    public static function httpRequest(Request $request, $response = null, float $responseTime = null): void
    {
        $context = [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'headers' => $request->headers->all(),
            'input' => $request->except(['password', 'password_confirmation', '_token']),
        ];

        if ($response) {
            $context['status_code'] = $response->getStatusCode();
            $context['response_size'] = strlen($response->getContent());
        }

        if ($responseTime) {
            $context['response_time'] = $responseTime;
        }

        $level = self::getHttpLogLevel($response ? $response->getStatusCode() : 200);
        
        self::log(
            $level,
            "HTTP {$request->method()} {$request->path()}",
            $context,
            SystemLog::CHANNEL_API
        );
    }

    /**
     * Log user activity
     */
    public static function userActivity(string $action, array $context = [], $user = null): void
    {
        $user = $user ?? auth()->user();
        
        $context = array_merge($context, [
            'user_id' => $user?->id,
            'user_name' => $user?->name,
            'user_email' => $user?->email,
            'organization_id' => $user?->organization_id,
        ]);

        self::log(
            SystemLog::LEVEL_INFO,
            "User activity: {$action}",
            $context,
            SystemLog::CHANNEL_APPLICATION
        );
    }

    /**
     * Log system events
     */
    public static function system(string $message, array $context = [], string $level = SystemLog::LEVEL_INFO): void
    {
        $context = array_merge($context, [
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'execution_time' => microtime(true) - LARAVEL_START,
        ]);

        self::log($level, $message, $context, SystemLog::CHANNEL_APPLICATION);
    }

    /**
     * Core logging method
     */
    private static function log(string $level, string $message, array $context = [], string $channel = SystemLog::CHANNEL_APPLICATION): void
    {
        try {
            // Also log to Laravel's default logger
            Log::channel('daily')->log($level, $message, $context);

            // Store in database for centralized viewing
            $logData = [
                'level' => $level,
                'message' => $message,
                'context' => $context,
                'channel' => $channel,
                'datetime' => now(),
                'environment' => app()->environment(),
            ];

            // Add request context if available
            if (app()->bound('request') && request()) {
                $request = request();
                $logData = array_merge($logData, [
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'url' => $request->fullUrl(),
                    'method' => $request->method(),
                    'session_id' => session()->getId(),
                    'request_id' => $request->header('X-Request-ID', uniqid()),
                ]);
            }

            // Add user context if available
            if (auth()->check()) {
                $logData['user_id'] = auth()->id();
                $logData['organization_id'] = auth()->user()->organization_id;
            }

            // Add performance metrics if available
            if (defined('LARAVEL_START')) {
                $logData['response_time'] = microtime(true) - LARAVEL_START;
                $logData['memory_usage'] = memory_get_usage(true);
            }

            SystemLog::create($logData);

        } catch (\Exception $e) {
            // Fallback to Laravel's default logger if database logging fails
            Log::channel('daily')->error('Failed to log to database: ' . $e->getMessage(), [
                'original_level' => $level,
                'original_message' => $message,
                'original_context' => $context,
            ]);
        }
    }

    /**
     * Get appropriate log level for HTTP status code
     */
    private static function getHttpLogLevel(int $statusCode): string
    {
        if ($statusCode >= 500) {
            return SystemLog::LEVEL_ERROR;
        } elseif ($statusCode >= 400) {
            return SystemLog::LEVEL_WARNING;
        } elseif ($statusCode >= 300) {
            return SystemLog::LEVEL_INFO;
        } else {
            return SystemLog::LEVEL_INFO;
        }
    }

    /**
     * Clean up old logs based on retention policy
     */
    public static function cleanup(int $retentionDays = 30): int
    {
        $cutoffDate = now()->subDays($retentionDays);
        
        return SystemLog::where('datetime', '<', $cutoffDate)->delete();
    }

    /**
     * Get log statistics
     */
    public static function getStats(int $hours = 24): array
    {
        $since = now()->subHours($hours);
        
        return [
            'total' => SystemLog::where('datetime', '>=', $since)->count(),
            'critical' => SystemLog::where('datetime', '>=', $since)->critical()->count(),
            'by_level' => SystemLog::where('datetime', '>=', $since)
                ->selectRaw('level, COUNT(*) as count')
                ->groupBy('level')
                ->pluck('count', 'level')
                ->toArray(),
            'by_channel' => SystemLog::where('datetime', '>=', $since)
                ->selectRaw('channel, COUNT(*) as count')
                ->groupBy('channel')
                ->pluck('count', 'channel')
                ->toArray(),
        ];
    }
}
