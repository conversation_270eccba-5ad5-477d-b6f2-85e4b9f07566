<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CheckRoleAssignments extends Command
{
    protected $signature = 'roles:check-assignments';
    protected $description = 'Check role assignments in the database directly';

    public function handle()
    {
        $this->info('Checking roles table:');
        $roles = DB::table('roles')->get();
        foreach ($roles as $role) {
            $this->info("Role ID: {$role->id}, Name: {$role->name}");
        }

        $this->info("\nChecking role_user assignments:");
        $assignments = DB::table('role_user')
            ->join('users', 'role_user.user_id', '=', 'users.id')
            ->join('roles', 'role_user.role_id', '=', 'roles.id')
            ->select('users.email', 'roles.name as role_name')
            ->get();

        foreach ($assignments as $assignment) {
            $this->info("User: {$assignment->email}, Role: {$assignment->role_name}");
        }
    }
}