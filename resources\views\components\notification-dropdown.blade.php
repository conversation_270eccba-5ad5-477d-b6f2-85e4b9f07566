<div x-data="{ open: false }" @click.away="open = false" class="relative">
    <button @click="open = !open" class="relative p-1 text-gray-400 hover:text-gray-500 focus:outline-none">
        <span class="sr-only">View notifications</span>
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
        </svg>
        @if($unreadCount > 0)
            <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"></span>
        @endif
    </button>

    <div x-show="open"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="absolute right-0 mt-2 w-96 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
        <div class="py-1" role="menu" aria-orientation="vertical">
            @forelse($notifications as $notification)
                <div class="px-4 py-3 {{ $notification->read_at ? 'bg-white' : 'bg-blue-50' }} hover:bg-gray-100">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <p class="text-sm font-medium text-gray-900">
                                    Order #{{ $notification->data['order_number'] }}
                                </p>
                                <span class="text-xs text-gray-500">
                                    {{ \Carbon\Carbon::parse($notification->data['changed_at'])->diffForHumans() }}
                                </span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">
                                {{ $notification->data['customer_name'] }} - {{ $notification->data['order_title'] }}
                            </p>
                            <div class="mt-2 flex items-center">
                                <span class="flex items-center text-sm">
                                    <span class="flex-shrink-0 w-2 h-2 rounded-full 
                                        @if($notification->data['new_status'] === 'Delivered')
                                            bg-green-400
                                        @elseif($notification->data['new_status'] === 'Processing')
                                            bg-blue-400
                                        @elseif($notification->data['new_status'] === 'Completed')
                                            bg-purple-400
                                        @else
                                            bg-yellow-400
                                        @endif
                                        mr-1.5">
                                    </span>
                                    <span class="text-gray-600">Status changed to</span>
                                    <span class="font-medium text-gray-900 ml-1">{{ $notification->data['new_status'] }}</span>
                                </span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">
                                {{ $notification->data['status_description'] }}
                            </p>
                        </div>
                        <div class="ml-4 flex-shrink-0">
                            @unless($notification->read_at)
                                <form action="{{ route('notifications.mark-as-read', $notification->id) }}" method="POST">
                                    @csrf
                                    <button type="submit" class="text-xs text-blue-600 hover:text-blue-800">
                                        Mark as read
                                    </button>
                                </form>
                            @endunless
                        </div>
                    </div>
                </div>
            @empty
                <div class="px-4 py-3 text-sm text-gray-500">
                    No notifications
                </div>
            @endforelse

            @if($notifications->isNotEmpty())
                <div class="border-t border-gray-100 px-4 py-3">
                    <form action="{{ route('notifications.mark-all-as-read') }}" method="POST" class="flex justify-end">
                        @csrf
                        <button type="submit" class="text-xs text-gray-600 hover:text-gray-900">
                            Mark all as read
                        </button>
                    </form>
                </div>
            @endif
        </div>
    </div>
</div>