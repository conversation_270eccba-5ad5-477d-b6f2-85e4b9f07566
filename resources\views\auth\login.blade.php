@extends('layouts.auth')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-5">
        <div class="card shadow-lg">
            <div class="card-header text-white">
                <div class="shape-1"></div>
                <div class="shape-2"></div>
                <h3 class="text-center font-weight-light my-2">Login</h3>
            </div>
            <div class="card-body">
                @if(session('error'))
                <div class="alert alert-danger mb-4">
                    <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                </div>
                @endif

                <form method="POST" action="{{ route('login.submit') }}">
                    @csrf
                    <div class="form-floating mb-4">
                        <input class="form-control @error('email') is-invalid @enderror"
                               id="email" type="email" name="email"
                               value="{{ old('email') }}" placeholder="<EMAIL>" required autofocus />
                        <label for="email"><i class="fas fa-envelope me-2"></i>Email address</label>
                        @error('email')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                        @enderror
                    </div>
                    <div class="form-floating mb-4 position-relative">
                        <input class="form-control @error('password') is-invalid @enderror"
                               id="password" type="password" name="password"
                               placeholder="Password" required />
                        <label for="password"><i class="fas fa-lock me-2"></i>Password</label>
                        <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y text-decoration-none" style="z-index: 10;" onclick="togglePasswordVisibility('password')">
                            <i class="fas fa-eye-slash toggle-password" data-target="password"></i>
                        </button>
                        @error('password')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                        @enderror
                    </div>
                    <div class="form-check mb-4">
                        <input class="form-check-input" id="remember" type="checkbox" name="remember" {{ old('remember') ? 'checked' : '' }} />
                        <label class="form-check-label" for="remember">Remember Me</label>
                    </div>
                    <div class="d-flex align-items-center justify-content-between mt-4 mb-2">
                        <a class="small text-info" href="{{ route('password.request') }}"><i class="fas fa-key me-1"></i>Forgot Password?</a>
                        <button type="submit" class="btn btn-primary px-4">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center py-3">
                <div class="small">
                    <a href="{{ route('register') }}" class="text-info">
                        <i class="fas fa-user-plus me-1"></i>Need a new organization account? Sign up!
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
