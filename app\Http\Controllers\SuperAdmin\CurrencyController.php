<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Models\CurrencyRate;
use App\Models\CurrencyRateHistory;
use App\Models\CurrencySetting;
use App\Services\CurrencyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CurrencyController extends Controller
{
    protected $currencyService;

    public function __construct(CurrencyService $currencyService)
    {
        $this->middleware('super_admin');
        $this->currencyService = $currencyService;
    }

    /**
     * Display currency management dashboard
     */
    public function index()
    {
        $currencies = Currency::getActiveCurrencies();
        $currentRates = CurrencyRate::with(['fromCurrency', 'toCurrency', 'creator'])
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->get();

        $stats = $this->currencyService->getCurrencyStats();

        $recentChanges = CurrencyRateHistory::with(['fromCurrency', 'toCurrency', 'changedBy'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get override status and information
        $overrideEnabled = CurrencySetting::get('system_currency_override_enabled', false);
        $overrideCurrency = CurrencySetting::get('system_currency_override', 'USD');
        $overrideIp = CurrencySetting::get('override_ip_address', '');
        $overrideMethod = CurrencySetting::get('override_method', 'direct');

        $overrideInfo = [
            'enabled' => $overrideEnabled,
            'currency' => $overrideCurrency,
            'ip_address' => $overrideIp,
            'method' => $overrideMethod,
        ];

        return view('super_admin.currency.index', compact(
            'currencies',
            'currentRates',
            'stats',
            'recentChanges',
            'overrideInfo'
        ));
    }

    /**
     * Show form to update exchange rate
     */
    public function editRate($fromCurrency, $toCurrency)
    {
        $currentRate = CurrencyRate::where('from_currency', $fromCurrency)
            ->where('to_currency', $toCurrency)
            ->where('is_active', true)
            ->first();

        $fromCurrencyObj = Currency::getByCode($fromCurrency);
        $toCurrencyObj = Currency::getByCode($toCurrency);

        if (!$fromCurrencyObj || !$toCurrencyObj) {
            return redirect()->route('super_admin.currency.index')
                ->with('error', 'Invalid currency pair');
        }

        $history = $this->currencyService->getRateHistory($fromCurrency, $toCurrency, 20);

        return view('super_admin.currency.edit-rate', compact(
            'currentRate',
            'fromCurrencyObj',
            'toCurrencyObj',
            'history'
        ));
    }

    /**
     * Update exchange rate
     */
    public function updateRate(Request $request, $fromCurrency, $toCurrency)
    {
        $validator = Validator::make($request->all(), [
            'rate' => 'required|numeric|min:0.000001|max:999999.999999',
            'reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Get the Super Admin user ID
            $userId = auth()->guard('super_admin')->id() ?: auth()->id();

            // Debug logging
            \Log::info('Currency rate update attempt', [
                'from_currency' => $fromCurrency,
                'to_currency' => $toCurrency,
                'rate' => $request->rate,
                'user_id' => $userId,
                'reason' => $request->reason,
                'super_admin_guard' => auth()->guard('super_admin')->check(),
                'regular_auth' => auth()->check(),
            ]);

            $this->currencyService->updateExchangeRate(
                $fromCurrency,
                $toCurrency,
                $request->rate,
                $userId,
                $request->reason
            );

            return redirect()->route('super_admin.currency.index')
                ->with('success', "Exchange rate updated successfully: 1 {$fromCurrency} = {$request->rate} {$toCurrency}");

        } catch (\Exception $e) {
            \Log::error('Currency rate update failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'from_currency' => $fromCurrency,
                'to_currency' => $toCurrency,
                'rate' => $request->rate,
            ]);

            return redirect()->back()
                ->with('error', 'Failed to update exchange rate: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show rate history
     */
    public function rateHistory($fromCurrency, $toCurrency)
    {
        $fromCurrencyObj = Currency::getByCode($fromCurrency);
        $toCurrencyObj = Currency::getByCode($toCurrency);

        if (!$fromCurrencyObj || !$toCurrencyObj) {
            return redirect()->route('super_admin.currency.index')
                ->with('error', 'Invalid currency pair');
        }

        $history = CurrencyRateHistory::where('from_currency', $fromCurrency)
            ->where('to_currency', $toCurrency)
            ->with('changedBy')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('super_admin.currency.rate-history', compact(
            'history',
            'fromCurrencyObj',
            'toCurrencyObj'
        ));
    }

    /**
     * Show currency settings
     */
    public function settings()
    {
        $settings = CurrencySetting::getAll();
        
        return view('super_admin.currency.settings', compact('settings'));
    }

    /**
     * Update currency settings
     */
    public function updateSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'default_currency' => 'required|string|size:3|exists:currencies,code',
            'auto_currency_detection' => 'boolean',
            'currency_decimal_places' => 'required|integer|min:0|max:6',
            'nigerian_ip_ranges' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Update settings
            CurrencySetting::set('default_currency', $request->default_currency);
            CurrencySetting::set('auto_currency_detection', $request->has('auto_currency_detection'), 'boolean');
            CurrencySetting::set('currency_decimal_places', $request->currency_decimal_places, 'integer');
            
            // Update Nigerian IP ranges
            if ($request->nigerian_ip_ranges) {
                $ipRanges = array_filter(
                    array_map('trim', explode("\n", $request->nigerian_ip_ranges))
                );
                CurrencySetting::set('nigerian_ip_ranges', $ipRanges, 'json');
            }

            return redirect()->route('super_admin.currency.settings')
                ->with('success', 'Currency settings updated successfully');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update settings: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Get current rate via AJAX
     */
    public function getCurrentRate($fromCurrency, $toCurrency)
    {
        $rate = $this->currencyService->getExchangeRate($fromCurrency, $toCurrency);
        
        return response()->json([
            'rate' => $rate,
            'formatted' => $rate ? number_format($rate, 6) : null,
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency,
        ]);
    }

    /**
     * Test IP geolocation
     */
    public function testGeolocation(Request $request)
    {
        $ipAddress = $request->ip_address ?: $request->ip();
        
        $geolocationService = app(\App\Services\IpGeolocationService::class);
        $locationInfo = $geolocationService->getLocationInfo($ipAddress);
        $detectedCurrency = $geolocationService->detectCurrencyByIp($ipAddress);
        
        return response()->json([
            'ip_address' => $ipAddress,
            'location_info' => $locationInfo,
            'detected_currency' => $detectedCurrency,
        ]);
    }

    /**
     * Bulk update rates (for future use)
     */
    public function bulkUpdateRates(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'rates' => 'required|array',
            'rates.*.from_currency' => 'required|string|size:3',
            'rates.*.to_currency' => 'required|string|size:3',
            'rates.*.rate' => 'required|numeric|min:0.000001',
            'reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $updatedRates = [];
            $userId = auth()->guard('super_admin')->id() ?: auth()->id();
            
            foreach ($request->rates as $rateData) {
                $updatedRate = $this->currencyService->updateExchangeRate(
                    $rateData['from_currency'],
                    $rateData['to_currency'],
                    $rateData['rate'],
                    $userId,
                    $request->reason
                );
                
                $updatedRates[] = $updatedRate;
            }

            return response()->json([
                'success' => true,
                'message' => 'Exchange rates updated successfully',
                'updated_rates' => $updatedRates,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update exchange rates: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Set system-wide currency override
     */
    public function setCurrencyOverride(Request $request)
    {
        $request->validate([
            'method' => 'required|in:direct,ip_simulation',
            'currency' => 'required_if:method,direct|in:USD,NGN',
            'ip_address' => 'required_if:method,ip_simulation|ip',
        ]);

        try {
            $method = $request->method;
            $currency = null;
            $ipAddress = '';

            if ($method === 'direct') {
                $currency = $request->currency;
            } else {
                // IP simulation method
                $ipAddress = $request->ip_address;
                $ipGeolocationService = app(\App\Services\IpGeolocationService::class);
                $currency = $ipGeolocationService->detectCurrencyByIp($ipAddress);
            }

            // Update settings
            CurrencySetting::set('system_currency_override_enabled', true);
            CurrencySetting::set('system_currency_override', $currency);
            CurrencySetting::set('override_ip_address', $ipAddress);
            CurrencySetting::set('override_method', $method);

            // Clear all user currency sessions to force re-detection
            $this->clearAllUserCurrencySessions();

            // Log the override action
            \Log::info('System currency override enabled', [
                'super_admin_id' => auth()->guard('super_admin')->id(),
                'method' => $method,
                'currency' => $currency,
                'ip_address' => $ipAddress,
            ]);

            $message = $method === 'direct'
                ? "System currency override enabled. All users will now see prices in {$currency}."
                : "System currency override enabled using IP {$ipAddress}. All users will now see prices in {$currency}.";

            return redirect()->route('super_admin.currency.index')
                ->with('success', $message);

        } catch (\Exception $e) {
            \Log::error('Failed to set currency override', [
                'error' => $e->getMessage(),
                'method' => $request->method,
                'currency' => $request->currency,
                'ip_address' => $request->ip_address,
            ]);

            return redirect()->back()
                ->with('error', 'Failed to set currency override: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Disable system-wide currency override
     */
    public function disableCurrencyOverride()
    {
        try {
            // Disable override
            CurrencySetting::set('system_currency_override_enabled', false);
            CurrencySetting::set('system_currency_override', 'USD');
            CurrencySetting::set('override_ip_address', '');
            CurrencySetting::set('override_method', 'direct');

            // Clear all user currency sessions to force re-detection
            $this->clearAllUserCurrencySessions();

            // Log the action
            \Log::info('System currency override disabled', [
                'super_admin_id' => auth()->guard('super_admin')->id(),
            ]);

            return redirect()->route('super_admin.currency.index')
                ->with('success', 'System currency override disabled. Users will now see currency based on their IP location.');

        } catch (\Exception $e) {
            \Log::error('Failed to disable currency override', [
                'error' => $e->getMessage(),
            ]);

            return redirect()->back()
                ->with('error', 'Failed to disable currency override: ' . $e->getMessage());
        }
    }

    /**
     * Test IP address for currency detection
     */
    public function testIpCurrency(Request $request)
    {
        $request->validate([
            'ip_address' => 'required|ip',
        ]);

        try {
            $ipGeolocationService = app(\App\Services\IpGeolocationService::class);
            $currency = $ipGeolocationService->detectCurrencyByIp($request->ip_address);
            $locationInfo = $ipGeolocationService->getLocationInfo($request->ip_address);

            return response()->json([
                'success' => true,
                'currency' => $currency,
                'location_info' => $locationInfo,
                'message' => "IP {$request->ip_address} would use currency: {$currency}"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to test IP: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear all user currency sessions
     */
    protected function clearAllUserCurrencySessions()
    {
        // Note: This is a simplified approach. In a production environment,
        // you might want to use a more sophisticated session management system
        // or store currency preferences in the database.

        // For now, we'll rely on the middleware to re-detect currency on next request
        // when it sees that the override settings have changed

        \Log::info('Currency sessions cleared due to override change');
    }
}
