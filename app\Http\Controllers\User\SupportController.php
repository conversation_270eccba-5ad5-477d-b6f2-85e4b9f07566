<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\SupportTicket;
use App\Models\SupportTicketReply;
use App\Models\KnowledgeBaseArticle;
use App\Models\KnowledgeBaseCategory;
use App\Services\LogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SupportController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display user support dashboard
     */
    public function index()
    {
        $user = Auth::user();

        // Get user's tickets
        $tickets = SupportTicket::where('user_id', $user->id)
            ->orWhere('organization_id', $user->organization_id)
            ->with(['assignedAdmin', 'latestReply'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Get ticket statistics
        $stats = [
            'total' => SupportTicket::where('user_id', $user->id)->count(),
            'open' => SupportTicket::where('user_id', $user->id)->open()->count(),
            'resolved' => SupportTicket::where('user_id', $user->id)->where('status', 'resolved')->count(),
            'avg_response_time' => $this->getAverageResponseTime($user->id),
        ];

        // Get recent knowledge base articles
        $recentArticles = KnowledgeBaseArticle::published()
            ->whereIn('visibility', ['public', 'customers_only'])
            ->with('category')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get popular articles
        $popularArticles = KnowledgeBaseArticle::published()
            ->whereIn('visibility', ['public', 'customers_only'])
            ->with('category')
            ->orderBy('view_count', 'desc')
            ->limit(5)
            ->get();

        return view('user.support.index', compact(
            'tickets',
            'stats',
            'recentArticles',
            'popularArticles'
        ));
    }

    /**
     * Show form to create a new ticket
     */
    public function create()
    {
        return view('user.support.create');
    }

    /**
     * Store a new support ticket
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|in:' . implode(',', array_keys(SupportTicket::getCategoryOptions())),
            'priority' => 'in:' . implode(',', array_keys(SupportTicket::getPriorityOptions())),
        ]);

        $user = Auth::user();

        $ticket = SupportTicket::create([
            'ticket_number' => SupportTicket::generateTicketNumber(),
            'title' => $request->title,
            'description' => $request->description,
            'category' => $request->category,
            'priority' => $request->priority ?? 'normal',
            'status' => SupportTicket::STATUS_OPEN,
            'user_id' => $user->id,
            'organization_id' => $user->organization_id,
        ]);

        // Log ticket creation
        LogService::info('Support ticket created by user', [
            'ticket_id' => $ticket->id,
            'ticket_number' => $ticket->ticket_number,
            'user_id' => $user->id,
            'organization_id' => $user->organization_id,
            'category' => $ticket->category,
            'priority' => $ticket->priority,
        ]);

        return redirect()->route('user.support.show', $ticket)
            ->with('success', 'Support ticket created successfully. Our team will respond soon.');
    }

    /**
     * Display a specific ticket
     */
    public function show(SupportTicket $ticket)
    {
        $user = Auth::user();

        // Check if user can view this ticket
        if ($ticket->user_id !== $user->id && $ticket->organization_id !== $user->organization_id) {
            abort(403, 'You do not have permission to view this ticket.');
        }

        $ticket->load(['assignedAdmin', 'replies.replier', 'organization']);

        return view('user.support.show', compact('ticket'));
    }

    /**
     * Add a reply to a ticket
     */
    public function reply(Request $request, SupportTicket $ticket)
    {
        $user = Auth::user();

        // Check if user can reply to this ticket
        if ($ticket->user_id !== $user->id && $ticket->organization_id !== $user->organization_id) {
            abort(403, 'You do not have permission to reply to this ticket.');
        }

        $request->validate([
            'message' => 'required|string',
        ]);

        // Don't allow replies to closed tickets
        if ($ticket->status === SupportTicket::STATUS_CLOSED) {
            return back()->with('error', 'Cannot reply to a closed ticket.');
        }

        $reply = SupportTicketReply::createFromUser($ticket, $user, $request->message);

        LogService::info('User replied to support ticket', [
            'ticket_id' => $ticket->id,
            'reply_id' => $reply->id,
            'user_id' => $user->id,
        ]);

        return back()->with('success', 'Your reply has been added. We will respond soon.');
    }

    /**
     * Display knowledge base
     */
    public function knowledgeBase()
    {
        $categories = KnowledgeBaseCategory::active()
            ->ordered()
            ->withCount(['publishedArticles' => function($query) {
                $query->whereIn('visibility', ['public', 'customers_only']);
            }])
            ->get();

        $featuredArticles = KnowledgeBaseArticle::published()
            ->whereIn('visibility', ['public', 'customers_only'])
            ->featured()
            ->with('category')
            ->limit(6)
            ->get();

        $popularArticles = KnowledgeBaseArticle::published()
            ->whereIn('visibility', ['public', 'customers_only'])
            ->with('category')
            ->orderBy('view_count', 'desc')
            ->limit(6)
            ->get();

        return view('user.support.knowledge-base', compact(
            'categories',
            'featuredArticles',
            'popularArticles'
        ));
    }

    /**
     * Display articles in a category
     */
    public function categoryArticles($categoryId)
    {
        $category = KnowledgeBaseCategory::findOrFail($categoryId);

        $articles = KnowledgeBaseArticle::published()
            ->whereIn('visibility', ['public', 'customers_only'])
            ->where('category_id', $category->id)
            ->with('category')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('user.support.category-articles', compact('category', 'articles'));
    }

    /**
     * Display a knowledge base article
     */
    public function article($articleId)
    {
        $article = KnowledgeBaseArticle::with('category')->findOrFail($articleId);

        // Check if article is accessible
        if (!in_array($article->visibility, ['public', 'customers_only']) || $article->status !== 'published') {
            abort(404);
        }

        // Increment view count
        $article->incrementViews();

        // Get related articles
        $relatedArticles = KnowledgeBaseArticle::published()
            ->whereIn('visibility', ['public', 'customers_only'])
            ->where('category_id', $article->category_id)
            ->where('id', '!=', $article->id)
            ->with('category')
            ->limit(3)
            ->get();

        return view('user.support.article', compact('article', 'relatedArticles'));
    }

    /**
     * Search knowledge base articles
     */
    public function search(Request $request)
    {
        $query = $request->get('q');

        if (empty($query)) {
            return redirect()->route('user.support.knowledge-base');
        }

        $articles = KnowledgeBaseArticle::published()
            ->whereIn('visibility', ['public', 'customers_only'])
            ->search($query)
            ->with('category')
            ->paginate(12)
            ->appends(['q' => $query]);

        return view('user.support.search-results', compact('articles', 'query'));
    }

    /**
     * Rate an article as helpful or not helpful
     */
    public function rateArticle(Request $request, $articleId)
    {
        $article = KnowledgeBaseArticle::findOrFail($articleId);

        $request->validate([
            'helpful' => 'required|boolean',
            'comment' => 'nullable|string|max:500',
        ]);

        // Check if user already rated this article
        $existingFeedback = \App\Models\KnowledgeBaseFeedback::where([
            'knowledge_base_article_id' => $article->id,
            'user_id' => Auth::id(),
        ])->first();

        if ($existingFeedback) {
            return response()->json([
                'success' => false,
                'message' => 'You have already rated this article.'
            ]);
        }

        // Create feedback
        \App\Models\KnowledgeBaseFeedback::create([
            'knowledge_base_article_id' => $article->id,
            'user_id' => Auth::id(),
            'is_helpful' => $request->helpful,
            'comment' => $request->comment,
            'ip_address' => $request->ip(),
        ]);

        // Update article counters
        if ($request->helpful) {
            $article->markAsHelpful();
        } else {
            $article->markAsNotHelpful();
        }

        LogService::info('User rated knowledge base article', [
            'article_id' => $article->id,
            'user_id' => Auth::id(),
            'is_helpful' => $request->helpful,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Thank you for your feedback!'
        ]);
    }

    /**
     * Get average response time for user's tickets
     */
    private function getAverageResponseTime($userId)
    {
        $tickets = SupportTicket::where('user_id', $userId)
            ->whereNotNull('first_response_at')
            ->get();

        if ($tickets->isEmpty()) {
            return 0;
        }

        $totalHours = $tickets->sum(function($ticket) {
            return $ticket->response_time;
        });

        return round($totalHours / $tickets->count(), 1);
    }
}
