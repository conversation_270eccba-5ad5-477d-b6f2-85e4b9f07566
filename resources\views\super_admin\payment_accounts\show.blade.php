@extends('super_admin.layouts.app')

@section('title', 'Payment Account Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Payment Account Details</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('super_admin.payment-accounts.index') }}">Payment Accounts</a>
                            </li>
                            <li class="breadcrumb-item active">{{ $paymentAccount->bank_name }}</li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-group" role="group">
                    <a href="{{ route('super_admin.payment-accounts.edit', $paymentAccount) }}" 
                       class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit Account
                    </a>
                    <a href="{{ route('super_admin.payment-accounts.index') }}" 
                       class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error') || $errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') ?? $errors->first() }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <!-- Account Information Card -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-university"></i> Account Information
                            </h5>
                            <div>
                                @if($paymentAccount->is_primary)
                                    <span class="badge bg-primary">Primary Account</span>
                                @endif
                                @if($paymentAccount->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-danger">Inactive</span>
                                @endif
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Bank Name</label>
                                        <div class="fw-bold">{{ $paymentAccount->bank_name }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Account Name</label>
                                        <div class="fw-bold">{{ $paymentAccount->account_name }}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Account Number</label>
                                        <div class="d-flex align-items-center">
                                            <code class="me-2" id="maskedAccountNumber">{{ $paymentAccount->masked_account_number }}</code>
                                            <button class="btn btn-sm btn-outline-secondary" 
                                                    onclick="toggleAccountNumber()"
                                                    title="Show/Hide full account number">
                                                <i class="fas fa-eye" id="eyeIcon"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary ms-1" 
                                                    onclick="copyAccountNumber()"
                                                    title="Copy account number">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                        <div class="d-none" id="fullAccountNumber">{{ $paymentAccount->account_number }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Account Type</label>
                                        <div>
                                            <span class="badge bg-secondary">{{ ucfirst($paymentAccount->account_type) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            @if($paymentAccount->routing_number || $paymentAccount->swift_code)
                                <div class="row">
                                    @if($paymentAccount->routing_number)
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label text-muted">Routing Number</label>
                                                <div class="fw-bold">{{ $paymentAccount->routing_number }}</div>
                                            </div>
                                        </div>
                                    @endif
                                    @if($paymentAccount->swift_code)
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label text-muted">SWIFT Code</label>
                                                <div class="fw-bold">{{ $paymentAccount->swift_code }}</div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            @endif

                            @if($paymentAccount->additional_instructions)
                                <div class="mb-3">
                                    <label class="form-label text-muted">Additional Instructions</label>
                                    <div class="border rounded p-3 bg-light">
                                        {{ $paymentAccount->additional_instructions }}
                                    </div>
                                </div>
                            @endif

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Created</label>
                                        <div>{{ $paymentAccount->created_at->format('M d, Y \a\t g:i A') }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Last Updated</label>
                                        <div>{{ $paymentAccount->updated_at->format('M d, Y \a\t g:i A') }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions Card -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-cogs"></i> Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('super_admin.payment-accounts.edit', $paymentAccount) }}" 
                                   class="btn btn-warning">
                                    <i class="fas fa-edit"></i> Edit Account
                                </a>

                                @if(!$paymentAccount->is_primary)
                                    <form method="POST" action="{{ route('super_admin.payment-accounts.set-primary', $paymentAccount) }}">
                                        @csrf
                                        <button type="submit" class="btn btn-primary w-100" 
                                                onclick="return confirm('Set this as the primary account?')">
                                            <i class="fas fa-star"></i> Set as Primary
                                        </button>
                                    </form>
                                @endif

                                <form method="POST" action="{{ route('super_admin.payment-accounts.toggle-status', $paymentAccount) }}">
                                    @csrf
                                    <button type="submit" class="btn btn-{{ $paymentAccount->is_active ? 'danger' : 'success' }} w-100"
                                            onclick="return confirm('{{ $paymentAccount->is_active ? 'Deactivate' : 'Activate' }} this account?')">
                                        <i class="fas fa-{{ $paymentAccount->is_active ? 'ban' : 'check' }}"></i> 
                                        {{ $paymentAccount->is_active ? 'Deactivate' : 'Activate' }}
                                    </button>
                                </form>

                                @if(!$paymentAccount->is_primary)
                                    <form method="POST" action="{{ route('super_admin.payment-accounts.destroy', $paymentAccount) }}">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger w-100"
                                                onclick="return confirm('Are you sure you want to delete this account? This action cannot be undone.')">
                                            <i class="fas fa-trash"></i> Delete Account
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Formatted Details Card -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-file-alt"></i> Formatted Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="bg-light border rounded p-3">
                                <pre class="mb-0" style="white-space: pre-wrap;">{{ $paymentAccount->formatted_details }}</pre>
                            </div>
                            <button class="btn btn-outline-secondary btn-sm mt-2 w-100" 
                                    onclick="copyFormattedDetails()">
                                <i class="fas fa-copy"></i> Copy Details
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let showingFullNumber = false;

function toggleAccountNumber() {
    const maskedElement = document.getElementById('maskedAccountNumber');
    const fullElement = document.getElementById('fullAccountNumber');
    const eyeIcon = document.getElementById('eyeIcon');

    if (showingFullNumber) {
        maskedElement.textContent = '{{ $paymentAccount->masked_account_number }}';
        eyeIcon.className = 'fas fa-eye';
        showingFullNumber = false;
    } else {
        maskedElement.textContent = fullElement.textContent;
        eyeIcon.className = 'fas fa-eye-slash';
        showingFullNumber = true;
    }
}

function copyAccountNumber() {
    const accountNumber = '{{ $paymentAccount->account_number }}';
    navigator.clipboard.writeText(accountNumber).then(() => {
        // Show temporary success message
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');

        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    }).catch(() => {
        alert('Failed to copy account number');
    });
}

function copyFormattedDetails() {
    const details = `{{ $paymentAccount->formatted_details }}`;
    navigator.clipboard.writeText(details).then(() => {
        // Show temporary success message
        const button = event.target;
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> Copied!';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');

        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    }).catch(() => {
        alert('Failed to copy details');
    });
}
</script>
@endsection
