<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Models\Order;

class OrderStatusChanged extends Notification implements ShouldQueue
{
    use Queueable;

    protected $order;
    protected $oldStatus;
    protected $newStatus;

    public function __construct(Order $order, $oldStatus, $newStatus)
    {
        $this->order = $order;
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;
    }

    public function via($notifiable)
    {
        return ['database'];
    }

    public function toArray($notifiable)
    {
        $statusDescriptions = [
            'Pending' => 'awaiting processing',
            'Processing' => 'in production',
            'Completed' => 'ready for delivery',
            'Delivered' => 'successfully delivered'
        ];

        return [
            'order_id' => $this->order->id,
            'order_number' => $this->order->order_number,
            'customer_name' => $this->order->customer_name,
            'order_title' => $this->order->order_title,
            'old_status' => $this->oldStatus,
            'new_status' => $this->newStatus,
            'status_description' => $statusDescriptions[$this->newStatus] ?? '',
            'changed_by' => auth()->user()->name ?? 'System',
            'changed_at' => now()->toDateTimeString(),
            'amount' => $this->order->total_amount,
            'expected_delivery' => $this->order->expected_delivery_date
        ];
    }

    public function toDatabase($notifiable)
    {
        return $this->toArray($notifiable);
    }
}