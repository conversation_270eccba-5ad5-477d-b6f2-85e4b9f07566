<?php

namespace App\Http\Controllers\Affiliate;

use App\Http\Controllers\Controller;
use App\Models\Affiliate;
use App\Models\AffiliateWithdrawal;
use App\Models\AffiliateSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class WithdrawalController extends Controller
{
    public function __construct()
    {
        // Middleware is applied at route level
    }

    /**
     * Display withdrawal history
     */
    public function index(Request $request)
    {
        $affiliate = $request->affiliate ?? $this->getAffiliate();

        if (!$affiliate || !$affiliate->isActive()) {
            return redirect()->route('affiliate.dashboard');
        }

        $query = $affiliate->withdrawals()->with(['processedBy']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('requested_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('requested_at', '<=', $request->date_to);
        }

        $withdrawals = $query->latest('requested_at')->paginate(20);

        // Get withdrawal summary
        $summary = [
            'total_requested' => $affiliate->withdrawals()
                ->whereNotIn('status', [AffiliateWithdrawal::STATUS_REJECTED, AffiliateWithdrawal::STATUS_CANCELLED])
                ->sum('amount'),
            'total_paid' => $affiliate->withdrawals()->where('status', AffiliateWithdrawal::STATUS_PAID)->sum('amount'),
            'pending_amount' => $affiliate->withdrawals()->where('status', AffiliateWithdrawal::STATUS_PENDING)->sum('amount'),
            'available_balance' => $affiliate->available_balance,
        ];

        return view('affiliate.withdrawals.index', compact('affiliate', 'withdrawals', 'summary'));
    }

    /**
     * Show withdrawal request form
     */
    public function create(Request $request)
    {
        $affiliate = $request->affiliate ?? $this->getAffiliate();

        if (!$affiliate || !$affiliate->isActive()) {
            return redirect()->route('affiliate.dashboard');
        }

        $settings = AffiliateSetting::getInstance();

        // Check if affiliate has sufficient balance
        if ($affiliate->available_balance < $settings->minimum_withdrawal) {
            return redirect()->route('affiliate.withdrawals')
                ->with('error', "Minimum withdrawal amount is {$settings->formatted_minimum_withdrawal}. Your available balance is \${$affiliate->available_balance}.");
        }

        // Check for pending withdrawals
        $pendingWithdrawal = $affiliate->withdrawals()
            ->where('status', AffiliateWithdrawal::STATUS_PENDING)
            ->first();

        if ($pendingWithdrawal) {
            return redirect()->route('affiliate.withdrawals')
                ->with('error', 'You already have a pending withdrawal request. Please wait for it to be processed before requesting another withdrawal.');
        }

        return view('affiliate.withdrawals.create', compact('affiliate', 'settings'));
    }

    /**
     * Store withdrawal request
     */
    public function store(Request $request)
    {
        $affiliate = $request->affiliate ?? $this->getAffiliate();

        if (!$affiliate || !$affiliate->isActive()) {
            return redirect()->route('affiliate.dashboard');
        }

        $settings = AffiliateSetting::getInstance();

        // Clean the request data based on payment method
        $requestData = $request->all();

        // Remove irrelevant fields based on payment method to prevent validation issues
        if ($request->payment_method === 'bank_transfer') {
            // Remove PayPal fields when bank transfer is selected
            unset($requestData['paypal_email']);
        } elseif ($request->payment_method === 'paypal') {
            // Remove bank fields when PayPal is selected
            unset($requestData['bank_name'], $requestData['account_number'], $requestData['account_name'], $requestData['routing_number']);
        }

        // Replace the request data
        $request->replace($requestData);

        $validationRules = [
            'amount' => [
                'required',
                'numeric',
                'min:' . $settings->minimum_withdrawal,
                'max:' . $affiliate->available_balance
            ],
            'payment_method' => ['required', 'in:bank_transfer,paypal'],
            'notes' => ['nullable', 'string', 'max:500'],
        ];

        // Add payment method specific validation rules
        if ($request->payment_method === 'bank_transfer') {
            $validationRules['bank_name'] = ['required', 'string', 'max:255'];
            $validationRules['account_number'] = ['required', 'string', 'max:255'];
            $validationRules['account_name'] = ['required', 'string', 'max:255'];
            $validationRules['routing_number'] = ['nullable', 'string', 'max:255'];
        } elseif ($request->payment_method === 'paypal') {
            $validationRules['paypal_email'] = ['required', 'email', 'max:255'];
        }

        $request->validate($validationRules);

        // Check for pending withdrawals again
        $pendingWithdrawal = $affiliate->withdrawals()
            ->where('status', AffiliateWithdrawal::STATUS_PENDING)
            ->first();

        if ($pendingWithdrawal) {
            return back()->withErrors(['error' => 'You already have a pending withdrawal request.']);
        }

        try {
            DB::beginTransaction();

            // Prepare payment details
            $paymentDetails = [];
            if ($request->payment_method === 'bank_transfer') {
                $paymentDetails = [
                    'method' => 'bank_transfer',
                    'bank_name' => $request->bank_name,
                    'account_number' => $request->account_number,
                    'account_name' => $request->account_name,
                    'routing_number' => $request->routing_number,
                ];
            } else {
                $paymentDetails = [
                    'method' => 'paypal',
                    'paypal_email' => $request->paypal_email,
                ];
            }

            // Calculate fees
            $feeAmount = $settings->calculateWithdrawalFee($request->amount);
            $netAmount = $request->amount - $feeAmount;

            // Create withdrawal request
            $withdrawal = AffiliateWithdrawal::create([
                'affiliate_id' => $affiliate->id,
                'amount' => $request->amount,
                'payment_method' => $request->payment_method,
                'payment_details' => $paymentDetails,
                'status' => AffiliateWithdrawal::STATUS_PENDING,
                'fee_amount' => $feeAmount,
                'net_amount' => $netAmount,
                'notes' => $request->notes,
            ]);

            // Deduct amount from available balance
            $affiliate->processWithdrawal($request->amount);

            DB::commit();

            // TODO: Send notification to admins
            $this->notifyAdminsOfWithdrawalRequest($withdrawal);

            return redirect()->route('affiliate.withdrawals')
                ->with('success', 'Withdrawal request submitted successfully. You will be notified once it is processed.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Failed to submit withdrawal request: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Show withdrawal details
     */
    public function show(Request $request, AffiliateWithdrawal $withdrawal)
    {
        $affiliate = $request->affiliate ?? $this->getAffiliate();

        if (!$affiliate || $withdrawal->affiliate_id !== $affiliate->id) {
            abort(404);
        }

        return view('affiliate.withdrawals.show', compact('affiliate', 'withdrawal'));
    }

    /**
     * Cancel withdrawal request
     */
    public function cancel(Request $request, AffiliateWithdrawal $withdrawal)
    {
        $affiliate = $request->affiliate ?? $this->getAffiliate();

        if (!$affiliate || $withdrawal->affiliate_id !== $affiliate->id) {
            abort(404);
        }

        if (!$withdrawal->isPending()) {
            return back()->withErrors(['error' => 'Only pending withdrawals can be cancelled.']);
        }

        try {
            $withdrawal->cancel('Cancelled by affiliate');

            return redirect()->route('affiliate.withdrawals')
                ->with('success', 'Withdrawal request cancelled successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to cancel withdrawal: ' . $e->getMessage()]);
        }
    }

    /**
     * Get the current user's affiliate record
     */
    protected function getAffiliate(): ?Affiliate
    {
        return Affiliate::where('user_id', Auth::guard('affiliate')->id())->first();
    }

    /**
     * Notify admins of new withdrawal request
     */
    protected function notifyAdminsOfWithdrawalRequest(AffiliateWithdrawal $withdrawal)
    {
        try {
            // TODO: Implement email notification to super admins
            \Log::info('New withdrawal request submitted', [
                'withdrawal_id' => $withdrawal->id,
                'affiliate_id' => $withdrawal->affiliate_id,
                'amount' => $withdrawal->amount
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to notify admins of withdrawal request', [
                'withdrawal_id' => $withdrawal->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
