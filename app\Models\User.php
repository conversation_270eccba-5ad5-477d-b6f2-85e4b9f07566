<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use App\Http\Traits\LogsUserActivity;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Notifications\CustomVerifyEmailNotification;
use App\Notifications\CustomResetPasswordNotification;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable, LogsUserActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'status',
        'last_deactivated_at',
        'deactivated_by',
        'archived_at',
        'archived_by',
        'phone',
        'position',
        'bio',
        'profile_photo_path',
        'organization_id',
        'branch_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'last_deactivated_at' => 'datetime',
        'archived_at' => 'datetime',
    ];

    // Cache duration for role checks
    const ROLE_CACHE_DURATION = 60; // 1 hour in minutes

    // User status constants
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_ARCHIVED = 'archived';

    protected static function boot()
    {
        parent::boot();

        // Log important user changes
        static::created(function ($user) {
            Log::info('New user created', ['user_id' => $user->id, 'email' => $user->email]);
        });

        static::updated(function ($user) {
            $changes = $user->getChanges();
            if (!empty($changes)) {
                Log::info('User updated', [
                    'user_id' => $user->id,
                    'changes' => array_diff_assoc($changes, ['updated_at' => $changes['updated_at'] ?? null])
                ]);
            }
        });

        static::deleted(function ($user) {
            Log::info('User deleted', ['user_id' => $user->id, 'email' => $user->email]);
        });
    }

    /**
     * Get the organization that the user belongs to.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the branch that the user belongs to.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get user activities
     */
    public function activities()
    {
        return $this->hasMany(UserActivity::class);
    }

    /**
     * Format role name consistently
     */
    protected function formatRoleName($role)
    {
        if (is_array($role)) {
            return array_map(function($r) {
                return trim(str_replace('-', ' ', $r));
            }, $role);
        }
        return trim(str_replace('-', ' ', $role));
    }

    /**
     * The roles that belong to the user.
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'role_user', 'user_id', 'role_id')
                    ->withTimestamps();
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole($role)
    {
        if (is_array($role)) {
            return $this->hasAnyRole($role);
        }

        return Cache::remember(
            "user_{$this->id}_has_role_" . trim($role),
            self::ROLE_CACHE_DURATION,
            fn() => $this->roles()->where('roles.name', trim($role))->exists()
        );
    }

    /**
     * Check if user has any of the given roles
     */
    public function hasAnyRole($roles)
    {
        if (empty($roles)) {
            return false;
        }

        $roleNames = collect(is_string($roles) ? explode('|', $roles) : $roles)
            ->map(fn($role) => trim($role))
            ->filter()
            ->values()
            ->toArray();

        if (empty($roleNames)) {
            return false;
        }

        return Cache::remember(
            "user_{$this->id}_has_any_role_" . implode('_', $roleNames),
            self::ROLE_CACHE_DURATION,
            fn() => $this->roles()->whereIn('roles.name', $roleNames)->exists()
        );
    }

    /**
     * Check if user has all given roles
     */
    public function hasAllRoles($roles)
    {
        if (empty($roles)) {
            return false;
        }

        $roleNames = collect(is_string($roles) ? explode(',', $roles) : $roles)
            ->map(fn($role) => trim($role))
            ->filter()
            ->values()
            ->toArray();

        if (empty($roleNames)) {
            return false;
        }

        return Cache::remember(
            "user_{$this->id}_has_all_roles_" . implode('_', $roleNames),
            self::ROLE_CACHE_DURATION,
            function() use ($roleNames) {
                $userRoles = $this->roles()->pluck('name')->toArray();
                return count(array_intersect($roleNames, $userRoles)) === count($roleNames);
            }
        );
    }

    /**
     * Get all permissions for the user's roles
     */
    public function getAllPermissions()
    {
        return Cache::remember(
            "user_{$this->id}_permissions",
            self::ROLE_CACHE_DURATION,
            fn() => $this->roles()->with('permissions')->get()
                ->pluck('permissions')
                ->flatten()
                ->unique('id')
        );
    }

    // Custom authentication using email field as username
    public function username()
    {
        return 'email';
    }

    public function toggleStatus()
    {
        $oldStatus = $this->status;
        $this->status = $this->status === 'active' ? 'inactive' : 'active';
        $this->save();

        // Clear user's role cache after status change
        $this->clearRoleCache();

        // Log the status change
        Log::info('User status toggled', [
            'user_id' => $this->id,
            'old_status' => $oldStatus,
            'new_status' => $this->status,
            'changed_by' => auth()->id() ?? 'system'
        ]);

        return $this;
    }

    /**
     * Send the email verification notification.
     */
    public function sendEmailVerificationNotification()
    {
        $this->notify(new CustomVerifyEmailNotification);
    }

    /**
     * Send the password reset notification.
     */
    public function sendPasswordResetNotification($token)
    {
        // Check if user is an affiliate
        $affiliate = \App\Models\Affiliate::where('user_id', $this->id)->first();

        if ($affiliate) {
            $this->notify(new \App\Notifications\AffiliateResetPasswordNotification($token));
        } else {
            $this->notify(new CustomResetPasswordNotification($token));
        }
    }

    public function isActive()
    {
        return $this->status === 'active';
    }

    protected function clearRoleCache()
    {
        Cache::forget("user_{$this->id}_permissions");
        $this->roles->each(function ($role) {
            Cache::forget("user_{$this->id}_has_role_{$role->name}");
        });
        Cache::forget("user_{$this->id}_has_any_roles_*");
        Cache::forget("user_{$this->id}_has_all_roles_*");
    }

    public function sessions()
    {
        return $this->hasMany(Session::class);
    }

    /**
     * Scope for active users
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope for inactive users
     */
    public function scopeInactive($query)
    {
        return $query->where('status', self::STATUS_INACTIVE);
    }

    /**
     * Scope for archived users
     */
    public function scopeArchived($query)
    {
        return $query->where('status', self::STATUS_ARCHIVED);
    }

    /**
     * Scope for non-archived users
     */
    public function scopeNotArchived($query)
    {
        return $query->where('status', '!=', self::STATUS_ARCHIVED);
    }

}

