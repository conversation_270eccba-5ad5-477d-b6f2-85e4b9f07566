<?php
/**
 * Test Profile and Logout Buttons for Organization Users
 *
 * This script tests if the profile and logout buttons are properly displayed
 * and functional in the organization user area.
 */

echo "=== Testing Profile and Logout Buttons for Organization Users ===\n\n";

// Test 1: Check if profile routes exist by examining route files
echo "1. Checking Profile Routes in Route Files:\n";
$webRoutesFile = 'routes/web.php';
if (file_exists($webRoutesFile)) {
    $routeContent = file_get_contents($webRoutesFile);

    $routesToCheck = [
        'profile.show' => "route('profile.show')",
        'profile.edit' => "route('profile.edit')",
        'profile.update' => "route('profile.update')",
        'logout' => "route('logout')"
    ];

    foreach ($routesToCheck as $routeName => $searchPattern) {
        if (strpos($routeContent, $routeName) !== false) {
            echo "   ✅ Route '$routeName' found in routes/web.php\n";
        } else {
            echo "   ❌ Route '$routeName' NOT FOUND in routes/web.php\n";
        }
    }

    // Check for specific route definitions
    if (strpos($routeContent, "Route::get('/profile'") !== false) {
        echo "   ✅ Profile GET route defined\n";
    }
    if (strpos($routeContent, "Route::put('/profile'") !== false) {
        echo "   ✅ Profile PUT route defined\n";
    }
    if (strpos($routeContent, "Route::post('/logout'") !== false) {
        echo "   ✅ Logout POST route defined\n";
    }
} else {
    echo "   ❌ Routes file not found: $webRoutesFile\n";
}

echo "\n2. Checking Layout File:\n";
$layoutFile = 'resources/views/layouts/app.blade.php';
if (file_exists($layoutFile)) {
    $content = file_get_contents($layoutFile);
    
    // Check for user dropdown
    if (strpos($content, 'id="userDropdown"') !== false) {
        echo "   ✅ User dropdown button found\n";
    } else {
        echo "   ❌ User dropdown button NOT FOUND\n";
    }
    
    // Check for profile link
    if (strpos($content, 'route(\'profile.show\')') !== false) {
        echo "   ✅ Profile link found\n";
    } else {
        echo "   ❌ Profile link NOT FOUND\n";
    }
    
    // Check for logout form
    if (strpos($content, 'route(\'logout\')') !== false) {
        echo "   ✅ Logout form found\n";
    } else {
        echo "   ❌ Logout form NOT FOUND\n";
    }
    
    // Check for Bootstrap dropdown classes
    if (strpos($content, 'dropdown-toggle') !== false && strpos($content, 'dropdown-menu') !== false) {
        echo "   ✅ Bootstrap dropdown classes found\n";
    } else {
        echo "   ❌ Bootstrap dropdown classes NOT FOUND\n";
    }
    
} else {
    echo "   ❌ Layout file not found: $layoutFile\n";
}

echo "\n3. Checking ProfileController:\n";
$controllerFile = 'app/Http/Controllers/ProfileController.php';
if (file_exists($controllerFile)) {
    $content = file_get_contents($controllerFile);
    
    if (strpos($content, 'public function show()') !== false) {
        echo "   ✅ ProfileController show method exists\n";
    } else {
        echo "   ❌ ProfileController show method NOT FOUND\n";
    }
    
    if (strpos($content, 'public function update(') !== false) {
        echo "   ✅ ProfileController update method exists\n";
    } else {
        echo "   ❌ ProfileController update method NOT FOUND\n";
    }
} else {
    echo "   ❌ ProfileController not found: $controllerFile\n";
}

echo "\n4. Checking Profile View:\n";
$profileView = 'resources/views/users/profile.blade.php';
if (file_exists($profileView)) {
    echo "   ✅ Profile view exists: $profileView\n";
} else {
    echo "   ❌ Profile view NOT FOUND: $profileView\n";
}

echo "\n5. CSS and JavaScript Check:\n";
$layoutContent = file_get_contents($layoutFile);

// Check for Bootstrap CSS
if (strpos($layoutContent, 'bootstrap') !== false) {
    echo "   ✅ Bootstrap CSS/JS found\n";
} else {
    echo "   ❌ Bootstrap CSS/JS NOT FOUND\n";
}

// Check for dropdown initialization
if (strpos($layoutContent, 'bootstrap.Dropdown') !== false) {
    echo "   ✅ Bootstrap dropdown initialization found\n";
} else {
    echo "   ❌ Bootstrap dropdown initialization NOT FOUND\n";
}

echo "\n=== Test Summary ===\n";
echo "The profile and logout buttons should be visible in the top-right corner\n";
echo "of the organization user dashboard. If they're not visible, check:\n\n";
echo "1. Make sure you're logged in as an organization user\n";
echo "2. Check browser console for JavaScript errors\n";
echo "3. Verify Bootstrap CSS/JS is loading properly\n";
echo "4. Check if any custom CSS is hiding the dropdown\n";
echo "5. Try refreshing the page or clearing browser cache\n\n";

echo "To test manually:\n";
echo "1. Login as an organization user\n";
echo "2. Go to the dashboard\n";
echo "3. Look for a button with your name in the top-right corner\n";
echo "4. Click it to see the dropdown with Profile and Logout options\n\n";

echo "Test completed!\n";
?>
