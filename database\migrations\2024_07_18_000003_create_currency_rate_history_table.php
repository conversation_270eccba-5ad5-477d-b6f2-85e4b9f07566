<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currency_rate_history', function (Blueprint $table) {
            $table->id();
            $table->string('from_currency', 3);
            $table->string('to_currency', 3);
            $table->decimal('old_rate', 15, 6)->nullable();
            $table->decimal('new_rate', 15, 6);
            $table->string('action', 20); // 'created', 'updated', 'deactivated'
            $table->text('reason')->nullable(); // Reason for rate change
            $table->unsignedBigInteger('changed_by'); // User or Super Admin who made the change
            $table->json('metadata')->nullable(); // Additional data like IP, user agent, etc.
            $table->timestamps();

            // Note: changed_by can reference either users or super_admins table
            // We'll handle this in the model relationships instead of foreign key constraints
            $table->index(['from_currency', 'to_currency']);
            $table->index('changed_by');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currency_rate_history');
    }
};
