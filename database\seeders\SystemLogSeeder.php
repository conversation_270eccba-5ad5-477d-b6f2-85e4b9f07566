<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SystemLog;
use App\Models\User;
use App\Models\Organization;
use Carbon\Carbon;

class SystemLogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::limit(5)->get();
        $organizations = Organization::limit(3)->get();

        // Create sample log entries
        $logEntries = [
            [
                'level' => SystemLog::LEVEL_INFO,
                'message' => 'User logged in successfully',
                'channel' => SystemLog::CHANNEL_AUTHENTICATION,
                'context' => ['login_method' => 'email', 'remember_me' => true],
            ],
            [
                'level' => SystemLog::LEVEL_WARNING,
                'message' => 'Failed login attempt',
                'channel' => SystemLog::CHANNEL_SECURITY,
                'context' => ['email' => '<EMAIL>', 'reason' => 'invalid_password'],
            ],
            [
                'level' => SystemLog::LEVEL_ERROR,
                'message' => 'Database connection timeout',
                'channel' => SystemLog::CHANNEL_DATABASE,
                'context' => ['timeout' => 30, 'query' => 'SELECT * FROM users'],
            ],
            [
                'level' => SystemLog::LEVEL_INFO,
                'message' => 'Order created successfully',
                'channel' => SystemLog::CHANNEL_APPLICATION,
                'context' => ['order_id' => 123, 'total_amount' => 99.99],
            ],
            [
                'level' => SystemLog::LEVEL_DEBUG,
                'message' => 'API request processed',
                'channel' => SystemLog::CHANNEL_API,
                'context' => ['endpoint' => '/api/orders', 'response_time' => 0.245],
            ],
            [
                'level' => SystemLog::LEVEL_CRITICAL,
                'message' => 'Payment gateway connection failed',
                'channel' => SystemLog::CHANNEL_APPLICATION,
                'context' => ['gateway' => 'stripe', 'error_code' => 'connection_timeout'],
            ],
            [
                'level' => SystemLog::LEVEL_INFO,
                'message' => 'User impersonation started',
                'channel' => SystemLog::CHANNEL_IMPERSONATION,
                'context' => ['target_user_id' => 2, 'super_admin_id' => 1],
            ],
            [
                'level' => SystemLog::LEVEL_WARNING,
                'message' => 'High memory usage detected',
                'channel' => SystemLog::CHANNEL_PERFORMANCE,
                'context' => ['memory_usage' => '512MB', 'threshold' => '256MB'],
            ],
            [
                'level' => SystemLog::LEVEL_ERROR,
                'message' => 'Email sending failed',
                'channel' => SystemLog::CHANNEL_APPLICATION,
                'context' => ['recipient' => '<EMAIL>', 'error' => 'SMTP timeout'],
            ],
            [
                'level' => SystemLog::LEVEL_INFO,
                'message' => 'Subscription plan changed',
                'channel' => SystemLog::CHANNEL_APPLICATION,
                'context' => ['from_plan' => 'Starter', 'to_plan' => 'Business'],
            ],
        ];

        foreach ($logEntries as $index => $entry) {
            $user = $users->random();
            $organization = $organizations->random();
            
            // Create logs with different timestamps
            $datetime = now()->subHours(rand(1, 48))->subMinutes(rand(0, 59));
            
            SystemLog::create([
                'level' => $entry['level'],
                'message' => $entry['message'],
                'context' => $entry['context'],
                'channel' => $entry['channel'],
                'datetime' => $datetime,
                'user_id' => rand(0, 1) ? $user->id : null,
                'organization_id' => rand(0, 1) ? $organization->id : null,
                'ip_address' => $this->generateRandomIP(),
                'user_agent' => $this->generateRandomUserAgent(),
                'url' => $this->generateRandomURL(),
                'method' => $this->generateRandomMethod(),
                'status_code' => $this->generateRandomStatusCode($entry['level']),
                'response_time' => rand(50, 2000) / 1000, // 0.05 to 2 seconds
                'memory_usage' => rand(50, 500) * 1024 * 1024, // 50MB to 500MB
                'environment' => 'local',
                'session_id' => 'sess_' . uniqid(),
                'request_id' => 'req_' . uniqid(),
                'created_at' => $datetime,
                'updated_at' => $datetime,
            ]);
        }

        // Create some recent critical logs
        for ($i = 0; $i < 5; $i++) {
            $user = $users->random();
            $organization = $organizations->random();
            
            SystemLog::create([
                'level' => SystemLog::LEVEL_ERROR,
                'message' => 'Critical system error occurred',
                'context' => [
                    'error_type' => 'database_error',
                    'stack_trace' => 'Error in line 123 of UserController.php',
                    'affected_users' => rand(1, 10)
                ],
                'channel' => SystemLog::CHANNEL_APPLICATION,
                'datetime' => now()->subMinutes(rand(1, 60)),
                'user_id' => $user->id,
                'organization_id' => $organization->id,
                'ip_address' => $this->generateRandomIP(),
                'user_agent' => $this->generateRandomUserAgent(),
                'url' => '/dashboard',
                'method' => 'GET',
                'status_code' => 500,
                'response_time' => rand(1000, 5000) / 1000,
                'memory_usage' => rand(200, 800) * 1024 * 1024,
                'environment' => 'local',
                'session_id' => 'sess_' . uniqid(),
                'request_id' => 'req_' . uniqid(),
            ]);
        }
    }

    private function generateRandomIP(): string
    {
        return rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);
    }

    private function generateRandomUserAgent(): string
    {
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
        ];
        
        return $userAgents[array_rand($userAgents)];
    }

    private function generateRandomURL(): string
    {
        $urls = [
            '/dashboard',
            '/orders',
            '/orders/create',
            '/users',
            '/settings',
            '/billing',
            '/api/orders',
            '/api/users',
            '/login',
            '/logout',
        ];
        
        return $urls[array_rand($urls)];
    }

    private function generateRandomMethod(): string
    {
        $methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
        return $methods[array_rand($methods)];
    }

    private function generateRandomStatusCode(string $level): int
    {
        switch ($level) {
            case SystemLog::LEVEL_ERROR:
            case SystemLog::LEVEL_CRITICAL:
                return [500, 502, 503, 504][array_rand([500, 502, 503, 504])];
            case SystemLog::LEVEL_WARNING:
                return [400, 401, 403, 404, 422][array_rand([400, 401, 403, 404, 422])];
            default:
                return [200, 201, 204][array_rand([200, 201, 204])];
        }
    }
}
