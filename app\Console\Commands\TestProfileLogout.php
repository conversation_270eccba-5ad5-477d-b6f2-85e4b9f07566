<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\File;

class TestProfileLogout extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:profile-logout';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test profile and logout functionality for organization users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Testing Profile and Logout Buttons for Organization Users ===');
        $this->newLine();

        // Test 1: Check if routes exist
        $this->info('1. Checking Profile Routes:');
        $routesToCheck = [
            'profile.show',
            'profile.edit', 
            'profile.update',
            'logout'
        ];

        foreach ($routesToCheck as $routeName) {
            try {
                $route = Route::getRoutes()->getByName($routeName);
                if ($route) {
                    $this->line("   ✅ Route '$routeName' exists: " . $route->uri());
                } else {
                    $this->line("   ❌ Route '$routeName' NOT FOUND");
                }
            } catch (\Exception $e) {
                $this->line("   ❌ Error checking route '$routeName': " . $e->getMessage());
            }
        }

        $this->newLine();

        // Test 2: Check layout file
        $this->info('2. Checking Layout File:');
        $layoutFile = resource_path('views/layouts/app.blade.php');
        
        if (File::exists($layoutFile)) {
            $content = File::get($layoutFile);
            
            $checks = [
                'userDropdown' => 'id="userDropdown"',
                'profile_link' => "route('profile.show')",
                'logout_form' => "route('logout')",
                'dropdown_toggle' => 'dropdown-toggle',
                'dropdown_menu' => 'dropdown-menu'
            ];
            
            foreach ($checks as $checkName => $searchString) {
                if (strpos($content, $searchString) !== false) {
                    $this->line("   ✅ $checkName found");
                } else {
                    $this->line("   ❌ $checkName NOT FOUND");
                }
            }
        } else {
            $this->line("   ❌ Layout file not found: $layoutFile");
        }

        $this->newLine();

        // Test 3: Check ProfileController
        $this->info('3. Checking ProfileController:');
        $controllerFile = app_path('Http/Controllers/ProfileController.php');
        
        if (File::exists($controllerFile)) {
            $content = File::get($controllerFile);
            
            $methods = [
                'show' => 'public function show()',
                'update' => 'public function update(',
                'edit' => 'public function edit()'
            ];
            
            foreach ($methods as $methodName => $searchString) {
                if (strpos($content, $searchString) !== false) {
                    $this->line("   ✅ $methodName method exists");
                } else {
                    $this->line("   ❌ $methodName method NOT FOUND");
                }
            }
        } else {
            $this->line("   ❌ ProfileController not found: $controllerFile");
        }

        $this->newLine();

        // Test 4: Check profile view
        $this->info('4. Checking Profile View:');
        $profileView = resource_path('views/users/profile.blade.php');
        
        if (File::exists($profileView)) {
            $this->line("   ✅ Profile view exists: $profileView");
        } else {
            $this->line("   ❌ Profile view NOT FOUND: $profileView");
        }

        $this->newLine();

        // Test 5: Route list for debugging
        $this->info('5. Available Routes (profile/logout related):');
        $routes = Route::getRoutes();
        
        foreach ($routes as $route) {
            $name = $route->getName();
            if ($name && (strpos($name, 'profile') !== false || strpos($name, 'logout') !== false)) {
                $methods = implode('|', $route->methods());
                $this->line("   📍 $name: $methods " . $route->uri());
            }
        }

        $this->newLine();
        $this->info('=== Test Summary ===');
        $this->line('If all checks pass, the profile and logout buttons should be visible');
        $this->line('in the top-right corner of the organization user dashboard.');
        $this->newLine();
        $this->line('To test manually:');
        $this->line('1. Login as an organization user');
        $this->line('2. Go to the dashboard');
        $this->line('3. Look for dropdown button with user name in top-right corner');
        $this->line('4. Click to see Profile and Logout options');
    }
}
