// User management utilities
console.log('User Management JS loaded - Version 2.0.6');

// Ensure window object exists
if (typeof window === 'undefined') {
    console.error('Window object not available');
}

// Make UserManagement globally accessible
console.log('Creating UserManagement object...');
window.UserManagement = {
    async toggleStatus(userId) {
        try {
            console.log('Toggling status for user:', userId);
            
            // Show loading state
            const button = document.querySelector(`button[onclick="UserManagement.toggleStatus(${userId})"]`);
            if (button) {
                button.disabled = true;
                button.style.opacity = '0.5';
            }
            
            // Debug: Log the URL being called
            const url = `/users/${userId}/toggle-status`;
            console.log('Making request to:', url);

            const response = await fetch(url, {
                method: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            });

            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            console.log('Content-Type:', contentType);

            if (!contentType || !contentType.includes('application/json')) {
                // If not JSON, get the text to see what was returned
                const text = await response.text();
                console.error('Non-JSON response received:', text.substring(0, 500));
                throw new Error('Server returned HTML instead of JSON. Check server logs for errors.');
            }

            const data = await response.json();
            console.log('Toggle response:', data);

            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}: Failed to update user status`);
            }

            // Show success message
            Swal.fire({
                title: 'Success!',
                text: data.message,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });

            // Refresh the page after a short delay
            setTimeout(() => {
                window.location.reload();
            }, 1500);

        } catch (error) {
            console.error('Toggle status error:', error);
            
            // Re-enable button
            const button = document.querySelector(`button[onclick="UserManagement.toggleStatus(${userId})"]`);
            if (button) {
                button.disabled = false;
                button.style.opacity = '1';
            }
            
            Swal.fire({
                title: 'Error!',
                text: error.message || 'Failed to update user status',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    },

    confirmDelete(userId, userName) {
        Swal.fire({
            title: 'Are you sure?',
            text: `This will permanently delete ${userName}. This action cannot be undone!`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete user',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                const deleteForm = document.querySelector(`form[data-user-id="${userId}"]`);
                if (deleteForm) {
                    deleteForm.submit();
                }
            }
        });
    },

    validatePasswordMatch() {
        const password = document.getElementById('password');
        const confirmation = document.getElementById('password_confirmation');
        const submitButton = document.querySelector('button[type="submit"]');

        if (password.value || confirmation.value) {
            if (password.value !== confirmation.value) {
                confirmation.setCustomValidity("Passwords don't match");
                submitButton.disabled = true;
            } else {
                confirmation.setCustomValidity('');
                submitButton.disabled = false;
            }
        }
    },

    validateForm() {
        const form = document.querySelector('form');
        const roleCheckboxes = document.querySelectorAll('input[name="roles[]"]');
        const emailInput = document.getElementById('email');
        const currentEmail = emailInput?.placeholder || '';

        form.addEventListener('submit', (e) => {
            const hasRoles = Array.from(roleCheckboxes).some(cb => cb.checked);

            // Allow empty email since it will use the current email
            if (emailInput && emailInput.value && !emailInput.validity.valid) {
                e.preventDefault();
                Swal.fire({
                    title: 'Error!',
                    text: 'Please enter a valid email address or leave it empty to keep the current one',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            if (!hasRoles) {
                e.preventDefault();
                Swal.fire({
                    title: 'Error!',
                    text: 'Please select at least one role',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    },

    init() {
        console.log('Initializing UserManagement');

        // Debug: Check if CSRF token exists
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (!csrfToken) {
            console.error('CSRF token not found in page head');
        } else {
            console.log('CSRF token found:', csrfToken.content.substring(0, 10) + '...');
        }

        // Debug: Check current URL
        console.log('Current URL:', window.location.href);
        console.log('Base URL:', window.location.origin);

        // Set up search functionality
        const searchInput = document.getElementById('userSearch');
        if (searchInput) {
            searchInput.addEventListener('input', this.filterUsers);
        }

        // Set up filter functionality
        const roleFilter = document.getElementById('roleFilter');
        const statusFilter = document.getElementById('statusFilter');
        
        if (roleFilter) {
            roleFilter.addEventListener('change', this.filterUsers);
        }
        
        if (statusFilter) {
            statusFilter.addEventListener('change', this.filterUsers);
        }

        // Debug: Check if CSRF token exists
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (!csrfToken) {
            console.error('CSRF token not found in page head');
        } else {
            console.log('CSRF token found:', csrfToken.content.substring(0, 10) + '...');
        }
    },

    animateValue: function(obj, start, end, duration) {
        if (start === end) return;

        const range = end - start;
        let current = start;
        const increment = end > start ? 1 : -1;
        const stepTime = Math.abs(Math.floor(duration / range));

        const timer = setInterval(function() {
            current += increment;
            obj.textContent = current.toLocaleString();

            if (current === end) {
                clearInterval(timer);
            }
        }, stepTime);
    },

    initializeStats: function() {
        console.log('Initializing stats animation...');
        document.querySelectorAll('.stat-value').forEach(stat => {
            const value = parseInt(stat.dataset.value) || 0;
            console.log('Animating stat:', stat, 'to value:', value);
            this.animateValue(stat, 0, value, 1000);
        });
    },

    filterUsers() {
        const searchTerm = document.getElementById('userSearch')?.value.toLowerCase() || '';
        const selectedRole = document.getElementById('roleFilter')?.value || '';
        const selectedStatus = document.getElementById('statusFilter')?.value || '';

        const rows = document.querySelectorAll('tbody tr[data-user-id]');
        let visibleCount = 0;

        rows.forEach(row => {
            const name = row.cells[0].textContent.toLowerCase();
            const email = row.cells[1].textContent.toLowerCase();
            const roles = Array.from(row.querySelectorAll('.role-badge')).map(badge => badge.dataset.roleId);
            const statusBadge = row.querySelector('.status-badge');
            const status = statusBadge ? statusBadge.dataset.status : 'active';

            const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
            const matchesRole = !selectedRole || roles.includes(selectedRole);
            const matchesStatus = !selectedStatus || status === selectedStatus;

            const shouldShow = matchesSearch && matchesRole && matchesStatus;
            row.style.display = shouldShow ? '' : 'none';
            if (shouldShow) visibleCount++;
        });

        const emptyState = document.querySelector('.empty-state');
        if (emptyState) {
            emptyState.style.display = visibleCount === 0 ? 'block' : 'none';
        }
    }
};

// Confirm UserManagement object was created
console.log('UserManagement object created:', typeof window.UserManagement);
console.log('UserManagement methods:', Object.keys(window.UserManagement));



// Initialize when document is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing UserManagement');
    if (typeof UserManagement !== 'undefined') {
        UserManagement.init();
    } else {
        console.error('UserManagement object not found during DOM ready');
    }
});

// Global function for delete confirmation
function confirmDelete(userName, userId) {
    return UserManagement.confirmDelete(userId, userName);
}

// Form validation for user edit form
const editUserForm = document.getElementById('editUserForm');
if (editUserForm) {
    editUserForm.addEventListener('submit', function(e) {
        let isValid = true;
        const password = document.getElementById('password');
        const confirmation = document.getElementById('password_confirmation');
        const email = document.getElementById('email');

        // Reset previous error messages
        document.querySelectorAll('.text-red-600').forEach(el => el.classList.add('hidden'));

        // Validate required fields
        this.querySelectorAll('[required]').forEach(field => {
            if (!field.value) {
                isValid = false;
                const errorEl = document.getElementById(`${field.id}-error`);
                if (errorEl) {
                    errorEl.textContent = `${field.previousElementSibling.textContent.trim()} is required`;
                    errorEl.classList.remove('hidden');
                }
            }
        });

        // Validate password match if either password field is filled
        if (password.value || confirmation.value) {
            if (password.value !== confirmation.value) {
                isValid = false;
                document.getElementById('password-confirmation-error').textContent = 'Passwords do not match';
                document.getElementById('password-confirmation-error').classList.remove('hidden');
            }
        }

        // Validate role selection
        const roleCheckboxes = document.querySelectorAll('input[name="roles[]"]:checked:not([disabled])');
        if (roleCheckboxes.length === 0) {
            isValid = false;
            document.getElementById('roles-error').textContent = 'Please select at least one role';
            document.getElementById('roles-error').classList.remove('hidden');
        }

        if (!isValid) {
            e.preventDefault();
            Swal.fire({
                title: 'Validation Error',
                text: 'Please check the form for errors',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        } else {
            // Show confirmation dialog for status change if status is being changed to inactive
            const statusSelect = document.getElementById('status');
            if (statusSelect.value === 'inactive' && !statusSelect.disabled) {
                e.preventDefault();
                Swal.fire({
                    title: 'Confirm Status Change',
                    text: 'Changing status to inactive will log out the user from all devices. Continue?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, change status',
                    cancelButtonText: 'No, keep active'
                }).then((result) => {
                    if (result.isConfirmed) {
                        this.submit();
                    } else {
                        statusSelect.value = 'active';
                    }
                });
            }
        }
    });
}




