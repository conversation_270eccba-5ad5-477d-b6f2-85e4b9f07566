<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            // Add annual billing fields if they don't exist
            if (!Schema::hasColumn('plans', 'annual_price')) {
                $table->decimal('annual_price', 10, 2)->nullable()->after('price');
            }
            if (!Schema::hasColumn('plans', 'billing_period')) {
                $table->string('billing_period')->default('monthly')->after('annual_price');
            }
            if (!Schema::hasColumn('plans', 'annual_discount_percentage')) {
                $table->integer('annual_discount_percentage')->default(0)->after('billing_period');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            $table->dropColumn(['annual_price', 'billing_period', 'annual_discount_percentage']);
        });
    }
};
