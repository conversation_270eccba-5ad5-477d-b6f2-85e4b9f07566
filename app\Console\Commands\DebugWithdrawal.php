<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\AffiliateWithdrawal;

class DebugWithdrawal extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:withdrawal {id=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug withdrawal data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $id = $this->argument('id');
        $withdrawal = AffiliateWithdrawal::find($id);

        if (!$withdrawal) {
            $this->error("Withdrawal #{$id} not found");
            return 1;
        }

        $this->info("Debugging Withdrawal #{$withdrawal->id}");
        $this->line("=====================================");

        // Raw database values
        $this->info("Raw Database Values:");
        $this->table(
            ['Field', 'Value', 'Type'],
            [
                ['id', $withdrawal->id, gettype($withdrawal->id)],
                ['amount', $withdrawal->getAttributes()['amount'] ?? 'NULL', gettype($withdrawal->getAttributes()['amount'] ?? null)],
                ['fee_amount', $withdrawal->getAttributes()['fee_amount'] ?? 'NULL', gettype($withdrawal->getAttributes()['fee_amount'] ?? null)],
                ['net_amount', $withdrawal->getAttributes()['net_amount'] ?? 'NULL', gettype($withdrawal->getAttributes()['net_amount'] ?? null)],
                ['status', $withdrawal->status, gettype($withdrawal->status)],
                ['payment_method', $withdrawal->payment_method, gettype($withdrawal->payment_method)],
                ['affiliate_id', $withdrawal->affiliate_id, gettype($withdrawal->affiliate_id)],
            ]
        );

        // Casted values
        $this->info("\nCasted Values:");
        $this->table(
            ['Field', 'Value', 'Type'],
            [
                ['amount', $withdrawal->amount, gettype($withdrawal->amount)],
                ['fee_amount', $withdrawal->fee_amount, gettype($withdrawal->fee_amount)],
                ['net_amount', $withdrawal->net_amount, gettype($withdrawal->net_amount)],
            ]
        );

        // Formatted values
        $this->info("\nFormatted Values:");
        $this->table(
            ['Field', 'Formatted'],
            [
                ['amount', '$' . number_format($withdrawal->amount, 2)],
                ['fee_amount', '$' . number_format($withdrawal->fee_amount, 2)],
                ['net_amount', '$' . number_format($withdrawal->net_amount, 2)],
            ]
        );

        // Payment details
        $this->info("\nPayment Details:");
        if ($withdrawal->payment_details) {
            foreach ($withdrawal->payment_details as $key => $value) {
                $this->line("  {$key}: {$value}");
            }
        } else {
            $this->line("  No payment details");
        }

        // Dates
        $this->info("\nDates:");
        $this->table(
            ['Field', 'Value'],
            [
                ['requested_at', $withdrawal->requested_at ? $withdrawal->requested_at->format('Y-m-d H:i:s') : 'NULL'],
                ['processed_at', $withdrawal->processed_at ? $withdrawal->processed_at->format('Y-m-d H:i:s') : 'NULL'],
                ['created_at', $withdrawal->created_at ? $withdrawal->created_at->format('Y-m-d H:i:s') : 'NULL'],
                ['updated_at', $withdrawal->updated_at ? $withdrawal->updated_at->format('Y-m-d H:i:s') : 'NULL'],
            ]
        );

        return 0;
    }
}
