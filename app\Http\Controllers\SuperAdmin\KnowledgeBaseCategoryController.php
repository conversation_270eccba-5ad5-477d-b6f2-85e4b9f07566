<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\KnowledgeBaseCategory;
use App\Services\LogService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class KnowledgeBaseCategoryController extends Controller
{
    /**
     * Display categories management
     */
    public function index()
    {
        $categories = KnowledgeBaseCategory::withCount(['articles', 'publishedArticles'])
            ->ordered()
            ->get();

        return view('super_admin.knowledge_base.categories.index', compact('categories'));
    }

    /**
     * Show form to create new category
     */
    public function create()
    {
        return view('super_admin.knowledge_base.categories.create');
    }

    /**
     * Store new category
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:knowledge_base_categories,name',
            'description' => 'nullable|string|max:500',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|max:7',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $category = KnowledgeBaseCategory::create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'icon' => $request->icon,
            'color' => $request->color ?: '#007bff',
            'sort_order' => $request->sort_order ?: 0,
            'is_active' => $request->boolean('is_active', true),
        ]);

        LogService::info('Knowledge base category created', [
            'category_id' => $category->id,
            'name' => $category->name,
        ]);

        return redirect()->route('super.knowledge-base.categories.index')
            ->with('success', 'Category created successfully.');
    }

    /**
     * Show form to edit category
     */
    public function edit(KnowledgeBaseCategory $category)
    {
        return view('super_admin.knowledge_base.categories.edit', compact('category'));
    }

    /**
     * Update category
     */
    public function update(Request $request, KnowledgeBaseCategory $category)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:knowledge_base_categories,name,' . $category->id,
            'description' => 'nullable|string|max:500',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|max:7',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $category->update([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'icon' => $request->icon,
            'color' => $request->color ?: '#007bff',
            'sort_order' => $request->sort_order ?: 0,
            'is_active' => $request->boolean('is_active', true),
        ]);

        LogService::info('Knowledge base category updated', [
            'category_id' => $category->id,
            'name' => $category->name,
        ]);

        return redirect()->route('super.knowledge-base.categories.index')
            ->with('success', 'Category updated successfully.');
    }

    /**
     * Delete category
     */
    public function destroy(KnowledgeBaseCategory $category)
    {
        // Check if category has articles
        if ($category->articles()->count() > 0) {
            return back()->with('error', 'Cannot delete category that contains articles. Please move or delete the articles first.');
        }

        $name = $category->name;
        $category->delete();

        LogService::info('Knowledge base category deleted', [
            'category_id' => $category->id,
            'name' => $name,
        ]);

        return redirect()->route('super.knowledge-base.categories.index')
            ->with('success', 'Category deleted successfully.');
    }

    /**
     * Reorder categories
     */
    public function reorder(Request $request)
    {
        $request->validate([
            'categories' => 'required|array',
            'categories.*' => 'exists:knowledge_base_categories,id',
        ]);

        foreach ($request->categories as $index => $categoryId) {
            KnowledgeBaseCategory::where('id', $categoryId)
                ->update(['sort_order' => $index + 1]);
        }

        LogService::info('Knowledge base categories reordered', [
            'category_order' => $request->categories,
        ]);

        return response()->json(['success' => true]);
    }

    /**
     * Toggle category active status
     */
    public function toggleActive(KnowledgeBaseCategory $category)
    {
        $category->update(['is_active' => !$category->is_active]);

        LogService::info('Knowledge base category status toggled', [
            'category_id' => $category->id,
            'name' => $category->name,
            'is_active' => $category->is_active,
        ]);

        return back()->with('success', 'Category status updated successfully.');
    }
}
