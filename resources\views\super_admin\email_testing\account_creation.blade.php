@extends('super_admin.layouts.app')

@section('title', 'Account Creation Testing')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">👥 Account Creation Testing</h1>
                <div class="btn-group" role="group">
                    <a href="{{ route('super_admin.email-testing.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Back to Email Testing
                    </a>
                    <a href="{{ route('super_admin.email-testing.auth-emails') }}" class="btn btn-outline-info">
                        <i class="fas fa-shield-alt"></i> Auth Email Testing
                    </a>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle"></i> 
                    @foreach($errors->all() as $error)
                        {{ $error }}<br>
                    @endforeach
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('account_details'))
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <h5><i class="fas fa-user-check"></i> Account Created Successfully</h5>
                    @php $details = session('account_details'); @endphp
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Account Type:</strong> {{ $details['type'] }}<br>
                            <strong>Email:</strong> {{ $details['email'] }}<br>
                            <strong>Password:</strong> {{ $details['password'] }}<br>
                            <strong>Role:</strong> {{ $details['role'] }}
                        </div>
                        <div class="col-md-6">
                            @if(isset($details['organization_id']))
                                <strong>Organization ID:</strong> {{ $details['organization_id'] }}<br>
                            @endif
                            @if(isset($details['affiliate_id']))
                                <strong>Affiliate ID:</strong> {{ $details['affiliate_id'] }}<br>
                            @endif
                            @if(isset($details['referral_code']))
                                <strong>Referral Code:</strong> {{ $details['referral_code'] }}<br>
                            @endif
                            <strong>User ID:</strong> {{ $details['user_id'] }}
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('cleanup_results'))
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <h5><i class="fas fa-broom"></i> Cleanup Results</h5>
                    @php $results = session('cleanup_results'); @endphp
                    <p>
                        <strong>Deleted Users:</strong> {{ $results['deleted_users'] }}<br>
                        <strong>Deleted Organizations:</strong> {{ $results['deleted_organizations'] }}<br>
                        <strong>Deleted Affiliates:</strong> {{ $results['deleted_affiliates'] }}
                    </p>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Account Creation Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-plus"></i> Create Test Accounts
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="accountCreationForm">
                        @csrf
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="test_email" class="form-label">Test Email Address</label>
                                    <input type="email" class="form-control" id="test_email" name="test_email" 
                                           value="{{ old('test_email', '<EMAIL>') }}" required>
                                    <div class="form-text">Enter the email address for the test account</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-2">
                                        <button type="submit" formaction="{{ route('super_admin.email-testing.create-organization-account') }}" class="btn btn-success">
                                            <i class="fas fa-building"></i> Create Organization Account
                                        </button>
                                        <button type="submit" formaction="{{ route('super_admin.email-testing.create-affiliate-account') }}" class="btn btn-warning">
                                            <i class="fas fa-handshake"></i> Create Affiliate Account
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" formaction="{{ route('super_admin.email-testing.cleanup-test-accounts') }}" class="btn btn-danger" 
                                        onclick="return confirm('This will delete all test accounts. Are you sure?')">
                                    <i class="fas fa-trash"></i> Cleanup Test Accounts
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Existing Test Accounts -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users"></i> Existing Test Accounts
                    </h5>
                </div>
                <div class="card-body">
                    @if($testUsers->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Roles</th>
                                        <th>Verified</th>
                                        <th>Created</th>
                                        <th>Organization</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($testUsers as $user)
                                        <tr>
                                            <td>{{ $user->name }}</td>
                                            <td>{{ $user->email }}</td>
                                            <td>
                                                @foreach($user->roles as $role)
                                                    <span class="badge bg-primary">{{ $role->name }}</span>
                                                @endforeach
                                            </td>
                                            <td>
                                                @if($user->email_verified_at)
                                                    <span class="badge bg-success">Yes</span>
                                                @else
                                                    <span class="badge bg-warning">No</span>
                                                @endif
                                            </td>
                                            <td>{{ $user->created_at->format('Y-m-d H:i') }}</td>
                                            <td>
                                                @if($user->organization_id)
                                                    <span class="badge bg-info">Org #{{ $user->organization_id }}</span>
                                                @else
                                                    <span class="text-muted">None</span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle"></i> 
                            No test accounts found. Create some test accounts to verify email flows.
                        </div>
                    @endif
                </div>
            </div>

            <!-- Account Types Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> Account Types & Email Flows
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-building"></i> Organization Account
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text"><strong>Creates:</strong></p>
                                    <ul class="small">
                                        <li>Organization record</li>
                                        <li>User with Organization Owner role</li>
                                        <li>Default organization settings</li>
                                    </ul>
                                    <p class="card-text"><strong>Email Flow:</strong></p>
                                    <ul class="small">
                                        <li>Welcome email (Organization type)</li>
                                        <li>Business-focused content</li>
                                        <li>Dashboard access CTA</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-handshake"></i> Affiliate Account
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text"><strong>Creates:</strong></p>
                                    <ul class="small">
                                        <li>User with Affiliate role</li>
                                        <li>Affiliate record with referral code</li>
                                        <li>Commission settings</li>
                                    </ul>
                                    <p class="card-text"><strong>Email Flow:</strong></p>
                                    <ul class="small">
                                        <li>Welcome email (Affiliate type)</li>
                                        <li>Commission-focused content</li>
                                        <li>Affiliate dashboard CTA</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Testing Process -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-route"></i> Account Creation Testing Process
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📋 Testing Steps</h6>
                            <ol class="small">
                                <li><strong>Create Organization Account:</strong> Tests organization welcome email flow</li>
                                <li><strong>Create Affiliate Account:</strong> Tests affiliate welcome email flow</li>
                                <li><strong>Check Mailtrap:</strong> Verify welcome emails are received</li>
                                <li><strong>Test Login:</strong> Use created accounts to test login flow</li>
                                <li><strong>Cleanup:</strong> Remove test accounts when done</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6>✅ Expected Email Flow</h6>
                            <div class="alert alert-info">
                                <ol class="mb-0 small">
                                    <li>Account creation triggers UserObserver</li>
                                    <li>UserObserver sends welcome email based on user type</li>
                                    <li>Email uses appropriate welcome message template</li>
                                    <li>Email delivered to Mailtrap for testing</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt"></i> Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ route('super_admin.email-testing.index') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-envelope"></i><br>
                                    <small>Email Testing Dashboard</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ route('super_admin.email-testing.auth-emails') }}" class="btn btn-outline-info">
                                    <i class="fas fa-shield-alt"></i><br>
                                    <small>Auth Email Testing</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ url('/register') }}" class="btn btn-outline-success" target="_blank">
                                    <i class="fas fa-external-link-alt"></i><br>
                                    <small>Manual Registration</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{{ url('/affiliate/register') }}" class="btn btn-outline-warning" target="_blank">
                                    <i class="fas fa-external-link-alt"></i><br>
                                    <small>Affiliate Registration</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
