<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Organization;
use App\Models\Plan;
use App\Models\Subscription;
use App\Services\ProrationService;
use Carbon\Carbon;

class TestFullPlanFunctionality extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:full-plan-functionality';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the full plan change functionality with proration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 TESTING FULL PLAN FUNCTIONALITY');
        $this->newLine();

        try {
            // Test 1: ProrationService instantiation
            $this->info('1. Testing ProrationService instantiation...');
            $prorationService = app(ProrationService::class);
            $this->line('   ✅ ProrationService created successfully');

            // Test 2: Get sample data
            $this->info('2. Testing data availability...');
            $plans = Plan::where('is_active', true)->get();
            $organizations = Organization::with('plan')->get();
            
            $this->line("   ✅ Found {$plans->count()} active plans");
            $this->line("   ✅ Found {$organizations->count()} organizations");

            if ($plans->count() < 2) {
                $this->warn('   ⚠️  Need at least 2 plans to test proration properly');
                return 1;
            }

            if ($organizations->count() < 1) {
                $this->warn('   ⚠️  Need at least 1 organization to test');
                return 1;
            }

            // Test 3: Proration calculations
            $this->info('3. Testing proration calculations...');
            $organization = $organizations->first();
            $plan1 = $plans->first();
            $plan2 = $plans->skip(1)->first();

            // Create a mock subscription
            $mockSubscription = new Subscription([
                'organization_id' => $organization->id,
                'plan_id' => $plan1->id,
                'status' => 'active',
                'start_date' => Carbon::now()->subDays(10),
                'end_date' => Carbon::now()->addDays(20),
                'amount_paid' => $plan1->price,
            ]);

            $proration = $prorationService->calculatePlanChange($mockSubscription, $plan2);
            
            $this->line('   ✅ Proration calculation successful');
            $this->line("      Type: {$proration['type']}");
            $this->line("      Net Amount: \${$proration['net_amount']}");
            $this->line("      Remaining Days: {$proration['remaining_days']}");
            $this->line("      Details: {$proration['proration_details']}");

            // Test 4: Immediate upgrade calculation
            $this->info('4. Testing immediate upgrade calculation...');
            $upgradeCalc = $prorationService->calculateImmediateUpgrade($organization, $plan2);
            
            $this->line('   ✅ Immediate upgrade calculation successful');
            $this->line("      Type: {$upgradeCalc['type']}");
            $this->line("      Immediate Charge: \${$upgradeCalc['immediate_charge']}");

            // Test 5: Cancellation refund calculation
            $this->info('5. Testing cancellation refund calculation...');
            $refundCalc = $prorationService->calculateCancellationRefund($mockSubscription);
            
            $this->line('   ✅ Cancellation refund calculation successful');
            $this->line("      Refund Amount: \${$refundCalc['refund_amount']}");
            $this->line("      Used Days: {$refundCalc['used_days']}");
            $this->line("      Remaining Days: {$refundCalc['remaining_days']}");

            // Test 6: Controller instantiation
            $this->info('6. Testing PlanChangeController instantiation...');
            $controller = app(\App\Http\Controllers\PlanChangeController::class);
            $this->line('   ✅ PlanChangeController created successfully');

            $this->newLine();
            $this->info('🎉 ALL TESTS PASSED! Full plan functionality is working correctly.');
            
            $this->newLine();
            $this->info('📋 Summary of available functionality:');
            $this->line('   • Proration calculations for plan changes');
            $this->line('   • Immediate upgrade/downgrade cost calculations');
            $this->line('   • End-of-cycle plan change scheduling');
            $this->line('   • Cancellation refund calculations');
            $this->line('   • Grace period handling');
            $this->line('   • Subscription management');

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Test failed: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return 1;
        }
    }
}
