<?php

namespace App\Http\Controllers\Organization;

use App\Http\Controllers\Controller;
use App\Models\SupportTicket;
use App\Models\SupportTicketReply;
use App\Models\OrganizationCommunication;
use App\Models\KnowledgeBaseArticle;
use App\Services\LogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SupportController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:Organization Owner|Manager'); // Only organization admins can access
    }

    /**
     * Display organization support dashboard
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $organizationId = $user->organization_id;

        // Build query with filters
        $query = SupportTicket::where('organization_id', $organizationId)
            ->with(['user', 'assignedAdmin', 'replies']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('ticket_number', 'like', "%{$search}%");
            });
        }

        // Get filtered tickets
        $tickets = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get ticket statistics
        $stats = [
            'total' => SupportTicket::where('organization_id', $organizationId)->count(),
            'open' => SupportTicket::where('organization_id', $organizationId)->where('status', 'open')->count(),
            'in_progress' => SupportTicket::where('organization_id', $organizationId)->where('status', 'in_progress')->count(),
            'resolved' => SupportTicket::where('organization_id', $organizationId)->where('status', 'resolved')->count(),
        ];

        return view('organization.support.index', compact('tickets', 'stats'));
    }

    /**
     * Show form to create a new ticket
     */
    public function create()
    {
        $users = Auth::user()->organization->users()
            ->where('status', 'active')
            ->orderBy('name')
            ->get();

        return view('organization.support.create', compact('users'));
    }

    /**
     * Store a new support ticket
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|in:' . implode(',', array_keys(SupportTicket::getCategoryOptions())),
            'priority' => 'in:' . implode(',', array_keys(SupportTicket::getPriorityOptions())),
            'user_id' => 'nullable|exists:users,id',
        ]);

        $user = Auth::user();

        // Validate that the selected user belongs to the organization
        if ($request->user_id) {
            $selectedUser = \App\Models\User::find($request->user_id);
            if ($selectedUser->organization_id !== $user->organization_id) {
                return back()->withErrors(['user_id' => 'Selected user does not belong to your organization.']);
            }
        }

        $ticket = SupportTicket::create([
            'ticket_number' => SupportTicket::generateTicketNumber(),
            'title' => $request->title,
            'description' => $request->description,
            'category' => $request->category,
            'priority' => $request->priority ?? 'normal',
            'status' => SupportTicket::STATUS_OPEN,
            'user_id' => $request->user_id ?: $user->id,
            'organization_id' => $user->organization_id,
        ]);

        LogService::info('Support ticket created by organization admin', [
            'ticket_id' => $ticket->id,
            'ticket_number' => $ticket->ticket_number,
            'created_by_user_id' => $user->id,
            'ticket_user_id' => $ticket->user_id,
            'organization_id' => $user->organization_id,
        ]);

        return redirect()->route('organization.support.show', $ticket)
            ->with('success', 'Support ticket created successfully.');
    }

    /**
     * Display a specific ticket
     */
    public function show(SupportTicket $ticket)
    {
        $user = Auth::user();

        // Check if ticket belongs to user's organization
        if ($ticket->organization_id !== $user->organization_id) {
            abort(403, 'You do not have permission to view this ticket.');
        }

        $ticket->load(['user', 'assignedAdmin', 'replies.replier', 'organization']);

        return view('organization.support.show', compact('ticket'));
    }

    /**
     * Add a reply to a ticket
     */
    public function reply(Request $request, SupportTicket $ticket)
    {
        $user = Auth::user();

        // Check if ticket belongs to user's organization
        if ($ticket->organization_id !== $user->organization_id) {
            abort(403, 'You do not have permission to reply to this ticket.');
        }

        $request->validate([
            'message' => 'required|string',
        ]);

        // Don't allow replies to closed tickets
        if ($ticket->status === SupportTicket::STATUS_CLOSED) {
            return back()->with('error', 'Cannot reply to a closed ticket.');
        }

        $reply = SupportTicketReply::createFromUser($ticket, $user, $request->message);

        LogService::info('Organization admin replied to support ticket', [
            'ticket_id' => $ticket->id,
            'reply_id' => $reply->id,
            'user_id' => $user->id,
            'organization_id' => $user->organization_id,
        ]);

        return back()->with('success', 'Your reply has been added.');
    }

    /**
     * Display organization communications
     */
    public function communications()
    {
        $user = Auth::user();

        $communications = OrganizationCommunication::where('organization_id', $user->organization_id)
            ->with('sender')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('organization.support.communications', compact('communications'));
    }

    /**
     * Display a specific communication
     */
    public function showCommunication(OrganizationCommunication $communication)
    {
        $user = Auth::user();

        // Check if communication belongs to user's organization
        if ($communication->organization_id !== $user->organization_id) {
            abort(403, 'You do not have permission to view this communication.');
        }

        // Mark as read if not already
        $readReceipt = \App\Models\CommunicationReadReceipt::firstOrCreate([
            'organization_communication_id' => $communication->id,
            'user_id' => $user->id,
        ], [
            'read_at' => now(),
        ]);

        return view('organization.support.communication', compact('communication'));
    }

    /**
     * Display support analytics
     */
    public function analytics(Request $request)
    {
        $user = Auth::user();
        $organizationId = $user->organization_id;
        $period = $request->get('period', 30); // Default to 30 days

        $startDate = now()->subDays($period);

        // Get metrics
        $metrics = [
            'total_tickets' => SupportTicket::where('organization_id', $organizationId)
                ->where('created_at', '>=', $startDate)
                ->count(),
            'resolved_tickets' => SupportTicket::where('organization_id', $organizationId)
                ->where('created_at', '>=', $startDate)
                ->where('status', 'resolved')
                ->count(),
            'avg_response_time' => $this->getAverageResponseTime($organizationId, $startDate),
            'satisfaction_rate' => 85, // Placeholder - implement based on feedback system
        ];

        // Daily ticket data for chart
        $dailyTickets = SupportTicket::where('organization_id', $organizationId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $metrics['daily_tickets'] = [
            'labels' => $dailyTickets->pluck('date')->toArray(),
            'data' => $dailyTickets->pluck('count')->toArray(),
        ];

        // Status distribution
        $statusData = SupportTicket::where('organization_id', $organizationId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        $metrics['status_distribution'] = [
            'labels' => $statusData->pluck('status')->map(function($status) {
                return ucfirst(str_replace('_', ' ', $status));
            })->toArray(),
            'data' => $statusData->pluck('count')->toArray(),
        ];

        // Top users
        $metrics['top_users'] = \App\Models\User::where('organization_id', $organizationId)
            ->withCount(['supportTickets' => function($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }])
            ->having('support_tickets_count', '>', 0)
            ->orderBy('support_tickets_count', 'desc')
            ->limit(5)
            ->get();

        // Categories
        $metrics['categories'] = SupportTicket::where('organization_id', $organizationId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('category, COUNT(*) as count')
            ->groupBy('category')
            ->orderBy('count', 'desc')
            ->get();

        // Response time analysis
        $metrics['response_times'] = [
            'under_4h' => SupportTicket::where('organization_id', $organizationId)
                ->where('created_at', '>=', $startDate)
                ->whereNotNull('first_response_at')
                ->whereRaw('TIMESTAMPDIFF(HOUR, created_at, first_response_at) < 4')
                ->count(),
            '4h_to_24h' => SupportTicket::where('organization_id', $organizationId)
                ->where('created_at', '>=', $startDate)
                ->whereNotNull('first_response_at')
                ->whereRaw('TIMESTAMPDIFF(HOUR, created_at, first_response_at) BETWEEN 4 AND 24')
                ->count(),
            'over_24h' => SupportTicket::where('organization_id', $organizationId)
                ->where('created_at', '>=', $startDate)
                ->whereNotNull('first_response_at')
                ->whereRaw('TIMESTAMPDIFF(HOUR, created_at, first_response_at) > 24')
                ->count(),
            'no_response' => SupportTicket::where('organization_id', $organizationId)
                ->where('created_at', '>=', $startDate)
                ->whereNull('first_response_at')
                ->count(),
        ];

        return view('organization.support.analytics', compact('metrics'));
    }

    /**
     * Export organization tickets to CSV
     */
    public function export(Request $request)
    {
        $user = Auth::user();

        $tickets = SupportTicket::where('organization_id', $user->organization_id)
            ->with(['user', 'assignedAdmin'])
            ->orderBy('created_at', 'desc')
            ->get();

        $filename = 'support_tickets_' . $user->organization->name . '_' . now()->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($tickets) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Ticket Number',
                'Title',
                'Category',
                'Priority',
                'Status',
                'User',
                'Assigned To',
                'Created',
                'Resolved',
                'Response Time (hours)',
                'Resolution Time (hours)'
            ]);

            // CSV data
            foreach ($tickets as $ticket) {
                fputcsv($file, [
                    $ticket->ticket_number,
                    $ticket->title,
                    ucfirst(str_replace('_', ' ', $ticket->category)),
                    ucfirst($ticket->priority),
                    ucfirst(str_replace('_', ' ', $ticket->status)),
                    $ticket->user->name ?? 'N/A',
                    $ticket->assignedAdmin->name ?? 'Unassigned',
                    $ticket->created_at->format('Y-m-d H:i:s'),
                    $ticket->resolved_at ? $ticket->resolved_at->format('Y-m-d H:i:s') : '',
                    $ticket->response_time ?? '',
                    $ticket->resolution_time ?? '',
                ]);
            }

            fclose($file);
        };

        LogService::info('Organization exported support tickets', [
            'organization_id' => $user->organization_id,
            'exported_by_user_id' => $user->id,
            'ticket_count' => $tickets->count(),
        ]);

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get average response time for organization tickets
     */
    private function getAverageResponseTime($organizationId, $startDate = null)
    {
        $query = SupportTicket::where('organization_id', $organizationId)
            ->whereNotNull('first_response_at');

        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }

        $tickets = $query->get();

        if ($tickets->isEmpty()) {
            return 0;
        }

        $totalHours = $tickets->sum(function($ticket) {
            return $ticket->response_time ?? 0;
        });

        return round($totalHours / $tickets->count(), 1);
    }

    /**
     * Get ticket trends for the last 30 days
     */
    private function getTicketTrends($organizationId)
    {
        $startDate = now()->subDays(30);

        $trends = SupportTicket::where('organization_id', $organizationId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('count', 'date');

        return $trends;
    }
}
