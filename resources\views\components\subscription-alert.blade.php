@props(['guidance' => null])

@php
    $guidance = $guidance ?? session('subscription_guidance');
    $warning = session('subscription_warning');
    $info = session('subscription_info');
@endphp

@if($guidance || $warning || $info)
    @php
        $alert = $guidance ?? $warning ?? $info;
        $alertType = $guidance ? 'guidance' : (session()->has('subscription_warning') ? 'warning' : 'info');
        $bgColor = match($alertType) {
            'guidance' => match($alert['status']['level'] ?? 'info') {
                'critical' => 'bg-red-50 border-red-200',
                'warning' => 'bg-yellow-50 border-yellow-200',
                'success' => 'bg-green-50 border-green-200',
                default => 'bg-blue-50 border-blue-200'
            },
            'warning' => 'bg-yellow-50 border-yellow-200',
            default => 'bg-blue-50 border-blue-200'
        };
        $textColor = match($alertType) {
            'guidance' => match($alert['status']['level'] ?? 'info') {
                'critical' => 'text-red-800',
                'warning' => 'text-yellow-800',
                'success' => 'text-green-800',
                default => 'text-blue-800'
            },
            'warning' => 'text-yellow-800',
            default => 'text-blue-800'
        };
        $iconColor = match($alertType) {
            'guidance' => match($alert['status']['level'] ?? 'info') {
                'critical' => 'text-red-400',
                'warning' => 'text-yellow-400',
                'success' => 'text-green-400',
                default => 'text-blue-400'
            },
            'warning' => 'text-yellow-400',
            default => 'text-blue-400'
        };
    @endphp

    <div class="rounded-md {{ $bgColor }} border p-4 mb-6">
        <div class="flex">
            <div class="flex-shrink-0">
                @if($alertType === 'guidance' && ($alert['status']['level'] ?? '') === 'critical')
                    <svg class="h-5 w-5 {{ $iconColor }}" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                @elseif($alertType === 'warning' || ($alertType === 'guidance' && ($alert['status']['level'] ?? '') === 'warning'))
                    <svg class="h-5 w-5 {{ $iconColor }}" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                @else
                    <svg class="h-5 w-5 {{ $iconColor }}" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                @endif
            </div>
            <div class="ml-3 flex-1">
                <h3 class="text-sm font-medium {{ $textColor }}">
                    {{ $alert['title'] ?? ($alert['status']['title'] ?? 'Subscription Notice') }}
                </h3>
                <div class="mt-2 text-sm {{ $textColor }}">
                    <p>{{ $alert['message'] ?? ($alert['status']['message'] ?? '') }}</p>
                </div>
                
                @if(isset($alert['actions']) && count($alert['actions']) > 0)
                    <div class="mt-4">
                        <div class="-mx-2 -my-1.5 flex space-x-2">
                            @foreach($alert['actions'] as $action)
                                <a href="{{ $action['url'] }}" 
                                   class="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium
                                          {{ $action['type'] === 'primary' 
                                             ? 'bg-white text-gray-900 hover:bg-gray-50 border border-gray-300' 
                                             : 'text-gray-700 hover:text-gray-900' }}">
                                    @if(isset($action['icon']))
                                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            @switch($action['icon'])
                                                @case('credit-card')
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                                    @break
                                                @case('refresh')
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                                    @break
                                                @case('user')
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                    @break
                                                @case('support')
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12l.01.01M12 12l.01.01M12 12l.01.01M12 12l.01.01"></path>
                                                    @break
                                                @case('info')
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    @break
                                            @endswitch
                                        </svg>
                                    @endif
                                    {{ $action['text'] }}
                                </a>
                            @endforeach
                        </div>
                    </div>
                @elseif(isset($alert['action_text']) && isset($alert['action_url']))
                    <div class="mt-4">
                        <div class="-mx-2 -my-1.5 flex">
                            <a href="{{ $alert['action_url'] }}" 
                               class="bg-white px-3 py-1.5 rounded-md text-sm font-medium text-gray-900 hover:bg-gray-50 border border-gray-300">
                                {{ $alert['action_text'] }}
                            </a>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endif