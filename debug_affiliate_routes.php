<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

echo "=== Debugging Affiliate Routes ===\n\n";

try {
    // 1. Check if affiliate routes are registered
    echo "1. Checking affiliate route registration...\n";
    
    $routes = Route::getRoutes();
    $affiliateRoutes = [];
    
    foreach ($routes as $route) {
        $uri = $route->uri();
        if (strpos($uri, 'affiliate') !== false) {
            $affiliateRoutes[] = [
                'method' => implode('|', $route->methods()),
                'uri' => $uri,
                'name' => $route->getName(),
                'action' => $route->getActionName(),
                'middleware' => $route->middleware()
            ];
        }
    }
    
    if (count($affiliateRoutes) > 0) {
        echo "   Found " . count($affiliateRoutes) . " affiliate routes:\n";
        foreach ($affiliateRoutes as $route) {
            echo "   - {$route['method']} /{$route['uri']} -> {$route['name']} ({$route['action']})\n";
            if (!empty($route['middleware'])) {
                echo "     Middleware: " . implode(', ', $route['middleware']) . "\n";
            }
        }
    } else {
        echo "   ❌ No affiliate routes found!\n";
    }
    
    echo "\n2. Testing specific affiliate routes...\n";
    
    // Test route generation
    $testRoutes = [
        'affiliate.login',
        'affiliate.register', 
        'affiliate.dashboard'
    ];
    
    foreach ($testRoutes as $routeName) {
        try {
            $url = route($routeName);
            echo "   ✓ {$routeName} -> {$url}\n";
        } catch (Exception $e) {
            echo "   ❌ {$routeName} -> ERROR: {$e->getMessage()}\n";
        }
    }
    
    echo "\n3. Checking authentication status...\n";
    
    // Check current authentication status
    $guards = ['web', 'affiliate', 'super_admin'];
    foreach ($guards as $guard) {
        $isAuthenticated = Auth::guard($guard)->check();
        $user = Auth::guard($guard)->user();
        echo "   Guard '{$guard}': " . ($isAuthenticated ? 'AUTHENTICATED' : 'GUEST');
        if ($user) {
            echo " (User: {$user->email})";
        }
        echo "\n";
    }
    
    echo "\n4. Testing middleware behavior...\n";
    
    // Check if RedirectIfAuthenticated middleware exists
    if (class_exists('App\Http\Middleware\RedirectIfAuthenticated')) {
        echo "   ✓ RedirectIfAuthenticated middleware exists\n";
        
        // Check if the middleware is properly configured
        $middleware = new App\Http\Middleware\RedirectIfAuthenticated();
        echo "   ✓ Middleware can be instantiated\n";
    } else {
        echo "   ❌ RedirectIfAuthenticated middleware not found\n";
    }
    
    echo "\n5. Checking affiliate controller...\n";
    
    if (class_exists('App\Http\Controllers\Affiliate\AffiliateController')) {
        echo "   ✓ Affiliate controller exists\n";
        
        $controller = new App\Http\Controllers\Affiliate\AffiliateController();
        if (method_exists($controller, 'showLoginForm')) {
            echo "   ✓ showLoginForm method exists\n";
        } else {
            echo "   ❌ showLoginForm method not found\n";
        }
    } else {
        echo "   ❌ Affiliate controller not found\n";
    }
    
    echo "\n6. Checking affiliate login view...\n";
    
    $viewPath = resource_path('views/affiliate/login.blade.php');
    if (file_exists($viewPath)) {
        echo "   ✓ Affiliate login view exists: {$viewPath}\n";
    } else {
        echo "   ❌ Affiliate login view not found: {$viewPath}\n";
    }
    
    echo "\n=== Diagnosis ===\n";
    
    // Check if user is authenticated with web guard
    if (Auth::guard('web')->check()) {
        echo "⚠️  WARNING: User is authenticated with 'web' guard\n";
        echo "   This might cause the guest:affiliate middleware to redirect\n";
        echo "   Solution: Logout from main application or use incognito mode\n";
    }
    
    // Check if affiliate routes are properly loaded
    $affiliateLoginRoute = collect($affiliateRoutes)->firstWhere('name', 'affiliate.login');
    if ($affiliateLoginRoute) {
        echo "✓ Affiliate login route is properly registered\n";
        echo "  Method: {$affiliateLoginRoute['method']}\n";
        echo "  URI: /{$affiliateLoginRoute['uri']}\n";
        echo "  Middleware: " . implode(', ', $affiliateLoginRoute['middleware']) . "\n";
    } else {
        echo "❌ Affiliate login route is NOT registered\n";
    }
    
    echo "\n=== Recommendations ===\n";
    echo "1. If you're logged into the main application, logout first\n";
    echo "2. Try accessing affiliate login in incognito/private browser mode\n";
    echo "3. Clear browser cache and cookies\n";
    echo "4. Check if affiliate routes file is properly included\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
