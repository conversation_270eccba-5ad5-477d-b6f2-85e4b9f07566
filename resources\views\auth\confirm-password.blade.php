@extends('layouts.auth')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-5">
        <div class="card shadow-lg">
            <div class="card-header text-white">
                <div class="shape-1"></div>
                <div class="shape-2"></div>
                <h3 class="text-center font-weight-light my-2">Confirm Password</h3>
            </div>
            <div class="card-body">
                <p class="mb-4 text-muted">This is a secure area of the application. Please confirm your password before continuing.</p>

                <form method="POST" action="{{ route('password.confirm') }}">
                    @csrf

                    <div class="form-floating mb-4">
                        <input class="form-control @error('password') is-invalid @enderror"
                               id="password" type="password" name="password"
                               required />
                        <label for="password"><i class="fas fa-lock me-2"></i>Password</label>
                        @error('password')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                        @enderror
                    </div>

                    <div class="d-flex align-items-center justify-content-end mt-4 mb-2">
                        <button type="submit" class="btn btn-primary px-4">
                            <i class="fas fa-check-circle me-2"></i>Confirm
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
