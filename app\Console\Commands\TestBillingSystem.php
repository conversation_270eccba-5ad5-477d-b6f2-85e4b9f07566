<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Organization;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\SubscriptionPayment;
use App\Models\PaymentAccount;
use App\Models\User;
use Carbon\Carbon;

class TestBillingSystem extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'billing:test';

    /**
     * The console command description.
     */
    protected $description = 'Test the billing system functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Billing System...');
        
        try {
            // Test 1: Check if payment accounts exist
            $this->info('1. Checking payment accounts...');
            $paymentAccounts = PaymentAccount::active()->count();
            $this->info("   Found {$paymentAccounts} active payment accounts");
            
            if ($paymentAccounts === 0) {
                $this->warn('   No payment accounts found. Creating sample account...');
                PaymentAccount::create([
                    'account_name' => 'Test Company Ltd',
                    'bank_name' => 'Test Bank',
                    'account_number' => '**********',
                    'account_type' => 'current',
                    'is_active' => true,
                    'is_primary' => true,
                    'additional_instructions' => 'Test payment account for billing system testing.'
                ]);
                $this->info('   Sample payment account created');
            }

            // Test 2: Check organizations with subscriptions
            $this->info('2. Checking organizations with subscriptions...');
            $orgsWithSubs = Organization::whereHas('subscriptions')->count();
            $this->info("   Found {$orgsWithSubs} organizations with subscriptions");

            if ($orgsWithSubs === 0) {
                $this->warn('   No organizations with subscriptions found.');
                $this->info('   Please create an organization and subscription first using:');
                $this->info('   php artisan subscription:test');
                return;
            }

            // Test 3: Get a sample organization and subscription
            $organization = Organization::whereHas('subscriptions')->first();
            $subscription = $organization->subscriptions()->latest()->first();
            
            $this->info("3. Testing with organization: {$organization->name}");
            $this->info("   Subscription Plan: {$subscription->plan->name}");
            $this->info("   Subscription Status: {$subscription->status}");

            // Test 4: Create a test payment
            $this->info('4. Creating test payment...');
            $payment = SubscriptionPayment::create([
                'subscription_id' => $subscription->id,
                'organization_id' => $organization->id,
                'payment_reference' => 'TEST-' . now()->format('YmdHis'),
                'amount' => $subscription->plan->price,
                'payment_method' => 'bank_transfer',
                'payment_date' => now(),
                'status' => 'pending',
                'notes' => 'Test payment created by billing system test command'
            ]);
            $this->info("   Test payment created with ID: {$payment->id}");

            // Test 5: Test payment approval
            $this->info('5. Testing payment approval...');
            $superAdmin = User::whereHas('roles', function($q) {
                $q->where('name', 'Super Admin');
            })->first();

            if (!$superAdmin) {
                $this->warn('   No super admin found. Creating test super admin...');
                $superAdmin = User::create([
                    'name' => 'Test Super Admin',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now()
                ]);
                $superAdmin->assignRole('Super Admin');
                $this->info('   Test super admin created');
            }

            $payment->approve($superAdmin);
            $this->info('   Payment approved successfully');

            // Test 6: Test invoice generation
            $this->info('6. Testing invoice generation...');
            if (!$payment->invoice_number) {
                $payment->update([
                    'invoice_number' => $payment->generateInvoiceNumber(),
                    'invoice_generated_at' => now()
                ]);
            }
            $this->info("   Invoice generated: {$payment->invoice_number}");

            // Test 7: Test subscription calculations
            $this->info('7. Testing subscription calculations...');
            $totalPaid = $subscription->calculateTotalPaid();
            $outstandingBalance = $subscription->getOutstandingBalance();
            $this->info("   Total paid: \${$totalPaid}");
            $this->info("   Outstanding balance: \${$outstandingBalance}");

            // Test 8: Test payment history
            $this->info('8. Testing payment history...');
            $paymentCount = $subscription->payments()->count();
            $approvedCount = $subscription->approvedPayments()->count();
            $pendingCount = $subscription->pendingPayments()->count();
            $this->info("   Total payments: {$paymentCount}");
            $this->info("   Approved payments: {$approvedCount}");
            $this->info("   Pending payments: {$pendingCount}");

            $this->info('');
            $this->info('✅ Billing system test completed successfully!');
            $this->info('');
            $this->info('Test Results Summary:');
            $this->info("- Payment Accounts: {$paymentAccounts} active");
            $this->info("- Organizations with Subscriptions: {$orgsWithSubs}");
            $this->info("- Test Payment ID: {$payment->id}");
            $this->info("- Invoice Number: {$payment->invoice_number}");
            $this->info("- Payment Status: {$payment->status}");
            $this->info('');
            $this->info('You can now test the billing system in the web interface:');
            $this->info('- User Billing Dashboard: /billing');
            $this->info('- Super Admin Payment Accounts: /super-admin/payment-accounts');
            $this->info('- Super Admin Payment Management: /super-admin/subscription-payments');

        } catch (\Exception $e) {
            $this->error('❌ Billing system test failed!');
            $this->error('Error: ' . $e->getMessage());
            $this->error('File: ' . $e->getFile() . ':' . $e->getLine());
            return 1;
        }

        return 0;
    }
}
