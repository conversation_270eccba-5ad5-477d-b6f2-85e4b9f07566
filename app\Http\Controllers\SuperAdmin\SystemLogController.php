<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\SystemLog;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Carbon\Carbon;

class SystemLogController extends Controller
{
    public function __construct()
    {
        $this->middleware('super_admin');
    }

    /**
     * Display the system logs dashboard
     */
    public function index(Request $request)
    {
        $query = SystemLog::with(['user', 'organization'])
            ->orderBy('datetime', 'desc');

        // Apply filters
        $this->applyFilters($query, $request);

        // Get paginated results
        $logs = $query->paginate(50)->appends($request->query());

        // Get filter options
        $filterOptions = $this->getFilterOptions();

        // Get statistics
        $stats = $this->getLogStatistics($request);

        return view('super_admin.system_logs.index', compact(
            'logs',
            'filterOptions',
            'stats'
        ));
    }

    /**
     * Show detailed view of a specific log entry
     */
    public function show(SystemLog $systemLog)
    {
        $systemLog->load(['user', 'organization']);
        
        // Get related logs (same request ID or session)
        $relatedLogs = SystemLog::where(function($query) use ($systemLog) {
            if ($systemLog->request_id) {
                $query->where('request_id', $systemLog->request_id);
            } elseif ($systemLog->session_id) {
                $query->where('session_id', $systemLog->session_id)
                      ->where('datetime', '>=', $systemLog->datetime->subMinutes(5))
                      ->where('datetime', '<=', $systemLog->datetime->addMinutes(5));
            }
        })
        ->where('id', '!=', $systemLog->id)
        ->orderBy('datetime')
        ->limit(20)
        ->get();

        return view('super_admin.system_logs.show', compact('systemLog', 'relatedLogs'));
    }

    /**
     * Export logs to CSV
     */
    public function export(Request $request)
    {
        $query = SystemLog::with(['user', 'organization'])
            ->orderBy('datetime', 'desc');

        // Apply same filters as index
        $this->applyFilters($query, $request);

        // Limit export to prevent memory issues
        $logs = $query->limit(10000)->get();

        $filename = 'system_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($logs) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'ID',
                'DateTime',
                'Level',
                'Channel',
                'Message',
                'User',
                'Organization',
                'IP Address',
                'URL',
                'Method',
                'Status Code',
                'Response Time',
                'Memory Usage',
                'Environment'
            ]);

            // CSV data
            foreach ($logs as $log) {
                fputcsv($file, [
                    $log->id,
                    $log->datetime->format('Y-m-d H:i:s'),
                    $log->level,
                    $log->channel,
                    $log->message,
                    $log->user ? $log->user->name : '',
                    $log->organization ? $log->organization->name : '',
                    $log->ip_address,
                    $log->url,
                    $log->method,
                    $log->status_code,
                    $log->formatted_response_time,
                    $log->formatted_memory_usage,
                    $log->environment
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    /**
     * Delete old logs based on retention policy
     */
    public function cleanup(Request $request)
    {
        $request->validate([
            'days' => 'required|integer|min:1|max:365'
        ]);

        $cutoffDate = now()->subDays($request->days);
        $deletedCount = SystemLog::where('datetime', '<', $cutoffDate)->delete();

        return back()->with('success', "Deleted {$deletedCount} log entries older than {$request->days} days.");
    }

    /**
     * Get real-time log statistics via AJAX
     */
    public function stats(Request $request)
    {
        $stats = $this->getLogStatistics($request);
        return response()->json($stats);
    }

    /**
     * Apply filters to the query
     */
    private function applyFilters($query, Request $request)
    {
        // Date range filter
        if ($request->filled('date_from')) {
            $query->where('datetime', '>=', Carbon::parse($request->date_from)->startOfDay());
        }
        
        if ($request->filled('date_to')) {
            $query->where('datetime', '<=', Carbon::parse($request->date_to)->endOfDay());
        }

        // Level filter
        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        // Channel filter
        if ($request->filled('channel')) {
            $query->where('channel', $request->channel);
        }

        // Organization filter
        if ($request->filled('organization_id')) {
            $query->where('organization_id', $request->organization_id);
        }

        // User filter
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Search filter
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Critical logs only
        if ($request->boolean('critical_only')) {
            $query->critical();
        }

        // Recent logs (default to last 24 hours if no date filters)
        if (!$request->filled('date_from') && !$request->filled('date_to')) {
            $hours = $request->get('hours', 24);
            $query->recent($hours);
        }
    }

    /**
     * Get filter options for dropdowns
     */
    private function getFilterOptions()
    {
        return [
            'levels' => SystemLog::getLogLevels(),
            'channels' => SystemLog::getLogChannels(),
            'organizations' => Organization::where('is_active', true)
                ->orderBy('name')
                ->pluck('name', 'id'),
            'users' => User::where('status', 'active')
                ->orderBy('name')
                ->limit(100)
                ->pluck('name', 'id')
        ];
    }

    /**
     * Get log statistics
     */
    private function getLogStatistics(Request $request)
    {
        $baseQuery = SystemLog::query();
        
        // Apply same date filters
        if ($request->filled('date_from')) {
            $baseQuery->where('datetime', '>=', Carbon::parse($request->date_from)->startOfDay());
        }
        
        if ($request->filled('date_to')) {
            $baseQuery->where('datetime', '<=', Carbon::parse($request->date_to)->endOfDay());
        } else {
            // Default to last 24 hours
            $baseQuery->recent($request->get('hours', 24));
        }

        return [
            'total_logs' => (clone $baseQuery)->count(),
            'critical_logs' => (clone $baseQuery)->critical()->count(),
            'by_level' => (clone $baseQuery)
                ->selectRaw('level, COUNT(*) as count')
                ->groupBy('level')
                ->pluck('count', 'level')
                ->toArray(),
            'by_channel' => (clone $baseQuery)
                ->selectRaw('channel, COUNT(*) as count')
                ->groupBy('channel')
                ->pluck('count', 'channel')
                ->toArray(),
            'recent_errors' => (clone $baseQuery)
                ->where('level', 'error')
                ->orderBy('datetime', 'desc')
                ->limit(5)
                ->get(),
            'top_organizations' => (clone $baseQuery)
                ->whereNotNull('organization_id')
                ->selectRaw('organization_id, COUNT(*) as count')
                ->groupBy('organization_id')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->with('organization')
                ->get()
        ];
    }
}
