@extends('layouts.app')

@section('title', $article->title)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ route('user.support.index') }}">Support Center</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ route('user.support.knowledge-base') }}">Knowledge Base</a>
                    </li>
                    @if($article->category)
                    <li class="breadcrumb-item">
                        <a href="{{ route('user.support.category', $article->category->id) }}">{{ $article->category->name }}</a>
                    </li>
                    @endif
                    <li class="breadcrumb-item active" aria-current="page">{{ $article->title }}</li>
                </ol>
            </nav>

            <div class="row">
                <!-- Article Content -->
                <div class="col-lg-9">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h1 class="h3 mb-2">{{ $article->title }}</h1>
                                    <div class="d-flex align-items-center text-muted">
                                        <span class="me-3">
                                            <i class="fas fa-folder me-1"></i>
                                            {{ $article->category->name ?? 'General' }}
                                        </span>
                                        <span class="me-3">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ $article->reading_time }} min read
                                        </span>
                                        <span class="me-3">
                                            <i class="fas fa-eye me-1"></i>
                                            {{ $article->view_count }} views
                                        </span>
                                        @if($article->helpfulness_percentage)
                                            <span>
                                                <i class="fas fa-thumbs-up me-1"></i>
                                                {{ $article->helpfulness_percentage }}% helpful
                                            </span>
                                        @endif
                                    </div>
                                </div>
                                <div>
                                    {!! $article->visibility_badge !!}
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            @if($article->excerpt)
                                <div class="alert alert-info">
                                    <strong>Summary:</strong> {{ $article->excerpt }}
                                </div>
                            @endif

                            <div class="article-content">
                                {!! nl2br(e($article->content)) !!}
                            </div>

                            <!-- Article Tags -->
                            @if($article->tags && count($article->tags) > 0)
                                <div class="mt-4 pt-3 border-top">
                                    <h6>Tags:</h6>
                                    @foreach($article->tags as $tag)
                                        <span class="badge bg-secondary me-1">{{ $tag }}</span>
                                    @endforeach
                                </div>
                            @endif

                            <!-- Article Rating -->
                            <div class="mt-4 pt-3 border-top">
                                <h6>Was this article helpful?</h6>
                                <div class="d-flex align-items-center">
                                    <button type="button" class="btn btn-outline-success me-2" onclick="rateArticle(true)">
                                        <i class="fas fa-thumbs-up me-1"></i>
                                        Yes ({{ $article->helpful_count }})
                                    </button>
                                    <button type="button" class="btn btn-outline-danger me-3" onclick="rateArticle(false)">
                                        <i class="fas fa-thumbs-down me-1"></i>
                                        No ({{ $article->not_helpful_count }})
                                    </button>
                                    <small class="text-muted">
                                        Your feedback helps us improve our documentation
                                    </small>
                                </div>

                                <!-- Feedback Form (Hidden by default) -->
                                <div id="feedbackForm" class="mt-3" style="display: none;">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6>Tell us more (optional):</h6>
                                            <textarea id="feedbackComment" class="form-control mb-3" rows="3"
                                                      placeholder="How can we improve this article?"></textarea>
                                            <div class="d-flex justify-content-end">
                                                <button type="button" class="btn btn-secondary me-2" onclick="cancelFeedback()">
                                                    Cancel
                                                </button>
                                                <button type="button" class="btn btn-primary" onclick="submitFeedback()">
                                                    Submit Feedback
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Still Need Help? -->
                    <div class="card mt-4">
                        <div class="card-body text-center">
                            <h5 class="card-title">
                                <i class="fas fa-question-circle me-2"></i>
                                Still need help?
                            </h5>
                            <p class="card-text">If this article didn't solve your problem, our support team is here to help.</p>
                            <a href="{{ route('user.support.create') }}" class="btn btn-primary">
                                <i class="fas fa-envelope me-2"></i>
                                Contact Support
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-3">
                    <!-- Article Info -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Article Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>Category:</strong><br>
                                @if($article->category)
                                    <a href="{{ route('user.support.category', $article->category->id) }}" class="text-decoration-none">
                                        {{ $article->category->name }}
                                    </a>
                                @else
                                    <span class="text-muted">General</span>
                                @endif
                            </div>
                            <div class="mb-2">
                                <strong>Published:</strong><br>
                                {{ $article->published_at ? $article->published_at->format('M d, Y') : 'N/A' }}
                            </div>
                            <div class="mb-2">
                                <strong>Last Updated:</strong><br>
                                {{ $article->updated_at->format('M d, Y') }}
                            </div>
                            <div class="mb-2">
                                <strong>Views:</strong><br>
                                {{ number_format($article->view_count) }}
                            </div>
                            @if($article->helpfulness_percentage)
                            <div class="mb-2">
                                <strong>Helpfulness:</strong><br>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-success" role="progressbar"
                                         style="width: {{ $article->helpfulness_percentage }}%">
                                        {{ $article->helpfulness_percentage }}%
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Related Articles -->
                    @if($relatedArticles->count() > 0)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-link me-2"></i>
                                Related Articles
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                @foreach($relatedArticles as $relatedArticle)
                                <a href="{{ route('user.support.article', $relatedArticle) }}"
                                   class="list-group-item list-group-item-action">
                                    <h6 class="mb-1">{{ $relatedArticle->title }}</h6>
                                    <p class="mb-1 text-muted small">{{ $relatedArticle->truncated_excerpt }}</p>
                                    <small class="text-muted">{{ $relatedArticle->reading_time }}m read</small>
                                </a>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('user.support.knowledge-base') }}" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-book me-2"></i>Browse Knowledge Base
                                </a>
                                <a href="{{ route('user.support.search') }}" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-search me-2"></i>Search Articles
                                </a>
                                <a href="{{ route('user.support.create') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-envelope me-2"></i>Contact Support
                                </a>
                                <a href="{{ route('user.support.index') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-home me-2"></i>Support Center
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.article-content {
    font-size: 1.1rem;
    line-height: 1.7;
}

.article-content h1, .article-content h2, .article-content h3 {
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.article-content p {
    margin-bottom: 1rem;
}

.article-content ul, .article-content ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
}

.article-content code {
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.9rem;
}

.progress {
    background-color: #e9ecef;
}
</style>

<script>
let selectedRating = null;

function rateArticle(isHelpful) {
    selectedRating = isHelpful;
    document.getElementById('feedbackForm').style.display = 'block';

    // Scroll to feedback form
    document.getElementById('feedbackForm').scrollIntoView({ behavior: 'smooth' });
}

function cancelFeedback() {
    document.getElementById('feedbackForm').style.display = 'none';
    document.getElementById('feedbackComment').value = '';
    selectedRating = null;
}

function submitFeedback() {
    if (selectedRating === null) {
        alert('Please select whether this article was helpful or not.');
        return;
    }

    const comment = document.getElementById('feedbackComment').value;

    fetch('{{ route("user.support.rate-article", $article->id) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            helpful: selectedRating,
            comment: comment
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            document.getElementById('feedbackForm').style.display = 'none';
            // Optionally reload the page to update counters
            window.location.reload();
        } else {
            alert(data.message || 'An error occurred while submitting your feedback.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while submitting your feedback.');
    });
}
</script>
@endsection
