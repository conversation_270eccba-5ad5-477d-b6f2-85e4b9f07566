<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing plans to support both monthly and annual billing with 15% discount
        $plans = DB::table('plans')->get();
        
        foreach ($plans as $plan) {
            // Calculate annual price with 15% discount if not already set
            $annualPrice = null;
            if (!$plan->annual_price) {
                $monthlyPrice = $plan->price;
                $annualPrice = ($monthlyPrice * 12) * 0.85; // 15% discount
            }
            
            DB::table('plans')
                ->where('id', $plan->id)
                ->update([
                    'annual_price' => $annualPrice ?: $plan->annual_price,
                    'billing_period' => $plan->billing_period ?: 'both',
                    'annual_discount_percentage' => $plan->annual_discount_percentage ?: 15,
                ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Optionally revert changes if needed
        // DB::table('plans')->update([
        //     'annual_price' => null,
        //     'billing_period' => 'monthly',
        //     'annual_discount_percentage' => 0,
        // ]);
    }
};
