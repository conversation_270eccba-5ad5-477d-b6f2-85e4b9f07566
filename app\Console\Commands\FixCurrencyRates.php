<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\CurrencyRate;
use App\Models\CurrencySetting;

class FixCurrencyRates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'currency:fix-rates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix and create missing currency rates';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Fixing Currency Rates...');
        
        // Check current rates
        $usdToNgnRate = CurrencyRate::getCurrentRate('USD', 'NGN');
        $ngnToUsdRate = CurrencyRate::getCurrentRate('NGN', 'USD');
        
        $this->info("Current USD to NGN rate: " . ($usdToNgnRate ? number_format($usdToNgnRate, 4) : 'NOT FOUND'));
        $this->info("Current NGN to USD rate: " . ($ngnToUsdRate ? number_format($ngnToUsdRate, 6) : 'NOT FOUND'));
        
        if (!$usdToNgnRate) {
            $this->info('Creating missing currency rates...');
            
            // Create USD to NGN rate
            $rate = 1500.00; // 1 USD = 1500 NGN
            
            CurrencyRate::updateOrCreate(
                ['from_currency' => 'USD', 'to_currency' => 'NGN'],
                [
                    'rate' => $rate,
                    'is_active' => true,
                    'effective_from' => now(),
                ]
            );
            
            // Create NGN to USD rate (reverse)
            CurrencyRate::updateOrCreate(
                ['from_currency' => 'NGN', 'to_currency' => 'USD'],
                [
                    'rate' => 1 / $rate,
                    'is_active' => true,
                    'effective_from' => now(),
                ]
            );
            
            $this->info("✅ Created USD to NGN rate: " . number_format($rate, 2));
            $this->info("✅ Created NGN to USD rate: " . number_format(1 / $rate, 6));
        } else {
            $this->info('Currency rates already exist.');
        }
        
        // Test conversion
        $this->info('Testing currency conversion...');

        // Enable NGN override for testing
        CurrencySetting::set('system_currency_override_enabled', true);
        CurrencySetting::set('system_currency_override', 'NGN');

        $testAmount = 59.99;
        $this->info("Testing conversion of $" . number_format($testAmount, 2) . " USD...");

        // Test step by step
        $currencyService = app(\App\Services\CurrencyService::class);

        $this->info("Step 1: Helper functions");
        $this->info("  user_currency(): " . user_currency());
        $this->info("  base_currency(): " . base_currency());
        $this->info("  user_currency_symbol(): " . user_currency_symbol());

        $this->info("Step 2: Manual conversion");
        try {
            $converted = $currencyService->convert($testAmount, 'USD', 'NGN');
            $this->info("  Manual convert: " . number_format($converted, 2) . " NGN");

            $manualFormatted = $currencyService->format($converted, 'NGN');
            $this->info("  Manual format: " . $manualFormatted);
        } catch (\Exception $e) {
            $this->error("  Manual conversion failed: " . $e->getMessage());
        }

        $this->info("Step 3: format_price() function");
        $this->info("  Testing format_price($testAmount) - assuming input is USD");
        $formatted = format_price($testAmount);
        $this->info("  format_price result: " . $formatted);

        $this->info("  Testing format_price($testAmount, 'USD') - explicit USD");
        $formattedExplicit = format_price($testAmount, 'USD');
        $this->info("  format_price(USD) result: " . $formattedExplicit);

        // Calculate expected amount
        $currentRate = CurrencyRate::getCurrentRate('USD', 'NGN');
        $expectedAmount = $testAmount * $currentRate;
        $this->info("  Expected: ₦" . number_format($expectedAmount, 2));

        // Check if conversion is actually working
        if (strpos($formatted, '₦') !== false) {
            $extractedAmount = (float) str_replace(['₦', ','], '', $formatted);
            if (abs($extractedAmount - $expectedAmount) < 1) {
                $this->info('✅ Currency conversion is working correctly!');
            } else {
                $this->error('❌ Currency conversion is NOT working properly!');
                $this->error("  Got: ₦" . number_format($extractedAmount, 2));
                $this->error("  Expected: ₦" . number_format($expectedAmount, 2));
                $this->error("  The system is only changing symbols, not converting amounts!");

                // Debug the issue
                $this->info("Debug: Checking what's happening in format_price()");
                $this->info("  fromCurrency (default): " . base_currency());
                $this->info("  userCurrency: " . user_currency());
                $this->info("  Are they different? " . (base_currency() !== user_currency() ? 'YES' : 'NO'));

                if (base_currency() !== user_currency()) {
                    $this->info("  Should convert from " . base_currency() . " to " . user_currency());
                    try {
                        $debugConvert = $currencyService->convert($testAmount, base_currency(), user_currency());
                        $this->info("  Debug conversion result: " . number_format($debugConvert, 2));
                    } catch (\Exception $e) {
                        $this->error("  Debug conversion failed: " . $e->getMessage());
                    }
                } else {
                    $this->error("  Currencies are the same - no conversion happening!");
                }
            }
        } else {
            $this->error('❌ Still showing USD symbol instead of NGN.');
        }
        
        $this->info('Currency rates fix completed!');
        
        return 0;
    }
}
