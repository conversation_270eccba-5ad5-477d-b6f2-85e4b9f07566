<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_logs', function (Blueprint $table) {
            $table->id();
            
            // Core log information
            $table->string('level', 20)->index(); // emergency, alert, critical, error, warning, notice, info, debug
            $table->text('message');
            $table->json('context')->nullable();
            $table->string('channel', 50)->index(); // application, auth, database, security, etc.
            $table->timestamp('datetime')->index();
            
            // User and organization context
            $table->unsignedBigInteger('user_id')->nullable()->index();
            $table->unsignedBigInteger('organization_id')->nullable()->index();
            
            // Request context
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->text('url')->nullable();
            $table->string('method', 10)->nullable();
            $table->integer('status_code')->nullable();
            
            // Performance metrics
            $table->decimal('response_time', 8, 3)->nullable(); // in seconds
            $table->bigInteger('memory_usage')->nullable(); // in bytes
            
            // Additional metadata
            $table->json('tags')->nullable();
            $table->string('environment', 20)->nullable();
            $table->string('session_id')->nullable()->index();
            $table->string('request_id')->nullable()->index();
            
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('organization_id')->references('id')->on('organizations')->onDelete('set null');
            
            // Composite indexes for common queries
            $table->index(['level', 'datetime']);
            $table->index(['channel', 'datetime']);
            $table->index(['organization_id', 'datetime']);
            $table->index(['user_id', 'datetime']);
            $table->index(['datetime', 'level', 'channel']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_logs');
    }
};
