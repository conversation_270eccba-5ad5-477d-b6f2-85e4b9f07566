<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Organization;
use App\Models\Plan;
use App\Models\Subscription;
use App\Services\ProrationService;
use Carbon\Carbon;

class TestPhase4Features extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:phase4-features';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Phase 4 feature restrictions implementation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 TESTING PHASE 4 FEATURES');
        $this->newLine();

        // Test 1: Data Retention Policies
        $this->testDataRetentionPolicies();

        // Test 2: Proration Calculations
        $this->testProrationCalculations();

        // Test 3: Grace Period Implementation
        $this->testGracePeriod();

        // Test 4: Plan Change Workflow
        $this->testPlanChangeWorkflow();

        // Test 5: Expiration Notifications (dry run)
        $this->testExpirationNotifications();

        $this->newLine();
        $this->info('✅ ALL PHASE 4 FEATURES TESTED SUCCESSFULLY!');
        
        return 0;
    }

    private function testDataRetentionPolicies()
    {
        $this->info('📊 Testing Data Retention Policies...');
        
        try {
            // Test dry run of data retention enforcement
            $exitCode = $this->call('data:enforce-retention', ['--dry-run' => true]);
            
            if ($exitCode === 0) {
                $this->line('  ✅ Data retention enforcement command works');
            } else {
                $this->error('  ❌ Data retention enforcement command failed');
            }
        } catch (\Exception $e) {
            $this->error('  ❌ Data retention test failed: ' . $e->getMessage());
        }
    }

    private function testProrationCalculations()
    {
        $this->info('💰 Testing Proration Calculations...');
        
        try {
            $prorationService = app(ProrationService::class);
            
            // Get sample plans
            $plans = Plan::take(2)->get();
            if ($plans->count() < 2) {
                $this->warn('  ⚠️  Need at least 2 plans to test proration');
                return;
            }

            // Create a mock subscription
            $organization = Organization::first();
            if (!$organization) {
                $this->warn('  ⚠️  Need at least 1 organization to test proration');
                return;
            }

            $subscription = new Subscription([
                'organization_id' => $organization->id,
                'plan_id' => $plans[0]->id,
                'status' => 'active',
                'start_date' => Carbon::now()->subDays(10),
                'end_date' => Carbon::now()->addDays(20),
                'amount_paid' => $plans[0]->price,
            ]);

            // Test proration calculation
            $proration = $prorationService->calculatePlanChange($subscription, $plans[1]);
            
            $this->line('  ✅ Proration calculation works');
            $this->line("    Type: {$proration['type']}");
            $this->line("    Net Amount: \${$proration['net_amount']}");
            $this->line("    Remaining Days: {$proration['remaining_days']}");
            
        } catch (\Exception $e) {
            $this->error('  ❌ Proration test failed: ' . $e->getMessage());
        }
    }

    private function testGracePeriod()
    {
        $this->info('⏰ Testing Grace Period Implementation...');
        
        try {
            $organization = Organization::first();
            if (!$organization) {
                $this->warn('  ⚠️  Need at least 1 organization to test grace period');
                return;
            }

            // Test grace period methods
            $hasAccess = $organization->hasAccess();
            $accessStatus = $organization->access_status;
            $isInGracePeriod = $organization->isInGracePeriod();
            
            $this->line('  ✅ Grace period methods work');
            $this->line("    Has Access: " . ($hasAccess ? 'Yes' : 'No'));
            $this->line("    Access Status: {$accessStatus}");
            $this->line("    In Grace Period: " . ($isInGracePeriod ? 'Yes' : 'No'));
            
        } catch (\Exception $e) {
            $this->error('  ❌ Grace period test failed: ' . $e->getMessage());
        }
    }

    private function testPlanChangeWorkflow()
    {
        $this->info('🔄 Testing Plan Change Workflow...');
        
        try {
            // Test that the ProrationService can be instantiated
            $prorationService = app(ProrationService::class);
            
            $organization = Organization::first();
            $plans = Plan::take(2)->get();
            
            if (!$organization || $plans->count() < 2) {
                $this->warn('  ⚠️  Need organization and plans to test plan change workflow');
                return;
            }

            // Test immediate upgrade calculation
            $upgradeCalculation = $prorationService->calculateImmediateUpgrade($organization, $plans[1]);
            
            $this->line('  ✅ Plan change workflow methods work');
            $this->line("    Change Type: {$upgradeCalculation['type']}");
            $this->line("    Immediate Charge: \${$upgradeCalculation['immediate_charge']}");
            
        } catch (\Exception $e) {
            $this->error('  ❌ Plan change workflow test failed: ' . $e->getMessage());
        }
    }

    private function testExpirationNotifications()
    {
        $this->info('📧 Testing Expiration Notifications...');
        
        try {
            // Test dry run of expiration notifications
            $exitCode = $this->call('notifications:send-expiration', ['--dry-run' => true]);
            
            if ($exitCode === 0) {
                $this->line('  ✅ Expiration notifications command works');
            } else {
                $this->error('  ❌ Expiration notifications command failed');
            }
        } catch (\Exception $e) {
            $this->error('  ❌ Expiration notifications test failed: ' . $e->getMessage());
        }
    }
}
