@extends('layouts.app')

@section('title', 'Payment Accounts')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Payment Accounts</h1>
                <a href="{{ route('billing.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Billing
                </a>
            </div>

            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Payment Instructions:</strong> Use any of the accounts below to make your subscription payments. 
                After making a payment, return to the billing dashboard to submit your payment details for approval.
            </div>

            @if($paymentAccounts->count() > 0)
                <div class="row">
                    @foreach($paymentAccounts as $account)
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 {{ $account->is_primary ? 'border-primary' : '' }}">
                                <div class="card-header {{ $account->is_primary ? 'bg-primary text-white' : 'bg-light' }}">
                                    <h5 class="card-title mb-0">
                                        {{ $account->bank_name }}
                                        @if($account->is_primary)
                                            <span class="badge bg-light text-primary ms-2">Primary</span>
                                        @endif
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <strong>Account Name:</strong><br>
                                        <span class="text-muted">{{ $account->account_name }}</span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <strong>Account Number:</strong><br>
                                        <code class="fs-6">{{ $account->account_number }}</code>
                                        <button class="btn btn-sm btn-outline-secondary ms-2" 
                                                onclick="copyToClipboard('{{ $account->account_number }}')"
                                                title="Copy account number">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <strong>Account Type:</strong><br>
                                        <span class="badge bg-secondary">{{ ucfirst($account->account_type) }}</span>
                                    </div>
                                    
                                    @if($account->routing_number)
                                        <div class="mb-3">
                                            <strong>Routing Number:</strong><br>
                                            <code>{{ $account->routing_number }}</code>
                                            <button class="btn btn-sm btn-outline-secondary ms-2" 
                                                    onclick="copyToClipboard('{{ $account->routing_number }}')"
                                                    title="Copy routing number">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    @endif
                                    
                                    @if($account->swift_code)
                                        <div class="mb-3">
                                            <strong>SWIFT Code:</strong><br>
                                            <code>{{ $account->swift_code }}</code>
                                            <button class="btn btn-sm btn-outline-secondary ms-2" 
                                                    onclick="copyToClipboard('{{ $account->swift_code }}')"
                                                    title="Copy SWIFT code">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    @endif
                                    
                                    @if($account->additional_instructions)
                                        <div class="alert alert-warning p-2 mt-3">
                                            <small>
                                                <strong>Instructions:</strong><br>
                                                {{ $account->additional_instructions }}
                                            </small>
                                        </div>
                                    @endif
                                </div>
                                <div class="card-footer bg-light">
                                    <small class="text-muted">
                                        <i class="fas fa-shield-alt"></i> 
                                        Secure payment processing
                                    </small>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
                
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">How to Make a Payment</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-university text-primary"></i> Bank Transfer</h6>
                                        <ol class="small">
                                            <li>Log into your online banking or visit your bank</li>
                                            <li>Select "Transfer Money" or "Send Money"</li>
                                            <li>Enter the account details from above</li>
                                            <li>Include your organization name in the description</li>
                                            <li>Keep the transfer receipt/reference number</li>
                                        </ol>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-mobile-alt text-success"></i> Mobile Money</h6>
                                        <ol class="small">
                                            <li>Open your mobile money app</li>
                                            <li>Select "Send Money" or "Transfer"</li>
                                            <li>Enter the account number as the recipient</li>
                                            <li>Add your organization name in the note</li>
                                            <li>Save the transaction ID</li>
                                        </ol>
                                    </div>
                                </div>
                                
                                <div class="alert alert-success mt-3">
                                    <i class="fas fa-check-circle"></i>
                                    <strong>After Payment:</strong> 
                                    Return to the <a href="{{ route('billing.index') }}" class="alert-link">billing dashboard</a> 
                                    and submit your payment details using the "Submit Payment" button.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @else
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-university fa-3x text-muted mb-3"></i>
                        <h5>No Payment Accounts Available</h5>
                        <p class="text-muted">Payment accounts are currently being set up. Please contact support for payment instructions.</p>
                        <a href="{{ route('billing.index') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> Back to Billing
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Toast for copy notifications -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="copyToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i class="fas fa-check-circle text-success me-2"></i>
            <strong class="me-auto">Copied!</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            Text copied to clipboard successfully.
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show toast notification
        const toast = new bootstrap.Toast(document.getElementById('copyToast'));
        toast.show();
    }).catch(function(err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        
        // Show toast notification
        const toast = new bootstrap.Toast(document.getElementById('copyToast'));
        toast.show();
    });
}
</script>
@endsection
