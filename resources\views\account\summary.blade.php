@extends('layouts.app')
@section('title', 'Account Summary')
@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Account Summary</h2>
        <div class="flex items-center space-x-4">
            <form action="{{ route('account.summary') }}" method="GET" class="flex items-center space-x-4">
                <div>
                    <input type="date" name="start_date" value="{{ $startDate->format('Y-m-d') }}"
                           class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>
                <div>
                    <input type="date" name="end_date" value="{{ $endDate->format('Y-m-d') }}"
                           class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>
                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                    Filter
                </button>
            </form>
        </div>
    </div>

    <!-- Financial Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-sm font-medium text-gray-500">Total Orders</h3>
            <p class="text-2xl font-bold text-gray-900">{{ number_format($orderStats->total_orders) }}</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-sm font-medium text-gray-500">Total Revenue</h3>
            <p class="text-2xl font-bold text-blue-600">{{ format_money($orderStats->total_revenue) }}</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-sm font-medium text-gray-500">Total Collected</h3>
            <p class="text-2xl font-bold text-green-600">{{ format_money($orderStats->total_collected) }}</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-sm font-medium text-gray-500">Total Pending</h3>
            <p class="text-2xl font-bold text-red-600">{{ format_money($orderStats->total_pending) }}</p>
        </div>
    </div>

    <!-- Additional Financial Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-medium mb-4">Expenditures & Net Revenue</h3>
            <div class="space-y-4">
                <div>
                    <span class="text-gray-500">Total Expenditures:</span>
                    <span class="text-lg font-semibold text-red-600 ml-2">{{ format_money($expenditures) }}</span>
                </div>
                <div>
                    <span class="text-gray-500">Net Revenue:</span>
                    <span class="text-lg font-semibold {{ $netRevenue >= 0 ? 'text-green-600' : 'text-red-600' }} ml-2">
                        {{ format_money($netRevenue) }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Recent Payments -->
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-medium mb-4">Recent Payments</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Order #</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Customer</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        @foreach($recentPayments as $order)
                        <tr>
                            <td class="px-4 py-2 text-sm">{{ $order->order_number }}</td>
                            <td class="px-4 py-2 text-sm">{{ $order->customer_name }}</td>
                            <td class="px-4 py-2 text-sm">{{ format_money($order->amount_paid) }}</td>
                            <td class="px-4 py-2 text-sm">{{ $order->updated_at->format('M d, Y') }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pending Payments -->
        <div class="bg-white p-6 rounded-lg shadow-sm col-span-1 lg:col-span-2">
            <h3 class="text-lg font-medium mb-4">Pending Payments</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Order #</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Customer</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Total Amount</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Paid</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Pending</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        @foreach($pendingPayments as $order)
                        <tr>
                            <td class="px-4 py-2 text-sm">
                                <a href="{{ route('orders.show', $order) }}" class="text-blue-600 hover:text-blue-800">
                                    {{ $order->order_number }}
                                </a>
                            </td>
                            <td class="px-4 py-2 text-sm">{{ $order->customer_name }}</td>
                            <td class="px-4 py-2 text-sm">{{ format_money($order->total_amount) }}</td>
                            <td class="px-4 py-2 text-sm text-green-600">{{ format_money($order->amount_paid) }}</td>
                            <td class="px-4 py-2 text-sm text-red-600">{{ format_money($order->pending_payment) }}</td>
                            <td class="px-4 py-2 text-sm">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($order->status === 'Delivered')
                                        bg-green-100 text-green-800
                                    @elseif($order->status === 'Completed')
                                        bg-purple-100 text-purple-800
                                    @elseif($order->status === 'Processing')
                                        bg-blue-100 text-blue-800
                                    @else
                                        bg-yellow-100 text-yellow-800
                                    @endif">
                                    {{ $order->status }}
                                </span>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startDateInput = document.querySelector('input[name="start_date"]');
    const endDateInput = document.querySelector('input[name="end_date"]');

    // Set max date to today
    const today = new Date().toISOString().split('T')[0];
    startDateInput.setAttribute('max', today);
    endDateInput.setAttribute('max', today);

    // Update min/max dates when either input changes
    startDateInput.addEventListener('change', function() {
        endDateInput.setAttribute('min', this.value);
        if (endDateInput.value && endDateInput.value < this.value) {
            endDateInput.value = this.value;
        }
    });

    endDateInput.addEventListener('change', function() {
        startDateInput.setAttribute('max', this.value);
        if (startDateInput.value && startDateInput.value > this.value) {
            startDateInput.value = this.value;
        }
    });
});
</script>
@endpush
@endsection
