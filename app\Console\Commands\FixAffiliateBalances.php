<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Affiliate;
use App\Models\AffiliateEarning;
use App\Models\AffiliateWithdrawal;

class FixAffiliateBalances extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'affiliate:fix-balances';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix affiliate balance calculations based on earnings and withdrawals';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting affiliate balance fix...');

        $affiliates = Affiliate::all();
        $fixed = 0;

        foreach ($affiliates as $affiliate) {
            $this->info("Processing affiliate: {$affiliate->user->name} (ID: {$affiliate->id})");

            // Calculate correct balances
            $totalEarnings = $affiliate->earnings()->sum('amount');
            $pendingEarnings = $affiliate->earnings()->where('status', AffiliateEarning::STATUS_PENDING)->sum('amount');
            $approvedEarnings = $affiliate->earnings()->where('status', AffiliateEarning::STATUS_APPROVED)->sum('amount');
            $totalWithdrawn = $affiliate->withdrawals()->where('status', AffiliateWithdrawal::STATUS_PAID)->sum('amount');

            // Calculate available balance (approved earnings minus withdrawn amount)
            $availableBalance = $approvedEarnings - $totalWithdrawn;

            $this->line("  Current balances:");
            $this->line("    Total Earnings: {$affiliate->total_earnings} -> {$totalEarnings}");
            $this->line("    Pending Balance: {$affiliate->pending_balance} -> {$pendingEarnings}");
            $this->line("    Available Balance: {$affiliate->available_balance} -> {$availableBalance}");
            $this->line("    Withdrawn Amount: {$affiliate->withdrawn_amount} -> {$totalWithdrawn}");

            // Update affiliate balances
            $affiliate->update([
                'total_earnings' => $totalEarnings,
                'pending_balance' => $pendingEarnings,
                'available_balance' => max(0, $availableBalance), // Ensure non-negative
                'withdrawn_amount' => $totalWithdrawn,
            ]);

            $fixed++;
            $this->info("  ✓ Fixed balances for {$affiliate->user->name}");
        }

        $this->info("Completed! Fixed balances for {$fixed} affiliates.");

        // Show summary
        $this->newLine();
        $this->info('Summary of all affiliates:');
        $this->table(
            ['Affiliate', 'Total Earnings', 'Pending', 'Available', 'Withdrawn'],
            Affiliate::with('user')->get()->map(function ($affiliate) {
                return [
                    $affiliate->user->name,
                    '$' . number_format($affiliate->total_earnings, 2),
                    '$' . number_format($affiliate->pending_balance, 2),
                    '$' . number_format($affiliate->available_balance, 2),
                    '$' . number_format($affiliate->withdrawn_amount, 2),
                ];
            })
        );

        return 0;
    }
}
